<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wosai.pantheon</groupId>
    <artifactId>uf4c-app</artifactId>
    <version>5.21.0</version>
    <packaging>pom</packaging>
    <name>uf4c-app</name>
    <description>Wosai Project For Spring Boot</description>

    <parent>
        <groupId>com.wosai.pantheon</groupId>
        <artifactId>uranus</artifactId>
        <version>1.1.6</version>
    </parent>

    <modules>
        <module>uf4c-app-api</module>
        <module>uf4c-app-web</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <wosai.common.version>1.6.7</wosai.common.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <nextgen.version>2.0-SNAPSHOT</nextgen.version>
        <smart-goods.version>2.61.0</smart-goods.version>
    </properties>


    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

    <!-- 统一管理依赖版本 -->
    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.wosai.smart.goods</groupId>
                <artifactId>smart-goods-api</artifactId>
                <version>${smart-goods.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wosai.smart.goods</groupId>
                <artifactId>smart-goods-common</artifactId>
                <version>${smart-goods.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>4.1.116.Final</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.wosai.market</groupId>
                <artifactId>awesome-goods-api</artifactId>
                <version>2.32.0</version>
            </dependency>

            <dependency>
                <groupId>com.wosai.pantheon</groupId>
                <artifactId>uitem-core-api</artifactId>
                <version>2.48.0</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

</project>
