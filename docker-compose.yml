version: "2"
services:
  uf4c-app:
    build: .
    image: registry.wosai-inc.com/uf4c-app:$tag
    restart: always
    container_name: uf4c-app
    environment:
      - JAVA_TOOL_OPTIONS=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=50 -Xloggc:/app/log/gc.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Dspring.profiles.active=test -Djava.security.properties=/etc/java.security -Dserver.port=8080 -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
      - logDir=/app/log
    volumes:
      - /app/log/uf4c-app:/app/log
      - /opt/data:/opt/data
      - /opt/settings:/opt/settings