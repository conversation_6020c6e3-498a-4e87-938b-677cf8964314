# 引用git.wosai-inc.com/do/gitlab-ci这个项目中的maven.yml作为CI模板
#include:
#  project: "do/gitlab-ci"
#  file: "/maven.yml"
#  file: "/autodeploy.yml"
include:
  - project: "do/gitlab-ci"
    file: "/maven.yml"
  - project: "do/gitlab-ci"
    file: "/autodeploy.yml"

# 你的其他作业和配置...
# 以下是选填项
variables:
  MAVEN_CLI_OPTS: "-U -Dmaven.test.skip=true"
  JFROG_MVNC_OPTS: "--exclude-patterns=*uf4c-app-web*.jar"


#stages:
#  - deploy
#deploy-prod:
#  stage: deploy
#  script:
#    - mvn clean -U install -Dmaven.test.skip=true sonar:sonar -Dsonar.host.url=https://sonar.wosai-inc.com -Dsonar.login=****************************************
#    - docker build -t registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_NAME}-${CI_BUILD_REF} .
#    - docker push registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_NAME}-${CI_BUILD_REF}
#  tags:
#    - shell
#  only:
#    - tags
#deploy-release:
#  stage: deploy
#  script:
#    - mvn clean -U package -Dmaven.test.skip=true sonar:sonar -Dsonar.host.url=https://sonar.wosai-inc.com -Dsonar.login=****************************************
#    - docker build -t registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_SLUG}-${CI_BUILD_REF} .
#    - docker push registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_SLUG}-${CI_BUILD_REF}
#  tags:
#    - shell
#  only:
#    - /^release.*$/
#deploy-hotfix:
#  stage: deploy
#  script:
#    - mvn clean -U package -Dmaven.test.skip=true sonar:sonar -Dsonar.host.url=https://sonar.wosai-inc.com -Dsonar.login=****************************************
#    - docker build -t registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_SLUG}-${CI_BUILD_REF} .
#    - docker push registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_SLUG}-${CI_BUILD_REF}
#  tags:
#    - shell
#  only:
#    - /^hotfix.*$/
#deploy-feature:
#  stage: deploy
#  script:
#    - mvn clean -U package -Dmaven.test.skip=true sonar:sonar -Dsonar.host.url=https://sonar.wosai-inc.com -Dsonar.login=****************************************
#    - docker build -t registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_SLUG}-${CI_BUILD_REF} .
#    - docker push registry.wosai-inc.com/${CI_PROJECT_NAME}:${CI_BUILD_REF_SLUG}-${CI_BUILD_REF}
#  tags:
#    - shell
#  only:
#    - /^feature.*$/
