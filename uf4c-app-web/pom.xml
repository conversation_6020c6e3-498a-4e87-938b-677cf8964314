<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wosai.pantheon</groupId>
    <artifactId>uf4c-app-web</artifactId>
    <packaging>jar</packaging>
    <name>uf4c-app-web</name>
    <description>Wosai Project For Spring Boot</description>

    <parent>
        <groupId>com.wosai.pantheon</groupId>
        <artifactId>uf4c-app</artifactId>
        <version>5.21.0</version>
    </parent>

    <properties>
        <java.version>1.8</java.version>
        <wosai.common.version>1.6.7</wosai.common.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <nextgen.version>2.0-SNAPSHOT</nextgen.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>uf4c-app-api</artifactId>
            <version>5.21.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>2.2.4-alpha</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>28.0-jre</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
        <!--        <dependency>-->
        <!--            <groupId>com.squareup.okhttp3</groupId>-->
        <!--            <artifactId>okhttp</artifactId>-->
        <!--            <version>3.11.0</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
            <version>2.2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>vault-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>vault-sdk</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-springboot</artifactId>
            <version>5.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>wosai-database-instrumentation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.3</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.10.2</version>
        </dependency>


        <!--instrument-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-instrumentation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>apollo-client</artifactId>
                    <groupId>com.ctrip.framework.apollo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-jdbc</artifactId>
            <version>${nextgen.version}</version>
        </dependency>

        <!--web rpc-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>ufood-core-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>2.8.20-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>uitem-core-api</artifactId>
        </dependency>


        <!--test-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-test</artifactId>
        </dependency>

        <!--jackon-->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!--spring boot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.11.4</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.2.2.RELEASE</version>
        </dependency>

        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.4</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>3.7.26.ALL</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>mcc-api</artifactId>
            <version>1.50.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.market.user</groupId>
            <artifactId>customer-user-api</artifactId>
            <version>1.98.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>jjz-dataworks-api</artifactId>
            <version>1.3.0-SNAPSHOT</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.wosai.pantheon</groupId>-->
        <!--            <artifactId>awesome-order-api</artifactId>-->
        <!--            <version>3.1.0.sds-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>merchant-api</artifactId>
            <version>6.71.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>payment-sdk-java</artifactId>
            <version>1.28.2</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>awesome-goods-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>data-report-api</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>tethys-api</artifactId>
            <version>4.93.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.svc.access</groupId>
                    <artifactId>ka-svc-access-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>painter-api</artifactId>
            <version>1.43.1</version>
        </dependency>


        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>user-center-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>aop-gateway-api</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>merchant-user-api</artifactId>
            <version>1.5.3</version>
        </dependency>



        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>mini-apps-manage-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.6</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
            <version>1.4.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
            <version>1.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.shouqianba.mk</groupId>
            <artifactId>campus-takeout-delivery-api</artifactId>
            <version>4.31.2-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-commons</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
            <version>1.1.15</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-trace</artifactId>
            <version>1.1.15</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-qrcode-api</artifactId>
            <version>1.2.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>app-push-api</artifactId>
            <version>2.6.7-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>uprint-core-api</artifactId>
            <version>2.41.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>smartbiz-base</artifactId>
            <version>1.18.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>data-events-api</artifactId>
            <version>2.71.0</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-sentinel</artifactId>
            <version>1.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.shouqianba</groupId>
            <artifactId>campus-center-api</artifactId>
            <version>1.11.2-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.smart.goods</groupId>
            <artifactId>smart-goods-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shouqianba.smart</groupId>
            <artifactId>smart-translation-manager-api</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.shouqianba.smart</groupId>
            <artifactId>template-api</artifactId>
            <version>0.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>outer-service-adapter-api</artifactId>
            <version>1.34.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>uf4c-app</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.5</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.4.0-SNAPSHOT</version>
                <configuration>
                    <maxMessageLength>1000</maxMessageLength>
                    <enableCallerData>true</enableCallerData>
                    <!-- Pattern 需要自己配置显式打印 tid，线上JSON自带有tid -->
                    <patternLayout>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%tid] %-5level %logger{36}.%M - %msg%n
                    </patternLayout>
                    <profiles>
                        <profile>
                            <name>prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 输出到标准输出，格式是JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta,mock</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_FILE_JSON</ref> <!-- 输出到文件，格式是JSON -->
                                <ref>FT_CONSOLE_JSON</ref> <!-- 输出到标准输出，格式是 JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>default</name>   <!-- 在本地开发调试时，在IDE中设置 active profile为default -->
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref>
                            </references>
                        </profile>
                    </profiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
