package com.wosai.pantheon.uf4c.web.response;

import lombok.Data;

@Data
public class Response<T> {

    public static final String CODE_SUCCESS = "200";

    private String code;

    private String message;

    private T data;


    public Response(String code) {
        this.code = code;
    }

    public Response(String code, T data) {
        this.code = code;
        this.data = data;
    }

    public Response(String code, String message, T data) {
        this.code = code;
        this.data = data;
        this.message = message;
    }

    public Response(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
