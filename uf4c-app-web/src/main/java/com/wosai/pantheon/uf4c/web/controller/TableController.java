package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.service.apisix.TableService;
import com.wosai.pantheon.uf4c.web.requests.AddMustOrderRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * created by beiyu on 2021/2/17
 */
@RestController
@RequestMapping(path = "/api/v1/table", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequiredArgsConstructor
public class TableController {


    @Autowired
    TableService tableService;

    @RequestMapping("/peoplenum/update")
    @ResponseBody
    public Boolean updatePeopleNum(@RequestParam Map queryParam){
        return tableService.updatePeopleNum(ApiRequest.buildGetRequest(queryParam));
    }

    @RequestMapping("/add/must/order")
    @ResponseBody
    public boolean addMustOrderItem(@RequestBody AddMustOrderRequest addMustOrderRequest){
        if (StringUtils.isBlank(addMustOrderRequest.getFrom())){
            return false;
        }

        return tableService.addMustOrderItem(addMustOrderRequest.getTableId(), addMustOrderRequest.getStoreId(),
                addMustOrderRequest.getUserName(), addMustOrderRequest.getUserIcon(), addMustOrderRequest.getPeopleNum());
    }


    @RequestMapping("/order")
    @ResponseBody
    public Order getTableOrder(@RequestParam Map queryParam) {

        return tableService.getTableOrder(ApiRequest.buildGetRequest(queryParam));
    }


}
