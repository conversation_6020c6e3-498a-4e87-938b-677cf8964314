package com.wosai.pantheon.uf4c.gather.order;

import com.wosai.aop.gateway.model.ContentCenterGetModel;
import com.wosai.aop.gateway.response.ContentCenterVo;
import com.wosai.aop.gateway.service.ContentCenterService;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.merchant.api.CampusRemoteService;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.market.merchant.dto.WechatMchCode;
import com.wosai.market.merchant.dto.campus.CampusBaseInfo;
import com.wosai.market.merchant.dto.campus.request.StoreCampusListRequest;
import com.wosai.market.user.dto.PageResponse;
import com.wosai.market.user.dto.QueryUserAgreementRequest;
import com.wosai.market.user.dto.UserAddressDTO;
import com.wosai.market.user.service.UserAddressService;
import com.wosai.market.user.service.UserAgreementService;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.apisix.UserContext;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.gather.GatherBase;
import com.wosai.pantheon.uf4c.model.*;
import com.wosai.pantheon.uf4c.model.dto.DeliverFeeRequest;
import com.wosai.pantheon.uf4c.model.vo.BrandActivityVO;
import com.wosai.pantheon.uf4c.service.BrandActivityHelper;
import com.wosai.pantheon.uf4c.service.CartService;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.service.RedeemService;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV1;
import com.wosai.pantheon.uf4c.util.*;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.upay.core.service.SupportService;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static com.wosai.pantheon.order.enums.OrderType.TAKE_OUT_ORDER;

@Service
@Slf4j
public class GatherOrderService extends GatherBase {

    @Autowired
    private OrderServiceV1 orderServiceV1;
    @Autowired
    private CartService cartService;
    @Autowired
    private CampusRemoteService campusRemoteService;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private UserAgreementService userAgreementService;
    @Autowired
    private UserAddressService userAddressService;
    @Autowired
    private ConfigRemoteService configRemoteService;
    @Autowired
    private RedeemService redeemService;
    @Autowired
    private ContentCenterService contentCenterService;
    @Autowired
    private SupportService supportService;
    @Autowired
    private StoreRemoteService storeRemoteService;
    //    @Autowired
//    private WxGoodsHelper wxGoodsHelper;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private BrandActivityHelper brandActivityHelper;

    @Autowired
    private HttpServletRequest httpServletRequest;


    public Map<String, Object> main(GatherOrderRequest request, UserContext userContext) {
        Map<String, Object> dataMap = initMainResultMap();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        String acceptLanguage = HttpRequestUtil.getAcceptLanguage(httpServletRequest);
        if (userContext != null) {
            userContext.setXSmartAcceptLanguage(acceptLanguage);
        }
        futureList.add(tradeApp(request, dataMap));
        switch (request.getType()) {
            case SUBSCRIBE_ORDER:
                futureList.add(carts(request, dataMap, userContext));// 购物车
                if (request.isPacked()) { // 堂食打包才需要计算打包费
                    futureList.add(packAmount(request, dataMap, userContext));// 打包费
                }
                break;
            case TAKE_OUT_ORDER:
                futureList.add(packAmount(request, dataMap, userContext));// 打包费
                CompletableFuture bookTimesCompletableFuture = bookTimes(request, dataMap);
                futureList.add(bookTimesCompletableFuture);// 预约时间
                futureList.add(carts(request, dataMap, userContext));// 购物车
                futureList.add(campus(request, dataMap));// 是否校园店铺
                futureList.add(agreement(request, dataMap)); // 是否签署外卖协议
                if (Objects.isNull(request.getApiVersion()) || request.getApiVersion() <= 0) {
                    futureList.add(allAddress(request, dataMap)); // 所有地址
                }
                //待bookTimesCompletableFuture任务完成后，使用最近的的可预定时间计算配送费
                CompletableFuture deliveryCompletableFuture = bookTimesCompletableFuture.thenRunAsync(() -> defaultAddressAndDeliveryAmount(request, dataMap, userContext));
                futureList.add(deliveryCompletableFuture); // 默认地址和配送费
                break;
            case PRE_ORDER:
                futureList.add(packAmount(request, dataMap, userContext));// 打包费
                futureList.add(bookTimes(request, dataMap)); // 预约时间
                futureList.add(carts(request, dataMap, userContext));// 购物车
                futureList.add(agreement(request, dataMap)); // 是否签署外卖协议
                break;
            case EAT_FIRST_ORDER:
                futureList.add(carts(request, dataMap, userContext));// 购物车
                break;
            default:
                throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "不支持的订单类型");
        }
        // 如果当前订单类型、小程序appid支持品牌购，则查询品牌购信息
        if (brandActivityHelper.isEnabled(request.getAppid()) && BrandActivityHelper.supportOrderTypes.contains(request.getType())) {
            futureList.add(queryBrandActivity(request, dataMap)); // 品牌购
        }
        if (CollectionUtils.isNotEmpty(futureList)) {
            // 并行执行相关查询
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
            // 计算优惠
            discounts(request, dataMap);

            long totalAmount = MapUtils.getLongValue(dataMap, "totalAmount", 0)
                    + MapUtils.getLongValue(dataMap, "deliveryAmount", 0)
                    - MapUtils.getLongValue(dataMap, "reductionAmount", 0);
            // 计算订单总金额
            dataMap.put("totalAmount", totalAmount);

            if (!Objects.equals(request.getType(), TAKE_OUT_ORDER)) {
                long originalAmount = totalAmount + MapUtils.getLongValue(dataMap, "discountAmount", 0);
                // 计算非外卖订单，default金额组成
                dataMap.put("amountComposition", orderHelper.buildDefaultAmountComposition(originalAmount));
            }
            if (brandActivityHelper.isEnabled(request.getAppid())) {
                // 购物车中处理已购品牌购商品
                brandActivityHelper.processBrandProductCartNumber(dataMap);
            }
        }
        // 旧版本接口调用，主要有错误就抛出全局错误
        if ((Objects.isNull(request.getApiVersion()) || request.getApiVersion() == 0)
                && dataMap.containsKey("errors")) {
            List<ErrorInfo> errors = (List<ErrorInfo>) MapUtils.getObject(dataMap, "errors", new ArrayList<>());
            if (CollectionUtils.isNotEmpty(errors)) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, errors.get(0).getMessage());
            }
        }
        return dataMap;
    }

    public Map<String, Object> extra(GatherOrderExtraRequest request) {
        Map<String, Object> dataMap = initExtraResultMap();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        if (MiniProgramType.WECHAT == request.getMiniProgramType()) {
//            futureList.add(wxGoods(request, dataMap));
            futureList.add(wxMchInfo(request, dataMap));
        }
        if (MiniProgramType.ALIPAY == request.getMiniProgramType()) {
            futureList.add(payConfig(request, dataMap));
        }
        if (StringUtils.isNotBlank(request.getTerminalCode())
                && StringUtils.isNotBlank(request.getFieldCode())
                && StringUtils.isNotBlank(request.getFieldStyle())) {
            futureList.add(aop(request, dataMap));
        }
        if (CollectionUtils.isNotEmpty(futureList)) {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        }
        return dataMap;
    }


    private CompletableFuture tradeApp(GatherOrderRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "tradeApp");
            try {
                dataMap.put("tradeApp", orderHelper.getTradeApp(request.getStoreId(), request.getType()));
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_TRADE_APP_ERROR);
                logWarn(ReturnCode.GET_TRADE_APP_ERROR.getMessage() + ":" + e.getMessage(), "gatherTradeApp", request, e);
            }
            durationEnd(dataMap, "tradeApp");
        }), orderExtraExecutor);
    }

    /**
     * 查询品牌购活动
     *
     * @param request 聚合订单请求，包含必要的查询参数
     * @param dataMap 用于存储查询结果或其他数据的Map
     * @return CompletableFuture，表示异步执行的任务。
     * 如果不满足特定条件（如配置不支持、小程序类型不符、appid不在支持列表中），则直接返回一个空的Future。
     */
    private CompletableFuture queryBrandActivity(GatherOrderRequest request, Map<String, Object> dataMap) {
        if (!brandActivityHelper.isEnabled(request.getAppid())) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "brandActivity");
            try {
                BrandActivityVO brandActivityVO = brandActivityHelper.queryBrandActivity(request, Constants.BRAND_DISPLAY_POSITION_BRAND_CART);
                if (Objects.nonNull(brandActivityVO)) {
                    dataMap.put("brandActivity", brandActivityVO);
                }
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_WX_BRAND_ACTIVITY_ERROR);
                logWarn(ReturnCode.GET_WX_BRAND_ACTIVITY_ERROR.getMessage() + ":" + e.getMessage(), "gatherWxBrandActivity", request, e);
            }
            durationEnd(dataMap, "brandActivity");
        }), orderExtraExecutor);
    }

    /**
     * 查询微信加价购信息
     *
     * @param request
     * @param dataMap
     * @return
     */
//    private CompletableFuture wxGoods(GatherOrderExtraRequest request, Map<String, Object> dataMap) {
//        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
//            durationStart(dataMap, "wxGoods");
//            try {
//                Map wxGoodsMap = wxGoodsHelper.getStoreActs(request.getStoreId());
//                if (MapUtils.isNotEmpty(wxGoodsMap)) {
//                    dataMap.put("wxSubMchId", MapUtils.getString(wxGoodsMap, "subMchId", ""));
//                    dataMap.put("wxStoreId", MapUtils.getString(wxGoodsMap, "subStoreId", ""));
//                    dataMap.put("wxGoodsActs", MapUtils.getObject(wxGoodsMap, "actIds", ""));
//                    dataMap.put("wxProducts", MapUtils.getObject(wxGoodsMap, "products", ""));
//                }
//            } catch (Exception e) {
//                setError(dataMap, ReturnCode.GET_WX_GOODS_ERROR);
//                logError(ReturnCode.GET_WX_GOODS_ERROR.getMessage() + ":" + e.getMessage(), "gatherWxGoods", request, e);
//            }
//            durationEnd(dataMap, "wxGoods");
//        }), orderExtraExecutor);
//    }

    /**
     * 查询支付宝交易参数
     *
     * @param request
     * @param dataMap
     * @return
     */
    private CompletableFuture wxMchInfo(GatherOrderExtraRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "wxMchInfo");
            try {
                WechatMchCode wechatMchCode = storeRemoteService.getWechatMchCode(request.getStoreSn());
                if (Objects.nonNull(wechatMchCode)) {
                    dataMap.put("wxMchId", Optional.ofNullable(wechatMchCode.getMchCode()).orElse(""));
                    dataMap.put("wxSubMchId", Optional.ofNullable(wechatMchCode.getSubMchCode()).orElse(""));
                }
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_WX_MERCH_ERROR);
                logWarn(ReturnCode.GET_WX_MERCH_ERROR.getMessage() + ":" + e.getMessage(), "gatherWxMchInfo", request, e);
            }
            durationEnd(dataMap, "wxMchInfo");
        }), orderExtraExecutor);
    }

    /**
     * 查询支付宝交易参数
     *
     * @param request
     * @param dataMap
     * @return
     */
    private CompletableFuture payConfig(GatherOrderExtraRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "payConfig");
            try {
                Map params = supportService.getAllParams(request.getStoreSn(), null, request.getPayway(), request.getSubPayway());
                if (MapUtils.isNotEmpty(params)) {
                    dataMap.put("payConfig", params);
                }
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_PAY_CONFIG_ERROR);
                logWarn(ReturnCode.GET_PAY_CONFIG_ERROR.getMessage() + ":" + e.getMessage(), "gatherPayConfig", request, e);
            }
            durationEnd(dataMap, "payConfig");
        }), orderExtraExecutor);
    }

    /**
     * 查询AOP内容
     *
     * @param request
     * @param dataMap
     * @return
     */
    private CompletableFuture aop(GatherOrderExtraRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "aop");
            try {
                ContentCenterGetModel centerGetModel = new ContentCenterGetModel();
                centerGetModel.setClientVersion(request.getClientVersion());
                centerGetModel.setFieldCode(request.getFieldCode());
                centerGetModel.setMerchantId(request.getMerchantId());
                centerGetModel.setFieldStyle(request.getFieldStyle());
                centerGetModel.setTerminalCode(request.getTerminalCode());
                centerGetModel.setPage(request.getPage());
                centerGetModel.setPageSize(request.getPageSize());
                ListResult<ContentCenterVo> listResult = contentCenterService.getPlanContent(centerGetModel);
                if (Objects.nonNull(listResult)) {
                    dataMap.put("planContent", listResult);
                }
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_AOP_ERROR);
                logWarn(ReturnCode.GET_AOP_ERROR.getMessage() + ":" + e.getMessage(), "gatherAop", request, e);
            }

            durationEnd(dataMap, "aop");
        }), orderExtraExecutor);
    }


    /**
     * 门店是否在校园
     *
     * @param request
     * @param dataMap
     * @return
     */
    private CompletableFuture campus(GatherOrderRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "campus");
            try {
                StoreCampusListRequest storeCampusListRequest = new StoreCampusListRequest();
                storeCampusListRequest.setStoreId(request.getStoreId());
                List<CampusBaseInfo> list = campusRemoteService.storeCampusList(storeCampusListRequest);
                dataMap.put("inCampus", CollectionUtils.isNotEmpty(list));
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_CARTS_ERROR);
                logWarn(ReturnCode.GET_CARTS_ERROR.getMessage() + ":" + e.getMessage(), "gatherCampus", request, e);
            }
            durationEnd(dataMap, "campus");
        }), orderMainExecutor);
    }

    /**
     * 获取购物车
     *
     * @param request
     * @param dataMap
     * @return
     */
    private CompletableFuture carts(GatherOrderRequest request, Map<String, Object> dataMap, UserContext userContext) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "carts");
            try {
                ThreadLocalHelper.getRequestContextThreadLocal().get().setUserContextDTO(userContext);
                OrderMealTypeEnum mealTypeEnum = OrderType.EAT_FIRST_ORDER == request.getType() ? OrderMealTypeEnum.ROUND_MEAL : OrderMealTypeEnum.SINGLE;
                Cart cart = cartService.getCart(request.getStoreId(), request.getServiceType(), String.valueOf(request.getTableId()), mealTypeEnum, true);
                dataMap.put("cart", cart);
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_CARTS_ERROR);
                logWarn(ReturnCode.GET_CARTS_ERROR.getMessage() + ":" + e.getMessage(), "gatherCarts", request, e);
            }
            durationEnd(dataMap, "carts");
        }), orderMainExecutor);
    }

    /**
     * 查询预定时间
     *
     * @param request
     * @param dataMap
     */
    private CompletableFuture bookTimes(GatherOrderRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "bookTimes");
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("store_id", request.getStoreId());
                params.put("order_type", request.getType().name());
                List<TimeSplit.TimeResult> times = orderServiceV1.getBookTimeSplit(ApiRequest.buildGetRequest(params));
                dataMap.put("bookTimes", times);
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_BOOK_TIMES_ERROR);
                logWarn(ReturnCode.GET_BOOK_TIMES_ERROR.getMessage() + ":" + e.getMessage(), "gatherBookTimes", request, e);
            }
            durationEnd(dataMap, "bookTimes");
        }), orderMainExecutor);

    }

    /**
     * 查询默认地址和配送费
     *
     * @param request
     * @param dataMap
     */
    private void defaultAddressAndDeliveryAmount(GatherOrderRequest request, Map<String, Object> dataMap, UserContext userContext) {
        durationStart(dataMap, "defaultAddress");
        UserAddressDTO address = null;
        try {
            ThreadLocalHelper.getRequestContextThreadLocal().get().setUserContextDTO(userContext);
            // 查询默认地址
            Map<String, Object> params = new HashMap<>();
            params.put("storeId", request.getStoreId());
            params.put("appVersion", request.getApiVersion().toString());
            params.put("presetTime", request.getPresetTime());
            com.wosai.market.user.dto.apisix.ApiRequest apiRequest = new com.wosai.market.user.dto.apisix.ApiRequest();
            com.wosai.market.user.dto.apisix.UserContext userContext1 = new com.wosai.market.user.dto.apisix.UserContext();
            userContext1.setUserId(request.getUserId());
            apiRequest.setQuery(params);
            apiRequest.setUser(userContext1);
            address = userAddressService.defaultAddressV3(apiRequest);
        } catch (Exception e) {
            setError(dataMap, ReturnCode.GET_DEFAULT_ADDRESS_ERROR);
            logWarn(ReturnCode.GET_DEFAULT_ADDRESS_ERROR.getMessage() + ":" + e.getMessage(), "gatherDefaultAddress", request, e);
        }

        durationEnd(dataMap, "defaultAddress");
        durationStart(dataMap, "deliveryAmount");
        try {
            if (Objects.nonNull(address) && address.isValid()) {
                dataMap.put("defaultAddress", address);
                durationStart(dataMap, "deliveryAmount");
                Long presetTime = -1L;
                if (Objects.nonNull(request.getPresetTime()) && request.getPresetTime() > 0) {
                    presetTime = request.getPresetTime();
                } else {
                    try {
                        List<TimeSplit.TimeResult> bookTimes = (List<TimeSplit.TimeResult>) dataMap.get("bookTimes");
                        presetTime = bookTimes.get(0).getTimes().get(0);
                    } catch (Exception ignore) {
                    }
                }

                // 有默认地址，查询配送费
                DeliverInfo deliverInfo = convertDeliverInfo(address, getDeliveryType(request.getStoreId()), presetTime);
                DeliverFeeRequest deliverFeeRequest = new DeliverFeeRequest();
                deliverFeeRequest.setStoreId(request.getStoreId());
                deliverFeeRequest.setDeliveryInfo(deliverInfo);
                Map data = orderServiceV1.getDeliveryFee(new ApiRequest<>(deliverFeeRequest));
                dataMap.put("deliveryAmount", MapUtils.getLongValue(data, "delivery_fee"));
                dataMap.put("reductionAmount", MapUtils.getLongValue(data, "reduction_amount"));
                if (Objects.nonNull(MapUtils.getObject(data, "amountComposition"))) {
                    dataMap.put("amountComposition", MapUtils.getObject(data, "amountComposition"));
                }

            }
        } catch (Exception e) {
            ReturnCode returnCode = ReturnCode.GET_DELIVERY_AMOUNT_ERROR;
            if (e instanceof BusinessException) {
                if (ReturnCode.GET_DELIVERY_FEE_FOR_NOT_SUPPORT_ERROR.getCode().equals(((BusinessException) e).getCode())) {
                    returnCode = ReturnCode.GET_DELIVERY_FEE_FOR_NOT_SUPPORT_ERROR;
                }
            }
            setError(dataMap, returnCode);
            logWarn(ReturnCode.GET_DELIVERY_AMOUNT_ERROR.getMessage() + ":" + e.getMessage(), "gatherDeliveryAmount", request, e);
        }
        durationEnd(dataMap, "deliveryAmount");
    }

    /**
     * 查询所有地址
     *
     * @param request
     * @param dataMap
     */
    private CompletableFuture allAddress(GatherOrderRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "allAddress");
            try {
                // 查询默认地址
                Map<String, Object> params = new HashMap<>();
                params.put("storeId", request.getStoreId());
                params.put("appVersion", 1);
                com.wosai.market.user.dto.apisix.ApiRequest apiRequest = new com.wosai.market.user.dto.apisix.ApiRequest();
                com.wosai.market.user.dto.apisix.UserContext userContext = new com.wosai.market.user.dto.apisix.UserContext();
                userContext.setUserId(request.getUserId());
                apiRequest.setQuery(params);
                apiRequest.setUser(userContext);
                PageResponse<UserAddressDTO> pageResponse = userAddressService.allAddressV3(apiRequest);
                if (Objects.nonNull(pageResponse) && CollectionUtils.isNotEmpty(pageResponse.getValue())) {
                    dataMap.put("addressList", pageResponse.getValue());
                }
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_ALL_ADDRESS_ERROR);
                logWarn(ReturnCode.GET_ALL_ADDRESS_ERROR.getMessage() + ":" + e.getMessage(), "gatherAllAddress", request, e);
            }
            durationEnd(dataMap, "allAddress");
        }), orderMainExecutor);
    }


    /**
     * 优惠计算（轻餐）
     *
     * @param request
     * @param dataMap
     */
    private void discounts(GatherOrderRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "discounts");
        try {
            Cart cart = (Cart) MapUtils.getObject(dataMap, "cart");
            if (Objects.nonNull(cart)) {
                long totalAmount = cart.getTotalPrice() + MapUtils.getLongValue(dataMap, "packAmount", 0L);
                dataMap.put("totalAmount", totalAmount);
                request.getDiscountParams().setTotalAmount(Long.toString(totalAmount));
                request.setFromCart(request.isFromCart());
                RedeemResult redeemResult = redeemService.getRedeemResult(request.getDiscountParams(), ThreadLocalHelper.getUser());
                if (Objects.nonNull(redeemResult)) {
                    totalAmount = totalAmount - redeemResult.getTotalDiscount();
                    dataMap.put("totalAmount", totalAmount);
                    dataMap.put("discountAmount", redeemResult.getTotalDiscount());
                    dataMap.put("discounts", redeemResult);
                }
            }
        } catch (Exception e) {
            setError(dataMap, ReturnCode.GET_DISCOUNTS_AMOUNT_ERROR);
            logWarn(ReturnCode.GET_DISCOUNTS_AMOUNT_ERROR.getMessage() + ":" + e.getMessage(), "gatherDiscounts", request, e);
        }
        durationStart(dataMap, "discounts");
    }

    /**
     * 计算打包费
     *
     * @param request
     * @param dataMap
     */
    private CompletableFuture packAmount(GatherOrderRequest request, Map<String, Object> dataMap, UserContext userContext) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "packAmount");
            try {
                ThreadLocalHelper.getRequestContextThreadLocal().get().setUserContextDTO(userContext);
                Map<String, Object> params = new HashMap<>();
                params.put("store_id", request.getStoreId());
                params.put("type", request.getType().getMsg());
                Long amount = orderServiceV1.getPackAmount(ApiRequest.buildGetRequest(params));
                dataMap.put("packAmount", amount);
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_PACK_AMOUNT_ERROR);
                logWarn(ReturnCode.GET_PACK_AMOUNT_ERROR.getMessage() + ":" + e.getMessage(), "gatherPackAmount", request, e);
            }

            durationStart(dataMap, "packAmount");
        }), orderMainExecutor);
    }

    /**
     * 是否签署外卖协议
     *
     * @param request
     * @param dataMap
     * @return
     */
    private CompletableFuture agreement(GatherOrderRequest request, Map<String, Object> dataMap) {
        return CompletableFuture.runAsync(GatherTraceRunnable.of(() -> {
            durationStart(dataMap, "agreement");
            try {
                String userAgreementCode = apolloConfigHelper.getStringConfigValueByKey("userAgreementVersionCode", "");
                QueryUserAgreementRequest queryUserAgreementRequest = new QueryUserAgreementRequest();
                queryUserAgreementRequest.setAgreementCode(userAgreementCode);
                queryUserAgreementRequest.setUserId(request.getUserId());
                queryUserAgreementRequest.setUsableType("USER");
                queryUserAgreementRequest.setUsableValue(request.getUserId());
                Boolean has = userAgreementService.hasAgreementRecord(queryUserAgreementRequest);
                dataMap.put("hasAgreement", has);
            } catch (Exception e) {
                setError(dataMap, ReturnCode.GET_AGREEMENT_ERROR);
                logWarn(ReturnCode.GET_AGREEMENT_ERROR.getMessage() + ":" + e.getMessage(), "gatherAgreement", request, e);
            }

            durationStart(dataMap, "agreement");
        }), orderMainExecutor);

    }


    private Map<String, Object> initMainResultMap() {
        Map<String, Object> dataMap = new ConcurrentHashMap<>();
        dataMap.put("inCampus", false);
        dataMap.put("packAmount", 0L);
        dataMap.put("deliveryAmount", 0L);
        dataMap.put("reductionAmount", 0L);
        dataMap.put("totalAmount", 0L);
        dataMap.put("discountAmount", 0L);
        dataMap.put("discounts", "");
        dataMap.put("cart", "");
        dataMap.put("addressList", "");
        dataMap.put("defaultAddress", "");
        dataMap.put("bookTimes", new ArrayList<>());
        dataMap.put("hasAgreement", true);
        dataMap.put("tradeApp", "");
        dataMap.put("brandActivity", "");
        return dataMap;
    }

    private Map<String, Object> initExtraResultMap() {
        Map<String, Object> dataMap = new ConcurrentHashMap<>();
        dataMap.put("planContent", "");
        dataMap.put("wxMchId", "");
        dataMap.put("wxSubMchId", "");
        dataMap.put("wxStoreId", "");
        dataMap.put("wxGoodsActs", "");
        dataMap.put("wxProducts", "");
        dataMap.put("payConfig", "");
        return dataMap;
    }


    private DeliverInfo convertDeliverInfo(UserAddressDTO address, int deliveryType, Long presetTime) {
        DeliverInfo deliverInfo = new DeliverInfo();
        BeanUtils.copyProperties(address, deliverInfo);
        deliverInfo.setDeliveryType(String.valueOf(deliveryType));
        deliverInfo.setPresetTime(presetTime);

        // 处理默认楼层
        if (StringUtils.isNotBlank(address.getPreChooseFloor()) && CollectionUtils.isNotEmpty(address.getFloors())) {
            if (address.getFloors().stream().anyMatch(it -> Objects.equals(it.getFloor(), address.getPreChooseFloor()))) {
                deliverInfo.setFloor(address.getPreChooseFloor());
            }
        }
        return deliverInfo;
    }

    private Integer getDeliveryType(String storeId) {
        FindConfigByNameRequest findConfigByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TYPE);
        ConfigResponse configResponse = configRemoteService.findByName(findConfigByNameRequest);
        return (int) MccUtils.getLongValue(configResponse);
    }

}
