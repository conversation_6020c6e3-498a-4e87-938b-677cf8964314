package com.wosai.pantheon.uf4c.fallbackconfig.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.market.trade.service.PayService;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.smartbiz.base.pojo.RedeemResult;

import java.lang.reflect.Method;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;

public class PayServiceFallback extends JsonRPCFallbackDefine {
    private static final RedeemResult EMPTY_REDEEM_RESULT = new RedeemResult();

    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[] {
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("getRedeemResultForSubscribe");
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        return EMPTY_REDEEM_RESULT;
                    }
                },
                ElementMatchers::any
        };
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(PayService.class);
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}
