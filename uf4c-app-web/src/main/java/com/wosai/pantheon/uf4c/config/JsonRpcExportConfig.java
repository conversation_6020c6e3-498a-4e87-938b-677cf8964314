package com.wosai.pantheon.uf4c.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.googlecode.jsonrpc4j.InvocationListener;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import com.wosai.pantheon.uf4c.web.interceptor.JsonRpcTokenInterceptor;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Role;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2020-03-27
 */
@Configuration
public class JsonRpcExportConfig {

    @Bean
    @Role(BeanDefinition.ROLE_INFRASTRUCTURE)
    public AutoJsonRpcServiceImplExporter autoJsonRpcServiceImplExporter(
            ErrorResolver errorResolver,
            InvocationListener invocationListener,
            JsonRpcTokenInterceptor interceptor
    ) {
        AutoJsonRpcServiceImplExporter exporter =
                new AutoJsonRpcServiceImplExporter();
        exporter.setErrorResolver(errorResolver);

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        exporter.setObjectMapper(objectMapper);

        exporter.setInterceptorList(Arrays.asList(interceptor));
        exporter.setInvocationListener(invocationListener);
        return exporter;
    }
}
