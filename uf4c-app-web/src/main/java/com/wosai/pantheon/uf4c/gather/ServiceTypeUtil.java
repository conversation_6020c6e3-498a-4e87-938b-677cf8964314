package com.wosai.pantheon.uf4c.gather;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.CodeScene;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.types.AbstractServiceProcessor;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ServiceTypeData;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 计算ServiceType
 */
@Slf4j
@Component
public class ServiceTypeUtil extends GatherBase {
    @Autowired
    private List<AbstractServiceProcessor> serviceProcessors;

    public ServiceTypeData compute(GatherRequest request, Map<String, Object> dataMap) {
        logInfo("计算ServiceType", "GatherComputeServiceType", request);
        ServiceTypeData serviceTypeData = new ServiceTypeData();
        List<ServiceTypeData.ServiceItem> serviceTypeList = process(request, dataMap);
        if (CollectionUtils.isNotEmpty(serviceTypeList) && serviceTypeList.stream().noneMatch(ServiceTypeData.ServiceItem::isActive)) {
            serviceTypeList.get(0).setActive(true);
        }
        serviceTypeData.setServiceTypeList(serviceTypeList);
        serviceTypeData.setSelectServiceTypeShow(processServiceTypeIsShow(request, serviceTypeList, dataMap));
        for (int i = 0; i < serviceTypeList.size(); i++) {
            if (serviceTypeList.get(i).isActive()) {
                serviceTypeData.setServiceType(serviceTypeList.get(i).getServiceType());
                serviceTypeData.setServiceTypeName(serviceTypeList.get(i).getServiceTypeName());
                serviceTypeData.setCurrentServiceTypeIndex(i);
                break;
            }
        }
        try {
            dataMap.put("serviceTypes", JacksonUtil.beanToMap(serviceTypeData, new ObjectMapper()));
            return serviceTypeData;
        } catch (Exception e) {
            logWarn("计算serviceType出错：" + e.getMessage(), "GatherComputeServiceType", request, e);
            setError(dataMap, ReturnCode.SERVICE_COMPUTE_ERROR);
        }
        return serviceTypeData;
    }


    private List<ServiceTypeData.ServiceItem> process(GatherRequest request, Map<String, Object> dataMap) {
        List<ServiceTypeData.ServiceItem> list = new ArrayList<>();
        for (AbstractServiceProcessor serviceProcessor : serviceProcessors) {
            ServiceTypeData.ServiceItem item = serviceProcessor.process(request, dataMap);
            if (Objects.nonNull(item)) {
                list.add(item);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list.sort(Comparator.comparing(ServiceTypeData.ServiceItem::getSort));
        }
        return list;
    }

    private boolean processServiceTypeIsShow(GatherRequest request,
                                             List<ServiceTypeData.ServiceItem> list,
                                             Map<String, Object> dataMap) {
        Map<String, String> mccMap = (Map<String, String>) MapUtils.getMap(dataMap, "mcc");
        Map<String, String> extraMap = (Map<String, String>) MapUtils.getMap(dataMap, "extra");
        boolean onlyInStore = MapUtils.getBooleanValue(mccMap, Constants.ONLY_IN_STORE, false);
        String mealShareType = MapUtils.getString(extraMap, "mealShareType");
        boolean onTakeoutBusiness = MccUtils.getBooleanValue(mccMap, Constants.TAKEOUT_BUSINESS_STATUS, false);
        int deliveryType = MccUtils.getIntValue(mccMap, Constants.DELIVERY_TYPE);
        String scene = MapUtils.getString(extraMap, "scene", "");
        String goodsId = MapUtils.getString(extraMap, "goodsId");
        String from = MapUtils.getString(extraMap, "from");
        boolean show = true;
        ServiceTypeData.ServiceItem activeItem = null;
        if (list.size() == 1) {
            show = false;
            activeItem = list.get(0);
        } else {
            activeItem = list.stream().filter(ServiceTypeData.ServiceItem::isActive).findFirst().orElse(null);
            if (StringUtils.isNotBlank(request.getBuyAgainOrderType())) {
                // 从再来一单进入，且开启了“仅店内扫一扫”
                show = OrderType.SUBSCRIBE_ORDER.getMsg().equalsIgnoreCase(request.getBuyAgainOrderType()) && onlyInStore;
            }
            // 校园外卖进入
            if (Constants.From.CAMPUS.equalsIgnoreCase(request.getFrom())) {
                show = true;
            }
            // 外卖海报进入，但是外卖配送不可用
            if (StringUtils.isNotBlank(scene) && "poster".equalsIgnoreCase(mealShareType)) {
                show = onTakeoutBusiness && deliveryType == 1;
            }
            // 从商品分享进来，默认展示
            if (StringUtils.isNotBlank(goodsId)) {
                show = CodeScene.getCodeScene(scene) == CodeScene.P;
                if (Objects.nonNull(activeItem)) {
                    show = activeItem.getServiceType().intValue() == Constants.ServiceType.TAKEOUT;
                }
            }

        }

        if (Objects.nonNull(activeItem) && Constants.ServiceType.SCANNING.equals(activeItem.getServiceType())) {
            // 若当前是堂食，且需要扫一扫
            show = Constants.ServiceTypeAction.SCAN.equalsIgnoreCase(activeItem.getAction());
        }
        if (list.size() > 1) {
            // 非扫码进入，且传入storeId，并且不是切换serviceType，则弹框
            if (!CommonUtil.isValidURL(request.getUrl()) && StringUtils.isNotBlank(request.getStoreId()) && Objects.isNull(request.getServiceType())) {
                show = true;
            }
            if ("share_wechat".equalsIgnoreCase(from) || "share_alipay".equalsIgnoreCase(from)) {
                show = true;
            }
            // 从校园店铺进入
            if (Objects.nonNull(request.getCampusId()) && request.getCampusId() > 0) {
                show = true;
            }
            // 从聚合页店铺进入
            if (Objects.nonNull(request.getStoreSetId()) && request.getStoreSetId() > 0) {
                show = true;
            }
        }

        return show;
    }

}
