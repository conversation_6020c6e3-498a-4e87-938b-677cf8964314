package com.wosai.pantheon.uf4c.service.item.retail;

import com.wosai.pantheon.core.uitem.model.Category;
import com.wosai.pantheon.uf4c.model.item.*;
import com.wosai.pantheon.uf4c.util.RetailItemConverter;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smart.goods.common.constant.BusinessCodeEnum;
import com.wosai.smart.goods.common.constant.BusinessSceneEnum;
import com.wosai.smart.goods.common.model.BizResult;
import com.wosai.smart.goods.constant.TemplateCodeConstant;
import com.wosai.smart.goods.dto.CategoryDTO;
import com.wosai.smart.goods.dto.terms.TemplateProductQueryTerms;
import com.wosai.smart.goods.enums.OwnerTypeEnum;
import com.wosai.smart.goods.enums.ProductTypeEnum;
import com.wosai.smart.goods.enums.SpuSaleStatusEnum;
import com.wosai.smart.goods.enums.SpuStatusEnum;
import com.wosai.smart.goods.product.IProductRpcService;
import com.wosai.smart.goods.product.dto.ProductDTO;
import com.wosai.smart.goods.product.req.ProductReqWrapper;
import com.wosai.smart.goods.rpc.CategoryService;
import com.wosai.smart.goods.search.IProductSearchRpcService;
import com.wosai.smart.goods.search.req.CategoryProductCountReq;
import com.wosai.smart.goods.search.req.ProductSearchCursorQueryReq;
import com.wosai.smart.goods.search.res.CategoryProductCountStatistics;
import com.wosai.smart.goods.search.res.ProductCursorRes;
import com.wosai.smart.goods.stock.IStockRpcService;
import com.wosai.smart.goods.stock.dto.StockWithSpuIdDTO;
import com.wosai.web.api.ListResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 零售商品适配层服务
 */
@Service
public class RetailItemAdapterServiceImpl implements IRetailItemAdapterService {

    @Autowired
    private IProductSearchRpcService productSearchRpcService;

    @Autowired
    private CategoryService categoryRpcService;

    @Autowired
    private IProductRpcService productRpcService;

    @Autowired
    private IStockRpcService stockRpcService;

    @Override
    public ListResult<CategoryTreeDTO> queryCategories(RetailCategoryQueryReq req) {
        String storeId = req.getStoreId();

        // 筛选出需要展示的分类ID及商品数量
        CategoryProductCountReq countReq = new CategoryProductCountReq();
        countReq.setTmplCode(TemplateCodeConstant.RETAIL);
        countReq.setOwnerType(OwnerTypeEnum.STORE.getCode());
        countReq.setOwnerId(storeId);
        // 仅筛选上架的普通商品
        TemplateProductQueryTerms queryTerms = new TemplateProductQueryTerms();
        queryTerms.setProductTypes(Collections.singletonList(ProductTypeEnum.COMMON.getCode()));
        queryTerms.setSaleStatus(SpuSaleStatusEnum.LISTING.getCode());
        queryTerms.setSaleChannelFilters(RetailItemConverter.serviceType2SaleChannelFilters(req.getServiceType()));
        countReq.setQueryTerms(queryTerms);

        BizResult<List<CategoryProductCountStatistics>> countBizRs = productSearchRpcService.countUnderCategory(countReq);
        if (!countBizRs.isSuccess()) {
            throw new BusinessException(ReturnCode.CATEGORY_FIND_FAIL);
        }
        List<CategoryProductCountStatistics> countStatistics = countBizRs.getResult();
        if (CollectionUtils.isEmpty(countStatistics)) {
            return ListResult.emptyResult();
        }
        Map<Long, Long> itemCountMap = countStatistics.stream().collect(Collectors.toMap(CategoryProductCountStatistics::getCategoryId, CategoryProductCountStatistics::getCount, (k1, k2) -> k1));

        // 查询分类信息
        List<CategoryDTO> categories = categoryRpcService.listCategoryByIds(OwnerTypeEnum.STORE, storeId, new ArrayList<>(itemCountMap.keySet()));
        if (CollectionUtils.isEmpty(categories)) {
            return ListResult.emptyResult();
        }

        // 组装成树形结构
        Map<Long, List<CategoryDTO>> treeLeafMap = categories.stream()
                .filter(c -> Objects.nonNull(c.getParentId()) && c.getParentId() > 0)
                .collect(Collectors.groupingBy(CategoryDTO::getParentId));

        List<CategoryTreeDTO> trees = categories.stream()
                .filter(c -> Objects.isNull(c.getParentId()) || c.getParentId() < 0)
                .map(c -> {
                    CategoryTreeDTO tree = CategoryTreeDTO.fromRetail(c);
                    tree.setItemCount(MapUtils.getLong(itemCountMap, c.getCategoryId(), 0L).intValue());
                    List<CategoryTreeDTO> leaves = Optional.ofNullable(treeLeafMap.get(c.getCategoryId()))
                            .orElseGet(ArrayList::new)
                            .stream()
                            .map(l -> {
                                CategoryTreeDTO lt = CategoryTreeDTO.fromRetail(l);
                                lt.setItemCount(MapUtils.getLong(itemCountMap, l.getCategoryId(), 0L).intValue());
                                return lt;
                            })
                            .sorted(Comparator.comparing(Category::getDisplayOrder, Comparator.nullsLast(Integer::compareTo)))
                            .collect(Collectors.toList());
                    tree.setSubCategories(leaves);
                    return tree;
                })
                .sorted(Comparator.comparing(Category::getDisplayOrder, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());

        return new ListResult<>(trees);
    }

    @Override
    public RetailItemCursorQueryRes cursorQueryItem(RetailItemCursorQueryReq req) {
        String storeId = req.getStoreId();
        int pageSize = Optional.ofNullable(req.getPageSize()).orElse(30);

        ProductSearchCursorQueryReq queryReq = new ProductSearchCursorQueryReq();
        queryReq.setTmplCode(TemplateCodeConstant.RETAIL);
        queryReq.setOwnerType(OwnerTypeEnum.STORE.getCode());
        queryReq.setOwnerId(storeId);
        queryReq.setCursor(req.getCursor());
        queryReq.setPageSize(pageSize);

        // 设置筛选条件
        TemplateProductQueryTerms queryTerms = new TemplateProductQueryTerms();
        queryTerms.setProductTypes(Collections.singletonList(ProductTypeEnum.COMMON.getCode()));
        queryTerms.setSaleStatus(SpuSaleStatusEnum.LISTING.getCode());
        if (CollectionUtils.isNotEmpty(req.getCategoryIds())) {
            List<Long> queryCategoryIds = req.getCategoryIds().stream().map(Long::valueOf).collect(Collectors.toList());
            queryTerms.setCategoryIds(queryCategoryIds);
        }
        queryTerms.setSearchKeyword(req.getSearchWord());
        queryTerms.setSaleChannelFilters(RetailItemConverter.serviceType2SaleChannelFilters(req.getServiceType()));
        queryReq.setQueryTerms(queryTerms);

        // 筛选出满足条件的商品ID、游标
        BizResult<ListResult<ProductCursorRes>> cursorBizRs = productSearchRpcService.cursorQuerySpuId(queryReq);
        if (!cursorBizRs.isSuccess()) {
            throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
        }
        ListResult<ProductCursorRes> cursorResult = cursorBizRs.getResult();
        if (Objects.isNull(cursorResult)) {
            return RetailItemCursorQueryRes.emptyRes(0L);
        }

        RetailItemCursorQueryRes res = new RetailItemCursorQueryRes();
        long total = cursorResult.getTotal();
        res.setTotal(total);
        if (CollectionUtils.isEmpty(cursorResult.getRecords())) {
            return RetailItemCursorQueryRes.emptyRes(total);
        }

        List<ProductCursorRes> cursorResList = cursorResult.getRecords();
        // 最后一条记录的游标用于下一页查询
        res.setCursor(cursorResList.get(cursorResList.size() - 1).getCursor());

        // 查询商品详情
        List<String> spuIds = cursorResList.stream().map(p -> String.valueOf(p.getSpuId())).collect(Collectors.toList());
        Map<String, RetailItemDTO> itemMap = this.queryItemBySpuIds(storeId, spuIds);

        List<RetailItemDTO> sortedItems = cursorResList.stream()
                .map(cr -> {
                    RetailItemDTO item = itemMap.get(String.valueOf(cr.getSpuId()));
                    if (Objects.nonNull(item)) {
                        item.setCategoryIdPath(cr.getCategoryIds());
                    }
                    return item;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        res.setRecords(sortedItems);
        return res;
    }

    @Override
    public Map<String, RetailItemDTO> queryItemBySpuIds(String storeId, List<String> spuIds) {
        List<Long> querySpuIds = spuIds.stream().map(Long::valueOf).collect(Collectors.toList());

        // 查询商品详情
        ProductReqWrapper<List<Long>> detailQueryReq = new ProductReqWrapper<>();
        detailQueryReq.setBizCode(BusinessCodeEnum.SMART.getCode());
        detailQueryReq.setBizScene(BusinessSceneEnum.RETAIL.getCode());
        detailQueryReq.setTemplateCode(TemplateCodeConstant.RETAIL);
        detailQueryReq.setStoreId(storeId);
        detailQueryReq.setBizParams(querySpuIds);
        BizResult<List<ProductDTO>> detailBizRs = productRpcService.batchQueryBySpuIds(detailQueryReq);
        if (!detailBizRs.isSuccess()) {
            throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
        }

        // 筛选出未删除的商品
        List<ProductDTO> details = Optional.ofNullable(detailBizRs.getResult())
                .orElseGet(ArrayList::new)
                .stream()
                .filter(p -> Objects.equals(p.getSpu().getStatus(), SpuStatusEnum.DEFAULT.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyMap();
        }

        // 查询库存信息
        List<StockWithSpuIdDTO> stocks = stockRpcService.listBySpuIds(storeId, querySpuIds);
        Map<Long, List<StockWithSpuIdDTO>> spuStockMap = stocks.stream().collect(Collectors.groupingBy(StockWithSpuIdDTO::getSpuId));

        Map<String, RetailItemDTO> itemMap = new HashMap<>(details.size());
        for (ProductDTO detail : details) {
            Long spuId = detail.getSpu().getSpuId();
            RetailItemDTO item = new RetailItemDTO(detail);
            item.appendStockInfo(spuStockMap.get(spuId));
            itemMap.put(String.valueOf(spuId), item);
        }
        return itemMap;
    }

}
