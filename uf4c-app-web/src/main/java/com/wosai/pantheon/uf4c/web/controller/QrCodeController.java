package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.LoveQrcodeRequest;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping(path = "/api/v1/qrcode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class QrCodeController {
    @Autowired
    private OrderServiceV2 orderServiceV2;

    @PostMapping("/love")
    @ResponseBody
    public Map loveQrCode(@RequestBody LoveQrcodeRequest request) {
        return orderServiceV2.loveQrCode(new ApiRequest<>(request));
    }
}
