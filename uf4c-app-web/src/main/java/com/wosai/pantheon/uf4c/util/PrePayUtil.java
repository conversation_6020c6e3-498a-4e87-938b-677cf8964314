package com.wosai.pantheon.uf4c.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.data.util.StringUtil;
import com.wosai.market.tethys.api.dto.DiscountInfo;
import com.wosai.market.tethys.api.enums.DiscountsEnum;
import com.wosai.market.tethys.api.service.DeliveryFeeReductionActivityService;
import com.wosai.market.tethys.api.service.StoredCardRemoteService;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.market.trade.modal.SubscribePayRequest;
import com.wosai.market.trade.modal.constant.MarketTradeConstant;
import com.wosai.market.trade.service.PayService;
import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.pantheon.order.enums.OrderSource;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.order.enums.PackType;
import com.wosai.pantheon.order.model.dto.request.*;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.*;
import com.wosai.pantheon.uf4c.model.vo.BrandActivityProduct;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.service.StoreHelper;
import com.wosai.pantheon.uf4c.service.UserPreferencesService;
import com.wosai.pantheon.uf4c.web.exception.BaseException;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import com.wosai.smartbiz.payment.api.trade.defs.SubPayWay;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Service
@Slf4j
public class PrePayUtil {

    @Autowired
    private StoredCardRemoteService storedCardRemoteService;

    @Autowired
    private DeliveryFeeReductionActivityService deliveryFeeReductionActivityService;

    @Autowired
    private PayService payService;

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TerminalService terminalService;

//    @Autowired
//    private UfoodFeeService ufoodFeeService;


    @Autowired
    private UserPreferencesService userPreferencesService;

    @Autowired
    private StoreHelper storeHelper;


    private static final String PRE_PAY_EXCLUSIVE_KEY_PATTERN = "pay_exclusive:%s";

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    public PayResult lovePay(String storeId) {
        if (StringUtils.isBlank(storeId)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "门店ID为空");
        }
        String storeIds = apolloConfigHelper.getStringConfigValueByKey("loveBlackStoreIds", "");
        UserContextDTO user = ThreadLocalHelper.getUserNotThrowExcetpion();
        if (StringUtils.isNotBlank(storeIds) && storeIds.contains(storeId)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "该活动已下线");
        }

//        Map tradeTerminal = terminalService.getTerminalByDeviceFingerprint("jjz" + storeId);
        SubscribePayRequest request = new SubscribePayRequest();
//        request.setTerminalId(BeanUtil.getPropString(tradeTerminal, DaoConstants.ID));
//        request.setTerminalSn(BeanUtil.getPropString(tradeTerminal, Terminal.SN));
        request.setOrderSource(OrderSource.MINI);
        request.setWeixinAppId(user.getWeixinAppId());
        request.setPayerUid(user.getThirdpartyUserId());
        request.setUserId(user.getUserId());
        request.setChannelUserId(user.getThirdpartyUserId());
        request.setDiscountStrategy(OrderType.SUBSCRIBE_ORDER.getMsg());
        request.setScene(ThreadLocalHelper.getScene());
        request.setPayway(PayWay.WECHAT.getCode().toString());
        request.setSubPayway(SubPayWay.MINI.getCode().toString());
        request.setStoreId(storeId);
        request.setTotalAmount("1501");
        request.setRemark("为环卫工人提供爱心餐");
        request.setClientIp(StringUtil.empty(ThreadLocalHelper.getRealIp()) ? IpUtils.getIp() : ThreadLocalHelper.getRealIp());
        request.setPacked(PackType.INSIDE_ORDER);
        request.setPackAmount(0L);
        request.setItems(orderHelper.getDefaultLoveDiningItem());
        request.setItemsInfo(orderHelper.getDefaultLoveDiningItemDigest());
        request.setStoreId(storeId);
        return payService.payForSubscribe(request);
    }

    public PayResult prePay(Order order, PayParamWrapper payParamWrapper, RedeemParamWrapper redeemParamWrapper) {
        // 商户行业
        String merchantIndustryType = Constants.CATERING;
        if (storeHelper.isRetailStore(order.getStoreId())) {
            merchantIndustryType = Constants.RETAIL;
        }

        UserContextDTO user = ThreadLocalHelper.getUserNotThrowExcetpion();
        String sn = order.getSn();
        String payerUid = null;
        String userId = null;
        String weixinAppId = null;
        String alipayAppId = null;

        if (user != null) {
            if (Objects.equals(order.getPayway(), PayWay.SODEXO.getCode()) && StringUtils.isNotBlank(user.getCellphone())) {
                // 索迪斯支付时,payerUid为经过base64加密后的手机号
                payerUid = Base64.encode(user.getCellphone().getBytes(StandardCharsets.UTF_8));
            } else {
                payerUid = user.getThirdpartyUserId();
            }
            userId = user.getUserId();
            weixinAppId = user.getWeixinAppId();
            alipayAppId = user.getAlipayAppId();
        } else {
            userId = order.getBuyerUid();
        }

        RLock lock = redissonClient.getLock(String.format(PRE_PAY_EXCLUSIVE_KEY_PATTERN, userId));
        try {
            if (lock.tryLock(-1, 5000, TimeUnit.MILLISECONDS)) {
                SubscribePayRequest request = new SubscribePayRequest();
                request.setOrderSource(OrderSource.MINI);
                request.setCashierMode(order.isCashierMode());
                request.setTableId(order.getTableId());
                request.setTableName(order.getTableNo());
                request.setAreaId(order.getAreaId());
                request.setOrderTag(order.getOrderTag());

                request.setCashierBizParams(payParamWrapper.getCashierBizParams());
                request.setAcquiring(payParamWrapper.getAcquiring());
                request.setRedeemDigest(payParamWrapper.getRedeemDigest());
                request.setTotalDiscount(payParamWrapper.getTotalDiscount());
                request.setOperator(payParamWrapper.getOperator());
                request.setAllowCardPay(order.isAllowCardPay());

                request.setCellPhone(MapUtils.getString(order.getExtraInfo(), Constants.CELL_PHONE_KEY, null));


                OrderType orderType = orderHelper.getOrderType(order.getType());
                request.setWeixinAppId(weixinAppId);
                request.setAlipayAppId(alipayAppId);
                request.setPayerUid(payerUid);
                request.setUserId(userId);
                request.setChannelUserId(payerUid);
                request.setDiscountStrategy(orderType.getMsg());
                request.setScene(ThreadLocalHelper.getScene());


                request.setPayway(String.valueOf(order.getPayway()));


                request.setSubPayway(String.valueOf(order.getSubPayway()));
                request.setSubscribeOrderSn(order.getSn());
                request.setStoreId(order.getStoreId());
                request.setTotalAmount(String.valueOf(order.getEffectiveAmount()));
                if (payParamWrapper.isRechargeAndPay()) {
                    request.setRechargeAmount(payParamWrapper.getRechargeAmount());
                    request.setRechargeAndPay(payParamWrapper.isRechargeAndPay());
                    request.setStoredScene(payParamWrapper.getStoredScene());
                    request.setRechargeRuleId(payParamWrapper.getRechargeRuleId());
                }

                request.setHbFq(order.getExtra().getHbFq());
                request.setRemark(order.getRemark());
                request.setUserName(order.getBuyerLogin());
                request.setTerminalId(order.getQrCode());
                request.setTerminalName(order.getQrCodeName());
                request.setTerminalType(order.getQrCodeType());
                request.setTerminalSn(order.getTerminalSn());
                request.setClientSn(order.getClientSn());
                request.setPacked(Objects.equals(order.getPacked(), true) ? PackType.PACKED_ORDER : PackType.INSIDE_ORDER);
                request.setPackAmount(order.getPackAmount());
                request.setDeliverAmount(Optional.of(order.getExtra())
                        .map(Order.OrderExtra::getDeliveryInfo)
                        .map(DeliverInfo::getDeliveryFee)
                        .map(Long::valueOf)
                        .orElse(0L));
                request.setDeliverType(Optional.of(order.getExtra())
                        .map(Order.OrderExtra::getDeliveryInfo)
                        .map(DeliverInfo::getDeliveryType)
                        .map(Integer::valueOf)
                        .orElse(null));
                request.setIsDeliveryConvert(Optional.of(order.getExtra())
                        .map(Order.OrderExtra::getDeliveryInfo)
                        .map(DeliverInfo::getIsConvert)
                        .orElse(false));
                request.setDeliveryErrorCode(Optional.of(order.getExtra())
                        .map(Order.OrderExtra::getDeliveryInfo)
                        .map(DeliverInfo::getDeliveryErrorCode)
                        .orElse(""));
                request.setDeliveryErrorMsg(Optional.of(order.getExtra())
                        .map(Order.OrderExtra::getDeliveryInfo)
                        .map(DeliverInfo::getDeliveryErrorMsg)
                        .orElse(""));
                request.setClientIp(StringUtil.empty(ThreadLocalHelper.getRealIp()) ? IpUtils.getIp() : ThreadLocalHelper.getRealIp());
                request.setPreReduceNo(StringUtil.empty(order.getExtra().getPreReduceNo()) ? null : order.getExtra().getPreReduceNo());
                request.setMpScene(order.getExtra().getMpScene());
                request.setWxTraceId(order.getExtra().getWxTraceId());
                request.setMkCustomInfo(redeemParamWrapper.getMkCustomInfo());
                request.setClientTrackingData(order.getClientTrackingData());

                Map<String, Object> extraMap = new HashMap<>();
                extraMap.put(Constants.MERCHANT_INDUSTRY_TYPE, merchantIndustryType);
                extraMap.put(Constants.GROUP_BUYING_ID, Optional.of(order.getExtra()).map(Order.OrderExtra::getGroupBuyingId).orElse(""));
                extraMap.put("brandActivity", CollectionUtils.isNotEmpty(order.getItems())
                        && order.getItems().stream().anyMatch(CartItemCreate::isBrandAct));
                if (MapUtils.getBoolean(extraMap, "brandActivity")) {
                    BrandActivityProduct brandActivityProduct = order.getItems().stream().filter(CartItemCreate::isBrandAct).findFirst()
                            .map(CartItemCreate::getBrandActProduct)
                            .orElse(null);
                    if (Objects.nonNull(brandActivityProduct)) {
                        extraMap.put("brandActivityId", brandActivityProduct.getActivityId());
                    }
                }
                extraMap.put("deliveryFeeDetail", Optional.of(order.getExtra())
                        .map(Order.OrderExtra::getDeliveryInfo)
                        .map(DeliverInfo::getDeliveryFeeMap)
                        .orElse(new HashMap<>()));
                if (orderType == OrderType.TAKE_OUT_ORDER && order.isCampusOrder()) {
                    extraMap.put("deliveryFloor", Optional.of(order.getExtra())
                            .map(Order.OrderExtra::getDeliveryInfo)
                            .map(DeliverInfo::getFloor).orElse(""));

                    if (StringUtils.isNotBlank(MapUtils.getString(extraMap, "deliveryFloor"))) {
                        // 保存用户楼层偏好信息
                        userPreferencesService.saveAddressFloor(userId, Optional.of(order.getExtra())
                                .map(Order.OrderExtra::getDeliveryInfo)
                                .map(DeliverInfo::getId).orElse(""), MapUtils.getString(extraMap, "deliveryFloor", ""));
                    }

                }

                //组装订单
                if (orderType == OrderType.TAKE_OUT_ORDER || orderType == OrderType.PRE_ORDER) {
                    request.setOrderAddress(JacksonUtil.convert(order.getExtra().getDeliveryInfo(),
                            new TypeReference<OrderAddressRequest>() {
                            }));
                    if (Objects.isNull(request.getOrderAddress().getExtra())) {
                        request.getOrderAddress().setExtra(new HashMap<>());
                    }
                    request.getOrderAddress().getExtra().put("deliveryFloor", MapUtils.getString(extraMap, "deliveryFloor", ""));

                    if (orderType == OrderType.TAKE_OUT_ORDER) {
                        if (Objects.isNull(order.getExtra().getDeliveryInfo().getDistance())) {
                            order.getExtra().getDeliveryInfo().setDistance(0D);
                        }
                        OrderDeliveryRequest orderDeliveryRequest = new OrderDeliveryRequest();
                        if (request.getDeliverType() != null && 2 == request.getDeliverType()) {
                            orderDeliveryRequest.setType(0);
                        }
                        orderDeliveryRequest.setDeliverFee(Math.toIntExact(request.getDeliverAmount()));
                        orderDeliveryRequest.setDistance(order.getExtra().getDeliveryInfo().getDistance());
                        orderDeliveryRequest.setStatusCode(0);
                        request.setDeliveryInfo(orderDeliveryRequest);
                    }
                    request.setCampusException(order.getCampusException());
                    if (order.isBookOrder()) {
                        request.setBookOrder(true);
                        request.setBookOrderInfoDTO(order.getExtra().getBookOrderInfoDTO());
                    }
                    extraMap.put(Constants.ORDER_IN_CAMPUS, MapUtils.getBoolean(order.getExtraInfo(), Constants.ORDER_IN_CAMPUS));
                }
                if (orderType == OrderType.TAKE_OUT_ORDER && order.isCampusOrder()) {
                    request.setIsCampusOrder(true);
                    request.setCampusDeliveryFee(order.getExtra().getCampusDelivery().getFee());
                    if (!Objects.equals(0, order.getExtra().getDeliveryInfo().getCampusId())) {
                        request.setCampusId(order.getExtra().getDeliveryInfo().getCampusId());
                    }
                    request.setCampusDelivery(JacksonUtil.convert(order.getExtra().getCampusDelivery(),
                            new TypeReference<OrderCampusDeliveryRequest>() {
                            }));
                    if (Objects.nonNull(order.getExtra().getDeliveryInfo())) {
                        request.setCampusStation(JacksonUtil.convert(order.getExtra().getCampusStation(),
                                new TypeReference<OrderCampusStationRequest>() {
                                }));
                    }

                }
                /**
                 * 配送费减免相关处理逻辑
                 * 1.当订单类型为外卖订单，且配送费金额不为0时再进行减免促销判断
                 * 2.当门店开启了减免促销时，判断运费与减免金额的大小，获取最终的减免金额
                 * 3.将减免金额信息保存为redeem，提前保存
                 * 4.订单原始金额修改为减去减免金额后的金额
                 */
                if (orderType == OrderType.TAKE_OUT_ORDER && request.getDeliverAmount() > 0) {
                    // 订单类型为外卖订单，并且有配送费
                    // 查询门店的减免配送费促销
                    DiscountInfo discountInfo = null;
                    try {
                        discountInfo = deliveryFeeReductionActivityService.getDeliveryFeeReduction(new DiscountInfo() {{
                            setStoreId(request.getStoreId());
                        }});
                    } catch (Exception ex) {
                        log.warn("getDeliveryFeeReduction error", keyValue("method", "getDeliveryFeeReduction"), keyValue("arguments", request.getStoreId()), ex);
                    }
                    if (discountInfo != null && discountInfo.getDiscount() > 0) {
                        // 有减免促销，保存redeem信息
                        long redeemAmount = request.getDeliverAmount() - discountInfo.getDiscount() >= 0 ? discountInfo.getDiscount() : request.getDeliverAmount();
                        OrderRedeemRequest orderRedeemRequest = new OrderRedeemRequest();
                        orderRedeemRequest.setActivityId(discountInfo.getId());
                        orderRedeemRequest.setName(DiscountsEnum.TAKEOUT_DELIVERY_FEE_REDUCTION_ACTIVITY.getName());
                        orderRedeemRequest.setActivitySn(discountInfo.getActivitySn());
                        orderRedeemRequest.setDiscountAmount(redeemAmount);
                        orderRedeemRequest.setType(3);
                        orderRedeemRequest.setDiscountType(DiscountsEnum.TAKEOUT_DELIVERY_FEE_REDUCTION_ACTIVITY.getCode());
                        request.setRedeems(Collections.singletonList(orderRedeemRequest));

                        // 有减免促销，订单金额减去减免金额
                        request.setTotalAmount(String.valueOf(Long.parseLong(request.getTotalAmount()) - redeemAmount));
                    }
                }
                List<CartItemCreate> itemCreates = order.getItems();
                request.setItems(
                        Optional.ofNullable(itemCreates)
                                .map(items -> items.stream()
                                        .map(i -> OrderHelper.convertCartItem2CreateRequest(i)).filter(Objects::nonNull).collect(Collectors.toList())
                                )
                                .orElse(null)
                );


                request.setItemsInfo(
                        Optional.ofNullable(itemCreates)
                                .map(items -> items.stream()
                                        .map(i -> {
                                            OrderItemDigest orderItemDigest = new OrderItemDigest();
                                            orderItemDigest.setId(i.getItem().getId());
                                            orderItemDigest.setName(i.getItem().getName());
                                            orderItemDigest.setValue(i.getItem().getAttachedInfo());
                                            orderItemDigest.setPrice(i.getItem().getPrice());
                                            orderItemDigest.setNumber(i.getItem().getNumber());
                                            return orderItemDigest;
                                        })
                                        .collect(Collectors.toList())
                                )
                                .orElse(null)
                );
                //转换加价购
//                if (CollectionUtils.isNotEmpty(order.getWxGoods())) {
//                    List<OrderItemAddRequest> addItems = wxGoodsHelper.buildItemAddRequest(order.getStoreId(), order.getWxGoods(), itemCreates);
//                    Map<String, OrderItemAddRequest> map = request.getItems().stream().collect(Collectors.toMap(OrderItemAddRequest::getItemId, o -> o));
//                    Map<String, OrderItemDigest> infoMap = request.getItemsInfo().stream().collect(Collectors.toMap(BaseDTO::getId, o -> o));
//                    for (OrderItemAddRequest i : addItems) {
//                        if (map.get(i.getItemId()) != null) {
//                            //购物车中有和加价购相同的商品，加上数量即可
//                            OrderItemAddRequest addRequest = map.get(i.getItemId());
//                            addRequest.setCount(addRequest.getCount().add(i.getCount()));
//
//                            OrderItemDigest orderItemDigest = infoMap.get(addRequest.getItemId());
//                            orderItemDigest.setNumber(orderItemDigest.getNumber() + i.getCount().intValue());
//                        } else {
//                            //购物车中原本不存在的商品，新增
//                            request.getItems().add(i);
//
//                            request.getItemsInfo().add(new OrderItemDigest() {{
//                                setId(i.getItemId());
//                                setName(i.getName());
//                                setPrice(i.getEffectiveAmountPer().intValue());
//                                setNumber(i.getCount().intValue());
//                            }});
//                        }
//                    }
//                    //填充微信加价购信息
//                    List<WxGoodsSubsidy> collect = order.getWxGoods().stream()
//                            .map(i -> {
//                                WxGoodsSubsidy wxGoodsSubsidy = new WxGoodsSubsidy();
//                                BeanUtils.copyProperties(i, wxGoodsSubsidy);
//                                return wxGoodsSubsidy;
//                            }).collect(Collectors.toList());
//                    request.setWxGoodsSubsidyList(collect);
//                    //价格添加到总金额
//                    long vxGoodsPrice = addItems.stream().mapToLong(o -> o.getEffectiveAmountPer() * o.getCount().intValue()).sum();
//                    Long amount = Long.parseLong(request.getTotalAmount()) + vxGoodsPrice;
//                    request.setTotalAmount(String.valueOf(amount));
//                }

                Map amountComposition = orderHelper.getAmountComposition(request.getStoreId(), orderType,
                        Optional.of(order.getExtra()).map(Order.OrderExtra::getDeliveryInfo).orElse(null),
                        Long.parseLong(request.getTotalAmount()));
                extraMap.put("amountComposition", amountComposition);

                extraMap.put("sqbPaySource", payParamWrapper.getSqbPaySource());
                extraMap.put("payHeaders", payParamWrapper.getPayHeaders());

                Map alipayNOrderInfo = getalipayNOrderInfoFromItems(itemCreates);
                if(Objects.nonNull(alipayNOrderInfo)){
                    extraMap.put("alipayNOrderInfo", alipayNOrderInfo);
                }

                request.setExtraMap(extraMap);

                request.setAdditionalInfo(order.getAdditionalInfo());

                if (request.getCashierBizParams() != null || StringUtils.isNotBlank(request.getAcquiring())) {
                    return payService.payForSubscribe(request);
                }

                if (request.getPayway().equals(MarketTradeConstant.payWay.CARD.getCode().toString())
                        || request.getPayway().equals(MarketTradeConstant.payWay.CARDV2.getCode().toString())) {
                    return storedCardRemoteService.storedCardPayForSubscribeOrder(request);
                } else {
                    return payService.payForSubscribe(request);
                }
            } else {
                if (StringUtils.isNotBlank(sn)) {
                    log.info("用户正在重复下单，已阻止", keyValue("sn", sn), keyValue("user_id", userId));
                    throw new BusinessException(ReturnCode.BUSINESS_ERROR, "当前订单正在支付中，请稍后再试");
                } else {
                    log.info("用户正在重复下单，已阻止", keyValue("user_id", userId));
                    throw new BusinessException(ReturnCode.BUSINESS_ERROR, "您已下单，请稍后再试");
                }

            }
        } catch (InterruptedException ex) {
            log.warn("pre pay error", ex);
            throw new BaseException(ReturnCode.BUSINESS_ERROR, "系统错误，请稍后再试（PRE_PAY_ERROR）");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private Map getalipayNOrderInfoFromItems(List<CartItemCreate> items){
        return items.stream().map(i -> i.getExtraInfo()).filter(Objects::nonNull).findFirst().map(extraInfo -> MapUtils.getMap(extraInfo, "alipayNOrderInfo")).orElse(null);
    }


//    public Map getTerminal(String terminalSn, String storeId) {
//        Map terminal = null;
//        if (org.apache.commons.lang.StringUtils.isEmpty(terminalSn)) {
//            terminal = terminalService.getTerminalByDeviceFingerprint("jjz" + storeId);
//        } else {
//            terminal = terminalService.getTerminalBySn(terminalSn);
//        }
//        return terminal;
//    }

//    public Map getDiscountTerminal(String terminalSn, String storeId, String orderType) {
//        Map terminal = null;
//        if (!orderType.equals(MarketTradeConstant.orderType.PRE_ORDER.getMsg())
//                && !orderType.equals(MarketTradeConstant.orderType.SUBSCRIBE_ORDER.getMsg())
//                && !orderType.equals(MarketTradeConstant.orderType.TAKE_OUT_ORDER.getMsg())
//                && !orderType.equals(MarketTradeConstant.orderType.EAT_FIRST_ORDER.getMsg())
//        ) {
//            return this.getTerminal(terminalSn, storeId);
//        }
//        UfoodFeeCheckResponse ufoodFeeCheckResponse = ufoodFeeService.ufoodFeeCheck(storeId);
//        if (!ufoodFeeCheckResponse.isOldStore()) {
//            //新增商户
//            if (ufoodFeeCheckResponse.isUseFreeFee()) {
//                //零费率终端
//                terminal = getTerminalMap(storeId, MarketTradeConstant.terminalConfig.JJZUFOODDISCOUNTFEE.getConfigName());
//            } else {
//                //新终端 使用时 浮动费率 extended 字段sqb_scene传 service_fee_up
//                //文档https://confluence.wosai-inc.com/pages/viewpage.action?pageId=527598120
//                terminal = getTerminalMap(storeId, MarketTradeConstant.terminalConfig.JJZUFOODFEE.getConfigName());
//            }
//        } else {
//            //老商户优惠费率终端
//            terminal = getTerminalMap(storeId, MarketTradeConstant.terminalConfig.JJZDISCOUNTFEE.getConfigName());
//        }
//
//        return Optional.ofNullable(terminal).orElse(this.getTerminal(terminalSn, storeId));
//    }


//    private Map getTerminalMap(String storeId, String prefix) {
//        Map terminal = terminalService.getTerminalByDeviceFingerprint(prefix + storeId);
//        if (Objects.nonNull(terminal)) {
//            terminal.put("terminalDiscountType", prefix);
//            return terminal;
//        }
//        return null;
//    }
}
