package com.wosai.pantheon.uf4c.service;


import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.shouqianba.smart.template.api.dto.template.MainTemplateDetail;
import com.shouqianba.smart.template.api.interfaces.MpThemeSyncRemoteServiceV2;
import com.wosai.app.model.resp.StoreRenovationResp;
import com.wosai.market.merchant.api.CustomerStoreRemoteService;
import com.wosai.market.merchant.api.MerchantTypeRemoteService;
import com.wosai.market.merchant.api.MpThemeSyncRemoteService;
import com.wosai.market.merchant.dto.customer.request.StoreDetailRequest;
import com.wosai.market.merchant.dto.customer.response.StoreDetailResponse;
import com.wosai.pantheon.uf4c.gather.GatherBase;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.WeakReferenceCaller;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class StoreHelper {

    @Autowired
    private MerchantTypeRemoteService merchantRemoteService;
    @Autowired
    private CustomerStoreRemoteService customerStoreRemoteService;
    @Autowired
    private MpThemeSyncRemoteService mpThemeSyncRemoteService;
    @Autowired
    private MpThemeSyncRemoteServiceV2 mpThemeSyncRemoteServiceV2;

    public static final Config apolloConfig = ConfigService.getAppConfig();

    private static final Map<String, ImmutablePair<Long, String>> TEMPLATE_MAP = new HashMap<>();

    static {
        /**
         * <pre>
         * 门店首页
         * 经典	3526
         * MDD	3796
         * KFC	3797
         * 茶饮	3798
         *
         * 点单首页
         * 经典	3692
         * MDD	3712
         * KFC	3696
         * 茶饮	3713
         * </pre>
         */
        // 初始化模板映射表
        TEMPLATE_MAP.put("3526:3692", ImmutablePair.of(100000L, "经典"));
        TEMPLATE_MAP.put("3796:3712", ImmutablePair.of(100007L, "MDD"));
        TEMPLATE_MAP.put("3797:3696", ImmutablePair.of(100009L, "KFC"));
        TEMPLATE_MAP.put("3798:3713", ImmutablePair.of(100008L, "茶饮"));
        TEMPLATE_MAP.put("3526:3712", ImmutablePair.of(100065L, "经典XMDD"));
        TEMPLATE_MAP.put("3526:3696", ImmutablePair.of(100064L, "经典X开封菜"));
        TEMPLATE_MAP.put("3526:3713", ImmutablePair.of(100063L, "经典X茶饮"));
        TEMPLATE_MAP.put("3796:3692", ImmutablePair.of(100079L, "MDDX经典"));
        TEMPLATE_MAP.put("3796:3696", ImmutablePair.of(100070L, "MDDX开封菜"));
        TEMPLATE_MAP.put("3796:3713", ImmutablePair.of(100071L, "MDDX茶饮"));
        TEMPLATE_MAP.put("3797:3712", ImmutablePair.of(100069L, "开封菜XMDD"));
        TEMPLATE_MAP.put("3797:3692", ImmutablePair.of(100077L, "开封菜X经典"));
        TEMPLATE_MAP.put("3797:3713", ImmutablePair.of(100073L, "开封菜X茶饮"));
        TEMPLATE_MAP.put("3798:3692", ImmutablePair.of(100076L, "茶饮X经典"));
        TEMPLATE_MAP.put("3798:3696", ImmutablePair.of(100067L, "茶饮X开封菜"));
        TEMPLATE_MAP.put("3798:3712", ImmutablePair.of(100068L, "茶饮XMDD"));
    }

    /**
     * 判断门店是否零售商户
     *
     * @param storeId
     * @return
     */
    public boolean isRetailStore(String storeId) {
        return Boolean.TRUE.equals(WeakReferenceCaller.call(() -> merchantRemoteService.isRetailMerchantByStoreId(storeId)));
    }


    public String getMerchantIdByStoreId(String storeId) {
        StoreDetailResponse storeDetailResponse = getStoreInfo(storeId);
        if (Objects.nonNull(storeDetailResponse)) {
            return storeDetailResponse.getMerchantId();
        }
        return null;
    }

    public StoreDetailResponse getStoreInfo(String storeId) {
        StoreDetailRequest storeDetailRequest = new StoreDetailRequest();
        storeDetailRequest.setStoreId(storeId);
        StoreDetailResponse storeDetailResponse = customerStoreRemoteService.storeDetailInfo(storeDetailRequest);
        return storeDetailResponse;
    }

    public void processMultiTemplates(String storeId, Map<String, Object> dataMap) {
        if (!apolloConfig.getBooleanProperty("theme.sync.enable", false)) {
            return;
        }
        try {
            if (dataMap.containsKey("theme") && dataMap.containsKey("template")) {
                List<StoreRenovationResp> storeRenovations = (List<StoreRenovationResp>) MapUtils.getObject(dataMap, "theme");
                if (CollectionUtils.isEmpty(storeRenovations)) {
                    return;
                }
                StoreRenovationResp storePageRenovation = storeRenovations.stream().filter(it -> StringUtils.equalsIgnoreCase(it.getPage_id(), "xgaugooilxg7")).findFirst().orElse(null);
                StoreRenovationResp orderPageRenovation = storeRenovations.stream().filter(it -> StringUtils.equalsIgnoreCase(it.getPage_id(), "p0o2sfaiwk21")).findFirst().orElse(null);
                if (Objects.isNull(storePageRenovation)) {
                    storePageRenovation = new StoreRenovationResp();
                    storePageRenovation.setTemplate_id(3526);
                }
                if (Objects.isNull(orderPageRenovation)) {
                    orderPageRenovation = new StoreRenovationResp();
                    orderPageRenovation.setTemplate_id(3692);
                }
                // 1. 为了做主题的表，原主题数据中包含门店首页的配置，这里需要移除
                storeRenovations.removeIf(it -> StringUtils.equalsIgnoreCase(it.getPage_id(), "xgaugooilxg7"));
                dataMap.put("theme", storeRenovations);

                Long newTemplateId = transferTemplateId(storePageRenovation.getTemplate_id(), orderPageRenovation.getTemplate_id());
                MainTemplateDetail template = (MainTemplateDetail) MapUtils.getObject(dataMap, "template");
                if (Objects.isNull(template)) {
                    return;
                }
                Long templateId = template.getTemplateId();
                if (!Objects.equals(templateId, newTemplateId)) {

                    GatherBase.storeExecutor.execute(() -> {
                        // 如果二者不一致，则进行同步
                        if (apolloConfig.getBooleanProperty("use.smart.template", false)) {
                            mpThemeSyncRemoteServiceV2.syncByStore(storeId);
                        } else {
                            mpThemeSyncRemoteService.syncByStore(storeId);
                        }
                    });

                }
            }
        } catch (Exception e) {
            LogUtils.logWarn("主题增量同步出错", "processMultiTemplates", dataMap, e);
        }

    }

    private Long transferTemplateId(Integer storePageTemplateId, Integer orderPageTemplateId) {
        String uniqueId = String.format("%d:%d", storePageTemplateId, orderPageTemplateId);
        ImmutablePair<Long, String> result = TEMPLATE_MAP.get(uniqueId);
        if (Objects.isNull(result)) {
            return 100000L;
        }
        return result.getLeft();
    }

}
