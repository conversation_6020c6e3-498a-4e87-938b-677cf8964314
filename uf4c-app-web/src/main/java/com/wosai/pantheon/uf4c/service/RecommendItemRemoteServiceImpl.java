package com.wosai.pantheon.uf4c.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.enums.SaleSceneEnum;
import com.wosai.pantheon.uf4c.api.RecommendItemRemoteService;
import com.wosai.pantheon.uf4c.model.dto.RecommendItemQueryByStoresRequest;
import com.wosai.pantheon.uf4c.model.vo.StoreRecommendItemTuple;
import com.wosai.pantheon.uf4c.service.item.ProductPromotionQueryDTO;
import com.wosai.pantheon.uf4c.service.item.RecommendItemHelper;
import com.wosai.pantheon.uf4c.service.item.RecommendItemPromotion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/11/7
 */
@Service
@AutoJsonRpcServiceImpl
public class RecommendItemRemoteServiceImpl implements RecommendItemRemoteService {

    @Autowired
    private RecommendItemHelper recommendItemHelper;

    @Override
    public List<StoreRecommendItemTuple> findByStores(RecommendItemQueryByStoresRequest request) {
        int serviceType = Optional.ofNullable(request.getServiceType()).orElse(SaleSceneEnum.SCAN.getServiceType());
        int countLimit = Math.min(Optional.ofNullable(request.getCountLimit()).orElse(3), 10);

        List<ProductPromotionQueryDTO> queries = request.getStoreIds().stream()
                .map(storeId -> {
                    ProductPromotionQueryDTO query = new ProductPromotionQueryDTO();
                    query.setStoreId(storeId);
                    query.setServiceType(serviceType);
                    query.setCountLimit(countLimit);
                    return query;
                }).collect(Collectors.toList());
        List<RecommendItemPromotion> promotions = recommendItemHelper.queryByStores(queries);
        Map<String, RecommendItemPromotion> storeIdPromotionMap = promotions.stream()
                .collect(Collectors.toMap(RecommendItemPromotion::getStoreId, Function.identity(), (k1, k2) -> k1));

        return request.getStoreIds().stream()
                .map(storeId -> {
                    StoreRecommendItemTuple tuple = new StoreRecommendItemTuple();
                    tuple.setStoreId(storeId);
                    tuple.setItems(Collections.emptyList());
                    RecommendItemPromotion promotion = storeIdPromotionMap.get(storeId);
                    if (Objects.nonNull(promotion)) {
                        tuple.setItems(promotion.getItems());
                    }
                    return tuple;
                }).collect(Collectors.toList());
    }

    @Override
    public void refresh(String storeId) {
        ProductPromotionQueryDTO req = new ProductPromotionQueryDTO();
        req.setStoreId(storeId);
        recommendItemHelper.refreshByStore(req);
    }
}
