package com.wosai.pantheon.uf4c.service;

import com.wosai.pantheon.uf4c.model.sodexo.SodexoBalanceRequest;
import com.wosai.pantheon.uf4c.model.sodexo.SodexoPayRequest;
import com.wosai.pantheon.uf4c.model.sodexo.SodexoResponse;
import com.wosai.pantheon.uf4c.util.LogUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class UpaySodexoService {
    @Autowired
    private RestTemplate restTemplate;
    @Value("${service.sodexo.pay}")
    private String payUrl;
    @Value("${service.sodexo.balance}")
    private String balanceUrl;

    public SodexoResponse pay(SodexoPayRequest sodexoPayRequest) {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity httpEntity = new HttpEntity<>(sodexoPayRequest, headers);
        return sendPost(payUrl, httpEntity, SodexoResponse.class);
    }

    public SodexoResponse balance(SodexoBalanceRequest sodexoBalanceRequest) {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity httpEntity = new HttpEntity<>(sodexoBalanceRequest, headers);
        return sendPost(balanceUrl, httpEntity, SodexoResponse.class);
    }


    private <t> t sendPost(String url, HttpEntity<?> httpEntity, Class<t> responseClass) {
        try {
            LogUtils.logInfo("索迪斯接口调用", "sodexoSendPost", httpEntity.getBody());
            return restTemplate.postForObject(url, httpEntity, responseClass);
        } catch (Exception e) {
            LogUtils.logWarn("索迪斯接口调用", "sodexoSendPost", httpEntity.getBody(), e);
            return null;
        }
    }

}
