package com.wosai.pantheon.uf4c.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * created by shij on 2019/3/27
 */
@Configuration
public class RedisConfig {
    @Autowired
    private PrefixStringRedisSerializer prefixStringRedisSerializer;

    @Value("${jedis.maxTotal}")
    private int jedisMaxTotal;
    @Value("${jedis.maxIdle}")
    private int jedisMaxIdle;
    @Value("${jedis.maxWait}")
    private int jedisMaxWait;
    @Value("${jedis.connect.timeout}")
    private int jedisConnectTimeout;
    @Value("${redis.base.host}")
    private String redisHost;
    @Value("${redis.base.port}")
    private int redisPort;
    @Value("${redis.base.pass}")
    private String redisPass;
    @Value("${redis.base.db}")
    private int redisDb;
    // 聚合接口换缓存配置
    @Value("${redis.gather.host}")
    private String gatherRedisHost;
    @Value("${redis.gather.port}")
    private int gatherRedisPort;
    @Value("${redis.gather.pass}")
    private String gatherRedisPass;
    @Value("${redis.gather.db}")
    private int gatherRedisDb;


    //@Bean
    JedisPoolConfig jedisPoolConfig() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(jedisMaxIdle);
        poolConfig.setMaxTotal(jedisMaxTotal);
        poolConfig.setMaxWaitMillis(jedisMaxWait);
        return poolConfig;
    }

//    @Bean
//    JedisConnectionFactory jedisConnectionFactory(JedisPoolConfig jedisPoolConfig) {
//        JedisConnectionFactory jcf = new JedisConnectionFactory();
//        jcf.setHostName(redisHost);
//        jcf.setPort(redisPort);
//        jcf.setPoolConfig(jedisPoolConfig());
//        jcf.setUsePool(true);
//        jcf.setPassword(redisPass);
//        jcf.setDatabase(redisDb);
//        jcf.afterPropertiesSet();
//        return jcf;
//    }


    JedisConnectionFactory createJedisConnectionFactory(String host, int port, String pass, int db, JedisPoolConfig jedisPoolConfig) {
        JedisConnectionFactory jcf = new JedisConnectionFactory();
        jcf.setHostName(host);
        jcf.setPort(port);
        jcf.setPoolConfig(jedisPoolConfig);
        jcf.setUsePool(true);
        jcf.setPassword(pass);
        jcf.setDatabase(db);
        jcf.afterPropertiesSet();
        return jcf;
    }

    //@Bean
    JedisPool jedisPool(JedisPoolConfig jedisPoolConfig) {
        return new JedisPool(jedisPoolConfig, redisHost, redisPort, jedisConnectTimeout, redisPass, redisDb);
    }

    @Bean(name = "redisTemplate")
    RedisTemplate<String, Object> redisTemplate() {
        JedisConnectionFactory connectionFactory = createJedisConnectionFactory(redisHost, redisPort, redisPass, redisDb, jedisPoolConfig());
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setKeySerializer(prefixStringRedisSerializer);
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setConnectionFactory(connectionFactory);
        return template;
    }


    @Bean(name = "gatherRedisTemplate")
    RedisTemplate<String, Object> gatherRedisTemplate() {
        JedisConnectionFactory connectionFactory = createJedisConnectionFactory(gatherRedisHost, gatherRedisPort, gatherRedisPass, gatherRedisDb, jedisPoolConfig());
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setKeySerializer(prefixStringRedisSerializer);
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        Jackson2JsonRedisSerializer serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        serializer.setObjectMapper(snakeCaseMapper());
        template.setHashValueSerializer(serializer);
        template.setConnectionFactory(connectionFactory);
        return template;
    }


    @Bean
    RedisTemplate<String, String> stringRedisTemplate() {
        JedisConnectionFactory connectionFactory = createJedisConnectionFactory(redisHost, redisPort, redisPass, redisDb, jedisPoolConfig());
        final RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(prefixStringRedisSerializer);
        return template;
    }

    @Bean
    ValueOperations<String, String> valueOperations(RedisTemplate<String, String> stringRedisTemplate) {
        return stringRedisTemplate.opsForValue();
    }


    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        SingleServerConfig singleServerConfig = config.useSingleServer();
        singleServerConfig.setAddress("redis://" + redisHost + ":" + redisPort);
        singleServerConfig.setDatabase(redisDb);
        singleServerConfig.setPassword(redisPass);
        return Redisson.create(config);
    }

    ObjectMapper snakeCaseMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }
}
