package com.wosai.pantheon.uf4c.service;

import com.shouqianba.smart.translation.manager.api.rpc.TranslationRemoteService;
import com.shouqianba.smart.translation.manager.common.enums.FieldTypeEnum;
import com.shouqianba.smart.translation.manager.common.interfaces.TranslationControlInterface;
import com.shouqianba.smart.translation.manager.common.model.dto.FieldTypeDTO;
import com.shouqianba.smart.translation.manager.common.model.vo.NeedTranslateVO;
import com.shouqianba.smart.translation.manager.common.utils.TranslationConvertUtils;
import com.wosai.data.Row;
import com.wosai.market.enums.ProductTypeEnum;
import com.wosai.pantheon.core.uitem.model.AttributeDto;
import com.wosai.pantheon.core.uitem.model.AttributeOptionDto;
import com.wosai.pantheon.order.enums.SpuType;
import com.wosai.pantheon.order.model.dto.request.Attribute;
import com.wosai.pantheon.order.model.dto.request.Material;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.vo.*;
import com.wosai.pantheon.uf4c.util.EntityConvert;
import com.wosai.pantheon.uf4c.util.GatherTraceRunnable;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.smartbiz.oms.api.pojo.CartInfoDTO;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartGoodsDTO;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartItemDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.pantheon.uf4c.gather.GatherBase.durationEnd;
import static com.wosai.pantheon.uf4c.gather.GatherBase.durationStart;

@Service
public class TranslationConvertService {
    @Autowired
    private TranslationControlInterface translationControlInterface;

    @Autowired
    private TranslationRemoteService translationRemoteService;

    @Autowired
    private StoreHelper storeHelper;

    @Autowired
    private ItemHelper itemHelper;

    private static final ThreadPoolExecutor TRANSLATION_EXECUTOR = new ThreadPoolExecutor(10, 30, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

    @PostConstruct
    public void init() {
        TranslationConvertUtils.setTranslationSupplier(req -> translationRemoteService.convert(req));
    }

    public void convertGatherIndexData(Map<String, Object> dataMap, GatherRequest request) {
        try {
            durationStart(dataMap, "convertGatherIndexData");
            Map<String, Object> store = (Map<String, Object>) dataMap.get("store");
            String storeId = request.getStoreId();
            String acceptLanguage = request.getAcceptLanguage();
            if (StringUtils.isBlank(storeId)) {
                storeId = MapUtils.getString(store, "storeId");
            }
            if (StringUtils.isBlank(storeId)) {
                return;
            }
            String finalStoreId = storeId;

            NeedTranslateVO needTranslateVO = translationControlInterface.needTranslate(finalStoreId, acceptLanguage);
            if (!needTranslateVO.isNeedTranslate()) {
                return;
            }
            String language = needTranslateVO.getLanguage();
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            CompletableFuture<Void> mccFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertGatherMcc(dataMap, finalStoreId, language)), TRANSLATION_EXECUTOR);
            CompletableFuture<Void> storeFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertGatherStore(dataMap, finalStoreId, language)), TRANSLATION_EXECUTOR);
            futures.add(mccFuture);
            futures.add(storeFuture);


            Boolean retailStore = MapUtils.getBoolean(store, "retailStore", false);
            if (retailStore) {
                // 零售商品
                CompletableFuture<Void> retailItemFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertGatherRetailItem(dataMap, request.getStoreId(), language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> categoryFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertGatherCategory(dataMap, finalStoreId, language, FieldTypeEnum.RE_GOODS_CATEGORY)), TRANSLATION_EXECUTOR);
                futures.add(retailItemFuture);
                futures.add(categoryFuture);
            } else {
                CompletableFuture<Void> categoryFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertGatherCategory(dataMap, finalStoreId, language, FieldTypeEnum.CA_GOODS_CATEGORY)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> itemCategoryFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertGatherItemCategory(dataMap, request.getStoreId(), language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> itemFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertGatherItem(dataMap, request.getStoreId(), language)), TRANSLATION_EXECUTOR);
                futures.add(categoryFuture);
                futures.add(itemCategoryFuture);
                futures.add(itemFuture);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            durationEnd(dataMap, "convertGatherIndexData");
        } catch (Exception e) {
            LogUtils.logWarnWithError("convertGatherIndexData error", "convertGatherIndexData", dataMap, e);
        }
    }

    public void convertItemDetailVO(ItemDetailVO itemDetailVO, String storeId, String acceptLanguage) {
        try {
            NeedTranslateVO needTranslateVO = translationControlInterface.needTranslate(storeId, acceptLanguage);
            if (!needTranslateVO.isNeedTranslate()) {
                return;
            }
            boolean retailStore = storeHelper.isRetailStore(storeId);
            String spuType = itemDetailVO.getItem().getSpuType();
            String language = needTranslateVO.getLanguage();

            List<CompletableFuture<Void>> futures = new ArrayList<>();

            if (retailStore) {
                CompletableFuture<Void> itemFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertRetailItemDetailVOItem(itemDetailVO, storeId, language)), TRANSLATION_EXECUTOR);
                futures.add(itemFuture);
            } else {
                List<ItemDetailVO> itemDetailVOS = new ArrayList<>();
                itemDetailVOS.add(itemDetailVO);
                if (ProductTypeEnum.PACKAGE.name().equals(spuType)) {
                    List<ItemDetailVO> packageMustOrderProducts = itemDetailVO.getPackageMustOrderProducts();
                    if (CollectionUtils.isNotEmpty(packageMustOrderProducts)) {
                        itemDetailVOS.addAll(packageMustOrderProducts);
                    }

                    List<PackageOptionalGroup> packageOptionalGroups = itemDetailVO.getPackageOptionalGroups();
                    if (CollectionUtils.isNotEmpty(packageOptionalGroups)) {
                        packageOptionalGroups.forEach(group -> {
                            List<ItemDetailVO> products = group.getProducts();
                            if (CollectionUtils.isNotEmpty(products)) {
                                itemDetailVOS.addAll(products);
                            }
                        });
                    }
                }
                CompletableFuture<Void> itemFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertItemDetailVOItem(itemDetailVOS, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> specTitleFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertItemDetailVOSpecTitle(itemDetailVOS, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> specSkuFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertItemDetailVOSpecSku(itemDetailVOS, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> attributesTitleFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertItemDetailVOAttributesTitle(itemDetailVOS, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> attributesOptionsFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertItemDetailVOAttributesOptions(itemDetailVOS, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> materialsFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertItemDetailVOMaterials(itemDetailVOS, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> materialGroupsFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertItemDetailVOMaterialGroups(itemDetailVOS, storeId, language)), TRANSLATION_EXECUTOR);
                futures.add(itemFuture);
                futures.add(specTitleFuture);
                futures.add(specSkuFuture);
                futures.add(attributesTitleFuture);
                futures.add(attributesOptionsFuture);
                futures.add(materialsFuture);
                futures.add(materialGroupsFuture);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 套餐商品需要默认的描述内容
            if (ProductTypeEnum.PACKAGE.name().equals(spuType) && !retailStore) {
                String descriptionAfterConvert = itemHelper.generateDescriptionAfterConvert(itemDetailVO.getPackageMustOrderProducts(), itemDetailVO.getPackageOptionalGroups());
                if (StringUtils.isNotBlank(descriptionAfterConvert)) {
                    itemDetailVO.getItem().setDescription(descriptionAfterConvert);
                }
            }
        } catch (Exception e) {
            LogUtils.logWarnWithError("convertItemDetailVO error", "convertItemDetailVO", itemDetailVO, e);
        }
    }

    public void covertItemList(Map<String, Object> dataMap, String storeId, String acceptLanguage, boolean retailStore) {
        try {
            NeedTranslateVO needTranslateVO = translationControlInterface.needTranslate(storeId, acceptLanguage);
            if (!needTranslateVO.isNeedTranslate()) {
                return;
            }
            String language = needTranslateVO.getLanguage();
            durationStart(dataMap, "covertItemList");
            List<Map<String, Object>> goods = (List<Map<String, Object>>) dataMap.get("goods");
            if (CollectionUtils.isEmpty(goods)) {
                return;
            }
            List<ItemNewVo> items = new ArrayList<>();
            goods.forEach(it -> {
                List<ItemNewVo> toAdd = (List<ItemNewVo>) it.get("items");
                if (CollectionUtils.isEmpty(toAdd)) {
                    return;
                }
                items.addAll(toAdd);
            });
            String fieldType = FieldTypeEnum.CA_SPU_NAME.getCode();
            if (retailStore) {
                fieldType = FieldTypeEnum.RE_SPU_NAME.getCode();
            }
            List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", fieldType}});
            TranslationConvertUtils.convertObjects(items, storeId, fieldTypes, language);
            durationEnd(dataMap, "covertItemList");
        } catch (Exception e) {
            LogUtils.logWarnWithError("covertItemList error", "covertItemList", dataMap, e);
        }
    }

    public void convertStoreOrderList(List<Order> orders, String storeId, String acceptLanguage) {
        try {
            NeedTranslateVO needTranslateVO = translationControlInterface.needTranslate(storeId, acceptLanguage);
            if (!needTranslateVO.isNeedTranslate()) {
                return;
            }
            // 订单分成餐饮和零售两个列表
            List<Order> retailOrders = new ArrayList<>();
            List<Order> caOrders = new ArrayList<>();
            for (Order order : orders) {
                String merchantIndustryType = MapUtils.getString(order.getExtraInfo(), "merchantIndustryType");
                if (Objects.equals(merchantIndustryType, "RETAIL")) {
                    retailOrders.add(order);
                } else {
                    caOrders.add(order);
                }
            }

            String language = needTranslateVO.getLanguage();
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            CompletableFuture<Void> orderStoreNameFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertOrderStoreName(orders, storeId, language)), TRANSLATION_EXECUTOR);
            futures.add(orderStoreNameFuture);

            if (CollectionUtils.isNotEmpty(caOrders)) {
                CompletableFuture<Void> orderItemFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertOrderItem(caOrders, storeId, language, FieldTypeEnum.CA_SPU_NAME)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> orderSpecFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertOrderSpec(caOrders, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> orderAttributeFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertOrderAttribute(caOrders, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> orderMaterialFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertOrderMaterial(caOrders, storeId, language)), TRANSLATION_EXECUTOR);
                futures.add(orderItemFuture);
                futures.add(orderSpecFuture);
                futures.add(orderAttributeFuture);
                futures.add(orderMaterialFuture);
            }

            if (CollectionUtils.isNotEmpty(retailOrders)) {
                CompletableFuture<Void> orderItemFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertOrderItem(retailOrders, storeId, language, FieldTypeEnum.RE_SPU_NAME)), TRANSLATION_EXECUTOR);
                futures.add(orderItemFuture);
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            orders.forEach(it -> {
                it.getItems().forEach(EntityConvert::generateAttachInfo);
            });
        } catch (Exception e) {
            LogUtils.logWarnWithError("convertStoreOrderList error", "convertStoreOrderList", orders, e);
        }
    }

    public void convertCartItems(List<CartItemCreate> cartItemCreates, String storeId, String acceptLanguage) {
        try {
            if (CollectionUtils.isEmpty(cartItemCreates)) {
                return;
            }
            NeedTranslateVO needTranslateVO = translationControlInterface.needTranslate(storeId, acceptLanguage);
            if (!needTranslateVO.isNeedTranslate()) {
                return;
            }
            boolean retailStore = storeHelper.isRetailStore(storeId);
            String language = needTranslateVO.getLanguage();
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            if (retailStore) {
                CompletableFuture<Void> cartSpuFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertCartItemsSpu(cartItemCreates, storeId, language, FieldTypeEnum.RE_SPU_NAME)), TRANSLATION_EXECUTOR);
                futures.add(cartSpuFuture);
            } else {
                List<CartItemCreate> allCartItems = new ArrayList<>();
                cartItemCreates.forEach(it -> {
                    allCartItems.add(it);
                    if (ProductTypeEnum.PACKAGE.name().equals(it.getItem().getSpuType())) {
                        List<CartItemCreate> packageItems = it.getPackageItems();
                        if (CollectionUtils.isNotEmpty(packageItems)) {
                            allCartItems.addAll(packageItems);
                        }
                    }
                });

                CompletableFuture<Void> cartSpuFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertCartItemsSpu(allCartItems, storeId, language, FieldTypeEnum.CA_SPU_NAME)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> cartSpecFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertCartItemsSpec(allCartItems, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> cartAttributeFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertCartItemsAttribute(allCartItems, storeId, language)), TRANSLATION_EXECUTOR);
                CompletableFuture<Void> cartMaterialFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertCartItemsMaterial(allCartItems, storeId, language)), TRANSLATION_EXECUTOR);
                futures.add(cartSpuFuture);
                futures.add(cartSpecFuture);
                futures.add(cartAttributeFuture);
                futures.add(cartMaterialFuture);
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            cartItemCreates.forEach(EntityConvert::generateAttachInfo);
        } catch (Exception e) {
            LogUtils.logWarnWithError("convertCartItems error", "convertCartItems", cartItemCreates, e);
        }
    }

    public void convertRoundCartItems(CartInfoDTO cartInfoDTO, String storeId, String acceptLanguage) {
        try {
            if (null == cartInfoDTO || CollectionUtils.isEmpty(cartInfoDTO.getShoppingCartItems())) {
                return;
            }
            NeedTranslateVO needTranslateVO = translationControlInterface.needTranslate(storeId, acceptLanguage);
            if (!needTranslateVO.isNeedTranslate()) {
                return;
            }
            String language = needTranslateVO.getLanguage();
            List<ShoppingCartGoodsDTO> shoppingCartGoodsDTOS = cartInfoDTO.getShoppingCartItems().stream().map(ShoppingCartItemDTO::getShoppingCartGoods).collect(Collectors.toList());

            List<ShoppingCartGoodsDTO> allShoppingCartGoodsDTOS = new ArrayList<>();
            shoppingCartGoodsDTOS.forEach(it -> {
                allShoppingCartGoodsDTOS.add(it);
                SpuType spuType = it.getSpuType();
                if (Objects.equals(spuType, SpuType.PACKAGE)) {
                    List<ShoppingCartGoodsDTO> packageGoods = it.getPackageGoods();
                    if (CollectionUtils.isNotEmpty(packageGoods)) {
                        allShoppingCartGoodsDTOS.addAll(packageGoods);
                    }
                }
            });

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            CompletableFuture<Void> roundCartSpuFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertRoundCartItemsSpuSku(allShoppingCartGoodsDTOS, storeId, language)), TRANSLATION_EXECUTOR);
            CompletableFuture<Void> roundCartAttributeFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertRoundCartItemsAttribute(allShoppingCartGoodsDTOS, storeId, language)), TRANSLATION_EXECUTOR);
            CompletableFuture<Void> roundCartMaterialFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> convertRoundCartItemsMaterial(allShoppingCartGoodsDTOS, storeId, language)), TRANSLATION_EXECUTOR);
            futures.add(roundCartSpuFuture);
            futures.add(roundCartAttributeFuture);
            futures.add(roundCartMaterialFuture);
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            cartInfoDTO.getShoppingCartItems().forEach(it -> EntityConvert.generateAttachInfo(it.getShoppingCartGoods(), false));
        } catch (Exception e) {
            LogUtils.logWarnWithError("convertRoundCartItems error", "convertRoundCartItems", cartInfoDTO, e);
        }
    }

    private void convertRoundCartItemsSpuSku(List<ShoppingCartGoodsDTO> shoppingCartGoodsDTOS, String storeId, String language) {
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"spuTitle", FieldTypeEnum.CA_SPU_NAME.getCode()}, {"skuTitle", FieldTypeEnum.CA_SKU_NAME.getCode()}});
        TranslationConvertUtils.convertObjects(shoppingCartGoodsDTOS, storeId, fieldTypes, language);
    }

    private void convertRoundCartItemsAttribute(List<ShoppingCartGoodsDTO> shoppingCartGoodsDTOS, String storeId, String language) {
        List<Attribute> attributes = new ArrayList<>();
        shoppingCartGoodsDTOS.forEach(it -> {
            if (CollectionUtils.isNotEmpty(it.getRecipes())) {
                attributes.addAll(it.getRecipes());
            }
        });
        TranslationConvertUtils.convertObjects(attributes, storeId, FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_ATTRIBUTE.getCode()}, {"title", FieldTypeEnum.CA_ATTRIBUTE.getCode()}}), language);
    }

    private void convertRoundCartItemsMaterial(List<ShoppingCartGoodsDTO> shoppingCartGoodsDTOS, String storeId, String language) {
        List<Material> materials = new ArrayList<>();
        shoppingCartGoodsDTOS.forEach(it -> {
            if (CollectionUtils.isNotEmpty(it.getMaterials())) {
                materials.addAll(it.getMaterials());
            }
            if (CollectionUtils.isNotEmpty(it.getRecommendMaterials())) {
                materials.addAll(it.getRecommendMaterials());
            }
        });
        TranslationConvertUtils.convertObjects(materials, storeId, FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_MATERIAL.getCode()}}), language);
    }

    private void convertCartItemsSpu(List<CartItemCreate> cartItemCreates, String storeId, String language, FieldTypeEnum fieldTypeEnum) {
        List<CartItemCreate.Item> items = new ArrayList<>();
        cartItemCreates.forEach(it -> {
            items.add(it.getItem());
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", fieldTypeEnum.getCode()}});
        TranslationConvertUtils.convertObjects(items, storeId, fieldTypes, language);
    }

    private void convertCartItemsSpec(List<CartItemCreate> cartItemCreates, String storeId, String language) {
        List<CartItemCreate.Spec> specs = new ArrayList<>();
        cartItemCreates.forEach(it -> {
            CartItemCreate.Spec spec = it.getSpec();
            if (spec != null && StringUtils.isNotBlank(spec.getName())) {
                specs.add(spec);
            }
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_SKU_NAME.getCode()}});
        TranslationConvertUtils.convertObjects(specs, storeId, fieldTypes, language);
    }

    private void convertCartItemsAttribute(List<CartItemCreate> cartItemCreates, String storeId, String language) {
        List<CartItemCreate.Attribute> attributes = new ArrayList<>();
        cartItemCreates.forEach(it -> {
            List<CartItemCreate.Attribute> attributes1 = it.getAttributes();
            if (CollectionUtils.isNotEmpty(attributes1)) {
                attributes.addAll(attributes1);
            }
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_ATTRIBUTE.getCode()}, {"title", FieldTypeEnum.CA_ATTRIBUTE.getCode()}});
        TranslationConvertUtils.convertObjects(attributes, storeId, fieldTypes, language);
    }

    private void convertCartItemsMaterial(List<CartItemCreate> cartItemCreates, String storeId, String language) {
        List<CartItemCreate.Material> materials = new ArrayList<>();
        cartItemCreates.forEach(it -> {
            List<CartItemCreate.Material> materials1 = it.getMaterials();
            List<CartItemCreate.RecommendMaterial> materials2 = it.getRecommendMaterials();
            if (CollectionUtils.isNotEmpty(materials1)) {
                materials.addAll(materials1);
            }
            if (CollectionUtils.isNotEmpty(materials2)) {
                materials.addAll(materials2);
            }
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_MATERIAL.getCode()}});
        TranslationConvertUtils.convertObjects(materials, storeId, fieldTypes, language);
    }

    private void convertOrderStoreName(List<Order> orders, String storeId, String language) {
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"storeName", FieldTypeEnum.STORE_INFO.getCode()}});
        TranslationConvertUtils.convertObjects(orders, storeId, fieldTypes, language);
    }

    private void convertOrderItem(List<Order> orders, String storeId, String language, FieldTypeEnum fieldTypeEnum) {
        List<CartItemCreate.Item> items = new ArrayList<>();
        orders.forEach(it -> {
            List<CartItemCreate> cartItemCreates = it.getItems();
            if (CollectionUtils.isEmpty(cartItemCreates)) {
                return;
            }
            cartItemCreates.forEach(itemCreate -> {
                items.add(itemCreate.getItem());
            });
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", fieldTypeEnum.getCode()}});
        TranslationConvertUtils.convertObjects(items, storeId, fieldTypes, language);
    }

    private void convertOrderSpec(List<Order> orders, String storeId, String language) {
        List<CartItemCreate.Spec> specs = new ArrayList<>();
        orders.forEach(it -> {
            List<CartItemCreate> items = it.getItems();
            if (CollectionUtils.isEmpty(items)) {
                return;
            }
            items.forEach(item -> {
                CartItemCreate.Spec spec = item.getSpec();
                if (spec != null && StringUtils.isNotBlank(spec.getName())) {
                    specs.add(spec);
                }
            });
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_SKU_NAME.getCode()}});
        TranslationConvertUtils.convertObjects(specs, storeId, fieldTypes, language);
    }

    private void convertOrderAttribute(List<Order> orders, String storeId, String language) {
        List<CartItemCreate.Attribute> attributes = new ArrayList<>();
        orders.forEach(it -> {
            List<CartItemCreate> items = it.getItems();
            if (CollectionUtils.isEmpty(items)) {
                return;
            }
            items.forEach(item -> {
                List<CartItemCreate.Attribute> attrs = item.getAttributes();
                if (CollectionUtils.isEmpty(attrs)) {
                    return;
                }
                attributes.addAll(attrs);
            });
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_ATTRIBUTE.getCode()}, {"title", FieldTypeEnum.CA_ATTRIBUTE.getCode()}});
        TranslationConvertUtils.convertObjects(attributes, storeId, fieldTypes, language);
    }

    private void convertOrderMaterial(List<Order> orders, String storeId, String language) {
        List<CartItemCreate.Material> materials = new ArrayList<>();
        orders.forEach(it -> {
            List<CartItemCreate> items = it.getItems();
            if (CollectionUtils.isEmpty(items)) {
                return;
            }
            items.forEach(item -> {
                List<CartItemCreate.Material> attrs = item.getMaterials();
                if (CollectionUtils.isEmpty(attrs)) {
                    return;
                }
                materials.addAll(attrs);
            });
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_MATERIAL.getCode()}});
        TranslationConvertUtils.convertObjects(materials, storeId, fieldTypes, language);
    }

    private void convertItemDetailVOItem(List<ItemDetailVO> itemDetailVOs, String storeId, String language) {
        List<ItemVO> itemVOS = itemDetailVOs.stream().map(ItemDetailVO::getItem).collect(Collectors.toList());
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_SPU_NAME.getCode()}, {"categoryName", FieldTypeEnum.CA_GOODS_CATEGORY.getCode()}});
        TranslationConvertUtils.convertObjects(itemVOS, storeId, fieldTypes, language);
    }

    private void convertRetailItemDetailVOItem(ItemDetailVO itemDetailVO, String storeId, String language) {
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.RE_SPU_NAME.getCode()}});
        TranslationConvertUtils.convertObject(itemDetailVO.getItem(), storeId, fieldTypes, language);
    }

    private void convertItemDetailVOSpecTitle(List<ItemDetailVO> itemDetailVOs, String storeId, String language) {
        List<ItemSpecVO> itemSpecVOS = itemDetailVOs.stream().map(ItemDetailVO::getSpecs).filter(Objects::nonNull).collect(Collectors.toList());
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"title", FieldTypeEnum.CA_SKU_NAME.getCode()}});
        TranslationConvertUtils.convertObjects(itemSpecVOS, storeId, fieldTypes, language);
    }

    private void convertItemDetailVOSpecSku(List<ItemDetailVO> itemDetailVOs, String storeId, String language) {
        List<SpecOptionVO> specOptionVOS = itemDetailVOs.stream()
                .map(ItemDetailVO::getSpecs)
                .filter(Objects::nonNull)
                .map(ItemSpecVO::getOptions)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_SKU_NAME.getCode()}});
        TranslationConvertUtils.convertObjects(specOptionVOS, storeId, fieldTypes, language);
    }

    private void convertItemDetailVOAttributesTitle(List<ItemDetailVO> itemDetailVOs, String storeId, String language) {
        List<AttributeDto> attributes = itemDetailVOs.stream()
                .map(ItemDetailVO::getAttributes)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"title", FieldTypeEnum.CA_ATTRIBUTE.getCode()}});
        TranslationConvertUtils.convertObjects(attributes, storeId, fieldTypes, language);
    }

    private void convertItemDetailVOAttributesOptions(List<ItemDetailVO> itemDetailVOs, String storeId, String language) {
        List<AttributeOptionDto> options = itemDetailVOs.stream()
                .map(ItemDetailVO::getAttributes)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(AttributeDto::getOptions)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_ATTRIBUTE.getCode()}});
        TranslationConvertUtils.convertObjects(options, storeId, fieldTypes, language);
    }

    private void convertItemDetailVOMaterials(List<ItemDetailVO> itemDetailVOs, String storeId, String language) {
        List<MaterialVO> materials = itemDetailVOs.stream()
                .map(ItemDetailVO::getMaterials)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<MaterialVO> groupMaterials = itemDetailVOs.stream()
                .map(ItemDetailVO::getMaterialGroups)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(MaterialGroupVO::getMaterials)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<MaterialVO> all = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(materials)) {
            all.addAll(materials);
        }
        if (CollectionUtils.isNotEmpty(groupMaterials)) {
            all.addAll(groupMaterials);
        }

        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_MATERIAL.getCode()}});
        TranslationConvertUtils.convertObjects(all, storeId, fieldTypes, language);
    }

    private void convertItemDetailVOMaterialGroups(List<ItemDetailVO> itemDetailVOs, String storeId, String language) {
        List<MaterialGroupVO> materialGroups = itemDetailVOs.stream()
                .map(ItemDetailVO::getMaterialGroups)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_MATERIAL.getCode()}});
        TranslationConvertUtils.convertObjects(materialGroups, storeId, fieldTypes, language);
    }

    private void convertGatherItem(Map<String, Object> dataMap, String storeId, String language) {
        durationStart(dataMap, "convertGatherItem");
        Map<String, Object> goods = (Map<String, Object>) dataMap.get("goods");
        if (MapUtils.isEmpty(goods)) {
            return;
        }
        List<List<Map<String, Object>>> pages = (List<List<Map<String, Object>>>) goods.get("pages");
        if (CollectionUtils.isEmpty(pages)) {
            return;
        }
        List<Map<String, Object>> itemsAggByCategoryList = pages.get(0);
        if (CollectionUtils.isEmpty(itemsAggByCategoryList)) {
            return;
        }
        List<Object> items = new ArrayList<>();
        itemsAggByCategoryList.forEach(it -> items.addAll((List<Object>) it.get("items")));
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        List<CategoryNewVo> categories = (List<CategoryNewVo>) dataMap.get("category");
        if (CollectionUtils.isNotEmpty(categories)) {
            categories.forEach(it -> {
                List<ItemNewVo> itemNewVos = it.getItems();
                if (CollectionUtils.isNotEmpty(itemNewVos)) {
                    items.addAll(itemNewVos);
                }
            });
        }
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.CA_SPU_NAME.getCode()}});
        TranslationConvertUtils.convertObjects(items, storeId, fieldTypes, language);
        durationEnd(dataMap, "convertGatherItem");
    }

    private void convertGatherItemCategory(Map<String, Object> dataMap, String storeId, String language) {
        durationStart(dataMap, "convertGatherItemCategory");
        Map<String, Object> goods = (Map<String, Object>) dataMap.get("goods");
        if (MapUtils.isEmpty(goods)) {
            return;
        }
        List<List<Map<String, Object>>> pages = (List<List<Map<String, Object>>>) goods.get("pages");
        if (CollectionUtils.isEmpty(pages)) {
            return;
        }
        List<Map<String, Object>> categoryList = pages.get(0);
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }
        convertItemCategory(categoryList, storeId, language);
        durationEnd(dataMap, "convertGatherItemCategory");
    }

    private void convertGatherRetailItem(Map<String, Object> dataMap, String storeId, String language) {
        durationStart(dataMap, "convertGatherRetailItem");
        Map<String, Object> goods = (Map<String, Object>) dataMap.get("goods");
        if (MapUtils.isEmpty(goods)) {
            return;
        }
        List<Map<String, Object>> pages = (List<Map<String, Object>>) goods.get("goods");
        if (CollectionUtils.isEmpty(pages)) {
            return;
        }
        List<ItemNewVo> items = new ArrayList<>();
        pages.forEach(it -> {
            List<ItemNewVo> itemNewVos = (List<ItemNewVo>) it.get("items");
            if (CollectionUtils.isNotEmpty(itemNewVos)) {
                items.addAll(itemNewVos);
            }
        });
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", FieldTypeEnum.RE_SPU_NAME.getCode()}});
        TranslationConvertUtils.convertObjects(items, storeId, fieldTypes, language);
        durationEnd(dataMap, "convertGatherRetailItem");
    }

    private void convertGatherCategory(Map<String, Object> dataMap, String storeId, String language, FieldTypeEnum fieldTypeEnum) {
        durationStart(dataMap, "convertGatherCategory");
        List<CategoryNewVo> category = (List<CategoryNewVo>) dataMap.get("category");
        if (CollectionUtils.isEmpty(category)) {
            return;
        }
        List<Map<String, Object>> categoryMaps = new ArrayList<>();
        List<CategoryVo> categoryVos = new ArrayList<>();
        category.forEach(it -> {
            try {
                Field f =  Row.class.getDeclaredField("fields");
                f.setAccessible(true);
                Object value = f.get(it);
                Map<String, Object> categoryMap = (Map<String, Object>) value;
                categoryMaps.add(categoryMap);

                try {
                    List<CategoryVo> subCategories = (List<CategoryVo>) MapUtils.getObject(categoryMap, "sub_categories");
                    if (CollectionUtils.isNotEmpty(subCategories)) {
                        categoryVos.addAll(subCategories);
                    }
                } catch (Exception e) {
                    LogUtils.logWarnWithError("convertGatherCategory subCategories 出错", "convertGatherCategory", it, e);
                }

            } catch (NoSuchFieldException | IllegalAccessException e) {
                LogUtils.logWarnWithError("convertGatherCategory出错", "convertGatherCategory", it, e);
            }
        });
        if (CollectionUtils.isNotEmpty(categoryVos)) {
            categoryVos.forEach(it -> {
                try {
                    Field f =  Row.class.getDeclaredField("fields");
                    f.setAccessible(true);
                    Object value = f.get(it);
                    Map<String, Object> categoryMap = (Map<String, Object>) value;
                    categoryMaps.add(categoryMap);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    LogUtils.logWarnWithError("convertGatherCategory出错", "convertGatherCategory", it, e);
                }
            });
        }

        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"name", fieldTypeEnum.getCode()}});
        TranslationConvertUtils.convertMaps(categoryMaps, storeId, fieldTypes, language);
        durationEnd(dataMap, "convertGatherCategory");
    }

    private void convertItemCategory(List<Map<String, Object>> dataMaps, String storeId, String language) {
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"category_name", FieldTypeEnum.CA_GOODS_CATEGORY.getCode()}});
        TranslationConvertUtils.convertMaps(dataMaps, storeId, fieldTypes, language);
    }

    private void convertGatherMcc(Map<String, Object> dataMap, String storeId, String language) {
        durationStart(dataMap, "convertMcc");
        convertMcc((Map<String, Object>) dataMap.get("mcc"), storeId, language);
        durationEnd(dataMap, "convertMcc");
    }

    private void convertGatherStore(Map<String, Object> dataMap, String storeId, String language) {
        durationStart(dataMap, "convertStore");
        convertStore((Map<String, Object>) dataMap.get("store"), storeId, language);
        durationEnd(dataMap, "convertStore");
    }

    private void convertStore(Map<String, Object> storeMap, String storeId, String language) {
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"storeName", FieldTypeEnum.STORE_INFO.getCode()}});
        TranslationConvertUtils.convertMap(storeMap, storeId, fieldTypes, language);
    }

    private void convertMcc(Map<String, Object> mccMap, String storeId, String language) {
        List<FieldTypeDTO> fieldTypes = FieldTypeDTO.build(new String[][]{{"store_bulletin", FieldTypeEnum.STORE_INFO.getCode()}, {"store_takeout_bulletin", FieldTypeEnum.STORE_INFO.getCode()}});
        TranslationConvertUtils.convertMap(mccMap, storeId, fieldTypes, language);
    }
}
