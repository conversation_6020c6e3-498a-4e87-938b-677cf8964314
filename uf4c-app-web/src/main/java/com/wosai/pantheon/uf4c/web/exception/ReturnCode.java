package com.wosai.pantheon.uf4c.web.exception;

public enum ReturnCode {

    SYSTEM_EXCEPTION("E0001", "系统异常"),
    INVALID_PARAM_EXCEPTION("E0002", "请求参数错误"),
    BUSINESS_ERROR("E0003", "业务处理错误"),
    OSS_FILE_EXCEPTION("E0004", "OSS处理错误"),
    DATA_SIGN_EXCEPTION("E0005", "数字签名错误"),
    CORE_RPC_EXCEPTION("E0006", "核心层业务错误"),
    UPAY_GATEWAY_NETWORK_EXCEPTION("E0007", "支付网关连接异常"),
    UPAY_GATEWAY_SERVER_EXCEPTION("E0008", "支付网关响应异常"),
    OPEN_GATEWAY_NETWORK_EXCEPTION("E0009", "统一网关连接异常"),
    OPEN_GATEWAY_SERVER_EXCEPTION("E0010", "统一网关响应异常"),
    BLOCK_FALLBACK_ERROR("E0011", "当前使用人数太多，请稍后再试"),//熔断和限流的异常提示
    UNAUTHORIZED("40001", "授权失败"),
    UNKNOWN_MINIPROGRAM_TYPE("40002", "无效的小程序类型"),

    MERCHANT_INFO_ERROR("50000", "交易失败，商户状态异常"),
    ITEM_FIND_FAIL("50001", "获取商品信息失败"),
    CATEGORY_FIND_FAIL("50002", "获取分类信息失败"),
    ORDER_FIND_FAIL("50003", "获取订单信息失败"),
    ORDER_CAN_NOT_PAY("50004", "订单当前不可支付"),
    ORDER_ALREADY_EXPIRED("50005", "订单已失效"),
    ORDER_PAY_FAIL("50006", "订单支付失败"),
    ORDER_AMOUNT_ERROR("50007", "订单金额异常"),
    ORDER_PARAMS_ERROR("50008", "订单商品参数异常"),
    ITEM_NOT_FOR_SALE("50009", "购物车中存在已下架商品，请去购物车删除"),
    ITEM_OUT_OF_STOCK("50010", "购物车中存在已售罄商品，请去购物车删除"),
    ORDER_CONCURRENT("ORDER_CONCURRENT", "您的伙伴已经下单或清空了购物车"),
    CART_IS_EMPTY("50011", "购物车中没有商品，不能支付"),
    ITEM_OUT_OF_SKU("50012", "购物车中商品库存不足，请去购物车删除"),
    ITEM_PRICE_CHANGE("50013", "购物车中商品价格发生过变动了哦~"),
    MIN_PRICE_CHECK_ERROR("50014", "未满足起送价"),
    MUST_CATEGORY_ERROR("50015", "未选择必选品"),
    ORDER_AGAIN_CANNOT_SUPPORT_ERROR("50016", "此类订单不支持再来一单"),
    STORE_NAME_LENGTH_OVER("50017", "门店名称超过20个字"),
    STORE_NAME_LENGTH_OVER_2("50018", "门店名称超长"),
    ITEM_NOT_FOR_SALE_2("50019", "商品已下架"),
    ITEM_INFO_HAS_BEEN_CHANGED("50020", "商品信息已发生变更，请重新选购"),
    PRESET_TIME_HAS_BEAN_INVALID("50021", "由于您长时间未操作，预定时间已失效，请重新选择"),
    PRESET_TIME_OVER_BUSINESS_TIME("50022", "由于您长时间未操作，超过了商家接单时间，暂不支持下单"),
    CART_RECORD_NOT_EXIST("50023", "购物车记录不存在"),

    ITEM_NOT_SALE_IN_SECTION("50001", "商品当前时段不可售"),
    MATERIAL_ADD_ERROR("50024", "加料不可售"),

    /**
     * E开头的错误，为主流程所需数据错误，应阻断流程
     */
    STORE_DATA_ERROR("E0011", "门店信息加载失败"),
    CATEGORY_DATA_ERROR("E0012", "商品分类加载失败"),
    GOODS_DATA_ERROR("E0013", "商品信息加载失败"),
    MCC_DATA_ERROR("E0014", "配置信息加载失败"),
    APOLLO_DATA_ERROR("E0015", "阿波罗配置信息加载失败"),
    QRCODE_PARSE_ERROR("E0016", "二维码解析失败"),
    SERVICE_COMPUTE_ERROR("E0017", "服务类型计算失败"),
    STORE_ID_ERROR("E0018", "无法获取门店ID"),

    /**
     * 下单页聚合接口错误
     */
    GET_CARTS_ERROR("E0101", "获取购物车失败"),
    GET_PACK_AMOUNT_ERROR("E0102", "计算打包费失败"),
    GET_BOOK_TIMES_ERROR("E0103", "获取预定时间失败"),
    GET_CAMPUS_ERROR("E0104", "获取门店入驻校园信息失败"),
    GET_AGREEMENT_ERROR("E0105", "获取签署外卖协议失败"),
    GET_ALL_ADDRESS_ERROR("E0106", "获取所有地址失败"),
    GET_DEFAULT_ADDRESS_ERROR("E0107", "获取默认地址失败"),
    GET_DELIVERY_AMOUNT_ERROR("E0108", "计算配送费失败"),
    GET_DISCOUNTS_AMOUNT_ERROR("E0109", "计算订单优惠失败"),

    GET_DELIVERY_FEE_FOR_NOT_SUPPORT_ERROR("E0110", "收钱吧配送暂停服务，如门店开通自取服务，您可以切换至自取，到店取餐"),


    RECOMMEND_DATA_ERROR("60001", "推荐商品加载失败"),

    HOT_SALE_DATA_ERROR("60002", "热销商品加载失败"),

    ACTIVITY_DATA_ERROR("60003", "门店优惠加载失败"),

    SINGLE_ACTIVITY_DATA_ERROR("60004", "单品优惠加载失败"),

    SECOND_ACTIVITY_DATA_ERROR("60005", "第二份半价优惠加载失败"),

    LOGIN_ERROR("60006", "登录失败"),

    DISCOUNT_CATEGORY_DATA_ERROR("60007", "优惠分类商品加载失败"),

    RECENT_ITEMS_DATA_ERROR("60008", "点过的菜加载失败"),

    PRODUCT_TAG_DATA_ERROR("60009", "商品标签加载失败"),

    STORE_THEMES_DATA_ERROR("60010", "门店主题加载失败"),

    GOODS_SUBSIDY_ERROR("60011", "加价购活动信息获取失败"),

    DELIVERY_AREA_DATA_ERROR("60012", "可配送地址加载出错"),

    SUPPORT_CARD_PAY_ERROR("60013", "查询储值卡可支付场景出错"),

    GOODS_AND_CATEGORY_ACTIVITY_DATA_ERROR("60014", "查询商品最优的折扣加载失败"),

    CATEGORY_ACTIVITY_DATA_ERROR("60015", "查询商品分类折扣加载失败"),

    ORDER_CANCEL_EXPIRED("70001", "订单只能在接单一分钟内无责取消"),

    PRE_REDUCE_STOCK_ERROR("70002", "订单预扣库存失败，请重新下单"),

    DELIVERY_AREA_ERROR("80001", "地址不在商家配送区域内"),

    DELIVERY_CAMPUS_ERROR("80002", "地址不在商家服务校园内"),

    DELIVERY_CAMPUS_ADDRESS_ERROR("80003", "地址不包含校内宿舍楼信息"),

    DELIVERY_ADDRESS_ERROR("80004", "地址不在商家服务指定地址内"),

    SODEXO_ERROR("90001", "索迪斯支付异常"),

    GET_TRADE_APP_ERROR("90002", "获取tradeApp错误"),

    GET_AMOUNT_COMPOSITION_ERROR("90002", "获取amountComposition错误"),

    /**
     * 下单页额外数据错误
     */
    GET_WX_GOODS_ERROR("90001", "获取加价购信息失败"),

    GET_WX_MERCH_ERROR("90002", "获取微信商户信息失败"),

    GET_PAY_CONFIG_ERROR("90003", "获取支付宝信息失败"),

    GET_AOP_ERROR("90004", "获取AOP信息失败"),

    GET_PAY_CHANNEL_ACTIVITY_ERROR("90005", "获取支付源商户活动信息失败"),

    GET_WX_BRAND_ACTIVITY_ERROR("90006", "获取微信品牌购信息失败"),


    /**
     * 优惠券相关
     */
    NOT_ALLOW_ADD_AND_PAY("10302", "存在未支付商品，需要到结算页进行支付"),


    TABLE_CLEANED_CHOOSE_PEOPLE("10401", "桌台已清空，请重新选择就餐人数"),
    TABLE_CLEANED_ADD_ITEM("10402", "桌台已清空，请重新加购商品"),

    ;


    private String code;
    private String message;

    ReturnCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
