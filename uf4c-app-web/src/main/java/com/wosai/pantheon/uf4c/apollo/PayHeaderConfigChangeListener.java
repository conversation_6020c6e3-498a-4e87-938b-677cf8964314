package com.wosai.pantheon.uf4c.apollo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class PayHeaderConfigChangeListener {

    @Autowired
    ApolloConfigHelper apolloConfigHelper;

    @Value("${wosai-pay-headers}")
    String payHeadersStr;


    @ApolloConfigChangeListener(value = { "application" }, interestedKeys = {"wosai-pay-headers"})
    public void payHeaderConfigOnChange(ConfigChangeEvent configChangeEvent){

        ConfigChange configChange = configChangeEvent.getChange("wosai-pay-headers");

        if (configChange == null){
            return;
        }

        String newConfigValue = configChange.getNewValue();
        initPayHeaderConfig(newConfigValue);
    }


    @PostConstruct
    public void init(){
        initPayHeaderConfig(payHeadersStr);
    }

    private void initPayHeaderConfig(String configValue){

        if (StringUtils.isBlank(configValue)){
            apolloConfigHelper.setPayHeaderConfigMap(new HashMap<>());
            return;
        }

        Map<String, String> payHeadersMap = JSON.parseObject(configValue, new TypeReference<Map<String, String>>(){}) ;

        if (payHeadersMap == null){
            payHeadersMap = new HashMap<>();
        }

        apolloConfigHelper.setPayHeaderConfigMap(payHeadersMap);
    }

}
