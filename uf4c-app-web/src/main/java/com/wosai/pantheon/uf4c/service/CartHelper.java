package com.wosai.pantheon.uf4c.service;

import com.google.common.collect.Lists;
import com.wosai.pantheon.order.enums.MaterialAddSourceEnum;
import com.wosai.pantheon.order.enums.SpuType;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.util.EntityConvert;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartGoodsDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/11/15
 */
@Component
@Slf4j
public class CartHelper {

    /**
     * 限制展示推荐加料的购物车记录
     *
     * @param records
     */
    public void limitRecommendMaterial(List<Cart.Record> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 推荐加料仅展示前2条
        int recommendMaterialLimit = 2;
        for (Cart.Record record : records) {
            if (Objects.isNull(record) || CollectionUtil.isEmpty(record.getRecommendMaterials())
                    || Optional.ofNullable(record.getNum()).orElse(0) <= 0) {
                continue;
            }
            if (recommendMaterialLimit <= 0) {
                record.setRecommendMaterials(Lists.newArrayList());
            } else {
                recommendMaterialLimit--;
            }
        }
    }

    /**
     * 生成购物车商品唯一ID
     *
     * @param create
     * @return
     */
    public String generateItemUid(CartItemCreate create) {
        return this.generateItemUid(create, false);
    }

    /**
     * 生成购物车商品唯一ID
     *
     * @param create
     * @param isPackageSubGoods
     * @return
     */
    public String generateItemUid(CartItemCreate create, boolean isPackageSubGoods) {
        CartItemCreate.Item item = create.getItem();
        CartItemCreate.Spec spec = create.getSpec();
        List<CartItemCreate.Attribute> attributes = create.getAttributes();
        List<CartItemCreate.Material> materials = create.getMaterials();
        List<CartItemCreate.RecommendMaterial> rms = create.getRecommendMaterials();
        if (StringUtils.isEmpty(item.getId())) {
            throw new ParamException("商品信息有误：商品id为空");
        }
        StringBuilder sb = new StringBuilder(item.getId());

        // 将品牌购活动参数加入计算逻辑，以保证即使是同样的商品，品牌购与原始商品生成的uid不同，确保购物车中区分开
        sb.append(create.isBrandAct());
        if (create.isBrandAct() && Objects.nonNull(create.getBrandActProduct())) {
            sb.append(create.getBrandActProduct().getProductId());
        }

        if (!isPackageSubGoods && Objects.equals(item.getSpuType(), SpuType.PACKAGE.name())) {

            if (CollectionUtil.isEmpty(create.getPackageItems())) {
                throw new ParamException("未选择套餐内商品，请查证后再试");
            }

            List<String> packageItemUidList = create.getPackageItems().stream()
                    .map(i -> generateItemUid(i, true))
                    .collect(Collectors.toList());
            packageItemUidList.stream().forEach(uid -> sb.append(uid));

        }

        if (spec != null) {
            sb.append(spec.getId());
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(attributes)) {
//            try {
//                attributes.sort(Comparator.comparing(CartItemCreate.Attribute::getName));
//            } catch (Exception ignored) {
//            }
            for (CartItemCreate.Attribute attribute : attributes) {
                sb.append(attribute.getId());
            }
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(materials)) {
//            try {
//                materials.sort(Comparator.comparing(CartItemCreate.Material::getName));
//            } catch (Exception ignored) {
//            }
            for (CartItemCreate.Material material : materials) {
                sb.append(material.getId());
                sb.append(material.getNumber());
            }
        }

        sb.append(create.isOpenTableMustOrder());

        if (CollectionUtils.isNotEmpty(rms)) {
            for (CartItemCreate.RecommendMaterial rm : rms) {
                sb.append(rm.getId()).append(rm.getNumber()).append(rm.isSelected());
            }
            sb.append(UUID.randomUUID());
        }

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes());
    }

    public List<Cart.Material> toRoundCartRecommendMaterials(List<ShoppingCartGoodsDTO.RecommendMaterial> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(p.getNumber());
                    tmp.setSource(p.getSource());
                    tmp.setSelected(p.isSelected());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public List<Cart.Material> toSingleCartRecommendMaterials(List<CartItemCreate.RecommendMaterial> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(Optional.ofNullable(p.getNumber()).orElse(0));
                    tmp.setSource(p.getSource());
                    tmp.setSelected(p.isSelected());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public List<Cart.Material> toRoundMealCartMaterials(List<com.wosai.pantheon.order.model.dto.request.Material> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(p.getNumber());
                    tmp.setSource(p.getSource());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public List<Cart.Material> toSingleMealCartMaterials(List<CartItemCreate.Material> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .filter(p -> p.getNumber() != null)
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(p.getNumber());
                    tmp.setSource(p.getSource());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public List<Cart.Attribute> toSingleMealCartAttributes(List<CartItemCreate.Attribute> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .map(p -> {
                    Cart.Attribute tmp = new Cart.Attribute();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setSeq(p.getSeq());
                    tmp.setTitle(p.getTitle());
                    return tmp;
                }).collect(Collectors.toList());
    }


    public Integer calculateMaterialPrice(List<CartItemCreate.Material> materials) {
        if (CollectionUtils.isEmpty(materials)) {
            return 0;
        }
        return materials.stream()
                .filter(m -> m.getNumber() != null)
                .map(m -> m.getPrice().intValue() * m.getNumber())
                .reduce(0, Integer::sum);
    }

    public List<CartItemCreate> changeAfterAddOrRemoveMaterial(CartItemCreate existRecord, CartItemCreate changeRecord) {
        if (Objects.isNull(existRecord)) {
            return null;
        }
        CartItemCreate newRecord = new CartItemCreate();
        BeanUtils.copyProperties(existRecord, newRecord);

        // 删除的记录
        CartItemCreate deleteRecord = new CartItemCreate();
        deleteRecord.setTableId(existRecord.getTableId());
        deleteRecord.setItemUid(existRecord.getItemUid());

        int existRecordReduceNumber = existRecord.getItem().getNumber();
        deleteRecord.setNumber(-existRecordReduceNumber);
        CartItemCreate.Item deleteItem = new CartItemCreate.Item();
        deleteItem.setNumber(-existRecordReduceNumber);
        deleteRecord.setItem(deleteItem);

        // 新增的记录
        newRecord.getItem().setNumber(existRecordReduceNumber);
        newRecord.setNumber(existRecordReduceNumber);
        newRecord = this.addOrRemoveMaterial(newRecord, changeRecord);

        return Lists.newArrayList(deleteRecord, newRecord);
    }

    /**
     * 购物车单个商品加减加料
     *
     * @param existRecord
     * @param changeRecord
     * @return
     */
    public CartItemCreate addOrRemoveMaterial(CartItemCreate existRecord, CartItemCreate changeRecord) {
        if (Objects.isNull(existRecord)) {
            return null;
        }
        List<CartItemCreate.Material> changeMaterials = Optional.ofNullable(changeRecord)
                .map(CartItemCreate::getMaterials)
                .orElseGet(Lists::newArrayList)
                .stream()
                .filter(p -> Objects.nonNull(p.getNumber()) && !Objects.equals(p.getNumber(), 0))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(changeMaterials)) {
            return existRecord;
        }

        Map<String, CartItemCreate.Material> changeMidToMatMap = changeMaterials.stream()
                .collect(Collectors.toMap(CartItemCreate.Material::getId, Function.identity(), (k1, k2) -> k1));

        List<CartItemCreate.Material> existMaterials = ListUtils.defaultIfNull(existRecord.getMaterials(), Lists.newArrayList());
        int priceWithoutMaterial = existRecord.getItem().getPrice() - this.calculateMaterialPrice(existMaterials);

        for (Iterator<CartItemCreate.Material> iterator = existMaterials.iterator(); iterator.hasNext(); ) {
            CartItemCreate.Material existMaterial = iterator.next();
            CartItemCreate.Material changeMaterial = MapUtils.getObject(changeMidToMatMap, existMaterial.getId(), null);
            if (Objects.nonNull(changeMaterial)) {
                int finalNumber = existMaterial.getNumber() + changeMaterial.getNumber();
                if (finalNumber <= 0) {
                    iterator.remove();
                } else {
                    // 推荐加料限制只加一份
                    if (Objects.equals(existMaterial.getSource(), MaterialAddSourceEnum.ORDER_SUBMIT_RECOMMEND.getCode())) {
                        existMaterial.setNumber(1);
                    } else {
                        existMaterial.setNumber(finalNumber);
                    }
                }
                // 移除，这样剩下的就是新增的
                changeMaterials.remove(changeMaterial);
            }
        }
        // 过滤
        changeMaterials = changeMaterials.stream().filter(p -> p.getNumber() >= 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(changeMaterials)) {
            // 目前限制加一份，此处防止小程序传入大于1的数字
            changeMaterials.forEach(p -> p.setNumber(1));
            existMaterials.addAll(changeMaterials);
        }
        existRecord.setMaterials(existMaterials);

        if (CollectionUtils.isNotEmpty(existRecord.getRecommendMaterials())) {
            existRecord.getRecommendMaterials().forEach(p -> {
                CartItemCreate.Material selectedMaterial = existMaterials.stream()
                        .filter(m -> StringUtils.equals(m.getId(), p.getId()) && Objects.equals(m.getSource(), 3))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(selectedMaterial)) {
                    p.setNumber(selectedMaterial.getNumber());
                    p.setSelected(true);
                } else {
                    p.setNumber(0);
                    p.setSelected(false);
                }
            });
        }

        // 重新生成文本和价格
        String newItemUid = this.generateItemUid(existRecord);
        existRecord.setId(newItemUid);
        existRecord.setItemUid(newItemUid);
        EntityConvert.generateAttachInfo(existRecord);
        existRecord.getItem().setPrice(priceWithoutMaterial + this.calculateMaterialPrice(existMaterials));

        return existRecord;
    }

}
