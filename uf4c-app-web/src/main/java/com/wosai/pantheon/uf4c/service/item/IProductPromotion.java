package com.wosai.pantheon.uf4c.service.item;

import com.wosai.pantheon.uf4c.constant.ProductPromotionBizEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/6
 */
public interface IProductPromotion<REQ extends ProductPromotionQueryDTO, RES> {

    /**
     * 推广场景
     * @return
     */
    ProductPromotionBizEnum bizScene();

    /**
     * 多门店查询
     * @param reqs
     * @return
     */
    List<RES> queryByStores(List<REQ> reqs);

    /**
     * 指定门店刷新缓存
     * @param req
     */
    void refreshByStore(REQ req);

}
