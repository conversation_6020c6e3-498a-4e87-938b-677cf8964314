package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.pantheon.core.ufood.model.StoreConfig;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.RangeAreaDto;
import com.wosai.pantheon.uf4c.model.dto.MultiConfigRequest;

import java.util.List;
import java.util.Map;

@JsonRpcService(value = "/rpc/store")
public interface StoreService {

    StoreConfig getStoreConfig(ApiRequest apiRequest);

    List<ConfigResponse> getMultiStoreConfig(ApiRequest<MultiConfigRequest> apiRequest);

    RangeAreaDto getAreas(ApiRequest apiRequest);

    Map<String, Object> getStoreShareInfo(ApiRequest apiRequest);
}
