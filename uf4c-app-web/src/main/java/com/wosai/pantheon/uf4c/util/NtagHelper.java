package com.wosai.pantheon.uf4c.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

public class NtagHelper {


    public static Map getNtagInfo(){
        Map payHeaders = ThreadLocalHelper.getPayHeaders();
        String ntagid = MapUtils.getString(payHeaders, "nfc_tag_id",null);
        if(!StringUtils.isEmpty(ntagid)){
            String alipayUserId = ThreadLocalHelper.getUser().getThirdpartyUserId();
            String alipayAppId = ThreadLocalHelper.getUser().getAlipayAppId();
            Map ntagMap = new HashMap<>();
            ntagMap.put("nfcTagId", ntagid);
            ntagMap.put("alipayUserId", alipayUserId);
            ntagMap.put("alipayAppId", alipayAppId);
            return ntagMap;
        }
        return null;
    }

}
