package com.wosai.pantheon.uf4c.util;

import com.wosai.market.mcc.api.dto.request.BooleanConfigQueryRequest;
import com.wosai.market.mcc.api.dto.request.CreateConfigRequest;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.request.StringConfigQueryRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

public class MccUtils {
    public static final String MERCHANT_ACTIVATE_STAT = "activated";
    public static final String DELIVERY_MIN_PRICE = "delivery_min_price"; // 起送价
    public static final String STORE_MUST_CATEGORY = "store_must_category"; // 扫码点单必选分类
    public static final String STORE_TAKEOUT_MUST_CATEGORY = "store_takeout_must_category"; // 自营外卖必点分类
    public static final String PACK_TYPE = "pack_type"; // 点单打包费计算方式 0-按订单 1-按商品，默认1
    public static final String PACK_FEE = "pack_fee"; // 点单每单打包费
    public static final String TAKEOUT_PACK_TYPE = "takeout_pack_type";// 外卖打包费计算方式 0-按订单 1-按商品，默认1
    public static final String TAKEOUT_PACK_FEE = "takeout_pack_fee";// 外卖每单打包费
    public static final String CASHIER_MODE = "cashier_mode";// 收银机收银
    public static final String HOT_SALE_PRODUCT_CONFIG = "hot_sale_product_config";//热销商品

    public static final String MATERIAL_RECOMMEND = "material_recommend"; // 推荐加料开关
    public static final String MATERIAL_RECOMMEND_METHOD = "material_recommend_method"; // 推荐方式


    public static final String SHOW_ORDERED_DISH = "show_ordered_dish";// 展示点过的菜
    public static final String SHOW_DISCOUNT_CATEGORY = "show_discount_category";// 展示优惠分类

    public static final String TRUE = "true";

    public static final String FALSE = "false";

    public static final String SCAN_ORDER_PAY_TYPE = "scan_order_pay_type";
    public static final String MEAL_TYPE = "meal_type";
    public static final String SINGLE = "single";
    public static final String ONLY_ORDER = "only_order";
    public static final String ORDER_AND_PAY = "order_and_pay";

    public static final String DISPLAY_ITEM_NOT_IN_SALETIME = "display_item_not_in_saletime";

    public static final String DISPLAY_ITEM_NOT_IN_SALETIME_FOR_MINI = "display_item_not_in_saletime_for_mini";

    // 收钱吧配送不可用时，是否转自配送
    public static final String SQB_DELIVERY_CONVERT_SELF = "sqb_delivery_convert_self";

    // 小程序商品列表页显示模式
    public static final String MINI_GOODS_DISPLAY_MODE = "mini_goods_display_mode";

    // 小程序商品详情页图片显示模式
    public static final String MINIAPP_PRODUCT_IMAGE_DISPLAY_SIZE = "miniapp_product_image_display_size";

    /**
     * 开关打开
     */
    public static final String SWITCH_ON = "1";

    /**
     * 开关关闭
     */
    public static final String SWITCH_CLOSE = "0";
    // 必选分类范围
    public static final String STORE_MUST_RANGE = "store_must_category_range";
    public static final String TAKEOUT_MUST_RANGE = "store_takeout_must_category_range";

    public static FindConfigByNameRequest findConfigByNameRequest(OwnerType ownerType, String ownerId, String name) {
        FindConfigByNameRequest request = new FindConfigByNameRequest();
        request.setAppId(AppId.UFOOD.getAppId());
        request.setOwnerType(ownerType.getOwnerType());
        request.setOwnerId(ownerId);
        request.setName(name);
        return request;
    }

    public static BooleanConfigQueryRequest findBooleanConfigByNameRequest(OwnerType ownerType, String ownerId, String name, boolean defaultValue) {
        BooleanConfigQueryRequest request = new BooleanConfigQueryRequest();
        request.setAppId(AppId.UFOOD.getAppId());
        request.setOwnerType(ownerType.getOwnerType());
        request.setOwnerId(ownerId);
        request.setName(name);
        request.setDefaultValue(defaultValue);
        return request;
    }

    public static StringConfigQueryRequest findStringConfigByNameRequest(OwnerType ownerType, String ownerId, String name, String defaultValue) {
        StringConfigQueryRequest request = new StringConfigQueryRequest();
        request.setAppId(AppId.UFOOD.getAppId());
        request.setOwnerType(ownerType.getOwnerType());
        request.setOwnerId(ownerId);
        request.setName(name);
        request.setDefaultValue(defaultValue);
        return request;
    }

    public static CreateConfigRequest createConfigRequest(OwnerType ownerType, String ownerId, String name, String value, Boolean enabled) {
        CreateConfigRequest request = new CreateConfigRequest();
        request.setAppId(AppId.UFOOD.getAppId());
        request.setOwnerType(ownerType.getOwnerType());
        request.setOwnerId(ownerId);
        request.setName(name);
        request.setValue(value);
        request.setEnabled(null == enabled ? true : enabled);
        return request;
    }

    public static Boolean isMerchantActivated(ConfigResponse config) {
        return null != config && TRUE.equals(config.getValue());
    }

    public static long getLongValue(ConfigResponse config) {
        return null != config && StringUtils.isNotEmpty(config.getValue()) ? Long.parseLong(config.getValue()) : 0;
    }


    public static String getConfigValue(Map mccMap, String key) {
        if (Objects.isNull(mccMap)) {
            return null;
        }
        return MapUtils.getString(mccMap, key);
    }

    public static boolean getBooleanValue(Map mccMap, String key, boolean defaultValue) {
        String value = getConfigValue(mccMap, key);
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return "1".equalsIgnoreCase(value) || "true".equalsIgnoreCase(value);
    }

    public static int getIntValue(Map mccMap, String key) {
        String value = getConfigValue(mccMap, key);
        if (StringUtils.isNotBlank(value)) {
            try {
                return Integer.parseInt(value);
            } catch (Exception e) {
                return 0;
            }
        }
        return 0;
    }
}
