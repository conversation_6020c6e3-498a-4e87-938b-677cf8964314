package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.alipay.api.domain.Car;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.mcc.api.dto.request.BooleanConfigQueryRequest;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.pantheon.core.uitem.model.AttributeOptionDto;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.*;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.pantheon.uf4c.service.CartService;
import com.wosai.pantheon.uf4c.service.ItemHelper;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.service.RedeemService;
import com.wosai.pantheon.uf4c.service.apisix.SingleCartService;
import com.wosai.pantheon.uf4c.service.apisix.TableService;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.enums.DealTypeEnum;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
@Validated
public class SingleCartServiceImpl implements SingleCartService {

    @Autowired
    CartService cartService;

    @Autowired
    RedeemService redeemService;

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    ItemHelper itemHelper;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private TableService tableService;

    @Override
    public Cart addCart(ApiRequest<CartItemCreate> apiRequest) {
        return cartService.addCart(apiRequest.getBody(), OrderMealTypeEnum.SINGLE, DealTypeEnum.ADD);
    }

    @Override
    public Cart reduceCartItemCount(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();
        String id = MapUtils.getString(queryParam, "id");
        String storeId = MapUtils.getString(queryParam, "store_id");
        Integer serviceType = MapUtils.getInteger(queryParam, "service_type", 0);

        if (StringUtils.isEmpty(id)) {
            throw new ParamException("id不能为空");
        }

        if (StringUtils.isEmpty(storeId)) {
            throw new ParamException("store_id不能为空");
        }

        return cartService.reduceCartItemCount(id, storeId, serviceType);
    }


    @Override
    public Cart setCartItem(ApiRequest<CartItemCreate> apiRequest) {
        return cartService.addCart(apiRequest.getBody(), OrderMealTypeEnum.SINGLE, DealTypeEnum.SET_ITEM_NUM);
    }

    @Override
    public Cart setPeopleNum(ApiRequest<Map> apiRequest) {
        String storeId = MapUtils.getString(apiRequest.getBody(), "store_id");
        String userName = MapUtils.getString(apiRequest.getBody(), "user_name");
        String userIcon = MapUtils.getString(apiRequest.getBody(), "user_icon");
        Integer peopleNum = MapUtils.getInteger(apiRequest.getBody(), "people_num", 0);

        if (StringUtils.isEmpty(storeId)) {
            throw new ParamException("store_id不能为空");
        }

        cartService.setCartPeopleNum(storeId, Constants.ServiceType.SCANNING, peopleNum);

        List<CartItemCreate> cartItemCreates = orderHelper.getOpenTableMustOrderItems(storeId, null, userName, userIcon, peopleNum);


        if (CollectionUtils.isNotEmpty(cartItemCreates)) {
            return cartService.addMustOrderItems(storeId, Constants.ServiceType.SCANNING, cartItemCreates);
        }


        return cartService.getCart(storeId, Constants.ServiceType.SCANNING, null, OrderMealTypeEnum.SINGLE);
    }


    @Override
    public Cart getCart(ApiRequest<CartItemCreate> apiRequest) {

        Map queryParam = apiRequest.getQuery();
        Boolean fillRecommendMaterials = MapUtils.getBoolean(queryParam, "fill_recommend_materials", false);
        String storeId = MapUtils.getString(queryParam, "store_id");
        Integer serviceType = MapUtils.getInteger(queryParam, "service_type", 0);

        if (org.apache.commons.lang3.StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        return cartService.getCart(storeId, serviceType, null, OrderMealTypeEnum.SINGLE, fillRecommendMaterials);
    }

    @Override
    public Cart resetCart(ApiRequest<CartItemCreate> apiRequest) {

        Map queryParam = apiRequest.getQuery();
        String storeId = MapUtils.getString(queryParam, "store_id");
        Integer serviceType = MapUtils.getInteger(queryParam, "service_type", 0);

        if (org.apache.commons.lang3.StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        boolean cleanMustOrder = false;

        BooleanConfigQueryRequest muserOrderEnableConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.MUST_ORDER_ENABLE_CONFIG_KEY, false);

        boolean muserOrderEnable = configRemoteService.getBooleanConfig(muserOrderEnableConfigQueryRequest);

        if (muserOrderEnable) {
            BooleanConfigQueryRequest muserOrderEditableConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.MUST_ORDER_EDITABLE_CONFIG_KEY, false);

            //可编辑的时候，需要清空购物车
            boolean muserOrderEditable = configRemoteService.getBooleanConfig(muserOrderEditableConfigQueryRequest);

            cleanMustOrder = muserOrderEditable;
        } else {
            cleanMustOrder = true;
        }

        cartService.resetCart(storeId, serviceType, cleanMustOrder, false);

        return cartService.getCart(storeId, serviceType, null, OrderMealTypeEnum.SINGLE);
    }

    @Override
    public CartCheckResultDTO checkItemStatus(ApiRequest<CartItemCreate> apiRequest) {
        Map queryParam = apiRequest.getQuery();
        String storeId = MapUtils.getString(queryParam, "store_id");
        Integer serviceType = MapUtils.getInteger(queryParam, "service_type", 0);

        if (org.apache.commons.lang3.StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        return cartService.checkItemStatus(null, storeId, serviceType, OrderMealTypeEnum.SINGLE);
    }

    @Override
    public CartAndRedeem addOrReduceMaterial(ApiRequest<CartItemCreate> apiRequest) {
        UserContextDTO user = ThreadLocalHelper.getUser();
        Cart cart = cartService.addOrRemoveMaterial(apiRequest.getBody());
        if (Objects.isNull(cart)) {
            return new CartAndRedeem();
        }
        RedeemResult redeemResult = redeemService.getRedeemResult(apiRequest.getBody(), cart, false, user);
        return new CartAndRedeem(cart, redeemResult);
    }

    @Override
    public CartAndRedeem addCartAndRedeem(@Valid ApiRequest<CartItemCreate> apiRequest) {

        CartItemCreate create = apiRequest.getBody();

        UserContextDTO user = ThreadLocalHelper.getUser();
        Integer num = null;
        //优先获取create.item里面的number, number<0表示减购，>=0或者=null表示加购
        if (create.getItem() == null || create.getItem().getNumber() == null) {
            num = create.getNumber();
        } else {
            num = create.getItem().getNumber();
        }


        //判断是否加购，true:加购，false:减购
        boolean add = num == null || num >= 0;
        Cart cart;
        if (Objects.equals(create.getMealType(), OrderMealTypeEnum.SINGLE)) {
            //轻餐
            cart = singleCarts(create, add, num);
        } else {
            //围餐
            cart = roundMealCarts(create, add, num);
        }

        //购物车被清空了
        if (cart == null) {
            return new CartAndRedeem();
        }
        //拼装redeem参数
        RedeemResult redeemResult = redeemService.getRedeemResult(create, cart, create.isFromCart(), user);
        return new CartAndRedeem(cart, redeemResult);
    }

    @Override
    public void transCart(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();
        String storeId = MapUtils.getString(queryParam, "store_id");
        Integer serviceType = MapUtils.getInteger(queryParam, "service_type", 0);
        String formerUserId = MapUtils.getString(queryParam, "former_user_id");


        cartService.transCart(formerUserId, storeId, serviceType);
    }

    @Override
    public CartAndRedeem getCartAndRedeem(ApiRequest<CartsRequest> apiRequest) {
        CartsRequest cartsRequest = apiRequest.getBody();
        Boolean fillRecommendMaterials = Optional.ofNullable(cartsRequest.getFillRecommendMaterials()).orElse(false);
        String storeId = cartsRequest.getStoreId();
        Integer serviceType = Optional.ofNullable(cartsRequest.getServiceType()).orElse(CommonUtil.getServiceType(orderHelper.getOrderType(cartsRequest.getServiceTypeName())));

        if (org.apache.commons.lang3.StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }
        Cart cart = cartService.getCart(storeId, serviceType, null, OrderMealTypeEnum.SINGLE, fillRecommendMaterials);
        //品牌购自动加购逻辑处理。外卖和轻餐都要处理
        cart = cartService.processBrandAutoCarts(cart, storeId, serviceType);
        RedeemResult redeemResult = redeemService.getRedeemResult(cartsRequest, cart, cartsRequest.isFromCart(), ThreadLocalHelper.getUser());
        return new CartAndRedeem(cart, redeemResult);
    }

    @Override
    public CartAndRedeem addCartAndRedeemV2(ApiRequest<CartItemCreate> apiRequest) {

        CartItemCreate create = apiRequest.getBody();

        if(Objects.equals(create.getMealType(), OrderMealTypeEnum.ROUND_MEAL) && StringUtils.isNotBlank(create.getTableId())){
            if(!tableService.canContinueOrder(create.getTableId())){
                Cart cart = new Cart();
                cart.setCheckResult(Result.StatusInfo.error(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getCode(), ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getMessage()));
                return new CartAndRedeem(cart, null);
            }
        }

        return addCartAndRedeem(apiRequest);
    }

    @Override
    public CartAndRedeem addCartAndRedeemBatchV2(ApiRequest<CartItemCreateBatch> apiRequest) {
        CartItemCreateBatch request = apiRequest.getBody();
        List<CartItemCreate> createList = request.getItems();

        OrderMealTypeEnum mealType = null;
        String tableId = null;
        if(CollectionUtils.isNotEmpty(createList) && !Objects.isNull(createList.get(0))){
            mealType = createList.get(0).getMealType();
            tableId = createList.get(0).getTableId();
        }

        if(Objects.equals(mealType, OrderMealTypeEnum.ROUND_MEAL) && StringUtils.isNotBlank(tableId)){
            if(!tableService.canContinueOrder(createList.get(0).getTableId())){
                Cart cart = new Cart();
                //需要初始化，否则前端无法处理
                cart.setRecords(new ArrayList<>());
                cart.setSpuCountMap(new HashMap<>());
                cart.setCheckResult(Result.StatusInfo.error(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getCode(), ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getMessage()));
                return new CartAndRedeem(cart, null);
            }
        }
        CartAndRedeem cartAndRedeem = addCartAndRedeemBatch(apiRequest);
        Cart cart = cartAndRedeem.getCart();
        if(cart != null && Objects.equals(mealType, OrderMealTypeEnum.ROUND_MEAL) &&
                request.getTableOpenId() != null && !request.getTableOpenId().equals(cart.getTableOpenId())){
            cart.setCheckResult(Result.StatusInfo.error(ReturnCode.TABLE_CLEANED_ADD_ITEM.getCode(), ReturnCode.TABLE_CLEANED_ADD_ITEM.getMessage()));
        }
        return cartAndRedeem;
    }

    @Override
    public CartAndRedeem addCartAndRedeemBatch(ApiRequest<CartItemCreateBatch> apiRequest) {
        CartItemCreateBatch request = apiRequest.getBody();

        UserContextDTO user = ThreadLocalHelper.getUser();

        List<CartItemCreate> createList = request.getItems();

        //前置处理本地购物车传入商品条数过多的情况
        Long maxSize = apolloConfigHelper.getLongConfigValueByKey("cart.local.max", 200L);
        if (CollectionUtils.isNotEmpty(createList) && maxSize > 0 && createList.size() > maxSize) {
            //根据加购时间顺序，保留先加购的maxSize个商品。小程序采用头插法，先加购的在队列末尾
            createList = createList.subList(createList.size() - maxSize.intValue(), createList.size());
        }

        Cart cart = null;
        CartItemCreate firstItem = null;
        for (CartItemCreate create : createList) {
            if (firstItem == null) {
                firstItem = create;
            }
            Integer num = null;
            //优先获取create.item里面的number, number<0表示减购，>=0或者=null表示加购
            if (create.getItem() == null || create.getItem().getNumber() == null) {
                num = create.getNumber();
            } else {
                num = create.getItem().getNumber();
            }

            //判断是否加购，true:加购，false:减购
            boolean add = num == null || num >= 0;

            if (Objects.equals(create.getMealType(), OrderMealTypeEnum.SINGLE)) {
                //轻餐
                cart = singleCarts(create, add, num);
            } else {
                //围餐
                cart = roundMealCarts(create, add, num);
            }
        }

        //购物车被清空了
        if (cart == null) {
            return new CartAndRedeem();
        }

        //购物车sn不为空，说明是已有订单的继续加菜，不用请求优惠
        if(StringUtils.isNotBlank(cart.getSn())){
            return new CartAndRedeem(cart, null);
        }
        //拼装redeem参数
        //第一个参数只需要列表中的任何一个就可以,将营销的透传参数也补充一下
        firstItem.setMkCustomInfo(request.getMkCustomInfo());
        RedeemResult redeemResult = redeemService.getRedeemResult(firstItem, cart, firstItem.isFromCart(), user);
        return new CartAndRedeem(cart, redeemResult);
    }

    @Override
    public CartAndRedeem addCartAndRedeemBySpuIds(@Valid ApiRequest<SpecSpuCartItemCreate> apiRequest) {

        ApiRequest<CartItemCreateBatch> targetApiRequest = new ApiRequest<>();
        BeanUtils.copyProperties(apiRequest, targetApiRequest, "body");

        CartItemCreateBatch cartItemCreateBatch = new CartItemCreateBatch();

        SpecSpuCartItemCreate specSpuCartItemCreate = apiRequest.getBody();


        List<CartItemCreate> cartItemCreates = Optional.ofNullable(specSpuCartItemCreate)
                .map(SpecSpuCartItemCreate::getSpecificItems)
                .orElse(Collections.emptyList())
                .stream()
                .map(effectiveSkuId -> {
                    ItemDto itemDto = itemHelper.getItemDtoById(specSpuCartItemCreate.getStoreId(), effectiveSkuId.getSpuId(), specSpuCartItemCreate.getServiceType());

                    CartItemCreate cartItemCreate = new CartItemCreate();
                    cartItemCreate.setStoreId(specSpuCartItemCreate.getStoreId());
                    cartItemCreate.setServiceType(specSpuCartItemCreate.getServiceType());
                    cartItemCreate.setMerchantId(specSpuCartItemCreate.getMerchantId());
                    cartItemCreate.setPayway(specSpuCartItemCreate.getPayway());
                    cartItemCreate.setSubPayway(specSpuCartItemCreate.getSubPayway());
                    cartItemCreate.setDiscountStrategy(specSpuCartItemCreate.getDiscountStrategy());
                    cartItemCreate.setTerminalSn(specSpuCartItemCreate.getTerminalSn());
                    cartItemCreate.setMealType(specSpuCartItemCreate.getMealType());
                    cartItemCreate.setUserIcon(specSpuCartItemCreate.getUserIcon());
                    cartItemCreate.setUserName(specSpuCartItemCreate.getUserName());
                    cartItemCreate.setTableId(specSpuCartItemCreate.getTableId());
                    cartItemCreate.setMkCustomInfo(specSpuCartItemCreate.getMkCustomInfo());
                    // 通过商品券直接加购的商品 口味做法默认选择第一个
                    cartItemCreate.setAttributes(Optional.ofNullable(itemDto.getAttributes())
                            .orElse(Collections.emptyList())
                            .stream()
                            // 必须选择的做法
                            .filter(attributeDto -> attributeDto.getMust() == 1)
                            .map(attributeDto -> {
                                CartItemCreate.Attribute attribute = new CartItemCreate.Attribute();
                                attribute.setId(attributeDto.getId());
                                attribute.setTitle(attributeDto.getTitle());
                                attribute.setSeq(attributeDto.getSeq());
                                attribute.setName(Optional.ofNullable(attributeDto.getOptions())
                                        .orElse(Collections.emptyList())
                                        .stream()
                                        .findFirst()
                                        .map(AttributeOptionDto::getName)
                                        .orElse(null));
                                return attribute;
                            }).collect(Collectors.toList()));
                    cartItemCreateBatch.setMkCustomInfo(specSpuCartItemCreate.getMkCustomInfo());
                    // 将商品信息补充到CartItemCreate中
                    CartItemCreate.Item item = new CartItemCreate.Item();
                    item.setId(itemDto.getItem().getId());
                    item.setName(itemDto.getItem().getName());
                    item.setCategoryId(itemDto.getItem().getCategoryId());
                    item.setNumber(1);
                    String photoUrl = null;
                    if (StringUtil.isNotBlank(itemDto.getItem().getPhotoUrl())) {
                        String[] arr = itemDto.getItem().getPhotoUrl().split(",");
                        photoUrl = arr[0];
                    }
                    item.setPhotoUrl(photoUrl);
                    item.setUrl(photoUrl);
                    item.setCategorySort(itemDto.getItem().getCategorySort());
                    item.setDisplayOrder(itemDto.getItem().getDisplayOrder());
                    item.setPrice(itemDto.getItem().getPrice());
                    //规格信息匹配和设置
                    Optional.ofNullable(itemDto.getSpecs())
                            .orElse(Collections.emptyList())
                            .stream()
                            .filter(spec -> Objects.equals(spec.getId(), effectiveSkuId.getSkuId()))
                            .findFirst()
                            .ifPresent(spec -> {
                                CartItemCreate.Spec tempSpec = new CartItemCreate.Spec();
                                tempSpec.setId(spec.getId());
                                tempSpec.setName(spec.getName());
                                tempSpec.setPrice(spec.getPrice());
                                item.setPrice(spec.getPrice());
                                cartItemCreate.setSpec(tempSpec);
                            });
                    // 如果有规格,但是没有传入规格,则抛出异常
                    if (CollectionUtils.isNotEmpty(itemDto.getSpecs())) {
                        if (effectiveSkuId.getSkuId() == null || effectiveSkuId.getSkuId().isEmpty()) {
                            throw new ParamException("多规格商品必须指定规格");
                        }
                        if (cartItemCreate.getSpec() == null) {
                            throw new ParamException("商品规格不存在");
                        }
                    }
                    cartItemCreate.setItem(item);
                    Result<Boolean> result = itemHelper.isItemSalebleInMiniProgram(itemDto, cartItemCreate, cartItemCreate.getServiceType(), new HashMap<>());
                    if (!result.isSuccess()) {
                        throw new ParamException(result.getErrorMsg());
                    }
                    return cartItemCreate;
                }).collect(Collectors.toList());

        cartItemCreateBatch.setItems(cartItemCreates);
        targetApiRequest.setBody(cartItemCreateBatch);

        return addCartAndRedeemBatch(targetApiRequest);

    }

    private Cart singleCarts(CartItemCreate create, boolean add, int num) {
        if (add) {
            return cartService.addCart(create, OrderMealTypeEnum.SINGLE, DealTypeEnum.ADD);
        }
        return cartService.reduceCartItemCount(create.getItemUid(), create.getStoreId(), create.getServiceType(), -num);
    }

    private Cart roundMealCarts(CartItemCreate create, boolean add, int num) {
        if (add) {
            create.setNumber(num);
            create.getItem().setNumber(num);
            return cartService.addCart(create, OrderMealTypeEnum.ROUND_MEAL, DealTypeEnum.ADD);
        } else {
            create.setNumber(-num);
            create.getItem().setNumber(-num);
            return cartService.reduceRoundMealCart(create);
        }
    }
}
