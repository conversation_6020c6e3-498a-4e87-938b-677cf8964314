package com.wosai.pantheon.uf4c.service;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.shouqianba.mk.campustakeout.delivery.application.open.OpenTeamCampusRpcService;
import com.shouqianba.mk.campustakeout.delivery.enums.deliveryteam.SupportDeliveryStatusEnum;
import com.shouqianba.mk.campustakeout.delivery.response.dto.campus.CampusAggregateDeliveryInfoDTO;
import com.shouqianba.mk.campustakeout.delivery.response.order.ofct.OrderFulfillmentCycleTimeDTO;
import com.wosai.data.bean.BeanUtil;
import com.wosai.market.mcc.api.dto.request.BatchFindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.request.BooleanConfigQueryRequest;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.mcc.api.utils.DeliveryTimeUtils;
import com.wosai.market.merchant.api.CustomerStoreRemoteService;
import com.wosai.market.merchant.api.DeliveryAddressRemoteService;
import com.wosai.market.merchant.api.StoreSetService;
import com.wosai.market.merchant.dto.Location;
import com.wosai.market.merchant.dto.StoreSetSingleQrcode;
import com.wosai.market.merchant.dto.address.DeliveryAddressDto;
import com.wosai.market.merchant.dto.address.DeliveryAddressQueryReq;
import com.wosai.market.merchant.dto.campus.CampusStoreBaseInfo;
import com.wosai.market.merchant.dto.customer.request.StoreDetailRequest;
import com.wosai.market.merchant.dto.customer.response.StoreDetailResponse;
import com.wosai.market.tethys.api.dto.CategoryInfo;
import com.wosai.market.tethys.api.dto.DiscountInfo;
import com.wosai.market.tethys.api.dto.SkuInfo;
import com.wosai.market.tethys.api.enums.EffectiveType;
import com.wosai.market.tethys.api.service.DeliveryFeeReductionActivityService;
import com.wosai.market.trade.modal.OrderDeliverFeeRequest;
import com.wosai.market.trade.modal.TradeAppRequest;
import com.wosai.market.trade.modal.dada.DadaDeliverFeeResponse;
import com.wosai.market.trade.modal.upayDelivery.UpayDeliverFee;
import com.wosai.market.trade.modal.upayDelivery.UpayDeliverFeeRequest;
import com.wosai.market.trade.modal.upayDelivery.UpayDeliveryType;
import com.wosai.market.trade.service.PayService;
import com.wosai.market.user.dto.UserInfoDTO;
import com.wosai.market.user.service.UserService;
import com.wosai.pantheon.core.ufood.constant.OrderQrCodeType;
import com.wosai.pantheon.core.ufood.model.OrderQrCode;
import com.wosai.pantheon.core.ufood.service.OrderQrCodeService;
import com.wosai.pantheon.core.uitem.constant.ChargeMethodEnum;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.model.ItemSpec;
import com.wosai.pantheon.core.uitem.model.Material;
import com.wosai.pantheon.core.uitem.model.opentable.FindOpenTableMustOrderRequest;
import com.wosai.pantheon.core.uitem.model.opentable.OpenTableMustOrderItemDTO;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.core.uitem.service.OpenTableMustOrderService;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.BookOrderInfoDTO;
import com.wosai.pantheon.order.model.dto.request.Attribute;
import com.wosai.pantheon.order.model.dto.request.OrderItemAddRequest;
import com.wosai.pantheon.order.model.dto.request.OrderItemDigest;
import com.wosai.pantheon.order.model.dto.request.Spec;
import com.wosai.pantheon.order.model.dto.v2.OrderCampusDeliveryDTO;
import com.wosai.pantheon.order.model.dto.v2.OrderCampusStationDTO;
import com.wosai.pantheon.order.utils.OrderUtil;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.*;
import com.wosai.pantheon.uf4c.model.dto.DeliverFeeRequest;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import com.wosai.pantheon.uf4c.model.dto.RetailExtendItemDTO;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.model.vo.SpecOptionVO;
import com.wosai.pantheon.uf4c.util.*;
import com.wosai.pantheon.uf4c.util.CartHelper;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.RedeemSignUtil;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import com.wosai.smartbiz.payment.api.trade.defs.TradeApp;
import com.wosai.smartbiz.user.user.dto.req.MainCashierBaseReq;
import com.wosai.smartbiz.user.user.dto.vo.MainCashierCheckOnlineVO;
import com.wosai.smartbiz.user.user.services.MainCashierRegisterService;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.pantheon.order.constant.Constants.OrderAdditionalInfoKey;
import static com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.*;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * <AUTHOR>
 * @date 2020/4/9
 */
@Service
@Slf4j
public class OrderHelper {

    private static String OLD_QR_CODE_PATTERN = "^\\d+$";

    private static final List<TieredPrice> DEFAULT_TIERED_PRICING = Arrays.asList(
            new TieredPrice(0d, 3d, 600),
            new TieredPrice(3d, 6d, 1200),
            new TieredPrice(6d, -1d, 1200)
    );
    // 聚合配送业务标识：4-蜂鸟 5-美团 6-达达 7-顺丰 8-UU跑腿 9-麦芽田
    private static final List<String> UpayDeliveryTypes = Arrays.asList("4", "5", "6", "7", "8", "9");

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CartService cartService;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private OrderQrCodeService orderQrCodeService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private UserService userService;

    @Autowired
    private PayService payService;

    @Autowired
    private RedeemService redeemService;


    @Autowired
    private CustomerStoreRemoteService customerStoreRemoteService;

    @Autowired
    private DeliveryFeeReductionActivityService deliveryFeeReductionActivityService;

    @Autowired
    private StoreSetService storeSetService;

    @Autowired
    private MainCashierRegisterService mainCashierRegisterService;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private ItemHelper itemHelper;

    @Autowired
    private OpenTableMustOrderService openTableMustOrderService;

    @Autowired
    private CampusHelper campusHelper;


    @Autowired
    private DeliveryAreaHelper deliveryAreaHelper;
    @Autowired
    private DeliveryAddressRemoteService deliveryAddressRemoteService;
    @Autowired
    private OpenTeamCampusRpcService openTeamCampusRpcService;

    @Value("${test-env:false}")
    private boolean useTestApp;


    public Order convertPayRequest(PayRequest request) {

        OrderType orderType = getOrderType(request.getType());

        StoreInfo storeInfo = makeStoreInfo(request.getStoreId(), orderType, request.getTerminalId(), request.getTerminalSn());

        Order order = makeOrder(request, orderType, storeInfo);

        if (storeInfo.isCahiserMode()) {
            //收银机模式下，这个需要接单
            order.setCashierMode(true);
        } else {
            order.setCashierMode(false);
        }

        computeCartAmount(order, request.getStoreId(), orderType);

        checkMinPrice(request, order, orderType);

        fillAdditionalInfo(order, request);
        return order;
    }

    private Long checkPresetTime(Long presetTime, String storeId, OrderType orderType) {
        if (orderType == OrderType.PRE_ORDER) {
            if (presetTime == null) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "非商家接单时间，暂不支持下单哦");

            }
            // 判断仅当前的情况下，取单时间是否在营业范围内
            ConfigResponse businessTimeConfig = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TIMES));
            ConfigResponse presetTypeConfig = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.PRESET_TYPE));
            Integer presetType = Optional.ofNullable(presetTypeConfig).map(it -> Integer.parseInt(it.getValue())).orElse(0);
            if (presetType == 0) {
                Optional.ofNullable(businessTimeConfig)
                        .map(it -> businessTimeConfig.getValue())
                        .map(it -> JacksonUtil.toBean(businessTimeConfig.getValue(), new TypeReference<List<Map>>() {
                        }))
                        .ifPresent(times -> {
                            if (times.size() > 0 && times.stream().noneMatch(it -> TimeSplit.isInDate4PresetTime(
                                    BeanUtil.getPropString(it, "startTime"),
                                    BeanUtil.getPropString(it, "endTime"), presetTime))
                            ) {
                                throw new BusinessException(ReturnCode.PRESET_TIME_OVER_BUSINESS_TIME);
                            }
                        });
            }
            Date now = TimeSplit.getNowAfter15Date();
            if (presetTime < System.currentTimeMillis()) {
                throw new BusinessException(ReturnCode.PRESET_TIME_HAS_BEAN_INVALID);
            }
            if (presetTime - now.getTime() > 0) {
                return presetTime;
            }
        }
        return null;
    }

    /**
     * 验证外卖订单起送价
     * 优惠后的订单金额必须大于等于门店设置的起送价（起送价默认1元）
     *
     * @param order
     * @param orderType
     */
    private void checkMinPrice(PayRequest payRequest, Order order, OrderType orderType) {
        if (OrderType.TAKE_OUT_ORDER == orderType) {
            long amount = order.getOriginalAmount();
            long delivery_fee = order.getExtra().getDeliveryInfo().getDeliveryFee();
            amount = amount - delivery_fee;
            // 订单优惠后的金额
            long amount_after_discount = amount;
            // 查询起送价
            ConfigResponse configResponse = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, order.getStoreId(), MccUtils.DELIVERY_MIN_PRICE));
            long min_price = Math.max(MccUtils.getLongValue(configResponse), 1);
            RedeemResult redeemResult = null;
            if (StringUtils.isNotBlank(payRequest.getRedeemDigest())) {
                redeemResult = RedeemSignUtil.decryptRedeemResult(payRequest.getRedeemDigest());
            } else {
                // 查询门店优惠信息
                RedeemRequest redeemRequest = new RedeemRequest();

                redeemRequest.setStoreId(order.getStoreId());
                redeemRequest.setMerchantId(order.getMerchantId());
                redeemRequest.setTotalAmount(String.valueOf(amount));
                redeemRequest.setPayway(order.getPayway());
                redeemRequest.setSubPayway(order.getSubPayway());
                redeemRequest.setDiscountStrategy(OrderType.TAKE_OUT_ORDER.getMsg());
                redeemRequest.setCompatible(true);
                redeemRequest.setCombinedPayment(payRequest.getCombinedPayment());
                redeemRequest.setMkCustomInfo(payRequest.getMkCustomInfo());


                redeemResult = redeemService.getRedeemResult(redeemRequest, ThreadLocalHelper.getUser());

            }
            if (redeemResult != null && redeemResult.getTotalDiscount() > 0) {
                long merchantDiscountTotalAmount = 0;
                try {
                    if (!CollectionUtils.isEmpty(redeemResult.getRedeemDetails())) {
                        merchantDiscountTotalAmount = redeemResult.getRedeemDetails().stream()
                                .filter(it -> it.getType() == 3).mapToLong(RedeemResult.RedeemDetail::getDiscountAmount).sum();
                    }
                } catch (Exception e) {
                    LogUtils.logWarn("计算起送价优惠相关错误", "checkMinPrice", redeemResult, e);
                }

                // 若有优惠时，计算优惠后的订单金额
                amount_after_discount = amount - merchantDiscountTotalAmount;
            }
            if (amount_after_discount < min_price) {
                // 订单金额未满足起送价要求
                throw new BusinessException(ReturnCode.MIN_PRICE_CHECK_ERROR);
            }
        }
    }

    public List<OrderItemDigest> getDefaultLoveDiningItemDigest() {
        List<OrderItemDigest> items = new ArrayList<>();
        OrderItemDigest orderItemDigest = new OrderItemDigest();
        orderItemDigest.setId("love_feast");
        orderItemDigest.setName("爱心餐");
        orderItemDigest.setValue("");
        orderItemDigest.setPrice(1501);
        orderItemDigest.setNumber(1);
        items.add(orderItemDigest);
        return items;
    }

    public List<OrderItemAddRequest> getDefaultLoveDiningItem() {
        List<OrderItemAddRequest> items = new ArrayList<>();
        OrderItemAddRequest itemRequest = new OrderItemAddRequest();
        itemRequest.setPackFee(0L);
        itemRequest.setItemId("love_feast");
        itemRequest.setName("爱心餐");
        itemRequest.setCount(new BigDecimal(1));
        itemRequest.setOriginalAmountPer(1501L);
        itemRequest.setEffectiveAmountPer(1501L);
        itemRequest.setCategoryId("love_feast");
        itemRequest.setSpuType(SpuType.PRODUCT);
        itemRequest.setSkuType(SkuType.SINGLE);
        itemRequest.setCategorySort(1);
        itemRequest.setItemSort(1);
        items.add(itemRequest);
        return items;

    }


    public static OrderItemAddRequest convertCartItem2CreateRequest(CartItemCreate i) {
        OrderItemAddRequest itemRequest = new OrderItemAddRequest();
        if (MapUtils.isEmpty(itemRequest.getExtraMap())) {
            itemRequest.setExtraMap(new HashMap<>());
        }
        patchMapWeightInfo(i.getExtraInfo(), itemRequest.getExtraMap());
        CartItemCreate.Item item = i.getItem();

        itemRequest.setPackFee(i.getPackFee());
        itemRequest.setReflect(i.getItemUid());
        itemRequest.setItemId(item.getId());
        itemRequest.setName(item.getName());
        itemRequest.setManualChangePrice(i.isManualChangePrice());

        if (i.getProcessStatus() == GoodsProcessStatus.PART_REFUNDED || i.getProcessStatus() == GoodsProcessStatus.PART_RETURNED) {
            itemRequest.setCount(new BigDecimal(item.getNumber() - Optional.ofNullable(item.getReturnNumber()).orElse(0)));
        } else {
            if (item.getNumberDecimal() != null) {
                itemRequest.setCount(item.getNumberDecimal());
            } else {
                itemRequest.setCount(new BigDecimal(item.getNumber()));
            }
        }

        itemRequest.setOriginalAmountPer(Long.valueOf(item.getPrice()));
        itemRequest.setEffectiveAmountPer(Long.valueOf(item.getPrice()));
        itemRequest.setUrl(item.getUrl());
        itemRequest.setCategoryId(item.getCategoryId());

        itemRequest.setPackageGroupId(i.getPackageGroupId());
        itemRequest.setPackageGroupName(i.getPackageGroupName());
        itemRequest.setSpuType(com.wosai.pantheon.uf4c.util.OrderHelper.getNotNullSpuType(item.getSpuType()));

        itemRequest.setCategorySort(item.getCategorySort());
        itemRequest.setItemSort(item.getDisplayOrder());

        Integer specCount = item.getIsMultiple();
        if (specCount != null && specCount > 1) {
            itemRequest.setSkuType(SkuType.MULTI);
        } else {
            itemRequest.setSkuType(SkuType.SINGLE);
        }

        Arrays.asList(OrderGoodsTagEnum.HOT_SALE, OrderGoodsTagEnum.RECOMMEND, OrderGoodsTagEnum.BOUGHT,
                OrderGoodsTagEnum.DISCOUNT, OrderGoodsTagEnum.MEITUAN_GROUP_COUPON, OrderGoodsTagEnum.DOUYIN_GROUP_COUPON,
                OrderGoodsTagEnum.PIAOFUTONG_GROUP_COUPON, OrderGoodsTagEnum.JINGDONG_GROUP_COUPON).forEach(p -> {
            if (TagUtil.hasTag(item.getItemTag(), p.getValue())) {
                itemRequest.setGoodsTag(OrderUtil.addTag(itemRequest.getGoodsTag(), p.getValue()));
            }
        });

        if (i.isOpenTableMustOrder()) {
            itemRequest.setGoodsTag(OrderUtil.addTag(itemRequest.getGoodsTag(), OrderGoodsTagEnum.OPENTABLE_MUST_ORDER.getValue()));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(i.getBrandActivityProducts())) {
            itemRequest.getExtraMap().put("brandActProducts", i.getBrandActivityProducts());
        }
        if (i.isBrandAct()) {
            itemRequest.getExtraMap().put("brandActivity", true);
            itemRequest.getExtraMap().put("brandProductSource", i.getBrandProductSource());
            itemRequest.getExtraMap().put("brandActivityId", i.getBrandActProduct().getActivityId());
            itemRequest.getExtraMap().put("stockId", i.getItem().getStockId());
        }
        if (MapUtils.isNotEmpty(i.getSkuConvert())) {
            itemRequest.getExtraMap().put("sku_convert", i.getSkuConvert());
        }
        if (MapUtils.isNotEmpty(i.getSnapshot())) {
            itemRequest.getExtraMap().put("snapshot", i.getSnapshot());
            String barcode = MapUtil.getString(i.getSnapshot(), "barcode");
            if (StringUtils.isNotBlank(barcode)) {
                itemRequest.getExtraMap().put("barcode", barcode);
            }
        }

        // 保存单位以及计价单位类型，购物车中的此数据是在购物车校验时维护进去的
        itemRequest.setSaleUnit(item.getUnit());
        itemRequest.setUnitType(GoodsUnitTypeEnum.NUMBER);
        itemRequest.setSpec(
                Optional.ofNullable(i.getSpec())
                        .map(CartItemCreate.Spec::getName)
                        .orElse(null)
        );
        itemRequest.setSpecInfo(Optional.ofNullable(i.getSpec())
                .map(it -> {
                    Spec spec = new Spec();
                    spec.setId(it.getId());
                    spec.setName(it.getName());
                    spec.setPrice(it.getPrice());
                    spec.setAttachedInfo(i.getItem().getAttachedInfo());
                    return spec;
                })
                .orElse(null)
        );
        itemRequest.setAttributes(
                Optional.ofNullable(i.getAttributes())
                        .map(attributes -> attributes.stream()
                                .map(CartItemCreate.Attribute::getName)
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        itemRequest.setAttributeInfos(
                Optional.ofNullable(i.getAttributes())
                        .map(attributes -> attributes.stream()
                                .map(it -> {
                                    Attribute attribute = new Attribute();
                                    attribute.setId(it.getId());
                                    attribute.setTitle(it.getTitle());
                                    attribute.setName(it.getName());
                                    attribute.setSeq(it.getSeq());
                                    return attribute;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );


        List<CartItemCreate> cartItemCreateList = CartHelper.mergeSameGoodsCartItem(i.getPackageItems());

        List<OrderItemAddRequest> packageGoods = Optional.ofNullable(cartItemCreateList)
                .map(packageItems -> packageItems.stream()
                        .map(packageItem -> convertCartItem2CreateRequest(packageItem))
                        .collect(Collectors.toList())
                )
                .orElse(null);
        itemRequest.setPackageGoods(packageGoods);

        itemRequest.setMaterials(
                Optional.ofNullable(i.getMaterials())
                        .map(materials -> materials.stream().filter(m -> m.getNumber() != null)
                                .map(m -> com.wosai.pantheon.uf4c.util.OrderHelper.convertMaterial(m))
                                .collect(Collectors.toList()))
                        .orElse(null)
        );
        return itemRequest;
    }

    private boolean isSqbDelivery(String storeId, int campusId) {
        try {
            CampusStoreBaseInfo campusStoreBaseInfo = campusHelper.getCampusStoreInfo(campusId, storeId);
            return Objects.nonNull(campusStoreBaseInfo) && campusStoreBaseInfo.getDeliveryType() == 1;
        } catch (Exception e) {
            LogUtils.logWarn("查询门店在校园是否收钱吧配送出错", "isSqbDelivery", ImmutableMap.of("storeId", storeId, "campusId", campusId));
        }
        return false;
    }

    private boolean isSqbDeliveryConvertSelf(String storeId) {
        try {
            ConfigResponse configResponse = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, MccUtils.SQB_DELIVERY_CONVERT_SELF));
            if (Objects.nonNull(configResponse)) {
                return BooleanUtils.toBoolean(configResponse.getValue());
            }
        } catch (Exception e) {
            LogUtils.logWarn("查询门店在校园是否转自配送出错", "isSqbDeliveryConvertSelf", storeId);
        }
        return true;
    }

    /**
     * 新校园配送查询逻辑，含校园自配送业务
     *
     * @param deliverInfo
     * @param storeId
     * @return
     */
    public DeliveryInfoAndMsg getCampusDeliveryInfo(DeliverInfo deliverInfo, String storeId) {
        DeliveryInfoAndMsg deliveryInfoAndMsg = new DeliveryInfoAndMsg();
        // 查询门店是否在校园
        if (!campusHelper.isStoreInAnyCampus(storeId)) {
            // 不在校园，则该逻辑结束，走普通外卖逻辑
            return deliveryInfoAndMsg;
        }
        // 门店在校园是否是收钱吧配送
        boolean isSqbDelivery = false;
        // 收钱吧不可配时，是否转自配送
        boolean sqbDeliveryConvertSelf = true;
        if (Objects.nonNull(deliverInfo.getCampusId()) && deliverInfo.getCampusId() > 0) {
            // 查询门店在校园配送方式
            isSqbDelivery = isSqbDelivery(storeId, deliverInfo.getCampusId());
            if (isSqbDelivery) {
                sqbDeliveryConvertSelf = isSqbDeliveryConvertSelf(storeId);
            }
        }
        // 调用配送方接口查询
        CampusAggregateDeliveryInfoDTO campusAggregateDeliveryInfoDTO = campusHelper.queryCampusInfoV2(deliverInfo.getCampusId(), storeId, deliverInfo.getAddressCode(), deliverInfo.getPresetTime(), deliverInfo.getFloor());
        if (Objects.isNull(campusAggregateDeliveryInfoDTO)) {
            if (isSqbDelivery && !sqbDeliveryConvertSelf) {
                // 是收钱吧配送，但是不需要转自配送时报错
                throw new BusinessException(ReturnCode.GET_DELIVERY_FEE_FOR_NOT_SUPPORT_ERROR);
            }
            deliveryInfoAndMsg.setCampusExceptionMessage("校园配送信息查询失败");
            return deliveryInfoAndMsg;
        }
        SupportDeliveryStatusEnum deliveryStatusEnum = campusAggregateDeliveryInfoDTO.getDeliveryStatusInfo().getSupportDeliveryStatus();
        if (deliveryStatusEnum == SupportDeliveryStatusEnum.NOT_SUPPORT_DELIVERY) {
            if (isSqbDelivery && !sqbDeliveryConvertSelf) {
                throw new BusinessException(ReturnCode.GET_DELIVERY_FEE_FOR_NOT_SUPPORT_ERROR);
            }
            deliveryInfoAndMsg.setCampusExceptionMessage(campusAggregateDeliveryInfoDTO.getDeliveryStatusInfo().getReason());
            // 校园不配送
            return deliveryInfoAndMsg;
        }
        deliverInfo.setPreCreateDeliveryId(campusAggregateDeliveryInfoDTO.getPreCreateDeliveryId());
        if (campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo() != null && StringUtils.isNotBlank(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getBuildingCode())) {
            //楼栋编码，打印使用
            if (deliverInfo.getExtra() == null) {
                deliverInfo.setExtra(new HashMap<>());
            }
            deliverInfo.getExtra().put("building_code", campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getBuildingCode());
        }
        // 自配送
        if (deliveryStatusEnum == SupportDeliveryStatusEnum.MERCHANT_DELIVERY) {
            // 校园商家自配送
            deliverInfo.setCampusDeliveryType(Constants.CampusDeliveryType.SELF);
            // 这里是校园自配送，校园配送信息就是用户配送信息
            deliveryInfoAndMsg.setDeliverInfo(deliverInfo);
            return deliveryInfoAndMsg;
        }
        if (deliveryStatusEnum == SupportDeliveryStatusEnum.SQB_DELIVERY) {
            // 这里新创建配送信息对象，供多段配送使用
            DeliverInfo tempInfo = new DeliverInfo();
            tempInfo.setAppVersion(deliverInfo.getAppVersion());
            tempInfo.setUserId(deliverInfo.getUserId());
            tempInfo.setExtra(deliverInfo.getExtra());
            // 校园商家自配送
            tempInfo.setCampusDeliveryType(Constants.CampusDeliveryType.SQB);
            tempInfo.setCampusOutsideDeliveryType(Constants.CampusDeliveryType.SQB);
            // 判断校内是否经过站点
            if (Objects.nonNull(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo()) && StringUtils.isNotBlank(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getStationId())) {
                tempInfo.setGender(1);
                // 校内配送经过中转站，设置配送信息为站点信息，以便第一段配送到站点
                tempInfo.setStationId(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getStationId());
                tempInfo.setLongitude(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getAddress().getLongitude());
                tempInfo.setAddress(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getAddress().getDetailAddress());
                tempInfo.setLatitude(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getAddress().getLatitude());
                tempInfo.setUserName(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getContactsName());
                tempInfo.setCellphone(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getContactsCellphone());
            } else {
                tempInfo.setLatitude(deliverInfo.getLatitude());
                tempInfo.setLongitude(deliverInfo.getLongitude());
                tempInfo.setAddress(deliverInfo.getAddress());
                tempInfo.setUserName(deliverInfo.getUserName());
                tempInfo.setCellphone(deliverInfo.getCellphone());
                tempInfo.setGender(deliverInfo.getGender());
            }
            tempInfo.setPreCreateDeliveryId(deliverInfo.getPreCreateDeliveryId());
            // 校园总配送费
            tempInfo.setDeliveryFee(Optional.ofNullable(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getDeliveryFee().intValue()).orElse(0));
            // 校园商家分摊配送费
            tempInfo.setDeliveryFeeAffordAmount(Optional.ofNullable(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getDeliveryFeeAffordAmount()).orElse(0L).intValue());
            // 校园上楼费用
            tempInfo.setFloorFee(Optional.ofNullable(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getUpstairsFee()).orElse(0L).intValue());
            tempInfo.setPriceExtraFee(Optional.ofNullable(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getPriceExtraFee()).orElse(0L).intValue());
            deliveryInfoAndMsg.setDeliverInfo(tempInfo);
        }

        return deliveryInfoAndMsg;
    }

    /**
     * 如果是校园订单则返回站包含点信息的deliverInfo
     * 不是则返回null
     */
    public DeliveryInfoAndMsg getCampusOrderInfo(DeliverInfo deliverInfo, String storeId) {
        if (apolloConfigHelper.getBooleanConfigValueByKey("campus.self.delivery", true)) {
            return getCampusDeliveryInfo(deliverInfo, storeId);
        }
        DeliveryInfoAndMsg deliveryInfoAndMsg = new DeliveryInfoAndMsg();
        // 收货地址是否在校内，并且有校园ID，才可能进行校园外卖配送
        if (deliverInfo.getType() == 1) {
            if (Objects.isNull(deliverInfo.getCampusId()) || deliverInfo.getCampusId() <= 0) {
                deliveryInfoAndMsg.setCampusExceptionMessage(CampusHelper.CAMPUS_ID_ERROR);
                log.info("userId={}的用户校园外卖下单未成功，原因:{}", ThreadLocalHelper.getUserId(), CampusHelper.CAMPUS_ID_ERROR);
                return deliveryInfoAndMsg;
            }
            if (StringUtils.isBlank(deliverInfo.getAddressCode())) {
                deliveryInfoAndMsg.setCampusExceptionMessage(ReturnCode.DELIVERY_CAMPUS_ADDRESS_ERROR.getMessage());
                log.info("userId={}的用户校园外卖下单未成功，原因:{}", ThreadLocalHelper.getUserId(), ReturnCode.DELIVERY_CAMPUS_ADDRESS_ERROR.getMessage());
                return deliveryInfoAndMsg;
            }
            // 查询门店在该校园的配送方式
            CampusStoreBaseInfo campusStoreInfo = campusHelper.getCampusStoreInfo(deliverInfo.getCampusId(), storeId);
            // 门店在校园支持配送，校内收钱吧配送或全程收钱吧配送
            if (Objects.nonNull(campusStoreInfo) && (campusStoreInfo.getDeliveryType() == 1 || campusStoreInfo.getOutSideDeliveryType() == 1)) {
                // 查询当前门店在该校园是否可以配送
                CampusAggregateDeliveryInfoDTO campusAggregateDeliveryInfoDTO = campusHelper.queryCampusInfoV2(deliverInfo.getCampusId(), storeId, deliverInfo.getAddressCode(), deliverInfo.getPresetTime(), deliverInfo.getFloor());
                // 判断是否能配送
                if (Objects.nonNull(campusAggregateDeliveryInfoDTO) && CampusHelper.CAMPUS_STATION_STATUS_OPEN.equalsIgnoreCase(campusAggregateDeliveryInfoDTO.getDeliveryStatusInfo().getStatus())) {
                    //如果是全程收钱吧配送、校内收钱吧配送、校外第三方配送，需要判断是否支持分账
                    if (campusStoreInfo.getOutSideDeliveryType() == 1 || campusStoreInfo.getDeliveryType() == 1 || isUpayDeliveryType(deliverInfo.getDeliveryType())) {
                        if (!campusHelper.isSharingOpened(storeId)) {
                            deliveryInfoAndMsg.setCampusExceptionMessage(CampusHelper.STORE_NONE_SHARING);
                            log.info("userId={}的用户校园外卖下单未成功，原因:{}", ThreadLocalHelper.getUserId(), CampusHelper.STORE_NONE_SHARING);
                            return deliveryInfoAndMsg;
                        }
                    }
                    if (campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo() != null && StringUtils.isNotBlank(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getBuildingCode())) {
                        //楼栋编码，打印使用
                        if (deliverInfo.getExtra() == null) {
                            deliverInfo.setExtra(new HashMap<>());
                        }
                        deliverInfo.getExtra().put("building_code", campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getBuildingCode());
                    }

                    DeliverInfo tempInfo = new DeliverInfo();
                    tempInfo.setAppVersion(deliverInfo.getAppVersion());
                    tempInfo.setCampusDeliveryType(campusStoreInfo.getDeliveryType());
                    tempInfo.setCampusOutsideDeliveryType(campusStoreInfo.getOutSideDeliveryType());
                    tempInfo.setUserId(deliverInfo.getUserId());
                    tempInfo.setExtra(deliverInfo.getExtra());

                    if (Objects.nonNull(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo()) && StringUtils.isNotBlank(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getStationId())) {
                        // 校园配送经过站点
                        tempInfo.setStationId(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getStationId());
                        tempInfo.setLongitude(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getAddress().getLongitude());
                        tempInfo.setAddress(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getAddress().getDetailAddress());
                        tempInfo.setLatitude(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getAddress().getLatitude());
                        tempInfo.setUserName(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getContactsName());
                        tempInfo.setCellphone(campusAggregateDeliveryInfoDTO.getDeliveryPathContractsInfo().getContactsCellphone());
                    } else {
                        // 不经过站点
                        tempInfo.setLatitude(deliverInfo.getLatitude());
                        tempInfo.setLongitude(deliverInfo.getLongitude());
                        tempInfo.setAddress(deliverInfo.getAddress());
                        tempInfo.setUserName(deliverInfo.getUserName());
                        tempInfo.setCellphone(deliverInfo.getCellphone());
                    }
                    tempInfo.setGender(1);
                    if (Objects.nonNull(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getDeliveryFee())) {
                        tempInfo.setDeliveryFee(campusStoreInfo.getDeliveryType() == 1 || campusStoreInfo.getOutSideDeliveryType() == 1 ? campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getDeliveryFee().intValue() : 0);
                    }
                    // 校园商家分摊配送费
                    tempInfo.setDeliveryFeeAffordAmount(Optional.ofNullable(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getDeliveryFeeAffordAmount()).orElse(0L).intValue());
                    // 校园上楼费用
                    tempInfo.setFloorFee(Optional.ofNullable(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getUpstairsFee()).orElse(0L).intValue());
                    tempInfo.setPriceExtraFee(Optional.ofNullable(campusAggregateDeliveryInfoDTO.getDeliveryFeeInfo().getPriceExtraFee()).orElse(0L).intValue());
                    deliveryInfoAndMsg.setDeliverInfo(tempInfo);
                    return deliveryInfoAndMsg;
                } else {
                    String errorInfo = CampusHelper.STORE_NONE_STATION;
                    if (Objects.nonNull(campusAggregateDeliveryInfoDTO)) {
                        errorInfo = errorInfo + ":" + campusAggregateDeliveryInfoDTO.getDeliveryStatusInfo().getReason();
                    }
                    deliveryInfoAndMsg.setCampusExceptionMessage(errorInfo);
                    log.info("userId={}的用户校园外卖下单未成功，原因:{}", ThreadLocalHelper.getUserId(), errorInfo);
                }
            } else {
                deliveryInfoAndMsg.setCampusExceptionMessage(CampusHelper.STORE_NONE_CAMPUS);
                log.info("userId={}的用户校园外卖下单未成功，原因:{}", ThreadLocalHelper.getUserId(), CampusHelper.STORE_NONE_CAMPUS);
            }
        }
        return deliveryInfoAndMsg;
    }


    /**
     * 对外提供：获取配送费接口
     *
     * @param request
     * @return
     */
    public DeliveryInfoWrapper getDeliveryFee(DeliverFeeRequest request) {
        //先获取校园配送的相关信息
        DeliveryInfoAndMsg deliveryInfoAndMsg = getCampusOrderInfo(request.getDeliveryInfo(), request.getStoreId());
        DeliverInfo campusDeliveryInfo = deliveryInfoAndMsg.getDeliverInfo();
        LogUtils.logInfo("计算配送费", "campusDeliveryInfo", campusDeliveryInfo);

        if (Objects.isNull(request.getDeliveryInfo().getDistance())) {
            request.getDeliveryInfo().setDistance(0.0);
        }

        StoreDetailRequest storeDetailRequest = new StoreDetailRequest();
        storeDetailRequest.setStoreId(request.getStoreId());
        StoreDetailResponse store = customerStoreRemoteService.storeDetailInfo(storeDetailRequest);
        if (store == null) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "查询商户信息发生异常");
        }

        Map<String, Integer> deliveryFeeMap = new HashMap<>();
        deliveryFeeMap.put(Constants.DeliveryFeeType.TOTAL, 0); // 总额
        deliveryFeeMap.put(Constants.DeliveryFeeType.CAMPUS, 0); // 校园配送费总额
        deliveryFeeMap.put(Constants.DeliveryFeeType.THIRD, 0); // 第三方配送
        deliveryFeeMap.put(Constants.DeliveryFeeType.SELF, 0); // 自配送
        deliveryFeeMap.put(Constants.DeliveryFeeType.SHARE, 0); // 校园分摊
        deliveryFeeMap.put(Constants.DeliveryFeeType.USER, 0); // 用户配送费
        deliveryFeeMap.put(Constants.DeliveryFeeType.REDUCTION, 0); // 减免配送费
        deliveryFeeMap.put(Constants.DeliveryFeeType.FLOOR, 0); // 楼层费
        deliveryFeeMap.put(Constants.DeliveryFeeType.PRICE_EXTRA, 0); // 大额附加费


        redisTemplate.delete(getDeliveryInfoKey(request.getStoreId()));

        DeliverInfo deliveryInfo = request.getDeliveryInfo();
        // 校园配送方式 1-校内收钱吧 2-校内自配送 0-不配送
        int campusDeliveryType = Objects.nonNull(campusDeliveryInfo) ? campusDeliveryInfo.getCampusDeliveryType() : Constants.CampusDeliveryType.NONE;
        // 校验门店可送区域或校园
        deliveryAreaHelper.checkDeliveryInfo(request.getStoreId(), deliveryInfo);

        FindConfigByNameRequest configByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, request.getStoreId(), Constants.DELIVERY_TYPE);
        String deliveryType = Optional.ofNullable(configRemoteService.findByName(configByNameRequest)).map(ConfigResponse::getValue).orElse("1");
        if (campusDeliveryType == Constants.CampusDeliveryType.SELF) {
            // 如果校园自配送，则配送方式改为自配送
            deliveryType = "2";
        }
        deliveryInfo.setDeliveryType(deliveryType);
        // 校园配送费
        int campusFee = 0;
        // 校园商家承担配送费
        int campusShareFee = 0;
        // 楼层费
        int floorFee = 0;
        // 大额附加费
        int priceExtraFee = 0;
        Location originalLocation = new Location(deliveryInfo.getLatitude(), deliveryInfo.getLongitude());
        Double originalDistance = deliveryInfo.getDistance();

        // campusDeliveryType:  1-校内收钱吧 2-校内自配送
        if (campusDeliveryType == Constants.CampusDeliveryType.SQB) {
            // 校内收钱吧时，需要将校园收货地址改成校内地址（该地址可能为原始地址，也可能为配送站地址）
            // 拿到最新的地址时，此时的配送距离需要重新计算
            deliveryInfo.setLatitude(campusDeliveryInfo.getLatitude());
            deliveryInfo.setLongitude(campusDeliveryInfo.getLongitude());
            Location location = store.getLocation();
            int distance = LocationUtil.getDistance(Double.parseDouble(location.getLat()), Double.parseDouble(location.getLon()),
                    Double.parseDouble(campusDeliveryInfo.getLatitude()), Double.parseDouble(campusDeliveryInfo.getLongitude()));
            deliveryInfo.setDistance((double) distance);
            // 校园实际配送费需要减去商家分摊部分
            campusFee = Optional.ofNullable(campusDeliveryInfo.getDeliveryFee()).orElse(0);
            campusShareFee = Optional.ofNullable(campusDeliveryInfo.getDeliveryFeeAffordAmount()).orElse(0);
            floorFee = Optional.ofNullable(campusDeliveryInfo.getFloorFee()).orElse(0);
            priceExtraFee = Optional.ofNullable(campusDeliveryInfo.getPriceExtraFee()).orElse(0);
            // 校园配送费总额
            deliveryFeeMap.put(Constants.DeliveryFeeType.CAMPUS, campusFee);
            // 商家分摊费用
            deliveryFeeMap.put(Constants.DeliveryFeeType.SHARE, campusShareFee);
            deliveryFeeMap.put(Constants.DeliveryFeeType.FLOOR, floorFee);
            deliveryFeeMap.put(Constants.DeliveryFeeType.PRICE_EXTRA, priceExtraFee);
        }
        int deliverFee = 0;
        deliveryInfo.setIsConvert(false);

        Order order = Order.builder().compatible(request.isCompatible()).build();
        deliveryInfo.setBuildingNumber(deliveryInfo.getBuildingNumber() == null ? "" : deliveryInfo.getBuildingNumber());
        deliveryInfo.setHouseNumber(deliveryInfo.getBuildingNumber() + deliveryInfo.getHouseNumber());

        if (campusDeliveryType == Constants.CampusDeliveryType.SQB) {
            // 收钱吧配送
            deliverFee = campusFee - campusShareFee;
            // 使用全程收钱吧，则不记录自配送和第三方配送的信息
            deliveryFeeMap.put(Constants.DeliveryFeeType.SELF, 0);
            deliveryFeeMap.put(Constants.DeliveryFeeType.THIRD, 0);
        } else {
            // 非收钱吧配送，需要根据门店配置计算配送费
            if ("2".equals(deliveryType)) {
                deliverFee = getSelfDeliverFee(request, store, deliveryInfo);
                // 自配送费用
                deliveryFeeMap.put(Constants.DeliveryFeeType.SELF, deliverFee);
            } else if ("3".equals(deliveryType)) {
                long amount = computeCartAmount(order, request.getStoreId(), OrderType.TAKE_OUT_ORDER);

                OrderDeliverFeeRequest orderDeliverFeeRequest = new OrderDeliverFeeRequest();
                orderDeliverFeeRequest.setStoreId(request.getStoreId());
                orderDeliverFeeRequest.setReceiverAddress(deliveryInfo.getAddress() + deliveryInfo.getHouseNumber());
                orderDeliverFeeRequest.setReceiverLat(Double.valueOf(deliveryInfo.getLatitude()));
                orderDeliverFeeRequest.setReceiverLng(Double.valueOf(deliveryInfo.getLongitude()));
                orderDeliverFeeRequest.setReceiverName(deliveryInfo.getUserName());
                orderDeliverFeeRequest.setReceiverPhone(deliveryInfo.getCellphone());
                orderDeliverFeeRequest.setCargoPrice(amount * 1.0 / 100);

                DadaDeliverFeeResponse deliveryFeeResponse = payService.getDeliveryFee(orderDeliverFeeRequest);
                deliverFee = (int) Math.ceil(deliveryFeeResponse.getDeliverFee() * 100);
            } else if (isUpayDeliveryType(deliveryType)) {
                long amount = computeCartAmount(order, request.getStoreId(), OrderType.TAKE_OUT_ORDER);
                UpayDeliverFeeRequest upayDeliverFeeRequest = new UpayDeliverFeeRequest();
                upayDeliverFeeRequest.setStore_client_sn(store.getStoreSn());
                upayDeliverFeeRequest.setOrder_total_amount(amount);
                upayDeliverFeeRequest.setReceiver_latitude(deliveryInfo.getLatitude());
                upayDeliverFeeRequest.setReceiver_longitude(deliveryInfo.getLongitude());
                upayDeliverFeeRequest.setProvider(Integer.parseInt(UpayDeliveryType.getByBizProvider(deliveryType).getProvider()));
                upayDeliverFeeRequest.setDeliver_address(deliveryInfo.getAddress() + deliveryInfo.getHouseNumber());

                String code = null; // 聚合配送返回的code
                String msg = null; // 聚合配送返回的msg
                try {
                    List<UpayDeliverFee> upayDeliveryFeeList = payService.getUpayDeliveryFee(upayDeliverFeeRequest);
                    String provider = UpayDeliveryType.getByBizProvider(deliveryType).getProvider();

                    if (!CollectionUtils.isEmpty(upayDeliveryFeeList)) {
                        UpayDeliverFee upayDeliverFee = upayDeliveryFeeList.get(0);
                        if (provider.equalsIgnoreCase(upayDeliverFee.getProvider())) {
                            if ("ok".equalsIgnoreCase(upayDeliverFee.getCode())) {
                                deliverFee = Math.toIntExact(upayDeliverFee.getPrice());
                                deliveryFeeMap.put(Constants.DeliveryFeeType.THIRD, deliverFee);
                            } else {
                                msg = upayDeliverFee.getMsg();
                                code = "fail".equalsIgnoreCase(upayDeliverFee.getCode())
                                        ? upayDeliverFee.getCode()
                                        : UpayDeliveryType.getByBizProvider(deliveryType).name() + upayDeliverFee.getCode();
                                throw new BusinessException(ReturnCode.BUSINESS_ERROR, upayDeliverFee.getMsg());
                            }
                        } else {
                            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "没有找到匹配的provider");
                        }
                    }
//                UpayDeliverFee upayDeliverFee = upayDeliveryFeeList.stream()
//                        .filter(fee -> fee.getProvider().equals(provider))
//                        .findFirst()
//                        .orElseThrow(() -> new BusinessException(ReturnCode.BUSINESS_ERROR, "没有找到匹配的provider"));
//                deliverFee = Math.toIntExact(upayDeliverFee.getPrice());
                } catch (Exception e) {
                    String canSwitch = Optional.ofNullable(configRemoteService.findByName(
                                    MccUtils.findConfigByNameRequest(OwnerType.STORE_ID,
                                            request.getStoreId(), Constants.SWITCH_SELF_DELIVERY)))
                            .map(ConfigResponse::getValue)
                            .orElse("true");
                    if ("true".equals(canSwitch)) {
                        log.warn("聚合配送费用获取失败切换自配送 storeId = {}", request.getStoreId(), e);
                        deliveryInfo.setIsConvert(true);
                        deliveryInfo.setDeliveryErrorMsg(msg);
                        deliveryInfo.setDeliveryErrorCode(code);
                        deliverFee = getSelfDeliverFee(request, store, deliveryInfo);
                        deliveryType = "2";
                    } else {
                        String errorTips = StringUtils.isNotBlank(code) ? String.format("[%s]", code) : "";
                        throw new BusinessException(ReturnCode.BUSINESS_ERROR, "该地址不在商家配送范围，请重新选择地址" + errorTips);
                    }
                }
            }
        }


        // 用户配送费
        deliveryFeeMap.put(Constants.DeliveryFeeType.USER, deliverFee);
        deliveryFeeMap.put(Constants.DeliveryFeeType.TOTAL, deliverFee + campusShareFee);
        deliveryInfo.setDeliveryFee(deliverFee);
        deliveryInfo.setDeliveryType(deliveryType);

        if (deliverFee > 0) {
            // 有配送费
            // 查询门店是否开启配送费减免促销
            // 查询门店的减免配送费促销
            DiscountInfo discountInfo = null;
            try {
                discountInfo = deliveryFeeReductionActivityService.getDeliveryFeeReduction(new DiscountInfo() {{
                    setStoreId(request.getStoreId());
                }});
            } catch (Exception ex) {
                log.warn("getDeliveryFeeReduction error", keyValue("method", "getDeliveryFeeReduction"), keyValue("arguments", request.getStoreId()), ex);
            }
            if (discountInfo != null && discountInfo.getDiscount() > 0) {
                // 有减免促销，计算优惠金额
                int redeemAmount = deliverFee - discountInfo.getDiscount() >= 0 ? discountInfo.getDiscount() : deliverFee;
                deliveryInfo.setReductionAmount(redeemAmount);
            }

        } else {
            deliveryInfo.setReductionAmount(0);
        }

        // 配送费减免

        deliveryFeeMap.put(Constants.DeliveryFeeType.REDUCTION, deliveryInfo.getReductionAmount());
        deliveryInfo.setDeliveryFeeMap(deliveryFeeMap);
        if (Objects.nonNull(campusDeliveryInfo)) {
            deliveryInfo.setLatitude(originalLocation.getLat());
            deliveryInfo.setLongitude(originalLocation.getLon());
            deliveryInfo.setDistance(originalDistance);
            deliveryInfo.setPreCreateDeliveryId(campusDeliveryInfo.getPreCreateDeliveryId());

        }
        DeliveryInfoWrapper deliveryInfoWrapper = new DeliveryInfoWrapper(request.getDeliveryInfo(), deliveryInfoAndMsg);
        String deliveryInfoKey = getDeliveryInfoKey(request.getStoreId());
        redisTemplate.boundValueOps(deliveryInfoKey).set(deliveryInfoWrapper);
        redisTemplate.expire(deliveryInfoKey, 3, TimeUnit.MINUTES);
        LogUtils.logInfo("缓存配送信息", "saveDeliveryInfoCache", deliveryInfoWrapper);
        return deliveryInfoWrapper;
    }

    private int getSelfDeliverFee(DeliverFeeRequest request, StoreDetailResponse store, DeliverInfo deliveryInfo) {
        // 判断地址类型,计算配送费
        Integer addressId = deliveryInfo.getAddressId();
        if (Objects.nonNull(addressId) && addressId > 0) {
            DeliveryAddressQueryReq deliveryAddressQueryReq = new DeliveryAddressQueryReq();
            deliveryAddressQueryReq.setIds(Arrays.asList(addressId));
            List<DeliveryAddressDto> deliveryAddressDtoList = deliveryAddressRemoteService.queryByIds(deliveryAddressQueryReq);
            if (CollectionUtil.isNotEmpty(deliveryAddressDtoList)) {
                return deliveryAddressDtoList.get(0).getDeliveryFee();
            }
            return 0;
        }

        FindConfigByNameRequest configByNameRequest;
        ConfigResponse response;
        int deliverFee;
        configByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, request.getStoreId(), Constants.DELIVERY_CHARGE_TYPE);
        response = configRemoteService.findByName(configByNameRequest);
        // 固定金额
        if (response == null || "1".equals(response.getValue())) {
            configByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, request.getStoreId(), Constants.DELIVERY_FEE);
            response = configRemoteService.findByName(configByNameRequest);
            deliverFee = response == null ? 0 : Integer.parseInt(response.getValue());
        } else {
            List<TieredPrice> tieredPrices = Optional.ofNullable(configRemoteService.findByName(
                            MccUtils.findConfigByNameRequest(OwnerType.STORE_ID,
                                    request.getStoreId(), Constants.DELIVERY_TIERED_PRICING)))
                    .map(ConfigResponse::getValue)
                    .map(it -> JacksonUtil.toBean(it, new TypeReference<List<TieredPrice>>() {
                    }))
                    .orElse(DEFAULT_TIERED_PRICING);

            deliverFee = computeDeliverFee(tieredPrices, Optional.ofNullable(deliveryInfo.getDistance()).orElseGet(() -> {
                Location location = store.getLocation();
                int distance = LocationUtil.getDistance(Double.parseDouble(location.getLat()), Double.parseDouble(location.getLon()),
                        Double.parseDouble(deliveryInfo.getLatitude()), Double.parseDouble(deliveryInfo.getLongitude()));
                return (double) distance * 1000;
            }));
        }
        return deliverFee;
    }

    public OrderType getOrderType(String type) {
        for (OrderType orderType : OrderType.values()) {
            if (orderType.getMsg().equals(type)) {
                return orderType;
            }
        }
        return OrderType.SUBSCRIBE_ORDER;
    }


    public List<CartItemCreate> getOpenTableMustOrderItems(String storeId, String tableId, String userName, String userIcon, Integer peopleNum) {


        BooleanConfigQueryRequest muserOrderEnableConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.MUST_ORDER_EDITABLE_CONFIG_KEY, false);


        boolean muserOrderEditable = configRemoteService.getBooleanConfig(muserOrderEnableConfigQueryRequest);

        //开台成功，添加、更新开台必点商品
        FindOpenTableMustOrderRequest request = new FindOpenTableMustOrderRequest();
        request.setBizStoreId(storeId);
        //默认传入扫码点单，围餐都是扫码点单
        request.setServiceType(2);
        ListResult<OpenTableMustOrderItemDTO> openItemListResult = openTableMustOrderService.findDetails(request);
        if (CollectionUtils.isEmpty(openItemListResult.getRecords())) {
            return Collections.emptyList();
        }

        List<OpenTableMustOrderItemDTO> openTableMustOrderItemDTOList = openItemListResult.getRecords();

        List<SkuInfo> singleActivitySkuInfoList = itemHelper.getSingActivitySkuInfos(storeId, 2);
        List<SkuInfo> secondActivitySkuInfoList = itemHelper.getSecondActivitySkuInfos(storeId, 2);
        Map<String, List<CategoryInfo>> categoryActivity = itemHelper.categoryDiscountDetails(storeId, 2, EffectiveType.YES);

        Map<String, Integer> spuCountMap = new HashMap<>();
        List<CartItemCreate> itemCreates = openTableMustOrderItemDTOList.stream()
                .map(itemDto -> {
                    ItemDetailVO itemDetailVO = itemHelper.processItemDetail(itemDto.getItemDto(), singleActivitySkuInfoList, secondActivitySkuInfoList, categoryActivity, null, Constants.ServiceType.SCANNING);

                    CartItemCreate cartItemCreate = new CartItemCreate();
                    cartItemCreate.setOpenTableMustOrder(true);
                    cartItemCreate.setOpenTableItemEditable(muserOrderEditable);
                    cartItemCreate.setPeopleNum(peopleNum);
                    cartItemCreate.setStoreId(storeId);
                    cartItemCreate.setTableId(tableId);
                    cartItemCreate.setServiceType(2);
                    cartItemCreate.setUserName(userName);
                    cartItemCreate.setUserIcon(userIcon);

                    CartItemCreate.Item item = new CartItemCreate.Item();

                    BeanUtils.copyProperties(itemDetailVO.getItem(), item);

                    if (itemDto.getChargeMethod() == ChargeMethodEnum.TABLE) {
                        item.setNumber(itemDto.getMustOrderNum());
                    } else {
                        item.setNumber(itemDto.getMustOrderNum() * peopleNum);
                    }

                    if (itemDto.getItemDto().getItem().getMinSaleNum() != null) {
                        if (item.getNumber() < itemDto.getItemDto().getItem().getMinSaleNum()) {
                            //小于最低可售数量,那么设置为起售份数
                            item.setNumber(itemDto.getItemDto().getItem().getMinSaleNum());
                        }
                    }

                    cartItemCreate.setItem(item);

                    if (itemDetailVO.getSpecs() != null) {

                        SpecOptionVO specOptionVO = itemDetailVO.getSpecs().getOptions().get(0);

                        CartItemCreate.Spec spec = new CartItemCreate.Spec();
                        spec.setName(specOptionVO.getName());
                        spec.setId(specOptionVO.getId());
                        spec.setPrice(specOptionVO.getPrice());
                        cartItemCreate.setSpec(spec);
                    }

                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemDetailVO.getAttributes())) {
                        cartItemCreate.setAttributes(itemDetailVO.getAttributes().stream()
                                .map(attributeDto -> {
                                    CartItemCreate.Attribute attribute = new CartItemCreate.Attribute();
                                    attribute.setName(attributeDto.getOptions().get(0).getName());
                                    attribute.setId(attributeDto.getOptions().get(0).getId());
                                    return attribute;
                                }).collect(Collectors.toList())
                        );
                    }
                    cartItemCreate.setCtime(System.currentTimeMillis());

                    //重新去拿下商品详情，因为开台必点返回的信息是处理过的
                    ItemDto dbItemDto = itemHelper.getItemDtoById(storeId, item.getId(), 2);

                    Result<Boolean> isItemSalebleInMiniProgramResult = itemHelper.isItemSalebleInMiniProgram(dbItemDto, cartItemCreate, 2, spuCountMap);

                    if (!isItemSalebleInMiniProgramResult.isSuccess()) {
                        return null;
                    }

                    EntityConvert.generateAttachInfo(cartItemCreate);

                    CartHelper.addSpuCount(spuCountMap, cartItemCreate.getItem().getId(), cartItemCreate.getItem().getNumber());

                    return cartItemCreate;

                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        itemCreates.stream().forEach(itemCreate -> {
            if (org.springframework.util.StringUtils.isEmpty(itemCreate.getId()) && org.springframework.util.StringUtils.isEmpty(itemCreate.getItemUid())) {
                itemCreate = cartService.checkAndFillItemInfo(itemCreate);
            }

        });

        return itemCreates;
    }

    private StoreInfo makeStoreInfo(String storeId, OrderType orderType, String terminalId, String terminalSn) {

        //判断是不是收银机模式，并检查收银机是否在线
        BooleanConfigQueryRequest booleanConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.CASHIER_MODE_CONFIG_KEY, false);
        boolean isCashierMode = configRemoteService.getBooleanConfig(booleanConfigQueryRequest);

        StoreDetailRequest storeDetailRequest = new StoreDetailRequest();
        storeDetailRequest.setStoreId(storeId);
        StoreDetailResponse storeDetailResponse = customerStoreRemoteService.storeDetailInfo(storeDetailRequest);


        if (orderType == OrderType.TAKE_OUT_ORDER) {
            ConfigResponse takeOutBusiness = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.TAKEOUT_BUSINESS_STATUS));
            if (Optional.ofNullable(takeOutBusiness).map(ConfigResponse::getValue).orElse("0").equals("0")) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "休息中，目前暂时无法下单");
            }
//            ConfigResponse deliveryTimes = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TIMES));
//            Optional.ofNullable(deliveryTimes)
//                    .map(it -> deliveryTimes.getValue())
//                    .map(it -> JacksonUtil.toBean(deliveryTimes.getValue(), new TypeReference<List<Map>>() {
//                    }))
//                    .ifPresent(times -> {
//                        if (times.size() > 0 && times.stream().noneMatch(it -> isInDate(
//                                BeanUtil.getPropString(it, "startTime"),
//                                BeanUtil.getPropString(it, "endTime")))
//                        ) {
//                            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "非外卖营业时间段不能下单~");
//                        }
//                    });
        } else if (orderType == OrderType.PRE_ORDER) {
            ConfigResponse takeOutBusiness = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.TAKEOUT_BUSINESS_STATUS));
            ConfigResponse presetBusiness = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.PRESET_BUSINESS_STATUS));
            if (Optional.ofNullable(takeOutBusiness).map(ConfigResponse::getValue).orElse("0").equals("0") || Optional.ofNullable(presetBusiness).map(ConfigResponse::getValue).orElse("1").equals("0")) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "休息中，目前暂时无法下单");
            }
        } else {
            ConfigResponse businessStatus = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.BUSINESS_STATUS));
            if (Optional.ofNullable(businessStatus).map(ConfigResponse::getValue).orElse("0").equals("0")) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "休息中，目前暂时无法下单");
            }
            ConfigResponse businessTimes = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.BUSINESS_TIMES));
            Optional.ofNullable(businessTimes)
                    .map(it -> businessTimes.getValue())
                    .map(it -> JacksonUtil.toBean(businessTimes.getValue(), new TypeReference<List<Map>>() {
                    }))
                    .ifPresent(times -> {
                        if (times.size() > 0 && times.stream().noneMatch(it -> TimeSplit.isInDate(
                                BeanUtil.getPropString(it, "startTime"),
                                BeanUtil.getPropString(it, "endTime")))
                        ) {
                            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "非营业时间段不能下单~");
                        }
                    });

            if (isCashierMode) {
                //检查主收银是否在线
                MainCashierBaseReq checkReq = new MainCashierBaseReq();
                checkReq.setMerchantId(storeDetailResponse.getMerchantId());
                checkReq.setStoreId(storeId);
                MainCashierCheckOnlineVO checkOnlineVO;
                checkOnlineVO = mainCashierRegisterService.checkMainCashierOnline(checkReq);
                if (!checkOnlineVO.getOnline()) {
                    throw new BusinessException(ReturnCode.BUSINESS_ERROR, "主收银机不在线，请联系店家进行下单哦~");
                }

            }
        }


        StoreInfo storeInfo = new StoreInfo();
        storeInfo.setCahiserMode(isCashierMode);
        storeInfo.setStoreId(storeId);
        storeInfo.setStoreName(storeDetailResponse.getStoreName());
        storeInfo.setMerchantId(storeDetailResponse.getMerchantId());
//        Map tradeTerminal = terminalService.getTerminalByDeviceFingerprint("jjz" + storeId);
        storeInfo.setTerminalSn(terminalSn);
//        storeInfo.setTerminalId(BeanUtil.getPropString(tradeTerminal, DaoConstants.ID));
//        storeInfo.setTerminalKey(BeanUtil.getPropString(tradeTerminal, Terminal.CURRENT_SECRET));
        storeInfo.setQrCodeId("");
        storeInfo.setQrCodeName("");

        if (StringUtils.isEmpty(terminalId)) {
            return storeInfo;
        }

        String qrCodeName;
        Integer qrCodeType;
        if (terminalId.matches(OLD_QR_CODE_PATTERN)) {
            OrderQrCode orderQrCodeByQrCode = orderQrCodeService.getOrderQrCodeByQrCode(terminalId);
            qrCodeName = Optional.ofNullable(orderQrCodeByQrCode).map(OrderQrCode::getName).orElse(null);
            qrCodeType = Optional.ofNullable(orderQrCodeByQrCode).map(OrderQrCode::getType).orElse(0);
            if (StringUtils.isNotEmpty(qrCodeName) && (OrderQrCodeType.TABLE_STICKER == qrCodeType || OrderQrCodeType.TABLE_CARD == qrCodeType)) {
                qrCodeType = 45;
            }
        } else if (terminalId.startsWith("store_set")) {
            String qrPrintCode = terminalId.replace("store_set", "");
            StoreSetSingleQrcode qrcodeByPrintCode = storeSetService.findQrcodeByPrintCode(qrPrintCode);
            qrCodeType = 45;
            qrCodeName = Optional.ofNullable(qrcodeByPrintCode).map(StoreSetSingleQrcode::getQrcodeName).orElse(null);
        } else {
            Map terminal = terminalService.getTerminalByTerminalId(terminalId);
            qrCodeName = BeanUtil.getPropString(terminal, Terminal.NAME);
            qrCodeType = BeanUtil.getPropInt(terminal, Terminal.TYPE);
        }
        storeInfo.setQrCodeId(terminalId);
        storeInfo.setQrCodeName(qrCodeName == null ? "" : qrCodeName);
        storeInfo.setQrCodeType(qrCodeType);
        return storeInfo;
    }


    public PayParamWrapper makePayParamWrapper(PayRequest request) {
        PayParamWrapper payParamWrapper = new PayParamWrapper();
        payParamWrapper.setCashierBizParams(request.getCashierBizParams());
        payParamWrapper.setRechargeAmount(request.getRechargeAmount());
        payParamWrapper.setRechargeAndPay(request.isRechargeAndPay());
        payParamWrapper.setStoredScene(request.getStoredScene());
        payParamWrapper.setRechargeRuleId(request.getRechargeRuleId());

        payParamWrapper.setRedeemDigest(request.getRedeemDigest());
        payParamWrapper.setTotalDiscount(request.getTotalDiscount());

        payParamWrapper.setAcquiring(request.getAcquiring());

        payParamWrapper.setSqbPaySource(request.getSqbPaySource());

        payParamWrapper.setPayHeaders(request.getPayHeaders());
        payParamWrapper.setOperator(request.getOperator());

        return payParamWrapper;
    }

    public RedeemParamWrapper buildRedeemParamWrapper(PayRequest request) {
        RedeemParamWrapper redeemParamWrapper = new RedeemParamWrapper();
        redeemParamWrapper.setMkCustomInfo(request.getMkCustomInfo());
        return redeemParamWrapper;
    }

    private Order makeOrder(PayRequest request, OrderType orderType, StoreInfo storeInfo) {
        Order order = Order.builder()
                .type(orderType.getMsg())
                .subject("扫码点餐")
                .payway(request.getPayWay() != null ? request.getPayWay() : Integer.valueOf(ThreadLocalHelper.getMiniProgramType().getCode()))
                .subPayway(4)
                .buyerUid(ThreadLocalHelper.getUserId())
                .buyerLogin(findUserNickName())
                .merchantId(storeInfo.getMerchantId())
                .storeId(storeInfo.getStoreId())
                .terminalSn(storeInfo.getTerminalSn())
                .terminalId(storeInfo.getTerminalId())
                .qrCode(storeInfo.getQrCodeId())
                .qrCodeName(storeInfo.getQrCodeName())
                .qrCodeType(storeInfo.getQrCodeType())
                .packed(request.getPacked())
                .packAmount(request.getPackAmount())
                .remark(request.getRemark())
                .tableId(request.getTableId())
                .tableNo(request.getTableName())
                .areaId(request.getAreaId())
                .wxGoods(request.getWxGoods())
                .build();


        if (request.isRechargeAndPay()) {
            order.setPayway(Integer.valueOf(ThreadLocalHelper.getMiniProgramType().getCode()));
        }

        if (orderType == OrderType.TAKE_OUT_ORDER) {
            order.setTableId(null);
            order.setTableNo(null);
        }


        Order.OrderExtra orderExtra = new Order.OrderExtra();
        orderExtra.setStoreName(storeInfo.getStoreName());
        orderExtra.setHbFq(request.getHbFq());

        if (OrderType.TAKE_OUT_ORDER == orderType) {
            if (request.getDeliveryInfo() == null) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "外卖订单收货信息必填");
            }
            if (StringUtils.isEmpty(request.getDeliveryInfo().getAddress())) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "外卖订单收货地址必填");
            }
            if (Objects.isNull(request.getDeliveryInfo().getDistance())) {
                request.getDeliveryInfo().setDistance(0.0);
            }
            // 校验可送区域和校园
            deliveryAreaHelper.checkDeliveryInfo(request.getStoreId(), request.getDeliveryInfo());

            // 2024年01月04日 去掉后端校验，全部交由收银台处理
            // 储值卡支付时判断是否为商家自配送，若不是则不允许下单
            if (apolloConfigHelper.getBooleanConfigValueByKey("prepayCheckCardPay", false)) {
                FindConfigByNameRequest configByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, request.getStoreId(), Constants.DELIVERY_TYPE);
                String deliveryType = Optional.ofNullable(configRemoteService.findByName(configByNameRequest)).map(ConfigResponse::getValue).orElse("1");
                if (!"2".equalsIgnoreCase(deliveryType)) {
                    order.setAllowCardPay(false);
                    if (PayWay.GIFT_CARD.getCode().equals(request.getPayWay())) {
                        throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商家使用第三方配送服务，此类订单暂不支持储值卡支付，请使用其他支付方式");
                    }
                }
            }

            request.setDeliveryInfo(campusHelper.fillCampusInfo(request.getStoreId(), request.getDeliveryInfo()));
            // 缓存中的配送信息对象
            DeliverInfo deliverInfo = new DeliverInfo();
            // 校内信息
            DeliveryInfoAndMsg deliveryInfoAndMsg = new DeliveryInfoAndMsg();
            //apollo配置，查询配送费是否走缓存
            DeliveryInfoWrapper deliveryInfoWrapper = null;
            if (apolloConfigHelper.getBooleanConfigValueByKey("useDeliveryFeeCache", true)) {
                deliveryInfoWrapper = Optional.ofNullable(redisTemplate.boundValueOps(getDeliveryInfoKey(storeInfo.getStoreId())).get()).map(DeliveryInfoWrapper.class::cast).orElse(null);
                if (Objects.nonNull(deliveryInfoWrapper)) {
                    if (Objects.nonNull(deliveryInfoWrapper.getDeliverInfo())) {
                        LogUtils.logInfo("组装订单使用缓存配送信息", "useDeliveryInfoCache", deliveryInfoWrapper);
                        // 将请求中的地址更换为缓存保存的
                        request.setDeliveryInfo(deliveryInfoWrapper.getDeliverInfo());
                    }
                }
            }
            if (Objects.isNull(deliveryInfoWrapper)) {
                // 缓存中没有，则进行实时查询
                DeliverFeeRequest deliverFeeRequest = new DeliverFeeRequest();
                deliverFeeRequest.setDeliveryInfo(request.getDeliveryInfo());
                deliverFeeRequest.setStoreId(storeInfo.getStoreId());
                deliverFeeRequest.setCompatible(request.isCompatible());
                deliveryInfoWrapper = getDeliveryFee(deliverFeeRequest);
            }
            deliverInfo = deliveryInfoWrapper.getDeliverInfo();
            deliveryInfoAndMsg = deliveryInfoWrapper.getCampusDeliveryInfoAndMsg();

            // 获取校内配送信息
            DeliverInfo campusDeliveryInfo = deliveryInfoAndMsg.getDeliverInfo();
            if (Objects.nonNull(campusDeliveryInfo)) {
                // 是校园配送订单
                order.setCampusOrder(true);
                if (StringUtils.isNotBlank(campusDeliveryInfo.getStationId())) {
                    // 配送站信息
                    OrderCampusStationDTO campusStation = new OrderCampusStationDTO();
                    campusStation.setUserId(request.getDeliveryInfo().getUserId());
                    campusStation.setStationId(campusDeliveryInfo.getStationId());
                    campusStation.setName(campusDeliveryInfo.getUserName());
                    campusStation.setGender(campusDeliveryInfo.getGender());
                    campusStation.setCellphone(campusDeliveryInfo.getCellphone());
                    campusStation.setAddress(campusDeliveryInfo.getAddress());
                    campusStation.setLatitude(campusDeliveryInfo.getLatitude());
                    campusStation.setLongitude(campusDeliveryInfo.getLongitude());
                    campusStation.setCampusId(request.getDeliveryInfo().getCampusId());
                    orderExtra.setCampusStation(campusStation);
                }
                // 校内配送信息
                OrderCampusDeliveryDTO campusDeliver = new OrderCampusDeliveryDTO();
                campusDeliver.setStationId(campusDeliveryInfo.getStationId());
                campusDeliver.setFee(campusDeliveryInfo.getDeliveryFee().longValue());
                campusDeliver.setStatusCode(0);
                campusDeliver.setType(campusDeliveryInfo.getCampusDeliveryType());
                orderExtra.setCampusDelivery(campusDeliver);
                if (apolloConfigHelper.getBooleanConfigValueByKey("prepayCheckCardPay", false)) {
                    if (campusDeliveryInfo.getCampusDeliveryType() == 1) {
                        order.setAllowCardPay(false);
                        if (PayWay.GIFT_CARD.getCode().equals(request.getPayWay()) || PayWay.CARD.getCode().equals(request.getPayWay())) {
                            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商家使用第三方配送服务，此类订单暂不支持储值卡支付，请使用其他支付方式");
                        }
                    }
                }
            } else {
                //下单请求为校园外卖，但因为各种原因商户不支持
                order.setCampusException(deliveryInfoAndMsg.getCampusExceptionMessage());
            }

            if (Objects.nonNull(campusDeliveryInfo) && campusDeliveryInfo.getCampusDeliveryType() == Constants.CampusDeliveryType.SQB) {
                deliverInfo.setDeliveryType("99"); // 收钱吧配送
            }
            deliverInfo.setDistance(request.getDeliveryInfo().getDistance());
            deliverInfo.setAddressCode(request.getDeliveryInfo().getAddressCode());
            deliverInfo.setAddressName(request.getDeliveryInfo().getAddressName());
            deliverInfo.setExtra(request.getDeliveryInfo().getExtra());
            //预订单校验
            deliverInfo.setPresetTime(request.getDeliveryInfo().getPresetTime());
            BookOrderInfoDTO bookOrderInfoDTO = makeBookOrderInfo(deliverInfo, request.getStoreId(), orderType, storeInfo.getMerchantId());
            if (bookOrderInfoDTO != null) {
                order.setBookOrder(true);
                orderExtra.setBookOrderInfoDTO(bookOrderInfoDTO);
                deliverInfo.setPresetTime(bookOrderInfoDTO.getBookTime());
            } else {
                deliverInfo.setPresetTime(null);
            }
            orderExtra.setDeliveryInfo(deliverInfo);
            //保存在Address.extra中
            if (deliverInfo.getExtra() == null) {
                deliverInfo.setExtra(new HashMap<>());
            }
            //校园外卖设置预计送达时间ETA
            if (order.isCampusOrder()) {
                if (deliverInfo.getDeliveryType().equals("99")) {
                    Map<String, Object> etaMap = new HashMap<>();
                    if (order.isBookOrder()) {
                        //预订单，使用新用户选择的预定时间
                        etaMap.put("start", deliverInfo.getPresetTime());
                        etaMap.put("end", deliverInfo.getPresetTime());
                    } else {
                        //即时单，请求配送方计算
                        OrderFulfillmentCycleTimeDTO etaData = campusHelper.queryCampusETA(request.getStoreId(), request.getDeliveryInfo());
                        if (etaData != null) {
                            etaMap.put("start", etaData.getStartTime());
                            etaMap.put("end", etaData.getEndTime());
                            // 校园预计送达时间：单位分钟
                            etaMap.put("estimateDeliveryTime", etaData.getEstimateDeliveryTime());
                        }
                    }
                    deliverInfo.getExtra().put("estimate_arrival_time", etaMap);
                }
                deliverInfo.getExtra().put("preCreateDeliveryId", deliverInfo.getPreCreateDeliveryId());
            }

        } else if (OrderType.PRE_ORDER == orderType) {
            //预订单校验
            BookOrderInfoDTO bookOrderInfoDTO = makeBookOrderInfo(request.getDeliveryInfo(), request.getStoreId(), orderType, storeInfo.getMerchantId());
            if (bookOrderInfoDTO != null) {
                order.setBookOrder(true);
                orderExtra.setBookOrderInfoDTO(bookOrderInfoDTO);
                request.getDeliveryInfo().setPresetTime(bookOrderInfoDTO.getBookTime());
            } else {
                request.getDeliveryInfo().setPresetTime(null);
            }
            orderExtra.setDeliveryInfo(request.getDeliveryInfo());
        }

        if (OrderType.PRE_ORDER == orderType || OrderType.TAKE_OUT_ORDER == orderType) {
            //自取和外卖，下单那一刻商家在校园，就属于校园的订单。
            Map<Object, Object> extraMap = order.getExtraInfo();
            if (extraMap == null) {
                extraMap = new HashMap<>();
            }
            extraMap.put(Constants.ORDER_IN_CAMPUS, campusHelper.isStoreInAnyCampus(storeInfo.getStoreId()));
            order.setExtraInfo(extraMap);
        }

        if (StringUtils.isNotBlank(request.getCellPhone())) {
            Map<Object, Object> extraMap = order.getExtraInfo();
            if (extraMap == null) {
                extraMap = new HashMap<>();
            }
            extraMap.put(Constants.CELL_PHONE_KEY, request.getCellPhone());
            order.setExtraInfo(extraMap);
        }


        if (request.getExtra() != null) {
            orderExtra.setGroupBuyingId(MapUtils.getString(request.getExtra(), Constants.GROUP_BUYING_ID, null));
        }

        order.setExtra(orderExtra);
        order.setCompatible(request.isCompatible());

        //校验微信加价购活动
//        if (!CollectionUtils.isEmpty(order.getWxGoods())) {
//            wxGoodsHelper.checkEnable(order.getWxGoods(), order.getStoreId());
//        }
        return order;
    }


    private String findUserNickName() {

        UserInfoDTO userInfo = WeakReferenceCaller.call(() -> userService.findUserInfoByUserIdAndPayWay(ThreadLocalHelper.getUserId(), Integer.parseInt(ThreadLocalHelper.getMiniProgramType().getCode())));
        if (null == userInfo) {
            return "佚名";
        }
        return userInfo.getNickName();

    }

    /**
     * 验证必点分类（业务上不再使用）
     *
     * @param itemCreateList
     * @param storeId
     * @param orderType
     */
    public void checkMustCategory(List<CartItemCreate> itemCreateList, String storeId, OrderType orderType) {
        List<String> categoryIds = itemCreateList.stream().map(ic -> ic.getItem().getCategoryId()).distinct().collect(Collectors.toList());
        checkMustCategorys(categoryIds, storeId, orderType);
    }

    public void checkMustCategorys(List<String> categoryIds, String storeId, OrderType orderType) {
        // 验证必点分类
        String mcc_key = "";
        String mustRangeKey = "";
        int serviceType = 0;
        if (OrderType.TAKE_OUT_ORDER == orderType || OrderType.PRE_ORDER == orderType) {
            mcc_key = MccUtils.STORE_TAKEOUT_MUST_CATEGORY;
            mustRangeKey = MccUtils.TAKEOUT_MUST_RANGE;
            serviceType = 1;
        }
        if (OrderType.SUBSCRIBE_ORDER == orderType || OrderType.EAT_FIRST_ORDER == orderType) {
            mcc_key = MccUtils.STORE_MUST_CATEGORY;
            mustRangeKey = MccUtils.STORE_MUST_RANGE;
            serviceType = 2;
        }
        // 必选分类配置
        ConfigResponse mustConfigResponse = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, mcc_key));
        // 商品适用分类：
        // 1.若未配置适用分类，则所有商品都要校验必选分类
        // 2.若已配置适用分类，则只要选择了任一适用分类中的商品，才需要校验必选分类
        ConfigResponse mustRangeResponse = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, mustRangeKey));
        if (mustConfigResponse != null && StringUtils.isNotEmpty(mustConfigResponse.getValue())) {
            // 查询该分类下的有效商品数量
            int itemCount = itemService.getStoreItemNumberByServiceType(storeId, mustConfigResponse.getValue(), serviceType);
            if (itemCount <= 0) {
                // 若必选分类下商品数量为0，则不校验
                return;
            }
            if (Objects.nonNull(mustRangeResponse)
                    && StringUtils.isNotBlank(mustRangeResponse.getValue())
                    && categoryIds.stream().noneMatch(it -> mustRangeResponse.getValue().contains(it))) {
                // 已配置适用范围，但是未选择范围内的商品，则不校验必选分类
                return;
            }
            // 判断是否有选择必选分类中的商品
            if (categoryIds.stream().noneMatch(ci -> ci.equalsIgnoreCase(mustConfigResponse.getValue()))) {
                throw new BusinessException(ReturnCode.MUST_CATEGORY_ERROR);
            }
        }

    }

    /**
     * 计算打包费
     *
     * @param storeId   门店ID
     * @param orderType 订单类型
     * @return
     */
    public PackFeeWrapper computePackAmount(String storeId, OrderType orderType, List<CartItemCreate> itemCreateList) {
        long packAmount = 0;
        String packTypeKey = "";
        String packFeeKey = "";
        if (orderType == OrderType.TAKE_OUT_ORDER || orderType == OrderType.PRE_ORDER) {
            packTypeKey = MccUtils.TAKEOUT_PACK_TYPE;
            packFeeKey = MccUtils.TAKEOUT_PACK_FEE;
        } else {
            packTypeKey = MccUtils.PACK_TYPE;
            packFeeKey = MccUtils.PACK_FEE;
        }
        ConfigResponse packTypeConfigResponse = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, packTypeKey));
        ConfigResponse packFeeConfigResponse = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, packFeeKey));

        PackFeeWrapper packFeeWrapper = new PackFeeWrapper();

        if (null != packTypeConfigResponse && "0".equalsIgnoreCase(packTypeConfigResponse.getValue())) {
            // 已设置为按订单计算打包费，取固定的打包费
            // 按订单收取配送费
            packAmount = MccUtils.getLongValue(packFeeConfigResponse);
            packFeeWrapper.setPackFeeType(0);
        } else {
            packFeeWrapper.setPackFeeType(1);
            // 按照商品收费：每个商品的打包费相加
            int serviceType = CommonUtil.getServiceType(orderType);
            if (itemCreateList == null) {
                itemCreateList = cartService.getItemCreateList(storeId, null, serviceType, OrderMealTypeEnum.SINGLE);
            }
            if (CollectionUtils.isEmpty(itemCreateList)) {
                throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR);
            }
            Map<String, Integer> packFeeMap = new HashMap<>();
            List<String> ids = itemCreateList.stream().map(item -> item.getItem().getId()).collect(Collectors.toList());
            ListResult<ItemDto> listResult = itemHelper.getItemDtoByIds(storeId, ids, serviceType);
            if (listResult != null && CollectionUtil.isNotEmpty(listResult.getRecords())) {
                for (ItemDto itemDto : listResult.getRecords()) {
                    Integer packFee = itemDto.getItem().getPackFee();
                    if (Objects.isNull(packFee)) {
                        packFee = 0;
                    }
                    packFeeMap.put(itemDto.getItem().getId(), packFee);
                }
                packFeeWrapper.setPackFeeMap(packFeeMap);
                packAmount = itemCreateList.stream()
                        .mapToLong(c -> {
                            Integer packFeeInteger = Optional.ofNullable(packFeeMap.get(c.getItem().getId())).orElse(0);
                            Long packFee = packFeeInteger.longValue();
                            c.setPackFee(packFee);
                            return c.getItem().getNumber() * packFee;
                        }).sum();
            }
        }

        packFeeWrapper.setPackFee(packAmount);


        return packFeeWrapper;
    }

    private long computeCartAmount(Order order, String storeId, OrderType orderType) {
        int serviceType = CommonUtil.getServiceType(orderType);

        List<CartItemCreate> itemCreateList = cartService.getItemCreateList(storeId, null, serviceType, OrderMealTypeEnum.SINGLE);
        if (CollectionUtils.isEmpty(itemCreateList)) {
            throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR);
        }
        long amount = 0;
        itemCreateList.sort(Comparator.comparingLong(CartItemCreate::getCtime));
        for (CartItemCreate v : itemCreateList) {
            CartItemCreate.Item item = v.getItem();
            Integer number = item.getNumber();
            if (number == null) {
                throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR);
            }
            // 非品牌商品才会查询商品信息
            if (!StringUtils.equalsIgnoreCase(Constants.BRAND_PRODUCT_SOURCE_BRAND, v.getBrandProductSource())) {
                ItemDto itemDto = itemHelper.getItemDtoById(storeId, item.getId(), serviceType);
                if (itemDto == null) {
                    throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
                }
            /*
            //redhat2022-06-07号注释：下单时验证库存放到了预扣库存逻辑中，这里不能验证库存了
            if (itemDto.getItem().getSku() != null && itemDto.getItem().getSku() < number) {
                throw new BusinessException(ReturnCode.ITEM_OUT_OF_SKU);
            }*/
                // 校验商品的服务类型
                if (serviceType != 0 && itemDto.getItem().getServiceType() != 0 && itemDto.getItem().getServiceType() != serviceType) {
                    throw new BusinessException(ReturnCode.ITEM_NOT_FOR_SALE);
                }

                //存放零售商品快照信息
                if (itemDto instanceof RetailExtendItemDTO) {
                    RetailExtendItemDTO retailItemDto = (RetailExtendItemDTO) itemDto;
                    v.setSnapshot(retailItemDto.getSnapshot());
                }
            }
            // 把商品的金额都加起来
            //amount += item.getPrice() * number;
            //amount += getItemPrice(v, itemDto) * number;
            amount += getItemPriceV2(v) * number;
        }
        // 需要判断打包费开关(如果是外卖单必须有打包费)
        if (Objects.equals(order.getPacked(), true) || OrderType.TAKE_OUT_ORDER == orderType
                || OrderType.PRE_ORDER == orderType) {
            order.setPacked(true);
            //amount += BeanUtil.getPropInt(order, PACK_AMOUNT);
            PackFeeWrapper wrapper = computePackAmount(storeId, orderType, itemCreateList);
            long packFee = Optional.ofNullable(wrapper).map(item -> item.getPackFee()).orElse(0L);
            amount += packFee;
            order.setPackAmount(packFee);

            if (wrapper != null && Objects.equals(wrapper.getPackFeeType(), 0)) {
                order.setOrderTag(OrderUtil.addTag(order.getOrderTag(), OrderTagEnum.ORDER_PACKAMOUNT.getValue()));
            }
        }

        // 验证必点分类，放在这里是为了避免有必选分类，但实际上必点分类下的商品库存不足
        // 在此逻辑之前，已经校验了库存
        checkMustCategory(itemCreateList, storeId, orderType);

        if (OrderType.TAKE_OUT_ORDER == orderType) {
            int deliveryFee = Optional.of(order).map(Order::getExtra)
                    .map(Order.OrderExtra::getDeliveryInfo)
                    .map(DeliverInfo::getDeliveryFee)
                    .orElse(0);
            amount += deliveryFee;
        }

        if (amount < 0) {
            throw new BusinessException(ReturnCode.ORDER_AMOUNT_ERROR);
        }

        order.setOriginalAmount(amount);
        order.setEffectiveAmount(amount);
        order.setTotalDiscount(0L);
        order.setItems(itemCreateList);

        return amount;
    }

    /**
     * 计算购物车商品的单价
     *
     * @param cartItem
     * @param itemDto
     * @return
     */
    private Integer getItemPrice(CartItemCreate cartItem, ItemDto itemDto) {

        if (Objects.equals(cartItem.getItem().getSpuType(), SpuType.PACKAGE.name())) {
            return cartItem.getItem().getPrice();
        }

        Integer dbPrice = 0;
        CartItemCreate.Spec spec = cartItem.getSpec();
        List<CartItemCreate.Material> materials = cartItem.getMaterials();
        List<ItemSpec> dbSpecs = itemDto.getSpecs();
        List<Material> dbMaterials = itemDto.getMaterials();
        if (!CollectionUtils.isEmpty(dbSpecs) && spec != null) {
            dbPrice += dbSpecs.stream()
                    .filter(s -> s.getId().equals(spec.getId()))
                    .map(ItemSpec::getPrice)
                    .findFirst()
                    .orElse(0);
        } else {
            dbPrice += itemDto.getItem().getPrice();
        }
        if (!CollectionUtils.isEmpty(dbMaterials) && !CollectionUtils.isEmpty(materials)) {
            Map<String, Material> map = dbMaterials
                    .stream()
                    .collect(Collectors.toMap(Material::getId, material -> material));
            dbPrice +=
                    materials
                            .stream()
                            .filter(m -> m.getNumber() != null)
                            .map(m ->
                                    Optional.ofNullable(map.get(m.getId()))
                                            .map(p -> p.getPrice() * m.getNumber())
                                            .orElse(0))
                            .reduce(0, Integer::sum);
        }

        return dbPrice;
    }

    /**
     * 计算购物车商品的单价, 使用购物车内的商品的价格计算，不再使用数据库中的伤心实时价格计算，这样可以避免商品变更导致金额不一致情况出现
     *
     * @param cartItem
     * @return
     */
    private Long getItemPriceV2(CartItemCreate cartItem) {
        if (Objects.equals(cartItem.getItem().getSpuType(), SpuType.PACKAGE.name())) {
            return cartItem.getItem().getPrice().longValue();
        }
        long resultPrice = cartItem.getItem().getPrice();
        return resultPrice;
    }

    private int computeDeliverFee(int instance) {
        int basePrice = 6;

        if (instance >= 3 && instance < 5) {
            basePrice += instance * 2 - 4;
        } else if (instance == 5) {
            basePrice += instance * 3 - 4 - 4;
        } else if (instance > 5) {
            basePrice += instance * 5 - 5 * 2 - 4 - 4;
        }

        return basePrice * 100;
    }

    private int computeDeliverFee(List<TieredPrice> tieredPrices, double distance) {
        int basePrice = 0;

        distance = distance / 1000;

        for (TieredPrice it : tieredPrices) {
            if (distance >= it.getStart() && (distance < it.getEnd() || it.getEnd() == -1d)) {
                basePrice = Optional.ofNullable(it.getPrice()).orElse(0);
                break;
            }
        }

        return basePrice;
    }


    /**
     * 是否是聚合配送
     *
     * @param deliveryType 配送方式
     * @return
     */
    private boolean isUpayDeliveryType(String deliveryType) {
        return UpayDeliveryTypes.contains(deliveryType);
    }

    private BookOrderInfoDTO makeBookOrderInfo(DeliverInfo deliverInfo, String storeId, OrderType orderType, String merchantId) {
        if (orderType != OrderType.TAKE_OUT_ORDER && orderType != OrderType.PRE_ORDER) {
            return null;
        }
        Long presetTime = deliverInfo.getPresetTime() == null ? -1L : deliverInfo.getPresetTime();
        BatchFindConfigByNameRequest request = new BatchFindConfigByNameRequest(AppId.UFOOD, OwnerType.STORE_ID, storeId, Constants.BOOK_ORDER_CONFIGS);
        List<ConfigResponse> configResponses = configRemoteService.batchFindByNames(request);
        Map<String, String> configMap = configResponses.stream().collect(Collectors.toMap(ConfigResponse::getName, ConfigResponse::getValue));
        if (presetTime == -1) {
            //即时单。没有预定信息。判断现在时间是否是营业时间内
            if (!DeliveryTimeUtils.inTimeInterval(configMap.get(Constants.DELIVERY_TIMES), System.currentTimeMillis())) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "店铺已打烊，暂时不能下单喔～");
            }
            //即时单，校验仅开启预定单开关。针对白名单(富士康)商户
            boolean onlyBook = MapUtils.getIntValue(configMap, OrderType.TAKE_OUT_ORDER == orderType
                    ? Constants.TAKEOUT_ONLY_BOOK_ORDER
                    : Constants.PRE_ONLY_BOOK_ORDER, 0) == 1;
            String whitelist = apolloConfigHelper.getStringConfigValueByKey("fushikang.merchant", "");
            List<String> list = Arrays.asList(whitelist.split(","));
            if (onlyBook && list.contains(merchantId)) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "店铺已打烊，暂时不能下单喔～");
            }

            return null;
        }
        String mccDays = null;
        int addTime = 0;
        if (OrderType.TAKE_OUT_ORDER == orderType) {
            String preStatus = Optional.ofNullable(configMap.get(Constants.TAKEOUT_PRE_STATUS)).orElse("0");
            if (!"1".equals(preStatus)) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商家暂未开启预订单功能，暂时不能下单喔～");
            }
            if (UpayDeliveryTypes.contains(deliverInfo.getDeliveryType())) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "非自配送商家不支持预订单，暂时不能下单喔～");
            }


            mccDays = Optional.ofNullable(configMap.get(Constants.TAKEOUT_PRE_DAYS)).orElse(Constants.DEFAULT_PRE_DAYS);
            addTime = Constants.DEFAULT_COOK_TIME + Constants.DEFAULT_DELIVERY_TIME;
        } else {
            mccDays = Optional.ofNullable(configMap.get(Constants.PRESET_DAYS)).orElse(Constants.DEFAULT_PRE_DAYS);
            addTime = Constants.DEFAULT_COOK_TIME;
        }
        List<Integer> days = JSONArray.parseArray(mccDays, Integer.class);
        //不在预定日
        if (!days.contains(TimeSplit.afterNowDays(new Date(presetTime)))) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "所选日期不在店铺可预订日期之内，暂时不能下单喔～");
        }
        //不在营业时间段
        if (!DeliveryTimeUtils.inTimeInterval(configMap.get(Constants.DELIVERY_TIMES), presetTime)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "所选时间不在店铺可预订时间之内，暂时不能下单喔～");
        }
        if (presetTime < System.currentTimeMillis()) {
            throw new BusinessException(ReturnCode.PRESET_TIME_HAS_BEAN_INVALID);
        }
        Date now = TimeSplit.getSoonDate(addTime);
        if (presetTime - now.getTime() > 0) {
            //组装预订单dto信息
            Integer printType = null;
            int remindTime = 60;
            if (OrderType.TAKE_OUT_ORDER == orderType) {
                printType = Optional.ofNullable(configMap.get(Constants.TAKEOUT_PRE_PRINT_TIME)).map(Integer::parseInt).orElse(2);
                remindTime = Optional.ofNullable(configMap.get(Constants.TAKEOUT_PRE_REMIND_TIME)).map(Integer::parseInt).orElse(60);
            } else {
                printType = Optional.ofNullable(configMap.get(Constants.PRESET_PRINT_TIME)).map(Integer::parseInt).orElse(2);
                remindTime = Optional.ofNullable(configMap.get(Constants.PRESET_REMIND_TIME)).map(Integer::parseInt).orElse(30);
            }
            BookOrderInfoDTO bookOrderInfoDTO = new BookOrderInfoDTO();
            bookOrderInfoDTO.setBookTime(presetTime);
            bookOrderInfoDTO.setRemindTime(presetTime - (long) remindTime * 60 * 1000);
            bookOrderInfoDTO.setPrintType(printType);
            return bookOrderInfoDTO;
        }
        return null;
    }

    private String getDeliveryInfoKey(String storeId) {
        return "deliveryInfo:v2:" + storeId + ":" + ThreadLocalHelper.getUserId();
    }

    public Map getAmountComposition(String storeId, OrderType orderType, DeliverInfo deliverInfo, long originalAmount) {
        if (OrderType.TAKE_OUT_ORDER != orderType) {
            // 非外卖订单，不返回金额构成
            return buildDefaultAmountComposition(originalAmount);
        }

        if (Objects.isNull(deliverInfo)) {
            // 从缓存中获取配送信息
            DeliveryInfoWrapper deliveryInfoWrapper = Optional.ofNullable(redisTemplate.boundValueOps(getDeliveryInfoKey(storeId)).get()).map(DeliveryInfoWrapper.class::cast).orElse(null);
            if (Objects.isNull(deliveryInfoWrapper) || Objects.isNull(deliveryInfoWrapper.getDeliverInfo())) {
                return buildDefaultAmountComposition(originalAmount);
            }
            deliverInfo = deliveryInfoWrapper.getDeliverInfo();
        }

        if (originalAmount <= 0) {
            return buildDefaultAmountComposition(originalAmount);
        }
        // 获取配送费的组成信息
        Map<String, Integer> deliveryFeeMap = deliverInfo.getDeliveryFeeMap();
        if (MapUtils.isEmpty(deliveryFeeMap)) {
            return buildDefaultAmountComposition(originalAmount);
        }
        // 用户实际支付配送费
        long userPayDeliveryAmount = MapUtils.getLongValue(deliveryFeeMap, Constants.DeliveryFeeType.USER, 0)
                - MapUtils.getLongValue(deliveryFeeMap, Constants.DeliveryFeeType.REDUCTION, 0);
        if (userPayDeliveryAmount <= 0) {
            // 用户实际需要支付金额为0，不返回金额构成
            return buildDefaultAmountComposition(originalAmount);
        }
        // 校园配送费
        long campus = MapUtils.getLongValue(deliveryFeeMap, Constants.DeliveryFeeType.CAMPUS, 0);
        // 第三方配送费
        long third = MapUtils.getLongValue(deliveryFeeMap, Constants.DeliveryFeeType.THIRD, 0);
        if (campus == 0 && third == 0) {
            // 没有校园或第三方配送费，不返回金额构成
            return buildDefaultAmountComposition(originalAmount);
        }

        Map amountComposition = new HashMap<>();
        amountComposition.put("compositionItems", buildAmountComposition(originalAmount, userPayDeliveryAmount));
        return amountComposition;
    }

    private long getUserPayDeliveryAmount(DeliverInfo deliverInfo) {
        if (null == deliverInfo) {
            return 0;
        }
        // 获取配送费的组成信息
        Map<String, Integer> deliveryFeeMap = deliverInfo.getDeliveryFeeMap();
        if (MapUtils.isEmpty(deliveryFeeMap)) {
            return 0;
        }
        // 用户实际支付配送费
        long userPayDeliveryAmount = MapUtils.getLongValue(deliveryFeeMap, Constants.DeliveryFeeType.USER, 0)
                - MapUtils.getLongValue(deliveryFeeMap, Constants.DeliveryFeeType.REDUCTION, 0);
        if (userPayDeliveryAmount <= 0) {
            return 0;
        }
        return userPayDeliveryAmount;
    }

    /**
     * 获取订单金额构成
     *
     * @param storeId
     * @param orderType
     * @param deliverInfo
     * @param packed
     * @return
     */
    public Map getAmountComposition(String storeId, OrderType orderType, DeliverInfo deliverInfo, boolean packed) {
        // 计算购物车商品总金额（含打包费）
        Order order = Order.builder().compatible(true).packed(packed).build();
        long goodsAmount = computeCartAmount(order, storeId, orderType);
        // 该方法只有orderMain聚合接口调用，从购物车算出来的商品+打包费总金额，需要把用户实际支付的配送费加上
        long originalAmount = goodsAmount + getUserPayDeliveryAmount(deliverInfo);
        return getAmountComposition(storeId, orderType, deliverInfo, originalAmount);
    }

    public Map buildDefaultAmountComposition(long originalAmount) {
        Map amountComposition = new HashMap<>();
        Map<String, Object> defaultCategory = new HashMap<>();
        defaultCategory.put("category", "default");
        defaultCategory.put("amount", String.valueOf(originalAmount));
        List<Map<String, Object>> defaultCategoryList = Collections.singletonList(defaultCategory);
        amountComposition.put("compositionItems", defaultCategoryList);
        return amountComposition;
    }

    /**
     * 组装金额构成数据
     *
     * @param goodsAmount
     * @param deliveryAmount
     * @return
     */
    public List<Map> buildAmountComposition(Long goodsAmount, Long deliveryAmount) {
        Map<String, Object> productMap = new HashMap();
        productMap.put("category", "product");
        productMap.put("amount", String.valueOf(goodsAmount - deliveryAmount));

        Map<String, Object> deliveryMap = new HashMap();
        deliveryMap.put("category", "delivery");
        deliveryMap.put("amount", String.valueOf(deliveryAmount));
        return Lists.newArrayList(productMap, deliveryMap);
    }


    /**
     * 获取tradeApp
     *
     * @param storeId
     * @param orderType
     * @return
     */
    public String getTradeApp(String storeId, OrderType orderType) {
        TradeAppRequest tradeAppRequest = new TradeAppRequest();
        tradeAppRequest.setStoreId(storeId);
        tradeAppRequest.setOrderType(orderType.getMsg());
        LogUtils.logInfo("获取TradeApp", "getTradeApp", tradeAppRequest);
        try {
            return payService.getTradeAppByOrderTypeAndStoreId(tradeAppRequest);
        } catch (Exception e) {
            LogUtils.logWarn("获取tradeApp失败", "getTradeApp", tradeAppRequest, e);
        }
        return "";
    }

    public String getTradeApp4RoundMeal(String tradeScene) {
        if ("payQrCode".equals(tradeScene)) {
            return getTradeApp(TradeApp.CASHIER_ORDER);
        } else {
            return getTradeApp(TradeApp.SCAN_ORDER);
        }
    }

    public Map getRoundMealAmountComposition(Long amount) {
        return buildDefaultAmountComposition(amount);
    }

    /**
     * 获取交易应用
     *
     * @param tradeApp
     * @return
     */
    public String getTradeApp(TradeApp tradeApp) {
        return tradeApp.getCode(useTestApp);
    }


    /**
     * 获取收银台相关参数
     * 目前仅处理tradeApp和amountComposition
     *
     * @param storeId     门店id
     * @param orderType   订单类型
     * @param deliverInfo 配送信息，若为null则从缓存中获取
     * @param packed      是否打包
     * @return
     */
    public Map<String, Object> getCashierBizParams(String storeId, OrderType orderType, DeliverInfo deliverInfo, boolean packed) {
        if (OrderType.TAKE_OUT_ORDER == orderType && Objects.isNull(deliverInfo)) {
            // 从缓存中获取配送信息
            DeliveryInfoWrapper deliveryInfoWrapper = Optional.ofNullable(redisTemplate.boundValueOps(getDeliveryInfoKey(storeId)).get()).map(DeliveryInfoWrapper.class::cast).orElse(null);
            if (Objects.nonNull(deliveryInfoWrapper)) {
                LogUtils.logInfo("计算金额构成使用缓存配送信息", "useDeliveryInfoCacheForCashierBizParams", deliveryInfoWrapper);
                deliverInfo = deliveryInfoWrapper.getDeliverInfo();
            }
        }
        Map<String, Object> params = new HashMap<>();
        // 获取tradeApp
        String tradeApp = getTradeApp(storeId, orderType);
        params.put("tradeApp", tradeApp);
        // 获取金额构成
        Map amountComposition = getAmountComposition(storeId, orderType, deliverInfo, packed);
        if (MapUtils.isNotEmpty(amountComposition)) {
            params.put("amountComposition", amountComposition);
        }
        return params;
    }

    private void fillAdditionalInfo(Order order, PayRequest request) {
        Map<String, Object> additionalInfo = new HashMap<>();

        additionalInfo.put(OrderAdditionalInfoKey.EXTRA_KEY_RECHARGE_AND_PAY, request.isRechargeAndPay());
        if (request.getGroupBuyingActivity() != null) {
            additionalInfo.put(OrderAdditionalInfoKey.EXTRA_KEY_GROUP_BUYING_ACTIVITY_ID,
                    request.getGroupBuyingActivity().getActivityId());
        }

        order.setAdditionalInfo(additionalInfo);
    }

    /**
     * @param fromMap 存在称重商品信息的map
     * @param toMap   待填入称重商品信息的map
     * @return
     * @description 从fromMap提取称重商品信息保存至toMap
     **/
    private static void patchMapWeightInfo(Map<Object, Object> fromMap, Map<String, Object> toMap) {
        if (fromMap == null || toMap == null) {
            return;
        }
        if (!GoodsUnitTypeEnum.WEIGHT.name().equals(MapUtils.getString(fromMap, EXTRA_KEY_UNIT_TYPE_KEY, ""))) {
            //不是称重商品，直接返回
            return;
        }
        if (!toMap.containsKey(EXTRA_KEY_UNIT_TYPE_KEY)) {
            toMap.put(EXTRA_KEY_UNIT_TYPE_KEY, MapUtils.getString(fromMap, EXTRA_KEY_UNIT_TYPE_KEY, ""));
        }
        if (!toMap.containsKey(EXTRA_KEY_SALE_WEIGHT)) {
            toMap.put(EXTRA_KEY_SALE_WEIGHT, MapUtils.getString(fromMap, EXTRA_KEY_SALE_WEIGHT, "1"));
        }
        if (!toMap.containsKey(EXTRA_INFO_GOODS_ORIGIN_PRICE)) {
            toMap.put(EXTRA_INFO_GOODS_ORIGIN_PRICE, MapUtils.getInteger(fromMap, EXTRA_INFO_GOODS_ORIGIN_PRICE, 1));
        }
    }

}

