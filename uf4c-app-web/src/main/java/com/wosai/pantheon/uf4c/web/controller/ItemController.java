package com.wosai.pantheon.uf4c.web.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.market.painter.api.dto.request.UfoodShare4CRequest;
import com.wosai.pantheon.core.uitem.model.CategoryResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.fallbackconfig.server.BlockHandleServer;
import com.wosai.pantheon.uf4c.model.CursorResult;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.dto.MultiCategoryRequest;
import com.wosai.pantheon.uf4c.model.dto.RecommendMaterialFindRequest;
import com.wosai.pantheon.uf4c.model.item.ItemVoBuildWrapper;
import com.wosai.pantheon.uf4c.model.vo.CategoryVo;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.model.vo.ItemNewVo;
import com.wosai.pantheon.uf4c.model.vo.RecommendMaterialTuple;
import com.wosai.pantheon.uf4c.service.ItemsNewHelper;
import com.wosai.pantheon.uf4c.service.apisix.ItemService;
import com.wosai.smart.goods.common.constant.ProductConstant;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * created by shij on 2019/4/1
 */
@RestController
@Slf4j
@RequestMapping(path = "/api/v1/items", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class ItemController {


    @Autowired
    private ItemService itemService;
    @Autowired
    private ItemsNewHelper itemsNewHelper;


    @GetMapping("/get")
    @ResponseBody
    public ItemDetailVO getItemDetailById(@RequestParam Map queryParam) {
        return getItemDetailByIdTemporary(queryParam);
    }


    /**
     * 2024-03-06
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/ItemController/getItemDetailById",blockHandlerClass = BlockHandleServer.class,blockHandler = "handleGetItemDetailById")
    private ItemDetailVO getItemDetailByIdTemporary(Map queryParam) {
        return itemService.getItemDetailById(ApiRequest.buildGetRequest(queryParam));
    }


    @PostMapping("/share")
    @ResponseBody
    public Map share(@RequestBody UfoodShare4CRequest request) {

        return itemService.share(new ApiRequest(request));

    }

    @PostMapping("/find")
    @ResponseBody
    public ListResult<ItemDetailVO> findItemDetails(@RequestBody ItemFind find) {
        return itemService.findItemDetails(new ApiRequest<>(find));
    }

    @PostMapping("/search")
    @ResponseBody
    public ListResult<ItemNewVo> findItemsNew(@RequestBody ItemFind find) {
        if (Objects.isNull(find.getApiVersion())) {
            find.setApiVersion(1);
        }
        CursorResult<ItemNewVo> newResult = new CursorResult<>();
        ListResult<ItemDetailVO> result = itemService.findItemDetails(new ApiRequest<>(find));
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getRecords())) {
            newResult.setTotal(result.getTotal());
            newResult.setRecords(result.getRecords().stream().map(it -> {
                ItemVoBuildWrapper buildWrapper = new ItemVoBuildWrapper(it, find.getServiceType());
                buildWrapper.setImageSizeForShow(ProductConstant.MediaSpecTypeEnum.RATIO_1_1.getCode());
                return itemsNewHelper.convert(buildWrapper);
            }).collect(Collectors.toList()));
            if (result instanceof CursorResult) {
                newResult.setCursor(((CursorResult) result).getCursor());
            }
        }
        return newResult;
    }

    @PostMapping("/findByMultiCategoryId")
    @ResponseBody
    public List<CategoryResult<ItemDetailVO>> findByMultiCategoryId(@RequestBody MultiCategoryRequest request) {
        return itemService.findByMultiCategoryId(new ApiRequest<>(request));
    }


    @PostMapping("/hotsale")
    @ResponseBody
    public List<ItemDetailVO> hotsale(@RequestBody Map<String, Object> map) {

        return itemService.hotsale(new ApiRequest<>(map));
    }


    @PostMapping("/recommend")
    @ResponseBody
    public ListResult<ItemDetailVO> recommend(@RequestBody ItemFind find) {
        return itemService.recommend(new ApiRequest<>(find));
    }



    @PostMapping("/category")
    public List<CategoryVo> category(@RequestBody ItemFind find) {
        return itemService.category(new ApiRequest<>(find));
    }


    @PostMapping("/index")
    public Map<String, Object> miniIndex(@RequestBody ItemFind find) {
        return miniIndexTemporary(find);
    }

    /**
     * 2024-03-06
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/ItemController/miniIndex", blockHandlerClass = BlockHandleServer.class, blockHandler = "handleMiniIndex")
    private Map<String, Object> miniIndexTemporary(ItemFind find) {
        return itemService.miniIndex(new ApiRequest<>(find));
    }


    /**
     * 获取商品的推荐加料
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/material/recommend")
    public List<RecommendMaterialTuple> findRecommendMaterials(@RequestBody @Valid RecommendMaterialFindRequest request) {
        return recommendMaterialTemporary(request);
    }

    /**
     * 2024-09-02
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/ItemController/recommendMaterial",blockHandlerClass = BlockHandleServer.class,blockHandler = "handleRecommendMaterial")
    private List<RecommendMaterialTuple> recommendMaterialTemporary(RecommendMaterialFindRequest request) {
        return itemService.findRecommendMaterials(new ApiRequest<>(request));
    }

}
