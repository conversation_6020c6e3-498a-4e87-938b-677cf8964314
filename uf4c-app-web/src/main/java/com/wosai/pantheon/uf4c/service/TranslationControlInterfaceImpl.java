package com.wosai.pantheon.uf4c.service;

import com.shouqianba.smart.translation.manager.api.rpc.TranslationRemoteService;
import com.shouqianba.smart.translation.manager.common.interfaces.TranslationControlInterface;
import com.shouqianba.smart.translation.manager.common.model.vo.NeedTranslateVO;
import com.shouqianba.smart.translation.manager.common.utils.AcceptLanguageConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TranslationControlInterfaceImpl implements TranslationControlInterface {
    @Autowired
    private TranslationRemoteService translationRemoteService;

    @Override
    public NeedTranslateVO needTranslate(String storeId, String language) {
        if (StringUtils.isAnyBlank(storeId, language)) {
            return new NeedTranslateVO(false, null);
        }

        // 是否是需要翻译的语言, 因为大部分都是中文简体，这种情况本地过滤掉。非中文简体的才走到
        String resolvedLanguage = AcceptLanguageConvertUtils.resolveLanguage(language);
        if (!AcceptLanguageConvertUtils.needTranslate(resolvedLanguage)) {
            return new NeedTranslateVO(false, null);
        }

        // 非中文简体的才走到才走到翻译管理服务查询门店是否开启C端翻译
        boolean show = translationRemoteService.translationShowToCustomers(storeId);
        return new NeedTranslateVO(show, resolvedLanguage);
    }
}
