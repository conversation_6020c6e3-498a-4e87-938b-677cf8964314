package com.wosai.pantheon.uf4c.gather.cache;

import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class GatherRedisUtil {

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private RedisTemplate gatherRedisTemplate;

    public void saveHashData(String key, String field, Object value) {
        gatherRedisTemplate.opsForHash().put(key, field, value);
    }

    public Object getHashValueByFiled(String key, String filed) {
        return gatherRedisTemplate.opsForHash().get(key, filed);
    }

    public Map<String, Object> getHashValueByFields(String key, List<String> fields) {
        return (Map<String, Object>) gatherRedisTemplate.opsForHash().multiGet(key, fields);
    }

    public Map<String, Object> getHashAllValues(String key) {
        return gatherRedisTemplate.opsForHash().entries(key);
    }

    public boolean hasKey(String key, String field) {
        return gatherRedisTemplate.opsForHash().hasKey(key, field);
    }

    public void setExpire(String key) {
        gatherRedisTemplate.expire(key, getExpire(), TimeUnit.MINUTES);
    }

    private int getExpire() {
        return Integer.parseInt(apolloConfigHelper.getStringConfigValueByKey("gatherCacheExpire", "720"));
    }
}
