package com.wosai.pantheon.uf4c.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.LinkedList;

/**
 * Create by zhiwen.zhu
 * On 2019/2/25
 */

@Slf4j
public class QRCodeUtils {
    private final static StringBuffer CHARACTER = new StringBuffer("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");

    private final static int CHARACTER_LEN = CHARACTER.length();

    public static String compress(long number, String code) {
        if (number >= CHARACTER_LEN) {
            int residue = (int) (number % CHARACTER_LEN);
            return compress((number - residue) / CHARACTER_LEN, CHARACTER.charAt(residue) + code);
        }
        return CHARACTER.charAt((int) number) + code;
    }

    /**
     * 正整数压缩
     *
     * @param number 正整数
     * @return 压缩结果
     */
    public static String compress(long number) {
        return compress(number, "");
    }

    public static String compress(String code) {
        return compress(Long.parseLong(code));
    }

    public static String decompress(String code) {
        LinkedList<String> characters = new LinkedList<>(Arrays.asList(code.split("")));
        String last = characters.removeLast();
        long number = 0;
        for (String character : characters) {
            number = (CHARACTER.indexOf(character) + number) * CHARACTER_LEN;
        }
        number = CHARACTER.indexOf(last) + number;
        return String.format("%010d", number);
    }

    public static void main(String[] args) {
        String s = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        System.out.println(String.valueOf(s.charAt(61)));
    }
}
