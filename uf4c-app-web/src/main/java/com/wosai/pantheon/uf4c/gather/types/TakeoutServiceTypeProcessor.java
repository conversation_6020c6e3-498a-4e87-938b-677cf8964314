package com.wosai.pantheon.uf4c.gather.types;

import com.wosai.market.mcc.api.constant.MerchantKeys;
import com.wosai.market.mcc.api.constant.StoreActivatedKeys;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.CodeScene;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ServiceTypeData;
import com.wosai.pantheon.uf4c.util.MccUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class TakeoutServiceTypeProcessor extends AbstractServiceProcessor {
    @Override
    public ServiceTypeData.ServiceItem process(GatherRequest request, Map<String, Object> dataMap) {
        ServiceTypeData.ServiceItem serviceItem = new ServiceTypeData.ServiceItem();
        serviceItem.setSort(2);
        serviceItem.setName("外卖配送");
        serviceItem.setServiceType(1);
        serviceItem.setServiceTypeName(OrderType.TAKE_OUT_ORDER.getMsg());
        serviceItem.setShortName("外卖");
        serviceItem.setActive(isActive(request, dataMap));
        Map<String, String> mccMap = (Map<String, String>) MapUtils.getMap(dataMap, "mcc");
        Map<String, String> mchMap = (Map<String, String>) MapUtils.getMap(dataMap, "mch_mcc");
        boolean isRetail = Objects.equals(Constants.RETAIL, MapUtils.getString(mchMap, MerchantKeys.MERCHANT_TYPE));
        // 到店业务开通状态
        boolean takeoutActivated;
        if (isRetail) {
            takeoutActivated = MccUtils.getBooleanValue(mccMap, StoreActivatedKeys.RETAIL_TAKEOUT_ACTIVATED, false);
        } else {
            takeoutActivated = MccUtils.getBooleanValue(mccMap, StoreActivatedKeys.TAKEOUT_DELIVERY_ACTIVATED, false);

        }
        boolean onTakeoutBusiness = MccUtils.getBooleanValue(mccMap, Constants.TAKEOUT_BUSINESS_STATUS, false);
        // 配送方式 1-不配送    >1-支持配送
        int deliveryType = MccUtils.getIntValue(mccMap, Constants.DELIVERY_TYPE);
        return takeoutActivated && onTakeoutBusiness && deliveryType > 1 ? serviceItem : null;
    }

    @Override
    public boolean isActive(GatherRequest request, Map<String, Object> dataMap) {
        Map<String, String> extraMap = (Map<String, String>) MapUtils.getMap(dataMap, "extra");
        String scene = MapUtils.getString(extraMap, "scene", "");
        String mealShareType = MapUtils.getString(extraMap, "mealShareType");
        String goodsId = MapUtils.getString(extraMap, "goodsId");
        // 外卖海报
        if (StringUtils.isNotBlank(scene) && "poster".equalsIgnoreCase(mealShareType)) {
            return true;
        }
        // 如果是商品码进入，默认外卖
        if (CodeScene.getCodeScene(scene) == CodeScene.P && StringUtils.isNotBlank(goodsId)) {
            return true;
        }
        if (Objects.nonNull(request.getCampusId()) && request.getCampusId() > 0) {
            return true;
        }
        return Constants.From.CAMPUS.equalsIgnoreCase(request.getFrom()) || OrderType.TAKE_OUT_ORDER.getMsg().equalsIgnoreCase(request.getBuyAgainOrderType());

    }
}
