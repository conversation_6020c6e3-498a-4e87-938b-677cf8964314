package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.OrderFind;
import com.wosai.pantheon.uf4c.model.Qrcode;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.web.api.PaginatedResult;

import java.util.Map;

@JsonRpcService(value = "/rpc/order/v3")
public interface OrderServiceV3 {

    PaginatedResult<Order> findOrders(ApiRequest<OrderFind> apiRequest);

    Order findLatestOne(ApiRequest<OrderFind> apiRequest);

    Order getBySn(ApiRequest<Map> apiRequest);

    Order getByTransSn(ApiRequest<Map> apiRequest);

    CartCheckResultDTO orderAgain(ApiRequest apiRequest);

    /**
     * 获取订单取餐二维码
     *
     * @param apiRequest
     * @return
     */
    Qrcode getPickUpMealsQrCodeBySn(ApiRequest<Map> apiRequest);
}
