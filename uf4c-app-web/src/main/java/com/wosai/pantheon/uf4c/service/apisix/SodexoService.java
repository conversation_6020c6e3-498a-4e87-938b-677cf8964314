package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * 索迪斯支付相关接口
 */
@JsonRpcService(value = "/rpc/sodexo")
@Validated
public interface SodexoService {

    /**
     * 调用索迪斯支付接口
     *
     * @param apiRequest
     * @return
     */
    boolean pay(ApiRequest<Map> apiRequest);

    /**
     * 查询余额接口
     *
     * @param apiRequest
     * @return
     */
    String balance(ApiRequest<Map> apiRequest);
}
