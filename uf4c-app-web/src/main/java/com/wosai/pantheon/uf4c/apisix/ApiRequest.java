package com.wosai.pantheon.uf4c.apisix;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

@Data
public class ApiRequest<T> implements Serializable {

    private UserContext user;

    @Valid
    private T body;

    private Map<String, Object> query;

    private Client client;

    public ApiRequest(T body) {
        this.body = body;
    }

    public static ApiRequest buildGetRequest(Map<String, Object> query) {
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setQuery(query);
        return apiRequest;
    }

    public ApiRequest() {
    }
}
