package com.wosai.pantheon.uf4c.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ApolloConfig {
    @Bean
    public Config mpVersionConfig() {
        return ConfigService.getConfig("marketing.mp-version-config");
    }

    @Bean
    public Config appConfig() {
        return ConfigService.getAppConfig();
    }
}
