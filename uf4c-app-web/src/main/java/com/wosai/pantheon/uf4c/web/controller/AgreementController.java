package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.market.user.dto.AddUserAgreementRequest;
import com.wosai.market.user.dto.QueryUserAgreementRequest;
import com.wosai.market.user.service.UserAgreementService;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/api/v1/agreement", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class AgreementController {

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private UserAgreementService userAgreementService;

    @PostMapping("/confirm")
    public Boolean confirm() {
        String userAgreementCode = apolloConfigHelper.getStringConfigValueByKey("userAgreementVersionCode", "");
        AddUserAgreementRequest addUserAgreementRequest = new AddUserAgreementRequest();
        addUserAgreementRequest.setAgreementCode(userAgreementCode);
        addUserAgreementRequest.setUserId(ThreadLocalHelper.getUserId());
        addUserAgreementRequest.setUsableType("USER");
        addUserAgreementRequest.setUsableValue(ThreadLocalHelper.getUserId());
        return userAgreementService.addAgreementRecord(addUserAgreementRequest);
    }

    @GetMapping("/has")
    public Boolean hasAgreement() {
        String userAgreementCode = apolloConfigHelper.getStringConfigValueByKey("userAgreementVersionCode", "");
        QueryUserAgreementRequest queryUserAgreementRequest = new QueryUserAgreementRequest();
        queryUserAgreementRequest.setAgreementCode(userAgreementCode);
        queryUserAgreementRequest.setUserId(ThreadLocalHelper.getUserId());
        queryUserAgreementRequest.setUsableType("USER");
        queryUserAgreementRequest.setUsableValue(ThreadLocalHelper.getUserId());
        return userAgreementService.hasAgreementRecord(queryUserAgreementRequest);
    }
}
