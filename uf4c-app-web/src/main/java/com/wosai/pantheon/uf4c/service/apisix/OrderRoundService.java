package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.InitRequest;
import com.wosai.pantheon.uf4c.model.dto.PayFirstRoundOrderPayRequest;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.model.payfirsttableorder.ItemsAndRedeem;
import com.wosai.smartbiz.oms.api.pojo.OrderMainWrapper;

@JsonRpcService(value = "/rpc/order/round")
public interface OrderRoundService {

    OrderMainWrapper init(ApiRequest<InitRequest> apiRequest);

    OrderMainWrapper addGoods(ApiRequest<InitRequest> apiRequest);

    PayResult roundPay(ApiRequest<PayRequest> apiRequest);

    Boolean canclePayLock(ApiRequest apiRequest);

    boolean cancelBatchGoods4PayFirstRoundOrder(PayFirstRoundOrderPayRequest request);

    ItemsAndRedeem getOrderGoodsAndRedeem(PayFirstRoundOrderPayRequest request);


    PayResult addGoodsAndPay(ApiRequest<PayFirstRoundOrderPayRequest> apiRequest);
}
