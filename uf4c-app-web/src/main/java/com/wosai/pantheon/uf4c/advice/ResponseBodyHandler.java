package com.wosai.pantheon.uf4c.advice;

import com.wosai.pantheon.uf4c.web.response.Response;
import com.wosai.pantheon.uf4c.web.response.SuccessResponse;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * created by shij on 2019/3/28
 */
@RestControllerAdvice(basePackages = {"com.wosai.pantheon.uf4c.web.controller"})
public class ResponseBodyHandler implements ResponseBodyAdvice {

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        if (o instanceof String || o instanceof Response) {
            return o;
        }

        return new SuccessResponse<>(o);
    }

}
