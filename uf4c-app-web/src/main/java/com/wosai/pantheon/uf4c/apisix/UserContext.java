package com.wosai.pantheon.uf4c.apisix;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wosai.market.user.dto.UserContextDTO;
import lombok.Data;


@Data
@JsonNaming(value = PropertyNamingStrategy.class)
public class UserContext extends UserContextDTO {


    /**
     * 以下是前端请求header中带的参数
     */

    /**
     * 对应header中Wosai-ExternalSource
     */
    private String externalSource;

    /**
     * 对应header中Wosai-Scene
     */
    private String scene;

    /**
     * 对应header中Wosai-MiniProgramType
     */
    private String miniProgramType;

    private String xSmartAcceptLanguage;
}
