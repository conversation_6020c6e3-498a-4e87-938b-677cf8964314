package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.MerchantInfo;
import com.wosai.pantheon.uf4c.model.dto.MultiConfigRequest;

import java.util.List;

@JsonRpcService(value = "/rpc/merchant")
public interface MerchantService {

    Boolean isCollection(ApiRequest apiRequest);

    List<ConfigResponse> getMerchantConfig(ApiRequest<MultiConfigRequest> apiRequest);

    MerchantInfo getMerchantInfo(ApiRequest apiRequest);
}
