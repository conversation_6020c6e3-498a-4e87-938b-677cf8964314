package com.wosai.pantheon.uf4c.service.item.retail;

import com.wosai.pantheon.uf4c.model.item.*;
import com.wosai.web.api.ListResult;

import java.util.List;
import java.util.Map;

/**
 * 零售商品适配层服务
 */
public interface IRetailItemAdapterService {

    /**
     * 查询分类
     */
    ListResult<CategoryTreeDTO> queryCategories(RetailCategoryQueryReq req);

    /**
     * 分页查询商品
     */
    RetailItemCursorQueryRes cursorQueryItem(RetailItemCursorQueryReq req);

    /**
     * 查询指定商品
     */
    Map<String, RetailItemDTO> queryItemBySpuIds(String storeId, List<String> spuIds);

}
