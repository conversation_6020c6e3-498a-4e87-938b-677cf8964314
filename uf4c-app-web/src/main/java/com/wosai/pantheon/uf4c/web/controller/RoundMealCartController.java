package com.wosai.pantheon.uf4c.web.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.apisix.UserContext;
import com.wosai.pantheon.uf4c.fallbackconfig.server.BlockHandleServer;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartAndRedeem;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.service.apisix.RoundMealCartService;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.smartbiz.base.apisix.uf4capp.Uf4cAppApiRequest;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.query.CartSyncQuery;
import com.wosai.smartbiz.oms.api.services.apisix.ApisixCartService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * created by beiyu on 2021/2/07
 */
@RestController
@RequestMapping(path = "/api/v1/carts/round", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequiredArgsConstructor
public class RoundMealCartController {

    @Autowired
    private final RoundMealCartService roundMealCartService;

    @Autowired
    private ApisixCartService apisixCartService;



    @RequestMapping("/add")
    @ResponseBody
    public Cart addCart(@RequestBody @Valid CartItemCreate request) {
        return roundMealCartService.addCart(new ApiRequest<>(request));
    }


    @RequestMapping("/set")
    @ResponseBody
    public Cart setCartItem(@RequestBody @Valid CartItemCreate request) {
        return roundMealCartService.setCartItem(new ApiRequest<>(request));
    }


    @RequestMapping("/reduce")
    @ResponseBody
    public Cart reduceCart(@RequestBody @Valid CartItemCreate request) {
        return roundMealCartService.reduceCart(new ApiRequest<>(request));
    }

    @RequestMapping("/clean")
    @ResponseBody
    public Cart cleanCart(@RequestParam Map queryParam) {
        return roundMealCartService.cleanCart(ApiRequest.buildGetRequest(queryParam));
    }

    @RequestMapping("/check")
    @ResponseBody
    public CartCheckResultDTO checkItemStatus(@RequestParam Map queryParam){
        return roundMealCartService.checkItemStatus(ApiRequest.buildGetRequest(queryParam));
    }


    @RequestMapping("/setCartMustOrderEditable")
    @ResponseBody
    public boolean setCartMustOrderEditable(@RequestBody CartsRequest cartsRequest) {
        return roundMealCartService.setCartMustOrderEditable(new ApiRequest<>(cartsRequest));
    }


    @RequestMapping("/list")
    @ResponseBody
    public Cart list(@RequestParam Map queryParam) {
        return listTemporary(queryParam);
    }


    /**
     * 2024-03-06
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/RoundMealCartController/list",blockHandlerClass = BlockHandleServer.class,blockHandler = "handleRoundMealList")
    private Cart listTemporary(Map queryParam) {
        return roundMealCartService.list(ApiRequest.buildGetRequest(queryParam));
    }

    @RequestMapping("/listAndRedeem")
    @ResponseBody
    public CartAndRedeem listAndRedeem(@RequestBody CartsRequest cartsRequest) {
        ApiRequest<CartsRequest> apiRequest = new ApiRequest<>(cartsRequest);
        UserContext userContext = new UserContext();
        userContext.setThirdpartyUserId(ThreadLocalHelper.getThirdPartyUserId());
        userContext.setUserId(ThreadLocalHelper.getUserId());
        apiRequest.setUser(userContext);
        return roundMealCartService.listAndRedeem(apiRequest);
    }

    @RequestMapping("/version")
    @ResponseBody
    public Long getCartVersion(@RequestBody CartSyncQuery cartsRequest) {
        Uf4cAppApiRequest<CartSyncQuery> apiRequest = new Uf4cAppApiRequest<>(cartsRequest);
        return apisixCartService.getCartVersion(apiRequest);
    }


}
