package com.wosai.pantheon.uf4c.fallbackconfig.client;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.shouqianba.smart.translation.manager.api.rpc.TranslationRemoteService;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;

import java.lang.reflect.Method;
import java.util.HashMap;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;

public class TranslationRemoteServiceFallBack extends JsonRPCFallbackDefine {
    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[] {
                new JsonRPCMethodFallbackHandler() {
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("convert");
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        return new HashMap<>();
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("translationShowToCustomers");
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        return false;
                    }
                },
                ElementMatchers::any
        };
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(TranslationRemoteService.class);
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}
