package com.wosai.pantheon.uf4c.model.item;

import com.wosai.pantheon.core.uitem.model.CategoryExt;
import com.wosai.smart.goods.dto.CategoryDTO;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024/6/13
 */
@Data
public class CategoryTreeDTO extends CategoryExt {

    /**
     * 下级分类
     */
    private List<CategoryTreeDTO> subCategories;

    public static CategoryTreeDTO fromRetail(CategoryDTO category) {
        Objects.requireNonNull(category);
        CategoryTreeDTO tree = new CategoryTreeDTO();
        tree.setId(String.valueOf(category.getCategoryId()));
        tree.setName(category.getTitle());
        tree.setDescription(null);
        tree.setDisplayOrder(category.getSeq());
        tree.setType(1);
        tree.setValid(true);
        tree.setCategoryType("NORMAL");
        return tree;
    }

}
