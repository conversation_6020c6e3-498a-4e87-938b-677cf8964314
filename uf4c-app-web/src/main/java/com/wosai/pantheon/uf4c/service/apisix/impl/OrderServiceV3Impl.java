package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.market.enums.ProductTypeEnum;
import com.wosai.market.painter.api.dto.request.PaintQrCodeRequest;
import com.wosai.market.painter.api.service.OrderQrCodePainterRemoteService;
import com.wosai.pantheon.core.uitem.model.*;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.OrderDTO;
import com.wosai.pantheon.order.model.dto.OrderItemDTO;
import com.wosai.pantheon.order.model.dto.PageDTO;
import com.wosai.pantheon.order.model.dto.request.Attribute;
import com.wosai.pantheon.order.model.dto.request.OrderQueryRequest;
import com.wosai.pantheon.order.pojo.OrderRefundApplyDTO;
import com.wosai.pantheon.order.service.OrderRefundApplyService;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.order.utils.OrderUtil;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.OrderFind;
import com.wosai.pantheon.uf4c.model.Qrcode;
import com.wosai.pantheon.uf4c.model.dto.RetailExtendItemDTO;
import com.wosai.pantheon.uf4c.service.CartService;
import com.wosai.pantheon.uf4c.service.ItemHelper;
import com.wosai.pantheon.uf4c.service.TranslationConvertService;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV3;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.EntityConvert;
import com.wosai.pantheon.uf4c.util.HttpRequestUtil;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.enums.config.MealTypeValueEnum;
import com.wosai.smartbiz.oms.api.enums.DealTypeEnum;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.query.table.TableQueryRequest;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import com.wosai.web.api.PaginatedResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.pantheon.uf4c.constant.OrderConstant.SN;
import static com.wosai.pantheon.uf4c.constant.OrderConstant.TRANS_SN;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class OrderServiceV3Impl implements OrderServiceV3 {

    public static final String SUCCESS = "success";

    public static final String FAIL = "fail";

    /**
     * 取餐二维码宽度
     */
    private static final int PICK_UP_MEALS_QR_CODE_WIDTH = 300;

    /**
     * 取餐二维码高度
     */
    private static final int PICK_UP_MEALS_QR_CODE_HEIGHT = 300;

    @Autowired
    private OrderQrCodePainterRemoteService orderQrCodePainterRemoteService;



    @Autowired
    private OrderService orderService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private ItemHelper itemHelper;

    @Autowired
    private TableRpcServiceV2 tableRpcService;

    @Autowired
    CartService cartService;

    @Autowired
    OrderRefundApplyService orderRefundApplyService;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private TranslationConvertService translationConvertService;

    @Override
    public PaginatedResult<Order> findOrders(ApiRequest<OrderFind> apiRequest) {

        OrderFind request = apiRequest.getBody();

        OrderQueryRequest orderQueryRequest = new OrderQueryRequest();
        if (request.getServiceType() == 1) { // 自营外卖
            orderQueryRequest.setOrderTypes(Arrays.asList(OrderType.TAKE_OUT_ORDER, OrderType.PRE_ORDER));
        } else if (request.getServiceType() == 2) {// 扫码点单
            orderQueryRequest.setOrderTypes(Arrays.asList(OrderType.EAT_FIRST_ORDER, OrderType.SUBSCRIBE_ORDER,OrderType.PAY_FIRST_TABLE_ORDER));
        } else {
            orderQueryRequest.setOrderTypes(Arrays.asList(OrderType.SUBSCRIBE_ORDER, OrderType.TAKE_OUT_ORDER, OrderType.PRE_ORDER, OrderType.EAT_FIRST_ORDER,OrderType.PAY_FIRST_TABLE_ORDER));
        }
        if (StringUtils.isNotEmpty(request.getStoreId())) {
            orderQueryRequest.setStoreId(request.getStoreId());
        }
        if (StringUtils.isNotEmpty(request.getMerchantId())) {
            orderQueryRequest.setMerchantId(request.getMerchantId());
        }
        orderQueryRequest.setUserId(ThreadLocalHelper.getUserId());
        PageDTO pageDTO = new PageDTO();
        pageDTO.setPage(request.getPage());
        pageDTO.setPageSize(request.getPageSize());
        orderQueryRequest.setPageDTO(pageDTO);
        PaginatedResult<OrderDTO> orders = orderService.getOrders(orderQueryRequest);

        PaginatedResult<Order> result = new PaginatedResult<>();
        result.setTotal(orders.getTotal());
        result.setPage(orders.getPage());
        result.setPageSize(orders.getPageSize());
        result.setRecords(Optional.ofNullable(orders)
                .map(PaginatedResult::getRecords)
                .map(records -> records.stream()
                        .map(EntityConvert::convertOrderDTO)
                        .collect(Collectors.toList()))
                .orElse(null));


        if (CollectionUtils.isNotEmpty(result.getRecords())){
            List<String> snList = result.getRecords().stream().map(item -> item.getSn()).collect(Collectors.toList());

            List<OrderRefundApplyDTO> applyDTOS = orderRefundApplyService.selectApplysBySnList(snList);
            if (!org.springframework.util.CollectionUtils.isEmpty(applyDTOS)) {
                Map<String, OrderRefundApplyDTO> applyDTOMap = applyDTOS.stream().collect(Collectors.toMap(OrderRefundApplyDTO::getSn, apply -> apply));

                for (Order order : result.getRecords()) {
                    OrderRefundApplyDTO applyDTO = applyDTOMap.get(order.getSn());
                    if (applyDTO != null) {
                        order.setRefundApply(applyDTO);
                    }
                }
            }
        }


        if (StringUtils.isNotBlank(request.getMealType())) {
            if (Objects.equals(request.getMealType(), MealTypeValueEnum.round_meal.getCode())) {
                //是围餐， 这种情况，要展示下这个桌子上已经存在，但不是自己下的订单
                TableQueryRequest tableQueryRequest = new TableQueryRequest();
                tableQueryRequest.setTableId(request.getTableId());
                Result<String> tableResult = tableRpcService.getTableOrderNo(tableQueryRequest);

                if (tableResult.isSuccess()) {
                    String sn = tableResult.getData();
                    if (StringUtils.isNotBlank(sn)) {
                        List<Order> orderRecords = result.getRecords();
                        boolean needAddOrder = false;
                        if (CollectionUtils.isNotEmpty(orderRecords)) {
                            if (!Objects.equals(orderRecords.get(0).getSn(), sn)) {
                                needAddOrder = true;
                            }
                        } else {
                            needAddOrder = true;
                        }

                        if (needAddOrder) {
                            Order order = EntityConvert.convertOrderDTO(orderService.getOrderBySn(sn));
                            List<Order> finalList = new ArrayList<>();
                            finalList.add(order);
                            if (CollectionUtils.isNotEmpty(orderRecords)) {
                                finalList.addAll(orderRecords);
                                result.setTotal(result.getTotal());
                            } else {
                                result.setTotal(1);
                            }
                            result.setRecords(finalList);
                        }

                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(result.getRecords())) {
            //先付围餐的一些状态展示是比较特殊的，这里特殊处理下
            result.getRecords().stream().forEach(order -> {
                if (StringUtils.equals(order.getType(), OrderType.PAY_FIRST_TABLE_ORDER.getMsg())) {
                    List<CartItemCreate> items = order.getItems();
                    if (!StringUtils.equals(order.getProcessStatus(),OrderProcessStatusEnum.COMPLETED.getCode())
                            && !StringUtils.equals(order.getProcessStatus(),OrderProcessStatusEnum.CLOSED.getCode())
                            && !StringUtils.equals(order.getProcessStatus(),OrderProcessStatusEnum.REVOKED.getCode())){
                        order.setEffectiveAmount(order.getOriginalAmount());
                        if (CollectionUtils.isNotEmpty(items)) {
                            boolean isAllAcceptFailed = true;
                            boolean isAllWaitAccepted = true;
                            boolean isAllNotAccept = true;
                            Long orderUnAcceptedGoodsTotalAmount = 0L;
                            for (CartItemCreate item : items) {
                                if (item.getProcessStatus() == GoodsProcessStatus.NOT_ACCEPTED) {
                                    orderUnAcceptedGoodsTotalAmount = item.getItem().getNumberDecimal().multiply(new BigDecimal(item.getItem().getPrice())).setScale(0, RoundingMode.HALF_UP).longValue();
                                }
                                if (item.getProcessStatus() == GoodsProcessStatus.ACCEPT_FAILED){
                                    continue;
                                }
                                if (item.getProcessStatus() != GoodsProcessStatus.NOT_ACCEPTED  ) {
                                    isAllNotAccept = false;
                                }
                                if (item.getProcessStatus() != GoodsProcessStatus.PAYED_BUT_NOT_ACCEPTED){
                                    isAllWaitAccepted = false;
                                }
                                if (item.getProcessStatus() != GoodsProcessStatus.ACCEPT_FAILED && item.getProcessStatus() != GoodsProcessStatus.CANCELED ) {
                                    isAllAcceptFailed = false;
                                }
                            }
                            if (isAllAcceptFailed) {
                                order.setStatus(OrderStatus.ACCEPT_FAILED.getCode());
                                order.setProcessStatus(OrderProcessStatusEnum.ACCEPT_FAILED.getCode());
                            }else {
                                if (isAllNotAccept) {
                                    order.setStatus(OrderStatus.CREATED.getCode());
                                    order.setProcessStatus(OrderProcessStatusEnum.INIT.getCode());
                                }
                                if (isAllWaitAccepted) {
                                    order.setStatus(OrderStatus.CREATED.getCode());
                                    order.setProcessStatus("WAIT_ACCEPT");
                                }
                            }

                            order.setOriginalAmount(order.getOriginalAmount() + orderUnAcceptedGoodsTotalAmount);
                            order.setEffectiveAmount(order.getEffectiveAmount() + orderUnAcceptedGoodsTotalAmount);
                        }
                    }
                }

            });
        }
        String acceptLanguage = HttpRequestUtil.getAcceptLanguage(httpServletRequest);
        List<Order> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        translationConvertService.convertStoreOrderList(records, request.getStoreId(), acceptLanguage);
        return result;
    }



    @Override
    public Order findLatestOne(ApiRequest<OrderFind> apiRequest) {
        OrderFind request = apiRequest.getBody();

        OrderQueryRequest orderQueryRequest = new OrderQueryRequest();
        orderQueryRequest.setOrderTypes(Arrays.asList(OrderType.SUBSCRIBE_ORDER));

        if (StringUtils.isNotEmpty(request.getStoreId())) {
            orderQueryRequest.setStoreId(request.getStoreId());
        }
        orderQueryRequest.setUserId(ThreadLocalHelper.getUserId());
        //要40分钟以内的订单
        orderQueryRequest.setDateStart(System.currentTimeMillis() - 40 * 60 * 1000);
        orderQueryRequest.setStatuses(Arrays.asList(OrderStatus.RECEIVED));


        PageDTO pageDTO = new PageDTO();
        pageDTO.setPage(1);
        pageDTO.setPageSize(1);
        orderQueryRequest.setPageDTO(pageDTO);
        PaginatedResult<OrderDTO> orders = orderService.getOrders(orderQueryRequest);

        List<Order> orderList = Optional.ofNullable(orders)
                .map(PaginatedResult::getRecords)
                .map(records -> records.stream()
                        .map(EntityConvert::convertOrderDTO)
                        .collect(Collectors.toList()))
                .orElse(null);
        if (CollectionUtils.isNotEmpty(orderList)) {
            return orderList.get(0);
        }
        return null;

    }



    @Override
    public Order getBySn(ApiRequest<Map> apiRequest) {
        Map request = apiRequest.getBody();
        return EntityConvert.convertOrderDTO(orderService.getOrderBySn(BeanUtil.getPropString(request, SN)));
    }

    @Override
    public Order getByTransSn(ApiRequest<Map> apiRequest) {
        Map request = apiRequest.getBody();
        return EntityConvert.convertOrderDTO(orderService.getOrderByTransSn(BeanUtil.getPropString(request, TRANS_SN)));
    }

    @Override
    public CartCheckResultDTO orderAgain(ApiRequest apiRequest) {

        Map queryParam = apiRequest.getQuery();

        String sn = MapUtils.getString(queryParam, "sn");

        if (StringUtils.isBlank(sn)){
            throw new ParamException("订单信息不能为空");
        }

        OrderDTO orderDTO = orderService.getOrderBySn(sn);
        if (orderDTO == null) {
            throw new BusinessException(ReturnCode.ORDER_AGAIN_CANNOT_SUPPORT_ERROR);
        }
        int serviceType = CommonUtil.getServiceType(orderDTO.getOrderType());
        // 清空购物车
        cartService.resetCart(orderDTO.getStoreId(), serviceType, true);

        CartCheckResultDTO cartCheckResult = new CartCheckResultDTO();
        List<CartCheckResultDTO.Item> invalid = new ArrayList<>();
        List<CartCheckResultDTO.Item> price = new ArrayList<>();
        if (orderDTO.getItems().stream().anyMatch(it -> "love_feast".equalsIgnoreCase(it.getItemId()))) {
            cartCheckResult.setSuccess(true);
            cartCheckResult.setPrice(price);
            cartCheckResult.setInvalid(invalid);
            return cartCheckResult;
        }
        for (OrderItemDTO i : orderDTO.getItems()) {// 查询商品详情

            if (i.getSpuType() == SpuType.PACKAGE) {
                cartService.resetCart(orderDTO.getStoreId(), serviceType, true);
                throw new BusinessException(ReturnCode.ORDER_AGAIN_CANNOT_SUPPORT_ERROR);
            }
            String photoUrl = null;
            ItemDto itemDto = itemHelper.getItemDtoById(orderDTO.getStoreId(), i.getItemId(), serviceType);
            if (Objects.isNull(itemDto)) {
                invalid.add(new CartCheckResultDTO.Item(i.getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                continue;
            }
            if (ProductTypeEnum.PACKAGE.name().equals(itemDto.getItem().getItemType())){
                cartService.resetCart(orderDTO.getStoreId(), serviceType, true);
                throw new BusinessException(ReturnCode.ORDER_AGAIN_CANNOT_SUPPORT_ERROR);
            }

            if (itemDto != null) {
                if (StringUtil.isNotBlank(itemDto.getItem().getPhotoUrl())) {
                    String[] arr = itemDto.getItem().getPhotoUrl().split(",");
                    photoUrl = arr[0];
                }
            }

            // 商品售卖类型发生变更，不可复购。这里做转化即可0=计件 1=称重
            int oldUnitType = GoodsUnitTypeEnum.WEIGHT == i.getUnitType() ? 1 : 0;
            if (!Objects.equals(oldUnitType, itemDto.getItem().getUnitType())) {
                invalid.add(new CartCheckResultDTO.Item(i.getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                continue;
            }

            // 由于零售支持称重商品按件转化售卖，这里需要判断转化关系是否还成立
            if (itemDto instanceof RetailExtendItemDTO) {
                Map<Object, Object> extraInfo = i.getExtraInfo();
                Map skuConvert = MapUtils.getMap(extraInfo, "sku_convert");
                //零售商品需要校验转换关系是否未发生变化
                if (((RetailExtendItemDTO) itemDto).getSkuConvert() != null || skuConvert !=null) {
                    if (!Objects.equals(
                            MapUtils.getDouble(((RetailExtendItemDTO) itemDto).getSkuConvert(), "per_takeout_weight", 0d),
                            MapUtils.getDouble(skuConvert, "per_takeout_weight", 0d))) {
                        continue;
                    }
                    // 称重转化单位发生变化，不处理
                    String oldTakeoutUnit = MapUtils.getString(skuConvert, "takeout_unit");
                    String newTakeoutUnit = MapUtils.getString(((RetailExtendItemDTO) itemDto).getSkuConvert(), "takeout_unit");
                    if (!StringUtils.equals(oldTakeoutUnit, newTakeoutUnit)) {
                        continue;
                    }
                }
            }

            if (!Optional.ofNullable(itemDto.getItem()).map(Item::getSaleTerminals).orElseGet(Lists::newArrayList).contains(Constants.SaleTerminal.MINI)) {
                // 不在售卖终端里
                invalid.add(new CartCheckResultDTO.Item(itemDto.getItem().getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                continue;
            }

            if (!ItemHelper.curTimeForSale(itemDto)) {
                //当前时段不可售
                invalid.add(new CartCheckResultDTO.Item(itemDto.getItem().getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                continue;
            }

            if (itemDto.getItem().getMinSaleNum() != null) {
                if (i.getCount().intValue() < itemDto.getItem().getMinSaleNum()) {
                    //小于最低可售数量
                    invalid.add(new CartCheckResultDTO.Item(itemDto.getItem().getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                    continue;
                }
            }

            if (itemDto == null || !itemDto.getItem().getForSale()
                    || itemDto.getItem().getOutOfStock()
                    || (serviceType != 0 && itemDto.getItem().getServiceType() != 0 && itemDto.getItem().getServiceType() != serviceType)) {// 商品失效
                invalid.add(new CartCheckResultDTO.Item(i.getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                continue;
            }
            CartItemCreate create = new CartItemCreate();
            create.setServiceType(serviceType);
            create.setStoreId(orderDTO.getStoreId());
            create.setCtime(System.currentTimeMillis());
            CartItemCreate.Item item = new CartItemCreate.Item();
            item.setId(itemDto.getItem().getId());
            item.setName(itemDto.getItem().getName());
            item.setCategoryId(itemDto.getItem().getCategoryId());
            item.setNumber(i.getCount().intValue());
            item.setPhotoUrl(photoUrl);
            item.setUrl(photoUrl);
            item.setCategorySort(itemDto.getItem().getCategorySort());
            item.setDisplayOrder(itemDto.getItem().getDisplayOrder());
            Integer totalPrice = itemDto.getItem().getPrice();
            if (CollectionUtil.isNotEmpty(itemDto.getSpecs())) {
                if (i.getSpecInfo() != null) {
                    // 处理规格数据，根据订单商品中的规格ID查询最新的规格信息

                    ItemSpec itemSpec = itemDto.getSpecs().stream().filter(s -> i.getSpecInfo().getId().equalsIgnoreCase(s.getId())).findFirst().orElse(null);
                    if (itemSpec == null) {
                        // 商品失效
                        invalid.add(new CartCheckResultDTO.Item(i.getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                        continue;
                    }
                    CartItemCreate.Spec spec = new CartItemCreate.Spec();
                    spec.setId(itemSpec.getId());
                    spec.setName(itemSpec.getName());
                    spec.setPrice(itemSpec.getPrice());
                    create.setSpec(spec);
                    totalPrice = itemSpec.getPrice();
                } else {
                    // 商品失效
                    invalid.add(new CartCheckResultDTO.Item(i.getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                    continue;
                }
            } else {
                if (i.getSpecInfo() != null) {
                    // 商品失效
                    invalid.add(new CartCheckResultDTO.Item(i.getName(), photoUrl, Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID));
                    continue;
                }
            }

            if (CollectionUtil.isNotEmpty(i.getAttributeInfos()) && CollectionUtil.isNotEmpty(itemDto.getAttributes())) {
                // 处理属性，匹配每一组里合适的值
                List<String> oldAttrIds = i.getAttributeInfos().stream().map(Attribute::getId).collect(Collectors.toList());
                List<AttributeOptionDto> newAttrs = Lists.newArrayList();
                for (AttributeDto attr : itemDto.getAttributes()) {
                    List<AttributeOptionDto> options = attr.getOptions();
                    if (CollectionUtils.isEmpty(options)) {
                        continue;
                    }
                    List<AttributeOptionDto> filterOptions = options.stream()
                            .filter(p -> oldAttrIds.contains(p.getId()))
                            .collect(Collectors.toList());
                    // 没有能匹配上的，且必选则默认选第一个
                    if (CollectionUtils.isEmpty(filterOptions) && Objects.equals(attr.getMust(), 1)) {
                        newAttrs.add(options.get(0));
                    }
                    // 支持多选的都选上，不支持的选一个
                    if (CollectionUtils.isNotEmpty(filterOptions)) {
                        if (Objects.equals(attr.getMultiple(), 1)) {
                            newAttrs.addAll(filterOptions);
                        } else {
                            newAttrs.add(filterOptions.get(0));
                        }
                    }
                }
                create.setAttributes(newAttrs.stream().map(p -> {
                    CartItemCreate.Attribute cartAttr = new CartItemCreate.Attribute();
                    cartAttr.setId(p.getId());
                    cartAttr.setName(p.getName());
                    return cartAttr;
                }).collect(Collectors.toList()));
            }

            if (CollectionUtil.isNotEmpty(i.getMaterials()) && CollectionUtil.isNotEmpty(itemDto.getMaterials())) {
                // 处理加料数据，根据订单商品中的加料ID查询最新的加料信息
                List<CartItemCreate.Material> materials = new ArrayList<>();
                i.getMaterials().forEach(m -> {
                    Material itemMaterial = itemDto.getMaterials().stream()
                            .filter(ma -> ma.getId().equalsIgnoreCase(m.getId()) && Objects.equals(ma.getStatus(), 1))
                            .findFirst().orElse(null);
                    if (itemMaterial != null) {
                        CartItemCreate.Material material = new CartItemCreate.Material();
                        material.setId(itemMaterial.getId());
                        material.setName(itemMaterial.getName());
                        material.setNumber(m.getNumber());
                        material.setPrice(Long.valueOf(itemMaterial.getPrice()));
                        materials.add(material);
                    }
                });
                totalPrice += materials.stream().mapToInt(m -> m.getNumber() * m.getPrice().intValue()).sum();
                create.setMaterials(materials);
            }
            item.setPrice(totalPrice);
            create.setItem(item);
            cartService.addCart(create, OrderMealTypeEnum.SINGLE, DealTypeEnum.ADD);

        }
        //加价购自动加购
        cartService.processBrandAutoCarts(null, orderDTO.getStoreId(), serviceType);
        cartCheckResult.setSuccess(CollectionUtil.isEmpty(invalid) && CollectionUtil.isEmpty(price));
        cartCheckResult.setInvalid(invalid);
        cartCheckResult.setPrice(price);
        return cartCheckResult;
    }


    @Override
    public Qrcode getPickUpMealsQrCodeBySn(ApiRequest<Map> apiRequest) {
        Map request = apiRequest.getBody();
        String sn = MapUtils.getString(request, SN);
        if (StringUtils.isBlank(sn)) {
            throw new ParamException("sn不能为空");
        }

        String content = OrderUtil.getOrderBusinessCode(sn, OrderQrCodeTypeEnum.TAKEN, com.wosai.pantheon.order.constant.Constants.ORDER_CODE_SEPARATOR);
        String qrcode = orderQrCodePainterRemoteService.paintWithoutCache(PaintQrCodeRequest.builder()
                .content(content)
                .width(PICK_UP_MEALS_QR_CODE_WIDTH)
                .height(PICK_UP_MEALS_QR_CODE_HEIGHT)
                .build());
        return Qrcode.builder()
                .content(content)
                .qrcode(qrcode)
                .build();
    }
}
