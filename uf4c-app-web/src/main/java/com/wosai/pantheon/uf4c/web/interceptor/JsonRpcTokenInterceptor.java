package com.wosai.pantheon.uf4c.web.interceptor;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.JsonRpcInterceptor;
import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.apisix.Client;
import com.wosai.pantheon.uf4c.apisix.UserContext;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.SpringContextUtil;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.UnauthorizedException;
import com.wosai.pantheon.uf4c.web.exception.UnknownMiniProgramTypeException;
import com.wosai.pantheon.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ObjectUtils;
import org.springframework.util.PathMatcher;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class JsonRpcTokenInterceptor implements JsonRpcInterceptor {

    private PathMatcher pathMatcher = new AntPathMatcher();

    private final List<String> excludePatterns = new ArrayList();

    private final List<String> includePatterns = new ArrayList<String>();


    public static final Config apolloConfig = ConfigService.getAppConfig();

    public ApolloConfigHelper apolloConfigHelper;


    public boolean matches(String lookupPath) {
        PathMatcher pathMatcherToUse = pathMatcher;
        if (!ObjectUtils.isEmpty(this.excludePatterns)) {
            for (String pattern : this.excludePatterns) {
                if (pathMatcherToUse.match(pattern, lookupPath)) {
                    return false;
                }
            }
        }
        if (ObjectUtils.isEmpty(this.includePatterns)) {
            return true;
        }
        for (String pattern : this.includePatterns) {
            if (pathMatcherToUse.match(pattern, lookupPath)) {
                return true;
            }
        }
        return false;
    }


    public JsonRpcTokenInterceptor addPathPatterns(String... patterns) {
        this.includePatterns.addAll(Arrays.asList(patterns));
        return this;
    }

    public JsonRpcTokenInterceptor excludePathPatterns(String... patterns) {
        this.excludePatterns.addAll(Arrays.asList(patterns));
        return this;
    }

    public boolean matches(Method method) {
        String uri = method.getDeclaringClass().getAnnotation(JsonRpcService.class).value() + "/" + method.getName();

        return matches(uri);
    }

    @Override
    public void preHandleJson(JsonNode json) {

    }

    @Override
    public void preHandle(Object target, Method method, List<JsonNode> params) {
    }

    @Override
    public void postHandle(Object target, Method method, List<JsonNode> params, JsonNode result) {

    }

    @Override
    public void postHandleJson(JsonNode json) {

    }

    @Override
    public void preCallMethod(Object target, Method method, Object... argument) {
        if (apolloConfigHelper == null) {
            apolloConfigHelper = SpringContextUtil.getBean(ApolloConfigHelper.class);
        }
        if (!matches(method)) {
            return;
        }
        for (Object arg : argument) {
            if (arg instanceof ApiRequest) {
                ApiRequest request = (ApiRequest) arg;

                UserContext user = request.getUser();
                Client client = request.getClient();
                if (user == null || client == null) {
                    throw new UnauthorizedException();
                }

                // 黑名单用户校验
                String userBlacklist = apolloConfig.getProperty("user.blacklist", "");
                if (StringUtil.isNotBlank(userBlacklist)
                        && StringUtil.isNotBlank(user.getThirdpartyUserId())
                        && userBlacklist.contains(user.getThirdpartyUserId())) {
                    LogUtils.logWarn("命中用户黑名单", "UserBlacklist", user);
                    throw new UnauthorizedException();
                }


                if (StringUtil.isBlank(user.getThirdpartyUserId())
                        || StringUtil.isBlank(user.getUserId())) {
                    throw new UnauthorizedException();
                }

                String miniProgramType = user.getMiniProgramType();
                if (StringUtil.isEmpty(miniProgramType)
                        || !EnumUtils.isValidEnum(MiniProgramType.class, miniProgramType)) {
                    throw new UnknownMiniProgramTypeException();
                }

                ThreadLocalHelper.getRequestContextThreadLocal().get()
                        .setScene(user.getScene());

                ThreadLocalHelper.getRequestContextThreadLocal().get()
                        .setMiniProgramType(MiniProgramType.valueOf(miniProgramType));

                ThreadLocalHelper.getRequestContextThreadLocal().get()
                        .setRealIp(
                                client.getIp()
                        );

                ThreadLocalHelper.getRequestContextThreadLocal().get()
                        .setUserContextDTO(user);

                try{
                    ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    Map<String, String> payHeaders = new HashMap<String, String>();
                    apolloConfigHelper.getPayHeaderConfigMap().forEach((key, value) -> {
                        String headerValue = requestAttributes.getRequest().getHeader(key);
                        if(StringUtils.isNotEmpty(headerValue) && StringUtils.isNotEmpty(value)){
                            payHeaders.put(value, headerValue);
                            log.info("Wosai-Pay-Headers:{}", keyValue("payHeader", value), keyValue("value", headerValue));
                        }
                    });
                    ThreadLocalHelper.getRequestContextThreadLocal().get()
                                .setPayHeaders(payHeaders);
                }catch(Exception e){
                    log.error("JsonRpcTokenInterceptor.preCallMethod error", keyValue("method", "getPayHeaders"), e);
                }

            }
        }
    }

    @Override
    public void afterCallMethod(Object target, Method method, Object result, Object... argument) {

    }
}
