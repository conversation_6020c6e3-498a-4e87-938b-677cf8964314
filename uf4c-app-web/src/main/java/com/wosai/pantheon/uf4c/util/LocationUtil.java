package com.wosai.pantheon.uf4c.util;

/**
 * <AUTHOR>
 * @date 2020/3/20
 */
public class LocationUtil {

    public static double EARTH_RADIUS = 6378.137;

    public static int getDistance(double lat1, double lng1, double lat2, double lng2) {
        lat1 = rad(lat1);
        lat2 = rad(lat2);
        double lat = lat1 - lat2;
        double lon = rad(lng1) - rad(lng2);
        double distance = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(lat / 2), 2)
                + Math.cos(lat1) * Math.cos(lat2)
                * Math.pow(Math.sin(lon / 2), 2)));
        distance = distance * EARTH_RADIUS;
        return (int) Math.ceil(distance);
    }

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }


    public static void main(String[] args) {
        System.out.println(getDistance(30.228052,120.19236, 30.236944,120.17095));
    }

}
