package com.wosai.pantheon.uf4c.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * Created by xuchmao on 18/7/25.
 */
@Slf4j
public class HttpRequestUtil {
    public static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    public static HttpSession getSession() {
        HttpServletRequest servletRequest = getRequest();
        return servletRequest.getSession();
    }

    public static String getAcceptLanguage(HttpServletRequest request) {
        try {
            if (null == request) {
                return null;
            }
            return request.getHeader("X-Smart-Accept-Language");
        } catch (Exception e) {
            return null;
        }
    }
}
