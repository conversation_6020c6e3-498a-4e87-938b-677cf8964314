package com.wosai.pantheon.uf4c.gather;

import com.ctrip.framework.apollo.Config;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.market.user.dto.LoginRequest;
import com.wosai.market.user.dto.LoginResponseDTO;
import com.wosai.market.user.service.UserService;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class GatherUserService extends GatherBase {
    @Autowired
    private UserService userService;

    @Autowired
    private Config appConfig;

    private static final String PLATFORM_APP_IDS_KEY = "platformAppIds";

    /**
     * 用户登录
     *
     * @param request
     * @param dataMap
     */
    public void login(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "login");
        try {
            LoginResponseDTO loginResponseDTO = null;
            // 商家小程序优先使用userId和appId、openId登录
            if (request.needLogin() && StringUtils.isNoneBlank(request.getLoginUserId(), request.getAppid(), request.getThirdPartUserId()) && isMmp(request.getAppid())) {
                loginResponseDTO = loginByCode(request, dataMap);
            }
            // 其次使用token直接插用户信息，不用登录
            if (Objects.isNull(loginResponseDTO) && StringUtils.isNotBlank(request.getToken())) {
                loginResponseDTO = loginByToken(request, dataMap);
            }
            // 最后尝试使用authCode登录，代码看起来重复，但是逻辑如此
            if (request.needLogin() && Objects.isNull(loginResponseDTO)) {
                loginResponseDTO = loginByCode(request, dataMap);
            }
            if (Objects.nonNull(loginResponseDTO)) {
                dataMap.put("login", JacksonUtil.beanToMap(loginResponseDTO, new ObjectMapper()));
                request.setUserId(loginResponseDTO.getUserId());
                request.setThirdPartUserId(loginResponseDTO.getThirdpartyUserId());
            }
        } catch (Exception e) {
            logWarn("登录失败：" + e.getMessage(), "GatherLogin", request, e);
            setError(dataMap, ReturnCode.LOGIN_ERROR);
        } finally {
            request.recentCountDown();
            durationEnd(dataMap, "login");
        }
    }

    /**
     * 是否商家小程序
     *
     * @param appId
     * @return
     */
    private boolean isMmp(String appId) {
        if (org.apache.commons.lang.StringUtils.isBlank(appId)) {
            return false;
        }
        String[] platformAppIds = appConfig.getArrayProperty(PLATFORM_APP_IDS_KEY, ",", new String[]{});
        List<String> platformAppIdList = Arrays.asList(platformAppIds);
        if (CollectionUtils.isEmpty(platformAppIdList)) {
            return false;
        }
        return !platformAppIdList.contains(appId);
    }

    /**
     * 根据token获取用户信息
     *
     * @param request
     * @param dataMap
     * @return
     */
    private LoginResponseDTO loginByToken(GatherRequest request, Map<String, Object> dataMap) {
        LoginResponseDTO loginResponseDTO = null;
        try {
            logInfo("根据token获取用户", "GatherLoginByToken", request);
            loginResponseDTO = userService.getUserInfo(request.getToken(), request.getMiniProgramType());
        } catch (Exception e) {
            logWarn("根据token获取用户失败：" + e.getMessage(), "GatherLoginByToken", request, e);
            setError(dataMap, ReturnCode.LOGIN_ERROR);
        }
        return loginResponseDTO;
    }

    /**
     * 根据微信AuthCode获取用户信息
     *
     * @param request
     * @param dataMap
     * @return
     */
    private LoginResponseDTO loginByCode(GatherRequest request, Map<String, Object> dataMap) {
        LoginResponseDTO loginResponseDTO = null;
        logInfo("用户登录", "GatherLoginByCode", request);
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setAuthCode(request.getCode());
        loginRequest.setMiniProgramType(request.getMiniProgramType());
        loginRequest.setExternalSource(null);
        loginRequest.setScene(request.getScene());
        loginRequest.setAppId(request.getAppid());
        loginRequest.setThirdPartUserId(request.getThirdPartUserId());
        loginRequest.setWxUnionId(StringUtils.isNotBlank(request.getWxUnionId()) ? request.getWxUnionId() : request.getUnionid());
        loginRequest.setUserId(request.getLoginUserId());
        try {
            loginResponseDTO = userService.login(loginRequest);
        } catch (Exception e) {
            logWarn("登录失败：" + e.getMessage(), "GatherLoginByCode", request, e);
            setError(dataMap, ReturnCode.LOGIN_ERROR);
        }
        return loginResponseDTO;
    }

}
