package com.wosai.pantheon.uf4c.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
public class LogUtils {
    public static void logError(String message, String method, Object request, Exception e) {
        log.error(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : ""),
                e);
    }

    public static void logInfo(String message, String method, Object request) {
        log.info(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : "")
        );
    }

    public static void logWarn(String message, String method, Object request) {
        log.warn(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : "")
        );
    }

    public static void logWarn(String message, String method, Object request, Exception e) {
        log.warn(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : ""), e
        );
    }

    public static void logWarnWithError(String message, String method, Object request, Exception e) {
        log.warn(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : ""),
                e
        );
    }
}
