package com.wosai.pantheon.uf4c.service;


import com.shouqianba.smart.translation.manager.common.utils.LogUtils;
import com.wosai.market.dto.ProductSku;
import com.wosai.market.dto.ProductSpu;
import com.wosai.market.dto.product.ProductDetail;
import com.wosai.market.enums.ProductTypeEnum;
import com.wosai.market.enums.SaleSceneEnum;
import com.wosai.market.enums.SaleTimeTypeEnum;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.tethys.api.dto.CategoryInfo;
import com.wosai.market.tethys.api.dto.SkuInfo;
import com.wosai.market.tethys.api.dto.request.*;
import com.wosai.market.tethys.api.enums.DiscountsEnum;
import com.wosai.market.tethys.api.enums.EffectiveType;
import com.wosai.market.tethys.api.enums.OrderType;
import com.wosai.market.tethys.api.modal.ActivityPriceConstant;
import com.wosai.market.tethys.api.service.ActivityRemoteService;
import com.wosai.market.tethys.api.service.SecondActivityRemoteService;
import com.wosai.market.tethys.api.service.SingleActivityRemoteService;
import com.wosai.pantheon.core.uitem.model.*;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.OrderGoodsTagEnum;
import com.wosai.pantheon.order.enums.SpuType;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.constant.ItemDiscountTypeEnum;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.dto.RetailExtendItemDTO;
import com.wosai.pantheon.uf4c.model.item.RetailItemDTO;
import com.wosai.pantheon.uf4c.model.vo.PackageOptionalGroup;
import com.wosai.pantheon.uf4c.model.vo.*;
import com.wosai.pantheon.uf4c.service.item.retail.IRetailItemAdapterService;
import com.wosai.pantheon.uf4c.util.CartHelper;
import com.wosai.pantheon.uf4c.util.*;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.smart.goods.common.constant.ProductConstant;
import com.wosai.smartbiz.base.mybatis.page.Page;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.gds.enums.ErrorCodeEnum;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.checkerframework.checker.nullness.Opt;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * created by shij on 2019/4/16
 */
@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Service
@Slf4j
public class ItemHelper {

    private static final String HOTSALE_REDIS_KEY_PRE = "com.uf4c.hotsale:";

    @Autowired
    private SingleActivityRemoteService singleActivityRemoteService;

    @Autowired
    private SecondActivityRemoteService secondActivityRemoteService;

    @Autowired
    private ActivityRemoteService activityRemoteService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private ItemService uitemCoreitemService;

    @Autowired
    private StoreHelper storeHelper;

    @Autowired
    private IRetailItemAdapterService retailItemAdapterService;

    private HashOperations<String, String, HotSaleItem> hashOperations;

    @Value("${hotsale.cache.minutes:60}")
    private Integer hotsaleItemCacheMinutes;

    @PostConstruct
    public void postConstruct() {
        hashOperations = redisTemplate.opsForHash();
    }

    private String getHotSaleReidsKey(String storeId, Integer serviceType) {
        return HOTSALE_REDIS_KEY_PRE + storeId + ":" + serviceType;
    }


    public Map<String, HotSaleItem> hotSaleItemMapFromCache(String storeId, Integer serviceType) {
        return hashOperations.entries(getHotSaleReidsKey(storeId, serviceType));
    }

    public void saleHostSaleItemMap(String storeId, Integer serviceType, Map<String, HotSaleItem> entries) {
        hashOperations.putAll(getHotSaleReidsKey(storeId, serviceType), entries);
        redisTemplate.expire(getHotSaleReidsKey(storeId, serviceType), hotsaleItemCacheMinutes, TimeUnit.MINUTES);
    }

    public ItemDto getItemDtoById(String storeId, String itemId, Integer serviceType) {
        if (StringUtils.isAnyBlank(storeId, itemId)) {
            return null;
        }
        if (storeHelper.isRetailStore(storeId)) {
            Map<String, RetailItemDTO> map = retailItemAdapterService.queryItemBySpuIds(storeId, Lists.newArrayList(itemId));
            RetailItemDTO retailItemDTO = map.getOrDefault(itemId, null);
            return retailItemDTO == null ? null : retailItemDTO.exportAsCatering(serviceType, storeId);
        } else {
            serviceType = Optional.ofNullable(serviceType).orElse(SaleSceneEnum.ALL.getServiceType());
            SingleItemDetailRequest request = new SingleItemDetailRequest();
            request.setStoreId(storeId);
            request.setItemId(itemId);
            request.setServiceType(serviceType);
            request.setFillPrintTemplate(false);
            return uitemCoreitemService.getItemDetailV3(request);
        }
    }

    public ListResult<ItemDto> getItemDtoByIds(String storeId, List<String> itemIds, Integer serviceType) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return ListResult.emptyResult();
        }
        if (storeHelper.isRetailStore(storeId)) {
            Map<String, RetailItemDTO> map = retailItemAdapterService.queryItemBySpuIds(storeId, itemIds);
            if (MapUtils.isEmpty(map)) {
                return ListResult.emptyResult();
            }
            List<ItemDto> itemDtoList = map.values().stream().map(retailItemDTO -> retailItemDTO.exportAsCatering(serviceType, storeId)).collect(Collectors.toList());
            return new ListResult<>(itemDtoList);
        } else {
            return uitemCoreitemService.findItemDetailsById(storeId, itemIds);
        }
    }

    public ItemDetailVO processItemDetail(ItemDto itemDto, Map<String, HotSaleItem> hotSaleItemMap) {
        return processItemDetail(itemDto, false, hotSaleItemMap);
    }

    public ItemDetailVO processItemDetail(ItemDto itemDto, boolean isPackageGoods, Map<String, HotSaleItem> hotSaleItemMap) {
        if (itemDto == null) {
            throw new BusinessException(ReturnCode.ITEM_NOT_FOR_SALE_2);
        }
        Item item = itemDto.getItem();
        // convert item
        ItemDetailVO detailVO = new ItemDetailVO();
        ItemVO itemVO = new ItemVO();
        detailVO.setItem(itemVO);

        BeanUtils.copyProperties(item, itemVO, "images");
        // 图片字段单独映射
        List<ItemImageVO> imageVos = Optional.ofNullable(item.getImages())
                .orElseGet(ArrayList::new)
                .stream()
                .map(image -> {
                    ItemImageVO imageVO = new ItemImageVO();
                    imageVO.setDisplayUrl(image.getDisplayUrl());
                    List<ItemImageVO.Spec> specVos = Optional.ofNullable(image.getSpecs())
                            .orElseGet(ArrayList::new)
                            .stream()
                            .map(spec -> {
                                ItemImageVO.Spec specVO = new ItemImageVO.Spec();
                                specVO.setType(spec.getType());
                                specVO.setUrl(spec.getUrl());
                                return specVO;
                            }).collect(Collectors.toList());
                    imageVO.setSpecs(specVos);
                    return imageVO;
                })
                .collect(Collectors.toList());
        itemVO.setImages(imageVos);

        // 其他字段
        itemVO.setSpuType(com.wosai.pantheon.uf4c.util.OrderHelper.getNotNullSpuType(item.getItemType()).name());
        itemVO.setAddCount(itemDto.getAddCount());
        if (itemDto instanceof RetailExtendItemDTO) {
            List<String> categoryPath = ((RetailExtendItemDTO) itemDto).getCategoryPath();
            if (!CollectionUtils.isEmpty(categoryPath)) {
                itemVO.setCategoryId(categoryPath.get(0));
                if (categoryPath.size() > 1) {
                    itemVO.setSubCategoryId(categoryPath.get(1));
                }
            }
        }
        appendHotSaleItemInfo(detailVO, hotSaleItemMap);

        itemVO.setIsMultiple(item.getSpecNum());

        // 套餐商品需要默认的描述内容
        if (ProductTypeEnum.PACKAGE.name().equals(item.getItemType()) && org.apache.commons.lang3.StringUtils.isBlank(item.getDescription())) {
            itemVO.setDescription(this.generateDescription(itemDto.getPackageMustOrderProducts(), itemDto.getPackageOptionalGroups()));
        }

        // convert spec
        List<ItemSpec> specs = itemDto.getSpecs();

        if (!CollectionUtils.isEmpty(specs)) {
            specs.sort(Comparator.comparingInt(ItemSpec::getPrice));
            itemVO.setPrice(specs.get(0).getPrice());
            specs.sort(Comparator.comparingInt(ItemSpec::getSeq));
            List<SpecOptionVO> options = new ArrayList<>();

            for (ItemSpec spec : specs) {
                SpecOptionVO option = new SpecOptionVO();
                BeanUtils.copyProperties(spec, option);
                options.add(option);
            }

            if (!CollectionUtils.isEmpty(options)) {
                ItemSpecVO specVO = new ItemSpecVO();
                specVO.setTitle(specs.get(0).getTitle());
                specVO.setOptions(options);
                detailVO.setSpecs(specVO);
            }

        }

        if (!isPackageGoods && itemDto.getPackageMustOrderProducts() != null) {
            detailVO.setPackageMustOrderProducts(itemDto.getPackageMustOrderProducts().stream().map(mustOrerProduct -> processItemDetail(mustOrerProduct, true, hotSaleItemMap)).collect(Collectors.toList()));
        }
        if (!isPackageGoods && itemDto.getPackageOptionalGroups() != null) {
            List<PackageOptionalGroup> groups = itemDto.getPackageOptionalGroups().stream().map(group -> {
                PackageOptionalGroup packageOptionalGroup = new PackageOptionalGroup();
                packageOptionalGroup.setGroupId(group.getGroupId());
                packageOptionalGroup.setGroupName(group.getGroupName());
                packageOptionalGroup.setMustOrderNum(group.getMustOrderNum());
                packageOptionalGroup.setSupportDuplicate(group.isSupportDuplicate());
                packageOptionalGroup.setProducts(group.getProducts().stream().map(optionalOrderProduct -> processItemDetail(optionalOrderProduct, true, hotSaleItemMap)).collect(Collectors.toList()));
                return packageOptionalGroup;
            }).collect(Collectors.toList());

            detailVO.setPackageOptionalGroups(groups);
        }

        detailVO.setAttributes(itemDto.getAttributes());
        detailVO.setItemTags(itemDto.getItemTags());
        // convert material
        detailVO.setMaterials(convertMaterialDto(itemDto.getMaterials()));
        detailVO.setMaterialGroups(this.toMaterialGroups(itemDto.getMaterialGroups()));
        if (!CollectionUtils.isEmpty(itemDto.getIngredients())) {
            detailVO.setIngredientNames(itemDto.getIngredients().stream().map(ItemIngredient::getName).collect(Collectors.toList()));
        }

        // 售卖时间信息
        appendSaleTime(detailVO, itemDto);

        return detailVO;
    }


    public Result<Boolean> isItemSalebleInMiniProgram(ItemDto itemDto, CartItemCreate cartItem, int serviceType, Map<String, Integer> spuCountMap) {

        if (itemDto == null) {
            // 商品不存在
            logCartItemRemoveReason(cartItem, "商品不存在或已经被删除");
            return Result.error(ErrorCodeEnum.FAIL, "失效");
        }

        String spuId = itemDto.getItem().getId();

        //零售商品不用校验称重
        if (!(itemDto instanceof RetailExtendItemDTO)) {
            if (Objects.equals(itemDto.getItem().getUnitType(), 1)) {
                //称重商品不能加购
                logCartItemRemoveReason(cartItem, "称重商品不能加购");
                return Result.error(ErrorCodeEnum.FAIL, "非可售终端");
            }
        }


        Integer curCount = MapUtils.getInteger(spuCountMap, spuId, 0);
        Integer saleNumber = cartItem.getItem().getNumber();
        Integer remainStock = Optional.ofNullable(itemDto.getItem().getSku()).orElse(Integer.MAX_VALUE);


        if (!itemDto.getItem().getForSale()) {
            logCartItemRemoveReason(cartItem, "商品已经下架");
            return Result.error(ErrorCodeEnum.FAIL, "下架");
        }


        if (Objects.equals(itemDto.getItem().getItemType(), ProductTypeEnum.PACKAGE.name())) {
            if (itemDto.getItem().getSubItemOutOfStock()) {
                logCartItemRemoveReason(cartItem, "子商品没有库存");
                return Result.error(ErrorCodeEnum.FAIL, "子商品失效");
            }
            if (itemDto.getItem().getOutOfStock()) {
                logCartItemRemoveReason(cartItem, "商品没有库存");
                return Result.error(ErrorCodeEnum.FAIL, "售罄");
            }
        } else {
            if (itemDto.getItem().getOutOfStock()) {
                logCartItemRemoveReason(cartItem, "商品没有库存");
                return Result.error(ErrorCodeEnum.FAIL, "售罄");
            }
        }

        if (curCount + saleNumber > remainStock) {
            logCartItemRemoveReason(cartItem, "商品库存不足");
            return Result.error(ErrorCodeEnum.FAIL, "库存不足");
        }

        if ((serviceType != 0 && itemDto.getItem().getServiceType() != 0 && itemDto.getItem().getServiceType() != serviceType)) {
            logCartItemRemoveReason(cartItem, "商品不再当前场景售卖");
            return Result.error(ErrorCodeEnum.FAIL, "非可售渠道");
        }

        if (!curTimeForSale(itemDto)) {
            //当前时段不可售
            logCartItemRemoveReason(cartItem, "商品不再当前时段售卖");
            return Result.error(ErrorCodeEnum.FAIL, "非可售时段");
        }

        if (!Optional.ofNullable(itemDto.getItem()).map(Item::getSaleTerminals).orElseGet(Lists::newArrayList).contains(Constants.SaleTerminal.MINI)) {
            //检查可售渠道
            logCartItemRemoveReason(cartItem, "商品不再当前终端售卖");
            return Result.error(ErrorCodeEnum.FAIL, "非可售终端");
        }

        if (itemDto.getItem().getMinSaleNum() != null) {
            if (cartItem.getItem().getNumber() < itemDto.getItem().getMinSaleNum()) {
                //小于最低可售数量
                logCartItemRemoveReason(cartItem, "商品售卖数量小于最低售卖数量");
                return Result.error(ErrorCodeEnum.FAIL, itemDto.getItem().getMinSaleNum() + "份起售");

            }
        }

        //校验套餐商品
        Result<Boolean> isPackageValidResult = isValidatePackageItem(cartItem, itemDto, spuCountMap);

        if (!isPackageValidResult.isSuccess()) {
            logCartItemRemoveReason(cartItem, isPackageValidResult.getErrorMsg());
            return Result.error(ErrorCodeEnum.FAIL, isPackageValidResult.getErrorCode());
        }

        return Result.success(true);
    }

    public void logCartItemRemoveReason(CartItemCreate cartItem, String reason) {
        log.info("mustOrderItem not add to cart! reason:{}, storeId:{}, spuId:{}", reason, cartItem.getStoreId(), cartItem.getItem().getId(),
                keyValue("method", "logMustOrderNotAddReason"),
                keyValue("is_must_order", cartItem.isOpenTableMustOrder()),
                keyValue("storeId", cartItem.getStoreId()),
                keyValue("user_id", Optional.ofNullable(ThreadLocalHelper.getUser()).map(user -> user.getUserId()).orElse(null)));
    }


    /**
     * 校验加购的套餐是否符合要求
     *
     * @param cartItem
     * @param itemDto
     * @return
     */
    private Result<Boolean> isValidatePackageItem(CartItemCreate cartItem, ItemDto itemDto, Map<String, Integer> spuCountMap) {
        if (!Objects.equals(cartItem.getItem().getSpuType(), SpuType.PACKAGE.name())) {
            //不是套餐，跳过
            return Result.success(true);
        }

        if (!Objects.equals(itemDto.getItem().getItemType(), ProductTypeEnum.PACKAGE.name())) {
            return Result.error("套餐失效", "商品信息错误，这不是一个有效的套餐");
        }

        List<CartItemCreate> packageItems = cartItem.getPackageItems();
        if (CollectionUtil.isEmpty(packageItems)) {
            return Result.error("子商品缺失", "未选择套餐子商品，请重新加购");
        }

        Map<String, Integer> packageItemCountMap = new HashMap<>();

        cartItem.getPackageItems().stream().forEach(packageItem -> {
            CartHelper.addSpuCount(packageItemCountMap, packageItem.getItem().getId(), packageItem.getItem().getNumber());
        });


        //必选商品，如果可以选择口味做法的情况下， 不同的口味做法是分成一个一个给到后台的
        //这里我们要根据spuId+skuId来唯一确定一个商品， 组装起来
        List<CartItemCreate> mustOrderItems = new ArrayList<>();
        Map<String, List<CartItemCreate>> mustOrderItemMaps = packageItems.stream()
                //只有没设置packageGroupId的是必选商品
                .filter(packageItem -> org.springframework.util.StringUtils.isEmpty(packageItem.getPackageGroupId()))
                .map(item -> {
                    mustOrderItems.add(item);
                    return item;
                }).collect(Collectors.groupingBy(itemCreate -> itemCreate.getItem().getId() + Optional.ofNullable(itemCreate.getSpec()).map(spec -> spec.getId()).orElse("")));

        Map<String, Integer> mustOrderItemSaleCountMap = new HashMap<>();
        //一个商品的真正售卖数量，是加起来的数量
        mustOrderItemMaps.entrySet().stream()
                .forEach(entry -> {
                    Integer saleCount = entry.getValue().stream().mapToInt(item -> item.getItem().getNumber()).sum();
                    mustOrderItemSaleCountMap.put(entry.getKey(), saleCount);
                });

        boolean mustOrderCheckPass = mustOrderItems.stream().allMatch(mustOrderItem -> {
            String itemKey = mustOrderItem.getItem().getId() + Optional.ofNullable(mustOrderItem.getSpec()).map(spec -> spec.getId()).orElse("");
            Integer pickedItemSaleCount = Optional.ofNullable(mustOrderItemSaleCountMap.get(itemKey)).orElse(0);
            //每一个被选中的商品，都必须在db配置中找到对应的商品，并且加购数量必须等于数据库配置
            return itemDto.getPackageMustOrderProducts().stream().anyMatch(dbMustOrder -> {
                //知道到任何一个，就算是匹配成功
                if (Objects.equals(dbMustOrder.getItem().getId(), mustOrderItem.getItem().getId())) {
                    boolean checkResult = checkItemMatch(mustOrderItem, pickedItemSaleCount, dbMustOrder, packageItemCountMap, spuCountMap);

                    if (!checkResult) {
                        return false;
                    }

                    if (Objects.equals(dbMustOrder.getAddCount(), pickedItemSaleCount)) {
                        return true;
                    }
                    return false;
                }
                return false;

            });
        });

        if (!mustOrderCheckPass) {
            return Result.error("子商品缺失", "套餐必选分类选择有误，请重新加购");
        }

        //可选分组内的商品
        Map<String, List<CartItemCreate>> optionProductsMap = packageItems.stream()
                .filter(packageItem -> !org.springframework.util.StringUtils.isEmpty(packageItem.getPackageGroupId()))
                .collect(Collectors.groupingBy(CartItemCreate::getPackageGroupId));


        Integer pickNum = Optional.ofNullable(optionProductsMap).map(i -> i.size()).orElse(0);
        Integer configNum = Optional.ofNullable(itemDto.getPackageOptionalGroups()).map(i -> i.size()).orElse(0);

        if (!Objects.equals(pickNum, configNum)) {
            return Result.error("子商品缺失", "套餐可选分组内商品数量选择错误，请重新加购");
        }

        if (pickNum > 0) {
            boolean optionalCheckPass = itemDto.getPackageOptionalGroups().stream().allMatch(group -> {
                //每一个分组都要满足条件
                List<CartItemCreate> uploadPickList = optionProductsMap.get(group.getGroupId());

                //如果可以选择口味做法的情况下， 不同的口味做法是分成一个一个给到后台的
                //这里我们要根据spuId+skuId来唯一确定一个商品， 组装起来
                Map<String, List<CartItemCreate>> optionalPickOrderItemMaps = uploadPickList.stream()
                        .collect(Collectors.groupingBy(itemCreate -> itemCreate.getItem().getId() + Optional.ofNullable(itemCreate.getSpec()).map(spec -> spec.getId()).orElse("")));

                Map<String, Integer> optionalItemSaleCountMap = new HashMap<>();
                //一个商品的真正售卖数量，是加起来的数量
                optionalPickOrderItemMaps.entrySet().stream()
                        .forEach(entry -> {
                            Integer saleCount = entry.getValue().stream().mapToInt(item -> item.getItem().getNumber()).sum();
                            optionalItemSaleCountMap.put(entry.getKey(), saleCount);
                        });

                if (!group.isSupportDuplicate()) {
                    //不支持重复点，那么每一个商品只能点一个
                    boolean groupCheckFail = Optional.ofNullable(uploadPickList).map(picItemList ->
                            picItemList.stream().anyMatch(picItem -> picItem.getItem().getNumber() > 1)
                    ).orElse(true);
                    if (groupCheckFail) {
                        return false;
                    }
                }

                Integer pickCount = Optional.ofNullable(uploadPickList).map(picItemList ->
                        picItemList.stream().mapToInt(picItem -> picItem.getItem().getNumber()).sum()
                ).orElse(0);

                if (!Objects.equals(pickCount, group.getMustOrderNum())) {
                    //选的数量不对
                    return false;
                }

                //判断选中的商品是否有售罄的
                return Optional.ofNullable(uploadPickList).map(picItemList -> {
                    //选中的商品，每个都不能售罄
                    return picItemList.stream().allMatch(picItem -> {
                        String itemKey = picItem.getItem().getId() + Optional.ofNullable(picItem.getSpec()).map(spec -> spec.getId()).orElse("");
                        Integer pickedItemSaleCount = Optional.ofNullable(optionalItemSaleCountMap.get(itemKey)).orElse(0);
                        //找到任何一个可以匹配到，并且为售罄的商品
                        return group.getProducts().stream().anyMatch(groupProduct -> checkItemMatch(picItem, pickedItemSaleCount, groupProduct, packageItemCountMap, spuCountMap));
                    });
                }).orElse(false);

            });


            if (!optionalCheckPass) {
                //子商品缺失
                return Result.error("子商品缺失", "套餐可选分组内商品选择错误，请重新加购");
            }
        }

        //校验通过了，那么更新下库存统计信息
        if (packageItemCountMap != null) {
            packageItemCountMap.entrySet().stream().forEach(entry -> {
                CartHelper.addSpuCount(spuCountMap, entry.getKey(), entry.getValue());
            });
        }

        return Result.success(true);


    }

    /**
     * 检查是否是购物车内商品和商品配置是否是同一个商品
     *
     * @param pickedItem
     * @param dbProduct
     * @return
     */
    private boolean checkItemMatch(CartItemCreate pickedItem, Integer pickedItemSaleCount, ItemDto dbProduct, Map<String, Integer> packageItemCountMap, Map<String, Integer> cartItemCountMap) {
        if (Objects.equals(dbProduct.getItem().getId(), pickedItem.getItem().getId())) {
            //SPU_ID必须一样
            if (pickedItem.getSpec() != null && org.apache.commons.lang3.StringUtils.isNotBlank(pickedItem.getSpec().getId())) {
                //被选中的商品有sku信息， 那么判断下是否是同一个sku
                if (CollectionUtil.isEmpty(dbProduct.getSpecs())) {
                    return false;
                }

                boolean findSku = dbProduct.getSpecs().stream().anyMatch(spec ->
                        Objects.equals(spec.getId(), pickedItem.getSpec().getId()));
                if (!findSku) {
                    return false;
                }
            }

            Integer remainStock = Optional.ofNullable(dbProduct.getItem().getSku()).orElse(Integer.MAX_VALUE);
            Integer saleCount = MapUtils.getInteger(packageItemCountMap, dbProduct.getItem().getId(), 0);
            Integer cartSaleCount = MapUtils.getInteger(cartItemCountMap, dbProduct.getItem().getId(), 0);

            if (dbProduct.getItem().getOutOfStock()) {
                //如果已经售罄了， 那么认为没找到
                return false;
            }


            if (dbProduct.getItem().getSku() != null && pickedItemSaleCount > dbProduct.getItem().getSku()) {
                //找到了，但是没有库存了
                return false;
            }

            if (saleCount + cartSaleCount > remainStock) {
                //购物车已经加购的这些商品已经超过了商品
                return false;
            }


            return true;
        }
        return false;
    }

    public ItemDetailVO processItemDetail(ItemDto itemDto,
                                          List<SkuInfo> singleActivitySkuInfoList,
                                          List<SkuInfo> secondActivitySkuInfoList,
                                          Map<String, List<CategoryInfo>> categoryActivity,
                                          Map<String, HotSaleItem> hotSaleItemMap,
                                          Integer serviceType) {
        ItemDetailVO itemDetailVO = this.processItemDetail(itemDto, hotSaleItemMap);
        appendSingleActivityInfo(serviceType, itemDetailVO, singleActivitySkuInfoList);
        appendSecondActivityInfo(itemDetailVO, secondActivitySkuInfoList);
        appendCategoryActivityInfo(itemDetailVO, categoryActivity);
        return itemDetailVO;
    }

    private static void appendCategoryActivityInfo(ItemDetailVO dv, Map<String, List<CategoryInfo>> categoryActivity) {
        if (Objects.isNull(dv) || MapUtils.isEmpty(categoryActivity)) {
            return;
        }
        List<CategoryInfo> singleCategoryActivity = categoryActivity.get("singleCategory");
        List<CategoryInfo> secondCategoryActivity = categoryActivity.get("secondCategory");

        if (CollectionUtils.isNotEmpty(singleCategoryActivity)) {
            appendCategorySingleActivityInfo(dv, singleCategoryActivity);
        }
        if (CollectionUtils.isNotEmpty(secondCategoryActivity)) {
            appendCategorySecondActivityInfo(dv, secondCategoryActivity);
        }
    }

    private static void appendCategorySingleActivityInfo(ItemDetailVO dv, List<CategoryInfo> singleCategoryActivity) {
        singleCategoryActivity.stream()
                .filter(it -> Objects.equals(it.getCategoryId(), dv.getItem().getCategoryId())
                        || Objects.equals(it.getCategoryId(), dv.getItem().getSubCategoryId()))
                .forEach(categoryInfo -> {
                    if (dv.getSpecs() == null) {
                        ItemDiscountPriceVO newDiscount = new ItemDiscountPriceVO();
                        newDiscount.setDiscountType(ItemDiscountTypeEnum.CATEGORY_SINGLE_DISCOUNT);
                        newDiscount.setDiscount(categoryInfo.getDiscountScale());
                        newDiscount.setQuotaCount(categoryInfo.getQuotaCount());
                        dv.getItem().setDiscountPrices(addDiscountPrice(dv.getItem().getDiscountPrices(), newDiscount));
                    } else {
                        Optional.of(dv.getSpecs())
                                .map(ItemSpecVO::getOptions)
                                .ifPresent(specs -> specs.forEach(spec -> {
                                    ItemDiscountPriceVO newDiscount = new ItemDiscountPriceVO();
                                    newDiscount.setDiscountType(ItemDiscountTypeEnum.CATEGORY_SINGLE_DISCOUNT);
                                    newDiscount.setDiscount(categoryInfo.getDiscountScale());
                                    newDiscount.setQuotaCount(categoryInfo.getQuotaCount());
                                    spec.setDiscountPrices(addDiscountPrice(spec.getDiscountPrices(), newDiscount));
                                }));
                    }
                });
    }

    private static void appendCategorySecondActivityInfo(ItemDetailVO dv, List<CategoryInfo> secondCategoryActivity) {
        secondCategoryActivity.stream()
                .filter(it -> Objects.equals(it.getCategoryId(), dv.getItem().getCategoryId())
                        || Objects.equals(it.getCategoryId(), dv.getItem().getSubCategoryId()))
                .forEach(categoryInfo -> {
                    if (dv.getSpecs() == null) {
                        ItemDiscountPriceVO newDiscount = new ItemDiscountPriceVO();
                        newDiscount.setDiscountType(ItemDiscountTypeEnum.SECOND_DISCOUNT);
                        newDiscount.setDiscount(categoryInfo.getDiscountScale());
                        newDiscount.setQuotaCount(categoryInfo.getQuotaCount());
                        dv.getItem().setDiscountPrices(addDiscountPrice(dv.getItem().getDiscountPrices(), newDiscount));
                        dv.getItem().setSecondActivityDiscount(categoryInfo.getDiscountScale());
                    } else {
                        Optional.of(dv.getSpecs())
                                .map(ItemSpecVO::getOptions)
                                .ifPresent(specs -> specs.forEach(spec -> {
                                    ItemDiscountPriceVO newDiscount = new ItemDiscountPriceVO();
                                    newDiscount.setDiscountType(ItemDiscountTypeEnum.SECOND_DISCOUNT);
                                    newDiscount.setDiscount(categoryInfo.getDiscountScale());
                                    newDiscount.setQuotaCount(categoryInfo.getQuotaCount());
                                    spec.setDiscountPrices(addDiscountPrice(spec.getDiscountPrices(), newDiscount));
                                    spec.setSecondActivityDiscount(categoryInfo.getDiscountScale());
                                }));
                    }
                });
    }

    public ListResult<ItemDetailVO> processItemDetailList(ListResult<ItemDto> result, Integer serviceType, boolean getGiftDiscountPrice, String userId) {
        if (result == null || result.getTotal() == 0 || CollectionUtil.isEmpty(result.getRecords())) {
            return null;
        }
        String storeId = result.getRecords().get(0).getItem().getStoreId();
        String merchantId = result.getRecords().get(0).getItem().getMerchantId();
        if (StringUtils.isBlank(merchantId)) {
            if (StringUtils.isNotBlank(storeId)) {
                merchantId = storeHelper.getMerchantIdByStoreId(storeId);
            }
        }
        ListResult<ItemDetailVO> voResult = new ListResult<>();
        voResult.setTotal(result.getTotal());
        List<ItemDetailVO> itemDetailVOS = new ArrayList<>();
        voResult.setRecords(itemDetailVOS);

        // 热销数据
        Map<String, HotSaleItem> hotSaleItemMap = this.hotSaleItemMapFromCache(storeId, serviceType);

        // 营销数据
        List<ItemDto> records = result.getRecords();
        Supplier<List<StoredActivityDetailRequest.GoodsDetail>> goodsDetailSupper = getStoredActivityGoodsDetailSupplier(records);
        Supplier<List<MemberActivityDetailRequest.GoodsDetail>> memberActivityGoodsDetailSupper = getMemberActivityGoodsDetailSupplier(records);

        List<SkuInfo> singleActivitySkus = this.getSingActivitySkuInfos(
                merchantId,
                storeId,
                serviceType,
                getGiftDiscountPrice,
                userId,
                goodsDetailSupper,
                memberActivityGoodsDetailSupper
        );
        List<SkuInfo> secondActivitySkus = this.getSecondActivitySkuInfos(storeId, serviceType);
        Map<String, List<CategoryInfo>> categoryActivity = this.categoryDiscountDetails(storeId, serviceType, EffectiveType.YES);

        for (ItemDto record : records) {
            ItemDetailVO itemDetailVO = processItemDetail(
                    record,
                    singleActivitySkus,
                    secondActivitySkus,
                    categoryActivity,
                    hotSaleItemMap,
                    serviceType
            );

            if (itemDetailVO != null) {
                itemDetailVOS.add(itemDetailVO);
            }
        }

        return voResult;
    }

    public static void mergeBestActivity(List<SkuInfo> singleActivitySkus, List<SkuInfo> secondActivitySkus, List<SkuInfo> bestActivitySkuInfos) {
        if (CollectionUtils.isNotEmpty(bestActivitySkuInfos)) {
            // 1:单品活动, 2:第二件半价活动
            // bestDiscountActivity这个字段现在只有这两个值
            Map<Integer, List<SkuInfo>> bestActivityMap = bestActivitySkuInfos.stream().collect(Collectors.groupingBy(SkuInfo::getBestDiscountActivity));
            singleActivitySkus.addAll(bestActivityMap.getOrDefault(1, Collections.emptyList()));
            secondActivitySkus.addAll(bestActivityMap.getOrDefault(2, Collections.emptyList()));
        }
    }

    private static Supplier<List<StoredActivityDetailRequest.GoodsDetail>> getStoredActivityGoodsDetailSupplier(List<ItemDto> records) {
        return () -> records.stream().map(it -> StoredActivityDetailRequest.GoodsDetail.builder()
                .price(it.getItem().getPrice())
                .quantity("1")
                .spuId(it.getItem().getId())
                .skuId(null)
                .build()).collect(Collectors.toList());
    }

    private static Supplier<List<MemberActivityDetailRequest.GoodsDetail>> getMemberActivityGoodsDetailSupplier(List<ItemDto> records) {
        return () -> {
            List<MemberActivityDetailRequest.GoodsDetail> goodsDetails = new ArrayList<>();
            records.forEach(it -> buildMemberActivityGoodsDetail(goodsDetails, it));
            return goodsDetails;
        };
    }

    public static List<MemberActivityDetailRequest.GoodsDetail> buildMemberActivityGoodsDetail(List<MemberActivityDetailRequest.GoodsDetail> goodsDetails, ItemDto it) {
        if (goodsDetails == null) {
            goodsDetails = new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(it.getSpecs())) {
            goodsDetails.add(MemberActivityDetailRequest.GoodsDetail.builder()
                    .price(it.getItem().getPrice())
                    .quantity("1")
                    .spuId(it.getItem().getId())
                    .skuId(null)
                    .build());
        } else {
            for (ItemSpec spec : it.getSpecs()) {
                goodsDetails.add(MemberActivityDetailRequest.GoodsDetail.builder()
                        .price(spec.getPrice())
                        .quantity("1")
                        .spuId(it.getItem().getId())
                        .skuId(spec.getId())
                        .build());
            }
        }

        return goodsDetails;
    }

    public static List<GoodsItem> getGoodsAndCategoryActivityGoodsDetailSupplier(List<ItemDto> records) {
        List<GoodsItem> goodsItems = new ArrayList<>();
        records.forEach(it -> {
            if (CollectionUtils.isEmpty(it.getSpecs())) {
                goodsItems.add(GoodsItem.builder()
                        .price(it.getItem().getPrice())
                        .categoryId(it.getItem().getCategoryId())
                        .spuId(it.getItem().getId())
                        .skuId(null)
                        .build());
            } else {
                for (ItemSpec spec : it.getSpecs()) {
                    goodsItems.add(GoodsItem.builder()
                            .price(spec.getPrice())
                            .categoryId(it.getItem().getCategoryId())
                            .spuId(it.getItem().getId())
                            .skuId(spec.getId())
                            .build());
                }
            }
        });

        return goodsItems;
    }

    public ListResult<ItemDetailVO> processItemDetailList(ListResult<ItemDto> result, Integer serviceType, String userId) {
        return processItemDetailList(result, serviceType, false, userId);
    }

    public ListResult<ItemDetailVO> processItemDetailList(String storeId,
                                                          Integer serviceType,
                                                          ListResult<ItemDto> items,
                                                          List<SkuInfo> singleActivitySkus,
                                                          List<SkuInfo> secondActivitySkus) {
        if (Objects.isNull(items) || Objects.equals(0, items.getTotal()) || CollectionUtils.isEmpty(items.getRecords())) {
            return ListResult.emptyResult();
        }

        ListResult<ItemDetailVO> itemVoResult = new ListResult<>();
        itemVoResult.setTotal(items.getTotal());

        List<ItemDetailVO> itemVos = Lists.newArrayList();
        itemVoResult.setRecords(itemVos);

        Map<String, HotSaleItem> hotSaleItemMap = hotSaleItemMapFromCache(storeId, serviceType);
        Map<String, List<CategoryInfo>> categoryActivity = this.categoryDiscountDetails(storeId, serviceType, EffectiveType.YES);

        items.getRecords().forEach(
                item -> Optional.ofNullable(
                        this.processItemDetail(
                                item,
                                singleActivitySkus,
                                secondActivitySkus,
                                categoryActivity,
                                hotSaleItemMap,
                                serviceType
                        )
                ).ifPresent(itemVos::add)
        );

        return itemVoResult;
    }


    /**
     * 构造商品详情
     *
     * @param storeId
     * @param serviceType
     * @param detailPage
     * @param getGiftDiscountPrice
     * @param userId
     * @return
     */
    public ListResult<ItemDetailVO> processItemDetailV2(String storeId, Integer serviceType, Page<ProductDetail> detailPage, boolean getGiftDiscountPrice, String userId) {
        if (Objects.isNull(detailPage) || CollectionUtils.isEmpty(detailPage.getPageData())) {
            return ListResult.emptyResult();
        }
        List<ProductDetail> details = detailPage.getPageData();
        String merchantId = details.get(0).getMerchantId();
        // 热销数据
        Map<String, HotSaleItem> hotSaleItemMap = this.hotSaleItemMapFromCache(storeId, serviceType);
        // 营销数据
        Supplier<List<StoredActivityDetailRequest.GoodsDetail>> goodsDetailSupper = getStoredActivityGoodsDetailV2(details);
        Supplier<List<MemberActivityDetailRequest.GoodsDetail>> memberActivityGoodsDetailSupper = getMemberActivityGoodsDetailSupplierV2(details);
        List<SkuInfo> singleActivitySkus = this.getSingActivitySkuInfos(
                merchantId,
                storeId,
                serviceType,
                getGiftDiscountPrice,
                userId,
                goodsDetailSupper,
                memberActivityGoodsDetailSupper
        );
        List<SkuInfo> secondActivitySkus = this.getSecondActivitySkuInfos(storeId, serviceType);
        Map<String, List<CategoryInfo>> categoryDiscountDetails = this.categoryDiscountDetails(storeId, serviceType, EffectiveType.YES);

        // 组装结果
        List<ItemDetailVO> dvs = details.stream()
                .map(d -> this.processItemDetailV2(d, serviceType, hotSaleItemMap, singleActivitySkus, secondActivitySkus, categoryDiscountDetails))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        ListResult<ItemDetailVO> result = new ListResult<>();
        result.setTotal(detailPage.getTotalCount());
        result.setRecords(dvs);
        return result;
    }

    private static Supplier<List<StoredActivityDetailRequest.GoodsDetail>> getStoredActivityGoodsDetailV2(List<ProductDetail> details) {
        return () -> details.stream()
                .map(d -> {
                    if (Objects.isNull(d.getSpu()) || CollectionUtils.isEmpty(d.getSkuList())) {
                        return null;
                    }
                    return StoredActivityDetailRequest.GoodsDetail.builder()
                            .price(ItemConverter.getMinPrice(d.getSkuList(), ProductSku::getSalePrice, null))
                            .quantity("1")
                            .spuId(d.getSpu().getSpuId())
                            .skuId(null)
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static Supplier<List<MemberActivityDetailRequest.GoodsDetail>> getMemberActivityGoodsDetailSupplierV2(List<ProductDetail> details) {
        return () -> {
            List<MemberActivityDetailRequest.GoodsDetail> goodsDetails = new ArrayList<>();
            for (ProductDetail it : details) {
                if (Objects.isNull(it.getSpu()) || CollectionUtils.isEmpty(it.getSkuList())) {
                    continue;
                }

                if (it.getSkuList().size() == 1) {
                    goodsDetails.add(MemberActivityDetailRequest.GoodsDetail.builder()
                            .price(it.getSkuList().get(0).getSalePrice().intValue())
                            .quantity("1")
                            .spuId(it.getSpu().getSpuId())
                            .skuId(null)
                            .build());
                } else {
                    for (ProductSku spec : it.getSkuList()) {
                        goodsDetails.add(MemberActivityDetailRequest.GoodsDetail.builder()
                                .price(spec.getSalePrice().intValue())
                                .quantity("1")
                                .spuId(it.getSpu().getSpuId())
                                .skuId(spec.getSkuId())
                                .build());
                    }
                }
            }
            return goodsDetails;
        };
    }

    private static List<GoodsItem> getGoodsAndCategoryActivityGoodsDetailSupplierV2(List<ProductDetail> details) {
        List<GoodsItem> goodsItems = new ArrayList<>();
        details.forEach(it -> {
            ProductSpu spu = it.getSpu();
            List<ProductSku> skuList = it.getSkuList();
            if (Objects.isNull(spu) || CollectionUtils.isEmpty(skuList)) {
                return;
            }

            if (skuList.size() == 1) {
                goodsItems.add(GoodsItem.builder()
                        .price(skuList.get(0).getSalePrice().intValue())
                        .categoryId(spu.getCategoryId())
                        .spuId(spu.getSpuId())
                        .skuId(null)
                        .build());
            } else {
                skuList.stream()
                        .map(spec -> GoodsItem.builder()
                                .price(spec.getSalePrice().intValue())
                                .categoryId(spu.getCategoryId())
                                .spuId(spu.getCategoryId())
                                .skuId(spec.getSkuId())
                                .build())
                        .forEach(goodsItems::add);
            }
        });

        return goodsItems;
    }


    /**
     * 构造商品详情
     *
     * @param detail
     * @param serviceType
     * @return
     */
    public ItemDetailVO processItemDetailV2(ProductDetail detail, Integer serviceType) {
        return this.processItemDetailV2(detail, serviceType, 
                Collections.emptyMap(), Collections.emptyList(), Collections.emptyList(), Collections.emptyMap());
    }
    
    /**
     * 构造商品详情
     *
     * @param detail
     * @param hotSaleItemMap
     * @param categoryDiscountDetails
     * @return
     */
    public ItemDetailVO processItemDetailV2(ProductDetail detail, Integer serviceType,
                                            Map<String, HotSaleItem> hotSaleItemMap,
                                            List<SkuInfo> singleActivitySkus, List<SkuInfo> secondActivitySkus,
                                            Map<String, List<CategoryInfo>> categoryDiscountDetails) {
        ItemDetailVO dv = ItemConverter.toItemDetailVo(detail);
        if (Objects.isNull(dv)) {
            throw new BusinessException(ReturnCode.ITEM_NOT_FOR_SALE_2);
        }
        
        appendHotSaleItemInfo(dv, hotSaleItemMap);
        appendSingleActivityInfo(serviceType, dv, singleActivitySkus);
        appendSecondActivityInfo(dv, secondActivitySkus);
        appendCategoryActivityInfo(dv, categoryDiscountDetails);
        return dv;
    }

    /**
     * 补充热销数据
     *
     * @param dv
     * @param hotSaleItemMap
     */
    public static void appendHotSaleItemInfo(ItemDetailVO dv, Map<String, HotSaleItem> hotSaleItemMap) {
        if (Objects.isNull(dv) || MapUtils.isEmpty(hotSaleItemMap)) {
            return;
        }
        HotSaleItem hotSaleItem = MapUtils.getObject(hotSaleItemMap, dv.getItem().getId());
        if (hotSaleItem != null) {
            dv.getItem().setHotsaleProduct(true);
            dv.getItem().setHotsaleSeq(hotSaleItem.getHotsaleSeq());
            dv.getItem().setLast30DaysSaleCount(hotSaleItem.getLast30DaysSaleCount());
            dv.getItem().setItemTag(TagUtil.addTag(dv.getItem().getItemTag(), OrderGoodsTagEnum.HOT_SALE.getValue()));
        }
    }


    /**
     * 补充单品活动，储值享折扣数据
     *
     * @param serviceType
     * @param dv
     * @param singleActivitySkus
     */
    public static void appendSingleActivityInfo(Integer serviceType, ItemDetailVO dv,
                                                List<SkuInfo> singleActivitySkus) {
        if (Objects.isNull(dv) || CollectionUtils.isEmpty(singleActivitySkus)) {
            return;
        }
        singleActivitySkus.stream()
                .filter(it -> Objects.equals(it.getSpuId(), dv.getItem().getId()))
                .forEach(sku -> {
                    boolean canEnjoy = true;
                    Integer activityPrice = null;
                    Integer memberDiscount = null;
                    Integer memberPrice = null;
                    if (sku.getActivityPriceMap() != null) {
                        activityPrice = sku.getActivityPriceMap().get(ActivityPriceConstant.ARRIVAL_ACTIVITY_PRICE);
                        if (Objects.equals(serviceType, Constants.ServiceType.TAKEOUT)) {
                            //外卖的情况下
                            if (sku.getActivityPriceMap().get(ActivityPriceConstant.TAKEOUT_ACTIVITY_PRICE) != null) {
                                activityPrice = sku.getActivityPriceMap().get(ActivityPriceConstant.TAKEOUT_ACTIVITY_PRICE);
                            }
                        }
                    }
                    if (sku.getStoredActivityPriceMap() != null) {
                        ItemDiscountPriceVO discountPriceVO = new ItemDiscountPriceVO();
                        //设置储值折扣价
                        Integer giftCardDiscountPrice = sku.getStoredActivityPriceMap().get(ActivityPriceConstant.ARRIVAL_ACTIVITY_PRICE);
                        if (Objects.equals(serviceType, Constants.ServiceType.TAKEOUT)) {
                            //外卖的情况下
                            if (sku.getStoredActivityPriceMap().get(ActivityPriceConstant.TAKEOUT_ACTIVITY_PRICE) != null) {
                                giftCardDiscountPrice = sku.getStoredActivityPriceMap().get(ActivityPriceConstant.TAKEOUT_ACTIVITY_PRICE);
                            }
                        }
                        discountPriceVO.setDiscountPrice(giftCardDiscountPrice);
                        discountPriceVO.setDiscountType(ItemDiscountTypeEnum.GIFT_CARD_DISCOUNT);
                        dv.getItem().setDiscountPrices(addDiscountPrice(dv.getItem().getDiscountPrices(), discountPriceVO));
                        dv.getItem().setGiftCardDiscountPrice(giftCardDiscountPrice);
                    }
                    if (sku.getMemberActivityPriceMap() != null) {
                        if (!MapUtils.getBoolean(sku.getMemberActivityPriceMap(), "is_member", true)) {
                            canEnjoy = false;
                        }
                        if (Objects.equals(serviceType, Constants.ServiceType.TAKEOUT)) {
                            //外卖
                            if (StringUtils.equals(MapUtils.getString(sku.getMemberActivityPriceMap(), ActivityPriceConstant.TAKEOUT_DISCOUNT_TYPE), "MEMBER_PRICE_DISCOUNT")) {
                                memberPrice = MapUtils.getInteger(sku.getMemberActivityPriceMap(), ActivityPriceConstant.TAKEOUT_ACTIVITY_PRICE);
                            }
                            if (MapUtils.getInteger(sku.getMemberActivityPriceMap(), ActivityPriceConstant.GOODS_DISCOUNT) != null) {
                                memberDiscount = MapUtils.getInteger(sku.getMemberActivityPriceMap(), ActivityPriceConstant.GOODS_DISCOUNT);
                            }
                        } else {
                            //堂食
                            if (StringUtils.equals(MapUtils.getString(sku.getMemberActivityPriceMap(), ActivityPriceConstant.ARRIVAL_DISCOUNT_TYPE), "MEMBER_PRICE_DISCOUNT")) {
                                memberPrice = MapUtils.getInteger(sku.getMemberActivityPriceMap(), ActivityPriceConstant.ARRIVAL_ACTIVITY_PRICE);
                            }
                            if (MapUtils.getInteger(sku.getMemberActivityPriceMap(), ActivityPriceConstant.GOODS_DISCOUNT) != null) {
                                memberDiscount = MapUtils.getInteger(sku.getMemberActivityPriceMap(), ActivityPriceConstant.GOODS_DISCOUNT);
                            }
                        }

                    }
                    if (!StringUtils.isEmpty(sku.getSkuId())) {
                        final Integer activityPriceFinal = activityPrice;
                        final Integer memberDiscountFinal = memberDiscount;
                        final Integer memberPriceFinal = memberPrice;
                        final boolean canEnjoyFinal = canEnjoy;
                        Optional.ofNullable(dv.getSpecs())
                                .map(ItemSpecVO::getOptions)
                                .flatMap(specs -> specs.stream()
                                        .filter(spec -> Objects.equals(spec.getId(), sku.getSkuId()))
                                        .findFirst()
                                )
                                .ifPresent(spec -> {
                                    if (activityPriceFinal != null) {
                                        ItemDiscountPriceVO discountPriceVO = new ItemDiscountPriceVO();
                                        discountPriceVO.setDiscountType(ItemDiscountTypeEnum.SINGLE_DISCOUNT);
                                        discountPriceVO.setDiscountPrice(activityPriceFinal);
                                        discountPriceVO.setQuotaCount(sku.getQuotaCount());
                                        spec.setDiscountPrices(addDiscountPrice(spec.getDiscountPrices(), discountPriceVO));
                                        spec.setActivityPrice(activityPriceFinal);
                                    }
                                    spec.setQuotaCount(sku.getQuotaCount());
                                    if (memberDiscountFinal != null) {
                                        ItemDiscountPriceVO discountPriceVO = new ItemDiscountPriceVO();
                                        discountPriceVO.setCanEnjoy(canEnjoyFinal);
                                        discountPriceVO.setDiscountType(ItemDiscountTypeEnum.MEMBER_GOODS_DISCOUNT);
                                        discountPriceVO.setDiscount(memberDiscountFinal);
                                        spec.setDiscountPrices(addDiscountPrice(spec.getDiscountPrices(), discountPriceVO));
                                    }
                                    if (memberPriceFinal != null) {
                                        ItemDiscountPriceVO discountPriceVO = new ItemDiscountPriceVO();
                                        discountPriceVO.setCanEnjoy(canEnjoyFinal);
                                        discountPriceVO.setDiscountType(ItemDiscountTypeEnum.MEMBER_PRICE_DISCOUNT);
                                        discountPriceVO.setDiscountPrice(memberPriceFinal);
                                        spec.setDiscountPrices(addDiscountPrice(spec.getDiscountPrices(), discountPriceVO));
                                    }
                                });

                    } else {
                        if (dv.getSpecs() == null) {
                            if (activityPrice != null) {
                                ItemDiscountPriceVO discountPriceVO = new ItemDiscountPriceVO();
                                discountPriceVO.setDiscountType(ItemDiscountTypeEnum.SINGLE_DISCOUNT);
                                discountPriceVO.setDiscountPrice(activityPrice);
                                discountPriceVO.setQuotaCount(sku.getQuotaCount());
                                dv.getItem().setDiscountPrices(addDiscountPrice(dv.getItem().getDiscountPrices(), discountPriceVO));
                                dv.getItem().setActivityPrice(activityPrice);
                            }
                            dv.getItem().setQuotaCount(sku.getQuotaCount());
                            if (memberDiscount != null) {
                                ItemDiscountPriceVO discountPriceVO = new ItemDiscountPriceVO();
                                discountPriceVO.setCanEnjoy(canEnjoy);
                                discountPriceVO.setDiscountType(ItemDiscountTypeEnum.MEMBER_GOODS_DISCOUNT);
                                discountPriceVO.setDiscount(memberDiscount);
                                dv.getItem().setDiscountPrices(addDiscountPrice(dv.getItem().getDiscountPrices(), discountPriceVO));
                            }
                            if (memberPrice != null) {
                                ItemDiscountPriceVO discountPriceVO = new ItemDiscountPriceVO();
                                discountPriceVO.setCanEnjoy(canEnjoy);
                                discountPriceVO.setDiscountType(ItemDiscountTypeEnum.MEMBER_PRICE_DISCOUNT);
                                discountPriceVO.setDiscountPrice(memberPrice);
                                dv.getItem().setDiscountPrices(addDiscountPrice(dv.getItem().getDiscountPrices(), discountPriceVO));
                            }
                        }
                    }
                });
    }

    private static List<ItemDiscountPriceVO> addDiscountPrice(List<ItemDiscountPriceVO> prices, ItemDiscountPriceVO newDiscount) {
        if (newDiscount == null || (newDiscount.getDiscountPrice() == null && newDiscount.getDiscount() == null)) {
            return prices;
        }
        if (prices == null) {
            prices = new ArrayList<>();
            prices.add(newDiscount);
            return prices;
        }

        for (ItemDiscountPriceVO price : prices) {
            if (price.getDiscountType() == newDiscount.getDiscountType()) {
                if ((price.getDiscountPrice() != null && newDiscount.getDiscountPrice() != null && newDiscount.getDiscountPrice() <= price.getDiscountPrice())
                        || (price.getDiscount() != null && newDiscount.getDiscount() != null && newDiscount.getDiscount() <= price.getDiscount())) {
                    price.setDiscountPrice(newDiscount.getDiscountPrice());
                    price.setDiscount(newDiscount.getDiscount());
                    price.setCanEnjoy(newDiscount.isCanEnjoy());
                    price.setQuotaCount(newDiscount.getQuotaCount());
                    return prices;
                } else {
                    //不需要处理：不需要替换，也不需要重复添加
                    return prices;
                }
            }
        }

        prices.add(newDiscount);
        return prices;
    }

    /**
     * 补充第二份优惠数据
     *
     * @param dv
     * @param secondActivitySkus
     */
    public static void appendSecondActivityInfo(ItemDetailVO dv, List<SkuInfo> secondActivitySkus) {
        if (Objects.isNull(dv) || CollectionUtils.isEmpty(secondActivitySkus)) {
            return;
        }
        secondActivitySkus.stream()
                .filter(it -> Objects.equals(it.getSpuId(), dv.getItem().getId()))
                .forEach(sku -> {
                    if (!StringUtils.isEmpty(sku.getSkuId())) {
                        Optional.ofNullable(dv.getSpecs())
                                .map(ItemSpecVO::getOptions)
                                .flatMap(specs -> specs.stream()
                                        .filter(spec -> Objects.equals(spec.getId(), sku.getSkuId()))
                                        .findFirst()
                                )
                                .ifPresent(spec -> {
                                    ItemDiscountPriceVO newDiscount = new ItemDiscountPriceVO();
                                    newDiscount.setDiscountType(ItemDiscountTypeEnum.SECOND_DISCOUNT);
                                    newDiscount.setDiscount(sku.getSecondActivityDiscount());
                                    spec.setDiscountPrices(addDiscountPrice(spec.getDiscountPrices(), newDiscount));
                                    spec.setSecondActivityDiscount(sku.getSecondActivityDiscount());
                                });

                    } else {
                        if (dv.getSpecs() == null) {
                            ItemDiscountPriceVO newDiscount = new ItemDiscountPriceVO();
                            newDiscount.setDiscountType(ItemDiscountTypeEnum.SECOND_DISCOUNT);
                            newDiscount.setDiscount(sku.getSecondActivityDiscount());
                            dv.getItem().setDiscountPrices(addDiscountPrice(dv.getItem().getDiscountPrices(), newDiscount));
                            dv.getItem().setSecondActivityDiscount(sku.getSecondActivityDiscount());
                        }
                    }
                });
    }

    /**
     * 获取单品优惠包含的sku信息
     *
     * @param storeId
     * @param serviceType
     * @return
     */
    public List<SkuInfo> getSingActivitySkuInfos(String storeId, Integer serviceType) {
        try {
            if (apolloConfigHelper.getServiceDegradeConfig().isRedeemQueryDegrade()) {
                return Lists.newArrayList();
            }
            SingleActivityDetailRequest request = buildSingleActivityDetailRequest(storeId, serviceType);
            return singleActivityRemoteService.detail(request);
        } catch (Exception ex) {
            return Lists.newArrayList();
        }
    }

    /**
     * 查询单品优惠信息
     * getGiftDiscountPrice = true，查询单品+储值折扣，否则只查单品
     *
     * @param merchantId
     * @param storeId
     * @param serviceType
     * @param getGiftDiscountPrice
     * @param userId
     * @param storeActivityGoodsDetailSupper
     * @return
     */
    public List<SkuInfo> getSingActivitySkuInfos(String merchantId, String storeId, Integer serviceType,
                                                 boolean getGiftDiscountPrice, String userId,
                                                 Supplier<List<StoredActivityDetailRequest.GoodsDetail>> storeActivityGoodsDetailSupper,
                                                 Supplier<List<MemberActivityDetailRequest.GoodsDetail>> memberActivityGoodsDetailSupper) {

        List<DiscountsEnum> discountsEnums = Lists.newArrayList(DiscountsEnum.SINGLE_ACTIVITY, DiscountsEnum.MEMBER_PRICE_DISCOUNT);
        GoodsActivityDetailRequest goodsActivityDetailRequest = new GoodsActivityDetailRequest();
        goodsActivityDetailRequest.setSingleOrSecondRequest(buildSingleActivityDetailRequest(storeId, serviceType));
        goodsActivityDetailRequest.setMemberRequest(
                MemberActivityDetailRequest.builder()
                        .storeId(storeId)
                        .merchantId(merchantId)
                        .marketUserId(userId)
                        .goodsDetails(memberActivityGoodsDetailSupper.get())
                        .build()
        );

        if (getGiftDiscountPrice) {
            goodsActivityDetailRequest.setStoredRequest(
                    StoredActivityDetailRequest.builder()
                            .goodsDetails(storeActivityGoodsDetailSupper.get())
                            .merchantId(merchantId)
                            .storeId(storeId)
                            .marketUserId(userId)
                            .effectiveType(EffectiveType.YES)
                            .orderType(serviceType == null ? OrderType.SUBSCRIBE_ORDER :
                                    serviceType == 1 ? OrderType.TAKE_OUT_ORDER : OrderType.SUBSCRIBE_ORDER)
                            .build()
            );
            discountsEnums.add(DiscountsEnum.STORED_INTERESTS_ACTIVITY);
        }

        goodsActivityDetailRequest.setDiscountsEnums(discountsEnums);
        List<SkuInfo> skuInfos = WeakReferenceCaller.call(() -> activityRemoteService.goodsDiscountDetails(goodsActivityDetailRequest));
        return skuInfos == null ? Lists.newArrayList() : skuInfos;
    }

    private static SingleActivityDetailRequest buildSingleActivityDetailRequest(String storeId, Integer serviceType) {
        return SingleActivityDetailRequest.builder()
                .storeId(storeId)
                .effectiveType(EffectiveType.YES)
                .orderType(serviceType == null ? OrderType.SUBSCRIBE_ORDER :
                        serviceType == 1 ? OrderType.TAKE_OUT_ORDER : OrderType.SUBSCRIBE_ORDER)
                .build();
    }

    /**
     * 获取第二份优惠优惠包含的sku信息
     *
     * @param storeId
     * @param serviceType
     * @return
     */
    public List<SkuInfo> getSecondActivitySkuInfos(String storeId, Integer serviceType) {
        try {
            if (apolloConfigHelper.getServiceDegradeConfig().isRedeemQueryDegrade()) {
                return Lists.newArrayList();
            }
            SingleActivityDetailRequest request = buildSingleActivityDetailRequest(storeId, serviceType);
            return secondActivityRemoteService.detail(request);
        } catch (Exception ex) {
            return Lists.newArrayList();
        }
    }


    public static Integer getPackageGoodsTotalPrice(CartItemCreate itemCreate) {
        return Optional.ofNullable(itemCreate.getPackageItems())
                .map(packageItems -> packageItems.stream()
                        .filter(packageItem -> !StringUtils.isEmpty(packageItem.getPackageGroupId()))
                        .mapToInt(packageItem -> packageItem.getItem().getPrice() * packageItem.getItem().getNumber())
                        .sum()
                ).orElse(0);
    }


    public ListResult<ItemDetailVO> processMultiplePhotoUrl(ListResult<ItemDetailVO> result) {
        result.getRecords().forEach(detail -> {
            detail.getItem().setPhotoUrl(processMultiplePhotoUrl(detail.getItem().getPhotoUrl()));
        });
        return result;
    }

    /**
     * 兼容处理多张图片C端不能正确显示的问题
     *
     * @param photoUrl
     * @return
     */
    private String processMultiplePhotoUrl(String photoUrl) {
        if (!StringUtils.isEmpty(photoUrl)) {
            String[] urlArr = photoUrl.split(",");
            if (urlArr.length > 1) {
                return urlArr[0];
            }
        }
        return photoUrl;
    }


    public CategoryResult<ItemDetailVO> processItemDetailList(CategoryResult<ItemDto> result, Integer serviceType) {
        if (result == null) {
            return null;
        }

        String storeId = result.getRecords().get(0).getItem().getStoreId();
        Map<String, HotSaleItem> hotSaleItemMap = hotSaleItemMapFromCache(storeId, serviceType);

        CategoryResult<ItemDetailVO> voResult = new CategoryResult<>();
        voResult.setTotal(result.getTotal());
        voResult.setCategoryId(result.getCategoryId());

        List<SkuInfo> singleActivitySkuInfoList = getSingActivitySkuInfos(storeId, serviceType);
        List<SkuInfo> secondActivitySkuInfoList = getSecondActivitySkuInfos(storeId, serviceType);

        Map<String, List<CategoryInfo>> categoryActivity = categoryDiscountDetails(storeId, serviceType, EffectiveType.YES);

        List<ItemDetailVO> itemDetailVOS = new ArrayList<>();
        for (ItemDto record : result.getRecords()) {
            ItemDetailVO itemDetailVO = processItemDetail(
                    record,
                    singleActivitySkuInfoList,
                    secondActivitySkuInfoList,
                    categoryActivity,
                    hotSaleItemMap,
                    serviceType
            );

            if (itemDetailVO != null) {
                itemDetailVOS.add(itemDetailVO);
            }
        }
        voResult.setRecords(itemDetailVOS);
        return voResult;
    }

    public List<MaterialVO> convertMaterialDto(List<Material> materials) {
        if (materials == null) {
            return null;
        }

        List<MaterialVO> materialVOS = new ArrayList<>();
        for (Material material : materials) {
            MaterialVO vo = new MaterialVO();
            BeanUtils.copyProperties(material, vo);
            materialVOS.add(vo);
        }

        return materialVOS;
    }

    public List<MaterialGroupVO> toMaterialGroups(List<MaterialGroup> groups) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(groups)) {
            return Lists.newArrayList();
        }
        return groups.stream()
                .map(p -> {
                    MaterialGroupVO vo = new MaterialGroupVO();
                    vo.setGroupId(p.getGroupId());
                    vo.setName(p.getName());
                    vo.setSeq(p.getSeq());
                    vo.setMaterials(this.convertMaterialDto(p.getMaterials()));
                    return vo;
                }).collect(Collectors.toList());
    }

    /**
     * 套餐生成明细描述
     *
     * @param mustOrderProducts
     * @param optionalGroups
     * @return
     */
    public String generateDescription(List<ItemDto> mustOrderProducts, List<com.wosai.pantheon.core.uitem.model.PackageOptionalGroup> optionalGroups) {

        List<String> descriptionList = Lists.newArrayList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mustOrderProducts)) {
            mustOrderProducts.forEach(p -> {
                String singleDesc = p.getItem().getName()
                        + Optional.ofNullable(p.getAddCount()).orElse(1)
                        + Optional.ofNullable(p.getItem().getUnit()).orElse("份");
                descriptionList.add(singleDesc);
            });
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(optionalGroups)) {
            optionalGroups.forEach(p -> {
                String singleDesc = p.getGroupName()
                        + Optional.ofNullable(p.getMustOrderNum()).orElse(1)
                        + "份(可选)";
                descriptionList.add(singleDesc);
            });
        }

        return org.apache.commons.lang3.StringUtils.join(descriptionList, "+");
    }


    public String generateDescriptionAfterConvert(List<ItemDetailVO> mustOrderProducts, List<PackageOptionalGroup> optionalGroups) {
        try {
            List<String> descriptionList = new ArrayList<>();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mustOrderProducts)) {
                mustOrderProducts.forEach(p -> {
                    String singleDesc = p.getItem().getName()
                            + Optional.ofNullable(p.getItem().getAddCount()).orElse(1)
                            + Optional.ofNullable(p.getItem().getUnit()).orElse("份");
                    descriptionList.add(singleDesc);
                });
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(optionalGroups)) {
                optionalGroups.forEach(p -> {
                    String singleDesc = p.getGroupName()
                            + Optional.ofNullable(p.getMustOrderNum()).orElse(1)
                            + "份(可选)";
                    descriptionList.add(singleDesc);
                });
            }

            return org.apache.commons.lang3.StringUtils.join(descriptionList, "+");
        } catch (Exception e) {
            LogUtils.logWarnWithError("generateDescriptionAfterConvert error", "generateDescriptionAfterConvert", mustOrderProducts, e);
            return null;
        }
    }

    /**
     * 补充售卖时间信息
     *
     * @param vo
     * @param dto
     */
    public static void appendSaleTime(ItemDetailVO vo, ItemDto dto) {
        Objects.requireNonNull(vo, "vo can not be null");
        ItemSaleTimeVO sv = toSaleTimeVo(dto);
        vo.setSaleTime(sv);
    }

    public static ItemSaleTimeVO toSaleTimeVo(ItemDto dto) {
        Objects.requireNonNull(dto, "dto can not be null");
        ItemSaleTimeVO sv = new ItemSaleTimeVO().setType(SaleTimeTypeEnum.ALL_TIME.getCode());
        if (Objects.nonNull(dto.getSaleTime())) {
            ItemSaleTime ist = dto.getSaleTime();
            sv.setType(ist.getType())
                    .setStartDate(ist.getStartDate())
                    .setEndDate(ist.getEndDate())
                    .setCycle(ist.getCycle())
                    .setTimes(toSalePtVos(ist.getTimes()));
        }
        return sv;
    }

    public static List<ItemSaleTimePartitionVO> toSalePtVos(List<TimeSection> ts) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(ts)) {
            return null;
        }
        return ts.stream()
                .map(t -> new ItemSaleTimePartitionVO().setStartTime(t.getStartTime()).setEndTime(t.getEndTime()))
                .collect(Collectors.toList());
    }

    /**
     * 当前时间是否在售
     *
     * @param itemDto
     * @return
     */
    public static boolean curTimeForSale(ItemDto itemDto) {
        Objects.requireNonNull(itemDto, "itemDto require not null");
        ItemSaleTime saleTime = itemDto.getSaleTime();
        if (Objects.isNull(saleTime)) {
            return true;
        }
        // 全时段售卖直接返回
        if (Objects.equals(SaleTimeTypeEnum.ALL_TIME.getCode(), saleTime.getType())) {
            return true;
        }
        LocalDateTime now = LocalDateTime.now();
        // 日期是否合适
        long timeStampNow = now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        boolean beforeStartDate = Objects.nonNull(saleTime.getStartDate()) && timeStampNow < saleTime.getStartDate();
        boolean afterEndDate = Objects.nonNull(saleTime.getEndDate()) && timeStampNow >= saleTime.getEndDate();
        if (beforeStartDate || afterEndDate) {
            return false;
        }
        // 是否在周期里
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(saleTime.getCycle())) {
            DayOfWeek dayOfWeek = now.getDayOfWeek();
            if (!saleTime.getCycle().contains(dayOfWeek.getValue())) {
                return false;
            }
        }
        // 是否在时间段内
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(saleTime.getTimes())) {
            boolean saleInPts = false;
            LocalTime localTime = now.toLocalTime();
            for (TimeSection ts : saleTime.getTimes()) {
                String startTimeStr = StringUtils.equals(ts.getStartTime(), "24:00") ? "23:59:59" : ts.getStartTime().concat(":00");
                String endTimeStr = StringUtils.equals(ts.getEndTime(), "24:00") ? "23:59:59" : ts.getEndTime().concat(":59");
                LocalTime startTime = LocalTime.parse(startTimeStr, DateTimeFormatter.ofPattern("HH:mm:ss"));
                LocalTime endTime = LocalTime.parse(endTimeStr, DateTimeFormatter.ofPattern("HH:mm:ss"));
                // 只要任一时段命中，校验就可停止
                if (localTime.isAfter(startTime) && localTime.isBefore(endTime)) {
                    saleInPts = true;
                    break;
                }
            }
            // 都没命中，直接返回不可售
            if (!saleInPts) {
                return false;
            }
        }
        return true;
    }

    /**
     * 过滤不在售卖时间的商品
     *
     * @param storeId
     * @param items
     * @return
     */
    public List<ItemDto> filterItemNotInSaleTime(String storeId, List<ItemDto> items) {
        if (CollectionUtils.isEmpty(items)) {
            return items;
        }
        boolean needFilter = this.needFilterItemNotInSaleTime(storeId);
        if (!needFilter) {
            return items;
        }
        items = items.stream()
                .filter(ItemHelper::curTimeForSale)
                .collect(Collectors.toList());
        return items;
    }

    /**
     * 是否需要过滤不在售卖时间的商品
     *
     * @param storeId
     * @return
     */
    public boolean needFilterItemNotInSaleTime(String storeId) {
        List<FindConfigByNameRequest> configReqs = Stream.of(MccUtils.DISPLAY_ITEM_NOT_IN_SALETIME, MccUtils.DISPLAY_ITEM_NOT_IN_SALETIME_FOR_MINI)
                .map(name -> {
                    FindConfigByNameRequest configReq = new FindConfigByNameRequest();
                    configReq.setAppId(AppId.UFOOD.getAppId());
                    configReq.setOwnerType(OwnerType.STORE_ID.getOwnerType());
                    configReq.setOwnerId(storeId);
                    configReq.setName(name);
                    return configReq;
                }).collect(Collectors.toList());
        List<ConfigResponse> configRes = configRemoteService.batchFindByName(configReqs);
        boolean switchOn = true;
        boolean effectMini = true;
        if (!CollectionUtils.isEmpty(configRes)) {
            switchOn = configRes.stream()
                    .filter(cr -> StringUtils.equalsIgnoreCase(cr.getName(), MccUtils.DISPLAY_ITEM_NOT_IN_SALETIME))
                    .findFirst()
                    .map(cr -> StringUtils.equalsIgnoreCase(cr.getValue(), MccUtils.SWITCH_ON))
                    .orElse(true);
            effectMini = configRes.stream()
                    .filter(cr -> StringUtils.equalsIgnoreCase(cr.getName(), MccUtils.DISPLAY_ITEM_NOT_IN_SALETIME_FOR_MINI))
                    .findFirst()
                    .map(cr -> StringUtils.equalsIgnoreCase(cr.getValue(), MccUtils.SWITCH_ON))
                    .orElse(true);
        }
        // 开关开启且对小程序生效 = 不过滤，其余情况均做过滤
        return !(switchOn && effectMini);
    }


    public Map<String, List<CategoryInfo>> categoryDiscountDetails(String storeId, Integer serviceType, EffectiveType effectiveType) {
        // 查询门店下挂靠在分类上的活动
        // 这个map的key只有两个, 分别是"singleCategory", "secondCategory", 分别对应单品和第二份活动
        return activityRemoteService.categoryDiscountDetails(
                CategoryActivityDetailRequest.builder()
                        .storeId(storeId)
                        .effectiveType(effectiveType)
                        .orderType(serviceType == 1 ? OrderType.TAKE_OUT_ORDER : OrderType.SUBSCRIBE_ORDER)
                        .build()
        );
    }

    /**
     * 处理返回不同尺寸的图片
     * @param detailVo
     * @param imageSize
     */
    public void processImageSizeForShow(ItemDetailVO detailVo, Integer imageSize) {
        if (Objects.isNull(imageSize)) {
            return;
        }
        ProductConstant.MediaSpecTypeEnum imageSizeEnum = ProductConstant.MediaSpecTypeEnum.getByCode(imageSize);
        if (Objects.isNull(imageSizeEnum)) {
            return;
        }
        List<ItemImageVO> images = detailVo.getItem().getImages();
        if (CollectionUtils.isEmpty(images)) {
            return;
        }
        for (ItemImageVO image : images) {
            if (CollectionUtils.isEmpty(image.getSpecs())) {
                continue;
            }
            Map<Integer, ItemImageVO.Spec> sizeMap = image.getSpecs().stream().collect(Collectors.toMap(ItemImageVO.Spec::getType, Function.identity(), (k1, k2) -> k1));
            // 找对应尺寸的图片,若没有对应尺寸的图片，优先取1：1，再取原图
            ItemImageVO.Spec selected = sizeMap.get(imageSizeEnum.getCode());
            if (Objects.isNull(selected)) {
                selected = sizeMap.get(ProductConstant.MediaSpecTypeEnum.RATIO_1_1.getCode());
            }
            if (Objects.isNull(selected)) {
                selected = sizeMap.get(ProductConstant.MediaSpecTypeEnum.ORIGINAL.getCode());
            }
            if (Objects.isNull(selected)) {
                continue;
            }
            image.setDisplayUrl(selected.getUrl());
        }
        // 拼接成字符串，返回
        String photoUrls = images.stream().map(ItemImageVO::getDisplayUrl).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        detailVo.getItem().setPhotoUrl(photoUrls);
    }

}
