package com.wosai.pantheon.uf4c.service;

import com.wosai.market.user.dto.SaveUserPreferencesRequest;
import com.wosai.market.user.enums.PreferencesBizTypeEnums;
import com.wosai.market.user.service.UserPreferencesRemoteService;
import com.wosai.pantheon.uf4c.util.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
public class UserPreferencesService {

    @Autowired
    private UserPreferencesRemoteService userPreferencesRemoteService;

    public void saveAddressFloor(String userId, String addressId, String floor) {
        if (StringUtils.isAnyBlank(userId, addressId)) {
            return;
        }
        SaveUserPreferencesRequest preferencesRequest = new SaveUserPreferencesRequest();
        preferencesRequest.setUserId(userId);
        preferencesRequest.setBizType(PreferencesBizTypeEnums.ADDRESS_FLOOR);
        preferencesRequest.setBizId(addressId);
        preferencesRequest.setContent(floor);
        CompletableFuture.runAsync(() -> savePreference(preferencesRequest));
    }


    private void savePreference(SaveUserPreferencesRequest preferencesRequest) {
        LogUtils.logInfo("保存用户偏好信息", "savePreference", preferencesRequest);
        try {
            userPreferencesRemoteService.savePreference(preferencesRequest);
        } catch (Exception e) {
            LogUtils.logWarn("保存用户偏好信息出错", "savePreference", preferencesRequest, e);
        }

    }
}
