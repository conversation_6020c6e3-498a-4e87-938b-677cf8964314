package com.wosai.pantheon.uf4c.gather.types;

import com.wosai.market.mcc.api.constant.MerchantKeys;
import com.wosai.market.mcc.api.constant.StoreActivatedKeys;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.CodeScene;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ServiceTypeData;
import com.wosai.pantheon.uf4c.util.MccUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class StoreServiceTypeProcessor extends AbstractServiceProcessor {
    public static final String[] QRCODE_TYPES = new String[]{"SELF_HELP_STORE", "STORE", "TABLE"};
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Override
    public ServiceTypeData.ServiceItem process(GatherRequest request, Map<String, Object> dataMap) {
        ServiceTypeData.ServiceItem serviceItem = new ServiceTypeData.ServiceItem();
        serviceItem.setSort(1);
        serviceItem.setName("店内堂食");
        serviceItem.setServiceType(2);
        serviceItem.setServiceTypeName(OrderType.SUBSCRIBE_ORDER.getMsg());
        serviceItem.setShortName("堂食");
        serviceItem.setActive(isActive(request, dataMap));


        if ("1242".equalsIgnoreCase(request.getMpScene()) || "1243".equalsIgnoreCase(request.getMpScene())) {
            return null;
        }

        // 从校园页进入不展示堂食
        if (Constants.From.CAMPUS.equalsIgnoreCase(request.getFrom())) {
            return null;
        }

        Map<String, String> mccMap = (Map<String, String>) MapUtils.getMap(dataMap, "mcc");
        Map<String, String> mchMap = (Map<String, String>) MapUtils.getMap(dataMap, "mch_mcc");
        boolean isRetail = Objects.equals(Constants.RETAIL, MapUtils.getString(mchMap, MerchantKeys.MERCHANT_TYPE));
        // 到店业务开通状态
        boolean scanActivated = false;
        if (!isRetail) {
            scanActivated = MccUtils.getBooleanValue(mccMap, StoreActivatedKeys.SCAN_ACTIVATED, false);
        }
        boolean onBusiness = MccUtils.getBooleanValue(mccMap, Constants.BUSINESS_STATUS, false);
        boolean needShowScan = MapUtils.getBooleanValue(mccMap, Constants.ONLY_IN_STORE, false);
        if (MapUtils.isNotEmpty(MapUtils.getMap(dataMap, "terminal"))) {
            Map<String, String> terminalMap = (Map<String, String>) MapUtils.getMap(dataMap, "terminal", null);
            // 桌台名称
            String name = MapUtils.getString(terminalMap, Constants.NAME);
            // 桌台名称
            String tableName = MapUtils.getString(terminalMap, Constants.TABLE_NAME);
            if (StringUtils.isBlank(tableName)) {
                tableName = name;
            }
            // 二维码类型
            String qrcodeType = MapUtils.getString(terminalMap, Constants.QRCODE_TYPE);
            // 业务类型
            int jjzBusinessType = MapUtils.getIntValue(terminalMap, Constants.JJZ_BUSINESS_TYPE, 1);
            needShowScan = needShowScan && jjzBusinessType == 1 && StringUtils.isEmpty(tableName);
            if (StringUtils.isNotBlank(qrcodeType)) {
                needShowScan = needShowScan && ArrayUtils.contains(QRCODE_TYPES, qrcodeType);
            }
        }
        if (needShowScan) {
            serviceItem.setAlias("扫一扫店内下单");
            serviceItem.setAction(Constants.ServiceTypeAction.SCAN);
        }

        return scanActivated && onBusiness ? serviceItem : null;
    }

    @Override
    public boolean isActive(GatherRequest request, Map<String, Object> dataMap) {
        Map<String, String> extraMap = (Map<String, String>) MapUtils.getMap(dataMap, "extra");
        String scene = MapUtils.getString(extraMap, "scene", "");
        String goodsId = MapUtils.getString(extraMap, "goodsId");

        if (apolloConfigHelper.getBooleanConfigValueByKey("enableOnlyBuyAgainPriority", false)) {
            if (StringUtils.isNotBlank(request.getBuyAgainOrderType())) {
                // 再来一单进入
                return OrderType.SUBSCRIBE_ORDER.getMsg().equalsIgnoreCase(request.getBuyAgainOrderType());
            }
        }

        if (MapUtils.isNotEmpty(MapUtils.getMap(dataMap, "terminal"))) {
            if (CodeScene.getCodeScene(scene) == CodeScene.P) {
                return false;
            }
            // 扫码进入
            return true;
        }
        if (StringUtils.isNotBlank(request.getBuyAgainOrderType())) {
            // 再来一单进入
            return OrderType.SUBSCRIBE_ORDER.getMsg().equalsIgnoreCase(request.getBuyAgainOrderType());
        }
        // 从门店详情页进入
        if (StringUtils.isNotBlank(request.getFrom())) {
            return Constants.From.MANUAL.equalsIgnoreCase(request.getFrom());
        }
        // 分享商品进入，默认堂食
        return CodeScene.getCodeScene(scene) != CodeScene.P && StringUtils.isNotBlank(goodsId);
    }
}
