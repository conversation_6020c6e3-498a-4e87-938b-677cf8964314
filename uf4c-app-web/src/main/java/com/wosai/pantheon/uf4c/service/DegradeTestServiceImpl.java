package com.wosai.pantheon.uf4c.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.uf4c.api.DegradeTestService;
import com.wosai.pantheon.uf4c.model.vo.HotSaleItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
@AutoJsonRpcServiceImpl
public class DegradeTestServiceImpl implements DegradeTestService {
    private static final String HOTSALE_REDIS_KEY_PRE = "com.uf4c.hotsale:";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public String test(String name) {
        return "success";
    }

    private String getHotSaleReidsKey(String storeId, Integer serviceType) {
        return HOTSALE_REDIS_KEY_PRE + storeId + ":" + serviceType;
    }

    @Override
    public String cleanHotsalCache(String storeId) {
        redisTemplate.delete(getHotSaleReidsKey(storeId, 1));
        redisTemplate.delete(getHotSaleReidsKey(storeId, 2));
        return "done";
    }
}
