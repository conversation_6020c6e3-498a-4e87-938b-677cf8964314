package com.wosai.pantheon.uf4c.web.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.fallbackconfig.server.BlockHandleServer;
import com.wosai.pantheon.uf4c.model.*;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.service.apisix.SingleCartService;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.web.api.ListResult;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * created by shij on 2019/7/26
 */
@RestController
@RequestMapping(path = "/api/v1/carts", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequiredArgsConstructor
public class CartController {


    @Autowired
    SingleCartService singleCartService;


    @PostMapping
    @ResponseBody
    public Cart addCart(@RequestBody @Valid CartItemCreate create) {
        return singleCartService.addCart(new ApiRequest<>(create));
    }

    @DeleteMapping("/{id}")
    @ResponseBody
    public Cart deleteCartItem(@PathVariable("id") String id, @RequestParam Map value) {
        value.put("id", id);
        return singleCartService.reduceCartItemCount(ApiRequest.buildGetRequest(value));
    }

    @PostMapping("/add")
    @ResponseBody
    public Cart addCartV2(@RequestBody @Valid CartItemCreate create) {
        return singleCartService.addCart(new ApiRequest<>(create));
    }

    @GetMapping("/trans")
    @ResponseBody
    public boolean trans(@RequestParam Map value) {
        singleCartService.transCart(ApiRequest.buildGetRequest(value));
        return true;
    }


    @PostMapping("/set")
    @ResponseBody
    public Cart setCartItem(@RequestBody @Valid CartItemCreate create) {
        return singleCartService.setCartItem(new ApiRequest<>(create));
    }


    @PostMapping("/people/set")
    @ResponseBody
    public Cart set(@RequestBody Map request) {

        return singleCartService.setPeopleNum(new ApiRequest<>(request));
    }

    @GetMapping("/delete")
    @ResponseBody
    public Cart deleteCartItemV2(@RequestParam Map value) {
        return singleCartService.reduceCartItemCount(ApiRequest.buildGetRequest(value));
    }

    @GetMapping
    @ResponseBody
    public Cart getCart(@RequestParam Map value) {

        return singleCartService.getCart(ApiRequest.buildGetRequest(value));
    }

    @GetMapping("/reset")
    public Cart resetCart(@RequestParam Map value) {

        return singleCartService.resetCart(ApiRequest.buildGetRequest(value));
    }

    @GetMapping("/check")
    public CartCheckResultDTO checkItemStatus(@RequestParam Map value) {

        return singleCartService.checkItemStatus(ApiRequest.buildGetRequest(value));
    }

    /**
     * 购物车内加减购加料
     *
     * @param create
     * @return
     */
    @PostMapping(value = "/addMaterialAndRedeem")
    @ResponseBody
    public CartAndRedeem addOrReduceMaterial(@RequestBody CartItemCreate create) {
        return singleCartService.addOrReduceMaterial(new ApiRequest<>(create));
    }


    @PostMapping("/addAndRedeem")
    @ResponseBody
    public CartAndRedeem addCartAndRedeem(@RequestBody @Valid CartItemCreate create) {
        return singleCartService.addCartAndRedeem(new ApiRequest<>(create));
    }

    @PostMapping("/addAndRedeem/batch")
    @ResponseBody
    public CartAndRedeem addCartAndRedeem(@RequestBody @Valid CartItemCreateBatch request) {
        return singleCartService.addCartAndRedeemBatch(new ApiRequest<>(request));
    }

    @PostMapping("/addAndRedeemBySpuIds")
    @ResponseBody
    public CartAndRedeem addCartAndRedeemBySpuIds(@RequestBody @Valid SpecSpuCartItemCreate request) {
        return singleCartService.addCartAndRedeemBySpuIds(new ApiRequest<>(request));
    }

    @PostMapping("/getCartAndRedeem")
    @ResponseBody
    public CartAndRedeem getCartAndRedeem(@RequestBody @Valid CartsRequest request) {
        return getCartAndRedeemTemporary(request);
    }

    /**
     * 2024-03-06
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/CartController/getCartAndRedeem",blockHandlerClass = BlockHandleServer.class,blockHandler = "handleGetCartAndRedeem")
    private CartAndRedeem getCartAndRedeemTemporary(CartsRequest request) {
        return singleCartService.getCartAndRedeem(new ApiRequest<>(request));
    }


}
