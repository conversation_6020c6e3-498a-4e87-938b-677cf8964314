package com.wosai.pantheon.uf4c.service.apisix;


import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.jielong.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 接龙活动、订单
 */
@JsonRpcService(value = "/rpc/jielong")
@Validated
public interface JielongService {


    /**
     * 通过接龙id查询接龙首页
     *
     * @param apiRequest
     * @return
     */
    Map<String, Object> query(ApiRequest apiRequest);

    /**
     * 加减购物车
     *
     * @param apiRequest
     * @return
     */
    JielongCart addCart(@Valid ApiRequest<JielongCartItem> apiRequest);

    /**
     * 下单页面校验
     *
     * @param apiRequest
     * @return
     */
    PayCheckResponse check(@Valid ApiRequest<JielongCheckRequest> apiRequest);


    /**
     * 接龙订单下单
     *
     * @param apiRequest
     * @return
     */
    PayResult pay(@Valid ApiRequest<JielongPayRequest> apiRequest);


    /**
     * 群接龙页面分页查询历史订单
     *
     * @param apiRequest
     * @return
     */
    List<JielongPage.OrderInfo> orderList(@Valid ApiRequest<JielongOrderQuery> apiRequest);

    /**
     * 支付成功后，用orderSn查订单信息
     *
     * @param apiRequest
     * @return
     */
    JielongPage.OrderInfo getOrderBySn(ApiRequest apiRequest);



}
