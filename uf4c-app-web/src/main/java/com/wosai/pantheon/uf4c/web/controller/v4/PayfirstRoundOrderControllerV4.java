package com.wosai.pantheon.uf4c.web.controller.v4;

import com.wosai.market.trade.modal.PayResult;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.PayFirstRoundOrderPayRequest;
import com.wosai.pantheon.uf4c.model.payfirsttableorder.ItemsAndRedeem;
import com.wosai.pantheon.uf4c.service.apisix.OrderRoundService;
import com.wosai.pantheon.uf4c.web.exception.BaseException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.enums.ErrorTipWayEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.query.table.TableQueryRequest;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @since 2022/5/20
 */
@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@RestController
@RequestMapping(path = "/api/v4/orders/payfirst/round", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class PayfirstRoundOrderControllerV4 {


    @Autowired
    private OrderRoundService orderRoundService;

    @Autowired
    private TableRpcServiceV2 tableRpcServiceV2;

    @PostMapping("/pay")
    @ResponseBody
    @SneakyThrows
    public PayResult pay(@RequestBody PayFirstRoundOrderPayRequest request) {
        PayResult payResult = new PayResult();
        try {
            //设置是否需要检查购物车商品
            request.setCheckCartItem(true);
            return orderRoundService.addGoodsAndPay(new ApiRequest<>(request));
        } catch (BaseException ex) {
            ErrorTipWayEnum errorTipWay = ErrorTipWayEnum.POPUP;
            String message = ex.getMessage();
            log.warn("RoundOrderControllerV4.addAndPay BaseException, storeId{},tableId:{},tableNo:{}", request.getStoreId(), request.getTableId(), request.getTableNo(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(errorTipWay)
                    .errorCode(ex.getCode())
                    .errorMsg(message)
                    .build();

            if (StringUtils.equals(ex.getCode(), ReturnCode.ORDER_CONCURRENT.getCode())) {
                TableQueryRequest tableQueryRequest = new TableQueryRequest();
                tableQueryRequest.setTableId(request.getTableId());
                Result<String> orderQueryResult = tableRpcServiceV2.getTableOrderNo(tableQueryRequest);
                if (orderQueryResult.isSuccess()) {
                    payResult.setClientSn(orderQueryResult.getData());
                }
            }

            payResult.setCartCheckResult(cartCheckResult);
        } catch (Exception ex) {
            log.warn("RoundOrderControllerV4.addAndPay Exception, storeId{},tableId:{},tableNo:{}", request.getStoreId(), request.getTableId(), request.getTableNo(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(ErrorTipWayEnum.POPUP)
                    .errorCode(null)
                    .errorMsg("系统错误，请联系收钱吧客服处理" + TraceContext.traceId())
                    .build();

            payResult.setCartCheckResult(cartCheckResult);
        }
        return payResult;
    }


    @PostMapping("/addAndPay")
    @ResponseBody
    @SneakyThrows
    public PayResult addAndPay(@RequestBody PayFirstRoundOrderPayRequest request) {
        request.setAddAndPay(true);
        return pay(request);
    }

    @PostMapping("/cancel")
    @ResponseBody
    @SneakyThrows
    public boolean cancel(@RequestBody PayFirstRoundOrderPayRequest request) {
        return orderRoundService.cancelBatchGoods4PayFirstRoundOrder(request);
    }

    /**
     * 获取先付围餐需要展示的商品和优惠
     */
    @PostMapping("/itemsAndRedeem")
    @ResponseBody
    @SneakyThrows
    public ItemsAndRedeem getOrderGoodsAndRedeem(@RequestBody PayFirstRoundOrderPayRequest request) {
        //要获取购物车商品
        //如果指定了batch_no那么就获取该批次商品
        //还需要获取收银机、app端下单但是未支付的商品
        return orderRoundService.getOrderGoodsAndRedeem(request);

    }


}
