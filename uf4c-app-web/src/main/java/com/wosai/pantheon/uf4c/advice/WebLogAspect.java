package com.wosai.pantheon.uf4c.advice;

import com.wosai.pantheon.uf4c.util.IpUtils;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.wosai.pantheon.uf4c.util.TraceContextUtils.fake;
import static net.logstash.logback.argument.StructuredArguments.keyValue;


/**
 * created by shij on 2019/4/25
 */
@Aspect
@Component
@Slf4j
public class WebLogAspect {


    @Pointcut("execution(* com.wosai.pantheon.uf4c.web.controller..*.*(..)) && !execution(* com.wosai.pantheon.uf4c.web.controller.HealthyController.*(..)) && !execution(* com.wosai.pantheon.uf4c.web.controller.MetricsController.*(..))\"" )
    private void controller() {
    }

    @Around("controller()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
//        String args = JacksonUtil.toJsonString(joinPoint.getArgs());
        Object[] args = joinPoint.getArgs();

        Map<String, Object> logMap = new HashMap<>();
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(servletRequestAttributes)) {
            return joinPoint.proceed();
        }
        HttpServletRequest request = servletRequestAttributes.getRequest();
        logMap.put("ip", IpUtils.getIp(request));
        logMap.put("uri", request.getRequestURI());
        String userId = null;
        if (ThreadLocalHelper.getRequestContextThreadLocal().get() != null && ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO() != null) {
            userId = ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO().getUserId();
            logMap.put("user_id", userId);
        }
        try {
            long startTime = System.currentTimeMillis();
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            logMap.put("cost", endTime - startTime);
            log.info(JacksonUtil.toJsonString(logMap),
                    keyValue("class", className),
                    keyValue("method", methodName),
                    keyValue("arguments", args),
                    keyValue("result", result),
                    keyValue("user_id", userId),
                    keyValue("uri", request.getRequestURI()),
                    keyValue("fake", fake()));
            return result;
        }catch (Exception ex){
            if (ex instanceof ParamException || ex instanceof BusinessException || ex instanceof com.wosai.pantheon.uf4c.web.exception.BusinessException){
                log.warn(JacksonUtil.toJsonString(logMap),
                        keyValue("class", className),
                        keyValue("method", methodName),
                        keyValue("arguments", args),
                        keyValue("user_id", userId),
                        keyValue("uri", request.getRequestURI()),
                        keyValue("fake", fake()),
                        keyValue("exception_type", ex.getClass().getName()),
                        keyValue("exception_msg", ex.getMessage()));
                throw ex;
            }
            if (ex instanceof MethodArgumentNotValidException){
                log.warn(JacksonUtil.toJsonString(logMap),
                        keyValue("class", className),
                        keyValue("method", methodName),
                        keyValue("arguments", args),
                        keyValue("user_id", userId),
                        keyValue("uri", request.getRequestURI()),
                        keyValue("fake", fake()),
                        keyValue("exception_type", ex.getClass().getName()),
                        keyValue("exception_msg", ex.getMessage()));
                throw ex;
            }

            log.error(JacksonUtil.toJsonString(logMap),
                    keyValue("class", className),
                    keyValue("method", methodName),
                    keyValue("arguments", args),
                    keyValue("user_id", userId),
                    keyValue("uri", request.getRequestURI()),
                    keyValue("fake", fake()),
                    ex);
            throw ex;
        }

    }
}
