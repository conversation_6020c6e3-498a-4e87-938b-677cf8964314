package com.wosai.pantheon.uf4c.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wosai.market.dto.StoreHotSaleConfigDO;
import com.wosai.market.dto.StoreHotSaleConfigExcludeDTO;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.request.V2.CommonRequest;
import com.wosai.market.response.BaseItemSummary;
import com.wosai.market.response.ItemSummary;
import com.wosai.market.response.ReportResponse;
import com.wosai.market.service.StatisticsService;
import com.wosai.market.service.store.StoreHotSaleConfigService;
import com.wosai.market.service.store.StoreHotSaleExcludeSpuService;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.model.ItemQuery;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.OrderGoodsTagEnum;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.HotSaleProductConfig;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.util.WeakReferenceCaller;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.MoneyUtil;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * <AUTHOR>
 * @create 2022/11/16
 */
@Service
@Slf4j
public class HotSaleItemHelper {

    @Autowired
    private ItemHelper itemHelper;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private ItemService itemRpcService;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private StoreHotSaleConfigService hotSaleConfigService;

    @Resource
    private StoreHotSaleExcludeSpuService hotSaleExcludeSpuService;

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 查询门店配置，默认未开启
     *
     * @param storeId
     * @return
     */
    public HotSaleProductConfig getConfig(String storeId) {
        HotSaleProductConfig config = new HotSaleProductConfig();
        config.setStoreId(storeId);
        config.setOpenSaleProduct(false);
        try {
            FindConfigByNameRequest findConfigByNameRequest = new FindConfigByNameRequest();
            findConfigByNameRequest.setAppId(AppId.UFOOD.getAppId());
            findConfigByNameRequest.setOwnerType(OwnerType.STORE_ID.getOwnerType());
            findConfigByNameRequest.setName(MccUtils.HOT_SALE_PRODUCT_CONFIG);
            findConfigByNameRequest.setOwnerId(storeId);
            ConfigResponse configResponse = configRemoteService.findByName(findConfigByNameRequest);
            if (Objects.nonNull(configResponse) && StringUtils.isNotBlank(configResponse.getValue())) {
                config = JSON.parseObject(configResponse.getValue(), HotSaleProductConfig.class);
            }
            return config;
        } catch (Exception ex) {
            log.warn("query hotSale item config error", keyValue("method", "queryHotSaleConfigFromMcc"), keyValue("storeId", storeId), ex);
            return config;
        }
    }

    /**
     * 单门店热销商品
     *
     * @param storeId
     * @param serviceType
     * @param days
     * @param config
     * @return
     */
    public ListResult<ItemDetailVO> listByStore(String storeId, int serviceType, long days, HotSaleProductConfig config) {
        if (StringUtils.isBlank(storeId) || Objects.isNull(config)) {
            return ListResult.emptyResult();
        }
        // 未开启，直接返回
        if (!config.isOpenSaleProduct()) {
            return ListResult.emptyResult();
        }
        // 查询手动设置的过滤商品
        Set<String> excludeItemIds = new HashSet<>(16);
        if (config.isAutoGenerate() && config.isHasExcludeSpus()) {
            Result<List<StoreHotSaleConfigExcludeDTO>> excludeResult = hotSaleExcludeSpuService.findByStoreId(storeId);
            if (Objects.nonNull(excludeResult) && CollectionUtils.isNotEmpty(excludeResult.getData())) {
                excludeResult.getData().forEach(p -> excludeItemIds.add(p.getSpuId()));
            }
        }

        // 自动生成 or 手动设置，获取商品ID和销量
        List<BaseItemSummary> itemSummaries;
        if (config.isAutoGenerate()) {
            itemSummaries = this.autoSummary(storeId, days, config, excludeItemIds);
        } else {
            itemSummaries = this.manualSummary(storeId, days);
        }
        if (CollectionUtils.isEmpty(itemSummaries)) {
            return ListResult.emptyResult();
        }

        // 补全商品信息
        List<ItemDetailVO> items = this.fillItemWithSaleCount(storeId, serviceType, itemSummaries);
        if (CollectionUtils.isEmpty(items)) {
            return ListResult.emptyResult();
        }

        // 二次过滤
        if (config.isAutoGenerate()) {
            items = items.stream()
                    .filter(p -> !this.autoExclude(config.isAutoFilter(), p.getItem().getName(), p.getItem().getPrice()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(items)) {
            return ListResult.emptyResult();
        }

        int maxShowNum = Optional.ofNullable(config.getMaxShowNum()).orElse(5);
        AtomicInteger seq = new AtomicInteger(1);
        items = items.stream()
                .sorted((o1, o2) -> MoneyUtil.getNotNullAmount(o2.getItem().getLast30DaysSaleCount()).compareTo(MoneyUtil.getNotNullAmount(o1.getItem().getLast30DaysSaleCount())))
                .limit(maxShowNum)
                .peek(p -> {
                    p.getItem().setItemTag(TagUtil.addTag(p.getItem().getItemTag(), OrderGoodsTagEnum.HOT_SALE.getValue()));
                    p.getItem().setHotsaleSeq(seq.getAndIncrement());
                    p.getItem().setHotsaleProduct(true);
                })
                .collect(Collectors.toList());
        return new ListResult<>(items);
    }

    /**
     * 自动生成的热销商品ID
     *
     * @param storeId
     * @param config
     * @param days
     * @param excludeItemIds
     * @return
     */
    public List<BaseItemSummary> autoSummary(String storeId, long days, HotSaleProductConfig config, Set<String> excludeItemIds) {
        List<BaseItemSummary> itemSummaries = new ArrayList<>(16);
        ReportResponse reportResponse = this.statisticsFromDataReport(storeId, null, days);
        if (Objects.isNull(reportResponse) || Objects.isNull(reportResponse.getItemSummary()) || CollectionUtils.isEmpty(reportResponse.getItemSummary().getItemSummary())) {
            return itemSummaries;
        }
        // 过滤 + 数量限制，这里返回最大数量 + 10，因为后续还有其他筛选条件
        int maxShowNum = Optional.ofNullable(config.getMaxShowNum()).orElse(5);
        itemSummaries = reportResponse.getItemSummary().getItemSummary().stream()
                .filter(p -> !this.autoExclude(config.isAutoFilter(), p.getName()))
                .filter(p -> !this.manualExclude(excludeItemIds, p.getItemId()))
                .limit(maxShowNum + 10)
                .collect(Collectors.toList());
        return itemSummaries;
    }

    /**
     * 手动设置的热销商品ID
     *
     * @param storeId
     * @return
     */
    public List<BaseItemSummary> manualSummary(String storeId, long days) {
        List<BaseItemSummary> summaries = new ArrayList<>(16);
        // 查询手动配置的商品
        Result<List<StoreHotSaleConfigDO>> manualResult = hotSaleConfigService.listByStoreId(storeId);
        if (Objects.isNull(manualResult) || !manualResult.isSuccess() || CollectionUtils.isEmpty(manualResult.getData())) {
            return summaries;
        }
        // 补充销量信息
        List<String> itemIds = manualResult.getData().stream().map(StoreHotSaleConfigDO::getSpuId).collect(Collectors.toList());

        ReportResponse reportResponse = this.statisticsFromDataReport(storeId, itemIds, days);
        Map<String, BaseItemSummary> itemIdToSummaryMap = Optional.ofNullable(reportResponse)
                .map(ReportResponse::getItemSummary)
                .map(ItemSummary::getItemSummary)
                .orElseGet(() -> new ArrayList<>(16))
                .stream()
                .collect(Collectors.toMap(BaseItemSummary::getItemId, Function.identity(), (k1, k2) -> k1));

        summaries = itemIds.stream()
                .map(id -> {
                    BaseItemSummary summary = MapUtils.getObject(itemIdToSummaryMap, id);
                    if (Objects.isNull(summary)) {
                        summary = new BaseItemSummary();
                        summary.setItemId(id);
                        summary.setCount(0L);
                        summary.setSaleCount(0D);
                    }
                    return summary;
                }).collect(Collectors.toList());
        return summaries;
    }

    /**
     * 统一自动过滤的配置，例如特定名称、特定价格
     *
     * @param name
     * @return true = 需要过滤，false = 不需要过滤
     */
    public boolean autoExclude(boolean checkName, String name, boolean checkPrice, Integer price) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        ApolloConfigHelper.HotsaleBlackList autoExcludeConfig = apolloConfigHelper.getHotsaleBlackList();
        if (Objects.isNull(autoExcludeConfig)) {
            return false;
        }
        if (checkName) {
            // 精准匹配的关键词
            if (CollectionUtils.isNotEmpty(autoExcludeConfig.getAccurateList())) {
                for (String acKeyWord : autoExcludeConfig.getAccurateList()) {
                    if (StringUtils.equals(acKeyWord, name)) {
                        return true;
                    }
                }
            }
            // 模糊匹配的关键词
            if (CollectionUtils.isNotEmpty(autoExcludeConfig.getFuzzyList())) {
                for (String fuzKeyWord : autoExcludeConfig.getFuzzyList()) {
                    if (name.contains(fuzKeyWord)) {
                        return true;
                    }
                }
            }
        }
        if (checkPrice) {
            if (Objects.isNull(price)) {
                return true;
            }
            if (price < autoExcludeConfig.getMinPrice()) {
                return true;
            }
        }
        return false;
    }

    public boolean autoExclude(boolean autoFilter, String itemName, Integer price) {
        return autoFilter && this.autoExclude(true, itemName, true, price);
    }

    public boolean autoExclude(boolean autoFilter, String itemName) {
        return autoFilter && this.autoExclude(true, itemName, false, null);
    }

    public boolean manualExclude(Set<String> excludeItemIds, String itemId) {
        return CollectionUtils.isNotEmpty(excludeItemIds) && excludeItemIds.contains(itemId);
    }

    /**
     * 统计门店商品销量
     *
     * @param storeId
     * @param itemIds 不传返回所有商品的销量
     * @param days
     * @return
     */
    public ReportResponse statisticsFromDataReport(String storeId, List<String> itemIds, long days) {
        try {
            CommonRequest commonRequest = CommonRequest.builder()
                    .begin(LocalDateTime.now().plusDays(-days)).end(LocalDateTime.now())
                    .storeSet(Sets.newHashSet(storeId))
                    .spuIdList(itemIds)
                    .build();
            ItemSummary itemSummary = WeakReferenceCaller.call(() -> statisticsService.hotSaleStatistics(commonRequest));
            ReportResponse reportResponse = new ReportResponse();
            reportResponse.setItemSummary(itemSummary);
            return reportResponse;
        } catch (Exception ex) {
            log.warn("statistics from data report error", keyValue("method", "statisticsFromDataReport"), keyValue("storeId", storeId), ex);
            return null;
        }
    }

    public List<ItemDetailVO> fillItemWithSaleCount(String storeId, int serviceType, List<BaseItemSummary> itemSummaries) {
        if (CollectionUtils.isEmpty(itemSummaries)) {
            return Lists.newArrayList();
        }
        // 查询商品信息
        List<String> itemIds = itemSummaries.stream().map(BaseItemSummary::getItemId).collect(Collectors.toList());
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setServiceType(serviceType);
        itemQuery.setForSale(true);
        itemQuery.setSaleTerminal(Constants.SaleTerminal.MINI);
        ListResult<ItemDto> itemResult = itemRpcService.findItemDetailsByIdAndQuery(storeId, itemIds, itemQuery, false, null);
        if (Objects.isNull(itemResult) || CollectionUtils.isEmpty(itemResult.getRecords())) {
            return Lists.newArrayList();
        }
        ListResult<ItemDetailVO> itemVoResult = itemHelper.processItemDetailList(itemResult, serviceType, ThreadLocalHelper.getUserId());
        Map<String, BaseItemSummary> itemIdToSummaryMap = itemSummaries.stream()
                .collect(Collectors.toMap(BaseItemSummary::getItemId, Function.identity(), (k1, k2) -> k1));

        return itemVoResult.getRecords().stream()
                .peek(iv -> {
                    BaseItemSummary summary = MapUtils.getObject(itemIdToSummaryMap, iv.getItem().getId());
                    if (Objects.isNull(summary)) {
                        iv.getItem().setLast30DaysSaleCount(0L);
                    } else {
                        iv.getItem().setLast30DaysSaleCount(summary.getCount());
                    }
                }).collect(Collectors.toList());
    }

}
