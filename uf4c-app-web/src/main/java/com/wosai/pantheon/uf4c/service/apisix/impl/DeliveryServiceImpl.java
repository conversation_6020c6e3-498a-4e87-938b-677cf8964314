package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.trade.modal.upayDelivery.RiderLocation;
import com.wosai.market.trade.service.PayService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.service.apisix.DeliveryService;
import com.wosai.smartbiz.base.exceptions.ParamException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class DeliveryServiceImpl implements DeliveryService {
    @Autowired
    private PayService payService;

    @Override
    public RiderLocation getRiderLocation(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();

        String sn = MapUtils.getString(queryParam, "sn");

        if (StringUtils.isBlank(sn)){
            throw new ParamException("订单sn不能为空");
        }

        return payService.getRiderLocation(sn);
    }
}
