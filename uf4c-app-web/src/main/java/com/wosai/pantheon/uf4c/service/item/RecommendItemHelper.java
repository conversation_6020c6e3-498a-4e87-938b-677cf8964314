package com.wosai.pantheon.uf4c.service.item;

import com.alibaba.fastjson.JSON;
import com.wosai.market.dto.product.ProductDetail;
import com.wosai.market.enums.SaleSceneEnum;
import com.wosai.market.service.store.StoreProductService;
import com.wosai.pantheon.uf4c.constant.ProductPromotionBizEnum;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.service.ItemHelper;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.gds.service.ProductRecommendRpcService;
import com.wosai.smartbiz.gds.vo.ProductRecommendRpcVO;
import com.wosai.smartbiz.gds.vo.StoreRecommendProductTuple;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/11/6
 */
@Service
public class RecommendItemHelper implements IProductPromotion<ProductPromotionQueryDTO, RecommendItemPromotion> {

    @Autowired
    ProductRecommendRpcService recommendRpcService;

    @Autowired
    StoreProductService storeProductService;

    @Autowired
    ItemHelper itemHelper;

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Resource
    ExecutorService productPromotionExecutor;

    /**
     * 缓存key格式
     */
    private final static String CACHE_KEY_FORMAT = "com.uf4c-app.product.promotion:%s:%s";

    /**
     * 缓存默认过期时间
     */
    private final static int CACHE_EXPIRE_HOUR = 8;

    @Override
    public ProductPromotionBizEnum bizScene() {
        return ProductPromotionBizEnum.RECOMMEND;
    }

    @Override
    public List<RecommendItemPromotion> queryByStores(List<ProductPromotionQueryDTO> reqs) {

        Map<String, RecommendItemPromotion> storeIdPromoMap = new ConcurrentHashMap<>(reqs.size());

        // 优先取缓存，每个门店单独一份缓存，因此使用multiGet
        List<String> storeIds = reqs.stream().map(ProductPromotionQueryDTO::getStoreId).collect(Collectors.toList());
        List<String> cacheKeys = storeIds.stream().map(this::genCacheKey).collect(Collectors.toList());
        List<Object> cacheResults = redisTemplate.opsForValue().multiGet(cacheKeys);
        if (CollectionUtils.isNotEmpty(cacheResults)) {
            cacheResults.forEach(cr -> {
                if (Objects.nonNull(cr)) {
                    RecommendItemPromotion p = JSON.parseObject(String.valueOf(cr), RecommendItemPromotion.class);
                    storeIdPromoMap.put(p.getStoreId(), p);
                }
            });
        }

        // 筛选出未命中缓存的门店，通过RPC请求查询推荐配置和补全商品信息
        List<ProductPromotionQueryDTO> uncachedReqs = reqs.stream().filter(req -> !storeIdPromoMap.containsKey(req.getStoreId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(uncachedReqs)) {
            List<CompletableFuture<Void>> collectRecommendItems = new ArrayList<>(uncachedReqs.size());

            // 查询门店的推荐配置
            List<String> uncachedStoreIds = uncachedReqs.stream().map(ProductPromotionQueryDTO::getStoreId).collect(Collectors.toList());
            Map<String, StoreRecommendProductTuple> recommendMap = Optional.ofNullable(recommendRpcService.findByStoreIds(uncachedStoreIds))
                    .orElseGet(ArrayList::new)
                    .stream()
                    .collect(Collectors.toMap(StoreRecommendProductTuple::getStoreId, Function.identity(), (k1, k2) -> k1));

            // 根据每个门店的不同推荐配置，在各自的线程中补全商品信息并放置缓存
            for (ProductPromotionQueryDTO uncachedReq : uncachedReqs) {
                String storeId = uncachedReq.getStoreId();
                CompletableFuture<Void> collectRecommendItem = CompletableFuture.runAsync(() -> {
                    RecommendItemPromotion rp = this.fillRecommendItemInfos(storeId, uncachedReq.getServiceType(), recommendMap);
                    redisTemplate.opsForValue().set(this.genCacheKey(storeId), JSON.toJSON(rp), CACHE_EXPIRE_HOUR, TimeUnit.HOURS);
                    storeIdPromoMap.put(storeId, rp);
                }, productPromotionExecutor);
                collectRecommendItems.add(collectRecommendItem);
            }
            CompletableFuture.allOf(collectRecommendItems.toArray(new CompletableFuture[0])).join();
        }

        // 处理返回数据
        return reqs.stream()
                .map(req -> {
                    RecommendItemPromotion np = new RecommendItemPromotion();
                    np.setStoreId(req.getStoreId());
                    np.setItems(Collections.emptyList());
                    RecommendItemPromotion op = storeIdPromoMap.get(req.getStoreId());
                    if (Objects.nonNull(op) && CollectionUtils.isNotEmpty(op.getItems())) {
                        List<ItemDetailVO> npItems = op.getItems().stream().limit(req.getCountLimit()).collect(Collectors.toList());
                        np.setItems(npItems);
                    }
                    return np;
                }).collect(Collectors.toList());
    }

    @Override
    public void refreshByStore(ProductPromotionQueryDTO req) {
        String storeId = req.getStoreId();
        redisTemplate.delete(this.genCacheKey(storeId));
    }

    public String genCacheKey(String storeId) {
        return String.format(CACHE_KEY_FORMAT, this.bizScene().getCode(), storeId);
    }

    /**
     * 补充推荐商品具体信息
     * @param storeId
     * @param serviceType
     * @param recommendMap
     * @return
     */
    private RecommendItemPromotion fillRecommendItemInfos(String storeId, Integer serviceType, Map<String, StoreRecommendProductTuple> recommendMap) {
        RecommendItemPromotion rp = new RecommendItemPromotion();
        rp.setStoreId(storeId);
        rp.setItems(Collections.emptyList());

        // 若没有推荐商品，默认放置空列表对象，也要放入缓存
        StoreRecommendProductTuple recommend = recommendMap.get(storeId);
        if (Objects.isNull(recommend) || CollectionUtils.isEmpty(recommend.getRecommendProducts())) {
            return rp;
        }

        // 补充商品信息
        List<String> spuIds = recommend.getRecommendProducts().stream().map(ProductRecommendRpcVO::getSpuId).distinct().collect(Collectors.toList());
        Result<List<ProductDetail>> productRs = storeProductService.getDetails(storeId, spuIds, SaleSceneEnum.getByServiceType(serviceType, SaleSceneEnum.SCAN));
        Map<String, ProductDetail> productMap = Optional.ofNullable(productRs)
                .map(Result::getData)
                .orElseGet(ArrayList::new)
                .stream()
                .collect(Collectors.toMap(p -> p.getSpu().getSpuId(), Function.identity(), (k1, k2) -> k1));

        List<ItemDetailVO> items = recommend.getRecommendProducts().stream()
                .sorted(Comparator.comparing(ProductRecommendRpcVO::getSort, Comparator.nullsLast(Integer::compareTo)))
                .map(r -> {
                    ProductDetail product = productMap.get(r.getSpuId());
                    if (Objects.isNull(product)) {
                        return null;
                    }
                    return itemHelper.processItemDetailV2(product, serviceType);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        rp.setItems(items);

        return rp;
    }

}
