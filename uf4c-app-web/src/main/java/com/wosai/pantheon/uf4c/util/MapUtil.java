package com.wosai.pantheon.uf4c.util;

import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

public class MapUtil {

    private static Pattern pattern;

    private static String camel2Dash(String source) {
        if (pattern == null) {
            pattern = Pattern.compile("([A-Z])");
        }
        return pattern.matcher(source).replaceAll("_$1").toLowerCase();
    }

    public static <T> T obj2Bean(final Object source, Class<T> targetClazz) throws IllegalAccessException, InstantiationException {
        if (source == null) return null;
        if (ClassUtils.isAssignable(source.getClass(), targetClazz)) {
            return (T) source;
        } else if (ClassUtils.isAssignable(source.getClass(), Map.class)) {
            return map2Bean((Map) source, targetClazz);
        }
        T target = targetClazz.newInstance();
        Field[] fields = FieldUtils.getAllFields(targetClazz);
        for (Field field : fields) {
            Field sourceField = ReflectionUtils.findField(source.getClass(), field.getName());
            if (sourceField == null) {
                sourceField = ReflectionUtils.findField(source.getClass(), camel2Dash(field.getName()));
            }
            if (sourceField != null) {
                Object value = ReflectionUtils.getField(sourceField, source);
                if (value != null) {
                    FieldUtils.writeField(target, field.getName(), value, true);
                }
            }
        }
        return target;
    }

    public static <T> T map2Bean(final Map sourceMap, Class<T> targetClazz) throws IllegalAccessException, InstantiationException {
        if (sourceMap == null) return null;
        T target = targetClazz.newInstance();
        if (sourceMap.isEmpty()) return target;
        Field[] fields = FieldUtils.getAllFields(targetClazz);
        for (Field field : fields) {
            String key = field.getName();
            if (!sourceMap.containsKey(key)) {
                // Camel 2 dash
                key = camel2Dash(field.getName());
            }
            Object value = sourceMap.get(key);
            if (value != null) {
                FieldUtils.writeField(target, field.getName(), value, true);
            }
        }
        return target;
    }

    public static String getString(Map map, Object key) {
        return get(map, key, String.class);
    }

    public static Optional<String> getOptionalString(Map map, Object key) {
        return Optional.ofNullable(get(map, key, String.class));
    }

    public static <T> T get(Map map, Object key, Class<T> valueType) {
        if (map == null || map.isEmpty() || !map.containsKey(key)) {
            return null;
        }

        Object object = map.get(key);

        if (object != null && ClassUtils.isAssignable(object.getClass(), valueType)) {
            return (T) object;
        }

        return null;
    }
}
