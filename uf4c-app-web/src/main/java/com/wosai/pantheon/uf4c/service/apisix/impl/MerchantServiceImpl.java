package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.request.StringConfigQueryRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.user.service.StoreCollectionService;
import com.wosai.pantheon.core.uitem.model.CategoryExt;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.MerchantInfo;
import com.wosai.pantheon.uf4c.model.dto.MultiConfigRequest;
import com.wosai.pantheon.uf4c.service.CategoryHelper;
import com.wosai.pantheon.uf4c.service.apisix.MerchantService;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.gds.enums.config.MealTypeValueEnum;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class MerchantServiceImpl implements MerchantService {
    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private StoreCollectionService storeCollectionService;

    @Autowired
    private CategoryHelper categoryHelper;

    @Override
    public MerchantInfo getMerchantInfo(ApiRequest apiRequest) {

        Map queryParam = apiRequest.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");
        String qrCodeId = MapUtils.getString(queryParam, "qr_code_id");
        Integer serviceType = MapUtils.getInteger(queryParam, "service_type", 2);

        if (StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        Map store = storeService.getStore(storeId);
        int status = BeanUtil.getPropInt(store, Store.STATUS);
        String bulletinKey = "";
        String businessKey = "";
        String mustCategoryKey = "";
        if (Constants.ServiceType.TAKEOUT.equals(serviceType)) {
            bulletinKey = Constants.STORE_TAKEOUT_BULLETIN;
            businessKey = Constants.TAKEOUT_BUSINESS_STATUS;
            mustCategoryKey = Constants.STORE_TAKEOUT_MUST_CATEGORY;
        } else {
            bulletinKey = Constants.STORE_BULLETIN;
            businessKey = Constants.BUSINESS_STATUS;
            mustCategoryKey = Constants.STORE_MUST_CATEGORY;
        }
        ConfigResponse bulletinConfig = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, bulletinKey));
        ConfigResponse businessConfig = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, businessKey));
        ConfigResponse mustCategoryConfig = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, mustCategoryKey));

        // 查商户信息
        MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setStoreId(storeId);
        merchantInfo.setSn(BeanUtil.getPropString(store, Store.SN));
        merchantInfo.setName(BeanUtil.getPropString(store, Store.NAME));
        merchantInfo.setBusinessStatus(status != Store.STATUS_ENABLED ? 0 : businessConfig == null ? 0 : businessConfig.getEnabled() ? 1 : 0);
        merchantInfo.setMerchantId(BeanUtil.getPropString(store, Store.MERCHANT_ID));
        merchantInfo.setMerchantSn(BeanUtil.getPropString(store, "merchant_sn"));
        // 查分类
        List<CategoryExt> effectiveCategories = categoryHelper.findEffectiveCategories(storeId, serviceType);
        if (mustCategoryConfig != null) {
            effectiveCategories.forEach(c -> c.setMust(c.getId().equalsIgnoreCase(mustCategoryConfig.getValue())));
        }

        merchantInfo.setCategories(effectiveCategories);
        if (bulletinConfig != null) {
            merchantInfo.setStoreBulletin(bulletinConfig.getValue());
        }


        StringConfigQueryRequest stringConfigQueryRequest = MccUtils.findStringConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.MEAL_TYPE_CONFIG_KEY, MealTypeValueEnum.single.getCode());
        String mealType = configRemoteService.getStringConfig(stringConfigQueryRequest);
        if (StringUtils.isBlank(mealType)) {
            mealType = MealTypeValueEnum.single.getCode();
        }

        merchantInfo.setMealType(mealType);
        // 查询是否收藏门店
        merchantInfo.setIsCollection(storeCollectionService.isCollection(storeId, ThreadLocalHelper.getUserId()));


//        if (StringUtils.isBlank(qrCodeId)){
//            return merchantInfo;
//        }

//        if (Objects.equals(mealType,MealTypeValueEnum.round_meal.getCode())) {
//            StoreTableDto tableQuery = new StoreTableDto();
//            tableQuery.setQrCodeId(qrCodeId);
//            tableQuery.setStoreId(storeId);
//            Result<StoreTableInfoVO> tableResult = storeTableRpcService.getTableByQrCodeId(tableQuery);
//
//            if (tableResult.isSuccess()) {
//                StoreTableInfoVO storeTableInfoVO = tableResult.getData();
//
//                if (storeTableInfoVO == null){
//                    throw new BusinessException(BUSINESS_ERROR,"桌台不存在");
//                }
//
//                TableInfoVO tableInfo = TableInfoVO.builder()
//                        .tableId(storeTableInfoVO.getTableId())
//                        .tableNo(storeTableInfoVO.getTableNo())
//                        .seatCount(storeTableInfoVO.getSeatCount())
//                        .status(storeTableInfoVO.getStatus())
//                        .build();
//
//                merchantInfo.setTableInfo(tableInfo);
//            }else{
//                throw new BusinessException(BUSINESS_ERROR,tableResult.getErrorCode());
//            }
//        }

        return merchantInfo;

    }

    @Override
    public List<ConfigResponse> getMerchantConfig(ApiRequest<MultiConfigRequest> apiRequest) {

        MultiConfigRequest request = apiRequest.getBody();

        return Optional.ofNullable(request)
                .map(it -> {
                    String merchantId = it.getMerchantId();
                    if (null == merchantId) {
                        return new ArrayList<ConfigResponse>();
                    }
                    List<FindConfigByNameRequest> findConfigByNameRequestList = it.getKeys().stream()
                            .map(key -> MccUtils.findConfigByNameRequest(OwnerType.MERCHANT_ID, merchantId, key))
                            .collect(Collectors.toList());

                    return configRemoteService.batchFindByName(findConfigByNameRequestList);
                }).orElse(null);
    }

    @Override
    public Boolean isCollection(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");

        if (StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        try {
            return storeCollectionService.isCollection(storeId, ThreadLocalHelper.getUserId());
        } catch (Exception e) {
            log.warn("查询是否收藏出错：storeId={},userId={}", storeId, ThreadLocalHelper.getUserId(), e);
            return false;
        }
    }
}
