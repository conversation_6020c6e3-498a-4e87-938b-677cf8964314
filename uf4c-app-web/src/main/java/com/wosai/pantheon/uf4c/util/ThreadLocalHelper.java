package com.wosai.pantheon.uf4c.util;

import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.web.exception.UnauthorizedException;
import com.wosai.pantheon.uf4c.web.exception.UnknownMiniProgramTypeException;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;

public class ThreadLocalHelper {
    @Getter
    private static ThreadLocal<RequestContext> requestContextThreadLocal = ThreadLocal.withInitial(RequestContext::new);


    public static void reset(){
        requestContextThreadLocal.remove();
    }


    public static UserContextDTO getUser() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getUserContextDTO)
                .orElseThrow(UnauthorizedException::new);
    }

    public static UserContextDTO getUserNotThrowExcetpion() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getUserContextDTO)
                .orElse(null);
    }

    public static String getUserId() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getUserContextDTO)
                .map(UserContextDTO::getUserId)
                .orElseThrow(UnauthorizedException::new);
    }

    public static String getThirdPartyUserId() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getUserContextDTO)
                .map(UserContextDTO::getThirdpartyUserId)
                .orElseThrow(UnauthorizedException::new);
    }

    public static MiniProgramType getMiniProgramType() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getMiniProgramType)
                .orElseThrow(UnknownMiniProgramTypeException::new);
    }

    public static String getScene() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getScene)
                .orElse(null);
    }

    public static String getRealIp() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getRealIp)
                .orElse(null);
    }

    public static String getSqbPaySource() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getSqbPaySource)
                .orElse(null);
    }

    public static String getAppid() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getAppid)
                .orElse(null);
    }

    public static Map getPayHeaders() {
        return Optional.ofNullable(requestContextThreadLocal.get())
                .map(RequestContext::getPayHeaders)
                .orElse(null);
    }
}
