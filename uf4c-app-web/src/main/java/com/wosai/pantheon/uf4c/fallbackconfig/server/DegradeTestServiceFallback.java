package com.wosai.pantheon.uf4c.fallbackconfig.server;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.pantheon.uf4c.api.DegradeTestService;

import java.lang.reflect.Method;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;

public class DegradeTestServiceFallback extends JsonRPCFallbackDefine {
    private static ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[] {
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("test");
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        return OBJECT_MAPPER.valueToTree("接口被限流了");
                    }
                },
                ElementMatchers::any
        };
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(DegradeTestService.class);
    }

    @Override
    public Provider getProvider() {
        return Provider.SERVER;
    }
}
