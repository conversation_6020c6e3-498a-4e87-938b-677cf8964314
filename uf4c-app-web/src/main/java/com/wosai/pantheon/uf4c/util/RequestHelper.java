package com.wosai.pantheon.uf4c.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletRequestWrapper;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * Created by ye<PERSON><PERSON> on 2017/5/9.
 */
@Component
@Slf4j
public class RequestHelper {
    public String getRequestParameterByKey(HttpServletRequest request, String key) {
        String value = null;
        Part fileUpload;
        try {
            // multipart/form-data
            fileUpload = request.getPart(key);
            if (fileUpload != null) {
                value = StreamUtils.copyToString(fileUpload.getInputStream(), UTF_8);
            }
        } catch (ServletException | IOException e) {
        }
        if (value == null) {
            // POST application/x-www-form-urlencoded
            // GET query string
            value = request.getParameter(key);
        }
        return value == null ? "" : value;
    }

    /**
     * 将 HttpServletRequest.getParameterMap 的返回 Map<String, String[]>  转为 Map<String, String>
     *
     * @param request HttpServletRequest
     * @return Map
     */
    public Map<String, String> getParameterMap(HttpServletRequest request) {
        Map<String, String[]> requestParams = new HashMap<>(request.getParameterMap());
        return requestParams.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue()[0])
                );
    }

    /**
     * 将Map转成URL参数 例如： aa=123&bb=123
     *
     * @param request
     * @return
     */
    public String convertMapToRequestParamsStr(HttpServletRequest request) {
        Map<String, String> params = getParameterMap(request);
        StringBuilder result = new StringBuilder();
        Iterator var3 = params.entrySet().iterator();
        while (var3.hasNext()) {
            Map.Entry<String, String> entry = (Map.Entry) var3.next();
            result.append(entry.getKey());
            result.append("=");
            result.append(entry.getValue());
            result.append("&");
        }
        return result.length() > 0 ? result.substring(0, result.length() - 1) : "";
    }

    public String getParameterFromInputStream(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
        try {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        } catch (Exception e) {
            log.warn("", e);
            return "";
        } finally {
            reader.close();
        }
    }

    public String getParameters(ServletRequest request) {
        while (true) {
            if (request instanceof ServletRequestWrapper) {
                if (request instanceof ContentCachingRequestWrapper) {
                    return StringUtils.toEncodedString(
                            ((ContentCachingRequestWrapper) request).getContentAsByteArray(),
                            Charset.forName(request.getCharacterEncoding()));
                }
                request = ((ServletRequestWrapper) request).getRequest();
            } else {
                break;
            }
        }

        return "";
    }
}