package com.wosai.pantheon.uf4c.model.jielong;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 加购请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JielongCartItem implements Serializable {
    /**
     * 商品id
     */
    @NotBlank
    String itemId;
    /**
     * 商品名称
     */
    String name;
    /**
     * 加购数量
     */
    @NotNull
    Integer number;
    /**
     * 商品价格
     */
    @NotNull
    @Min(1)
    Long price;
    /**
     * 接龙id
     */
    @NotNull
    @Min(0)
    Integer jielongId;
    /**
     * 门店id
     */
    @NotBlank
    String storeId;
}
