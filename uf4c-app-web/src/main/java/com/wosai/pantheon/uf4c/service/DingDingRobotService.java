package com.wosai.pantheon.uf4c.service;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static java.lang.System.currentTimeMillis;

@Slf4j
@Service
public class DingDingRobotService {
    public static final String ACCEPT_JSON       = "application/json";

    @Value("${dingding.robot.url}")
    private String dingdingRobotUrl;

    @Value("${spring.application.name}")
    private String serverName;

    private final OkHttpClient client = new OkHttpClient();

    public static final MediaType APPLICATION_JSON = MediaType.parse("application/json; charset=utf-8");

    public static final String TEMPLATE = "{" +
                                          "    \"msgtype\": \"text\", \n" +
                                          "    \"text\": {\n" +
                                          "        \"content\": \"%s : %s\"\n" +
                                          "    }\n" +
                                          "}";

    public ExecutorService executor = Executors.newSingleThreadExecutor();

    public void notification(String text) {

        executor.submit(() -> {
            try {
                long timestamp = currentTimeMillis();

                String              payload = String.format(TEMPLATE, serverName, text);
                okhttp3.RequestBody body    = okhttp3.RequestBody.create(APPLICATION_JSON, payload);

                okhttp3.Request okhttpRequest = new okhttp3.Request.Builder()
                        .addHeader("Accept", ACCEPT_JSON)
                        .url(dingdingRobotUrl)
                        .post(body)
                        .build();

                log.info("dingding message url {}, payload {}, timestamp {}", dingdingRobotUrl, payload, timestamp);
                okhttp3.Response okhttpResponse = client.newCall(okhttpRequest).execute();
                if (okhttpResponse.code() == 204) { // success
                    return;
                } else {
                    String error   = okhttpResponse.message();
                    String details = okhttpResponse.body().source().readByteString().utf8();
                    log.warn("dingding robot webhook api error {}, detail {}",
                              error, details);
                   
                }
            } catch (Exception e) {
                log.warn("dingding robot unexpected error, {}", e.getMessage(), e);
            }
        });

    }

}
