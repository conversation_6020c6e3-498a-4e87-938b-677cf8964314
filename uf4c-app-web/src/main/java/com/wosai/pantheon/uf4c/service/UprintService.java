package com.wosai.pantheon.uf4c.service;

import com.wosai.pantheon.order.pojo.OrderRefundApplyDTO;
import com.wosai.pantheon.printer.enums.PrintSourceType;
import com.wosai.pantheon.printer.enums.TemplateType;
import com.wosai.pantheon.printer.service.PrinterRpcService;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class UprintService {

    @Value("${service.uprint-core}")
    private String uprintCoreUrl;

    @Autowired
    PrinterRpcService printerRpcService;

    @Autowired
    private RestTemplate restTemplate;


    public void sendPrintJob(Order order) {
        HttpHeaders headers = new HttpHeaders();
        Map requestParams = JacksonUtil.convert(order, Map.class);
        HttpEntity httpEntity = new HttpEntity<>(requestParams, headers);
        this.sendPost(uprintCoreUrl + "/printjob/print", httpEntity, String.class);
    }

    public void printByTemplate(Map map) {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity httpEntity = new HttpEntity<>(map, headers);
        this.sendPost(uprintCoreUrl + "/printjob/printByTemplate", httpEntity, String.class);
    }

    public void printRefundApplyTemplate(OrderRefundApplyDTO refundApplyDTO, String storeName,PrintSourceType sourceType) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Map printData = new HashMap();
        printData.put("order_seq", refundApplyDTO.getOrderSeq());
        printData.put("store_name", storeName);
        printData.put("apply_time", sdf.format(new Date(refundApplyDTO.getApplyTime())));
        printData.put("sn", refundApplyDTO.getSn());
        printData.put("reason", refundApplyDTO.getApplyReason());

        printerRpcService.printByTemplateType(refundApplyDTO.getStoreId(), printData, sourceType, TemplateType.APPLY_REFUND);
        //待收银机申请退款和取消订单对应业务能力补齐，再完善这块能力
//        if (refundApplyDTO.getOrderType() == OrderType.TAKE_OUT_ORDER) {
//            CashierNotifyRequest cashierNotifyRequest = new CashierNotifyRequest();
//            cashierNotifyRequest.setFromType(TakeoutOrderFromTypeEnum.PLATFORM);
//            cashierNotifyRequest.setProcessType(TakeoutOrderProcessTypeEnum.REFUND);
//            cashierNotifyRequest.setNeedCheckCashierMode(true);
//            cashierNotifyRequest.setSn(refundApplyDTO.getSn());
//            cashierNotifyRequest.setStoreId(refundApplyDTO.getStoreId());
//            cashierNotifyRequest.setStoreName(storeName);
//            cashierNotifyRequest.setRemark(refundApplyDTO.getApplyReason());
//            orderCashierService.notifyTakeoutOrderAfterChange(cashierNotifyRequest);
//        }
    }

    public void printRefundCancelTemplate(OrderRefundApplyDTO refundApplyDTO, String storeName,PrintSourceType sourceType) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Map printData = new HashMap();
        printData.put("order_seq", refundApplyDTO.getOrderSeq());
        printData.put("store_name", storeName);
        printData.put("cancel_time", sdf.format(new Date(refundApplyDTO.getApplyTime())));
        printData.put("sn", refundApplyDTO.getSn());
        printData.put("reason", refundApplyDTO.getApplyReason());

        printerRpcService.printByTemplateType(refundApplyDTO.getStoreId(), printData, sourceType, TemplateType.ORDER_CANCELED);
        //待收银机申请退款和取消订单对应业务能力补齐，再完善这块能力
//        if (refundApplyDTO.getOrderType() == OrderType.TAKE_OUT_ORDER) {
//            CashierNotifyRequest cashierNotifyRequest = new CashierNotifyRequest();
//            cashierNotifyRequest.setFromType(TakeoutOrderFromTypeEnum.PLATFORM);
//            cashierNotifyRequest.setProcessType(TakeoutOrderProcessTypeEnum.CANCEL);
//            cashierNotifyRequest.setNeedCheckCashierMode(true);
//            cashierNotifyRequest.setSn(refundApplyDTO.getSn());
//            cashierNotifyRequest.setStoreId(refundApplyDTO.getStoreId());
//            cashierNotifyRequest.setStoreName(storeName);
//            cashierNotifyRequest.setRemark(refundApplyDTO.getApplyReason());
//            orderCashierService.notifyTakeoutOrderAfterChange(cashierNotifyRequest);
//        }
    }

    public void printRefundApplyRevokeTemplate(OrderRefundApplyDTO refundApplyDTO, String storeName) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Map printData = new HashMap();
        printData.put("order_seq", refundApplyDTO.getOrderSeq());
        printData.put("store_name", storeName);
        // 撤销申请时间我们没有存储,这个时间目前只展示在了打印小票上,所以我们用当前时间替代一下
        printData.put("revoke_time", sdf.format(new Date()));
        printData.put("sn", refundApplyDTO.getSn());

        printerRpcService.printByTemplateType(refundApplyDTO.getStoreId(), printData, PrintSourceType.SMART, TemplateType.REVOKE_APPLY_REFUND);
    }

    private <t> t sendPost(String url, HttpEntity<?> httpEntity, Class<t> responseClass) {
        try {
            log.info("UprintService request : {}", httpEntity.toString());
            t response = restTemplate.postForObject(url, httpEntity, responseClass);
            log.info("UprintService response : {}", response.toString());
            return response;
        } catch (Exception e) {
            log.warn("UprintService服务访问异常", e);
            return null;
        }
    }

}
