package com.wosai.pantheon.uf4c.gather.types;

import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ServiceTypeData;

import java.util.Map;

public abstract class AbstractServiceProcessor {

    /**
     * 判断是否包含这个ServiceType
     *
     * @param request
     * @param dataMap
     * @return
     */
    public abstract ServiceTypeData.ServiceItem process(GatherRequest request, Map<String, Object> dataMap);

    /**
     * 判断该服务类型是否默认选中
     *
     * @param request
     * @param dataMap
     * @return
     */
    public abstract boolean isActive(GatherRequest request, Map<String, Object> dataMap);
}
