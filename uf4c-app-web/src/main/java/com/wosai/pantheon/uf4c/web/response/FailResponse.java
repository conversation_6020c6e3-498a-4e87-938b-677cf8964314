package com.wosai.pantheon.uf4c.web.response;


import com.wosai.pantheon.uf4c.web.exception.ReturnCode;

public class FailResponse<T> extends Response<T> {

    public FailResponse(String code, String message) {
        super(code, message);
    }

    public FailResponse(ReturnCode returnCode) {
        super(returnCode.getCode(), returnCode.getMessage());
    }

    public FailResponse(ReturnCode returnCode, T data) {
        super(returnCode.getCode(), returnCode.getMessage(), data);
    }

    public FailResponse(String code, String message, T data) {
        super(code, message, data);
    }

}
