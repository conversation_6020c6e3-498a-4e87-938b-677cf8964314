package com.wosai.pantheon.uf4c.util;

import lombok.SneakyThrows;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServletServerHttpRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

public class WebUtil {
    public static boolean acceptJson(final HttpServletRequest httpServletRequest) {
        final ServletServerHttpRequest httpRequest = new ServletServerHttpRequest(httpServletRequest);
        if (httpRequest.getHeaders() != null &&
                httpRequest.getHeaders().getAccept() != null) {
            return httpRequest.getHeaders().getAccept().stream()
                    .anyMatch(it -> !it.isWildcardType() && it.includes(MediaType.APPLICATION_JSON));
        }
        return false;
    }

    @SneakyThrows
    public static void renderJSON(final HttpServletResponse response, String code, String message) {
        response.setStatus(200);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        final PrintWriter out = response.getWriter();
        out.print("{\"code\":" + code + ",\"message\":\"" + message + "\"}");
        out.flush();
    }
}
