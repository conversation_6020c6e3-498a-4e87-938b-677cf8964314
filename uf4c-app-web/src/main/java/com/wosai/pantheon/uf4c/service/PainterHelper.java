package com.wosai.pantheon.uf4c.service;


import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.market.mcc.api.dto.request.GenUidRequest;
import com.wosai.market.mcc.api.service.UidRemoteService;
import com.wosai.market.painter.api.dto.request.JielongPictureRequest;
import com.wosai.market.painter.api.dto.request.JielongSharePictureRequest;
import com.wosai.market.painter.api.service.PicturePainterRemoteService;
import com.wosai.pantheon.uf4c.model.jielong.JielongPage;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class PainterHelper {

    @Autowired
    private UidRemoteService uidRemoteService;
    @Autowired
    private PicturePainterRemoteService picturePainterRemoteService;
    @Value("${poster.jielong}")
    private String jielongPosterUrl;

    // 海报码内容
    private static final String POSTER_JIELONG_CONTENT = "storeId=%s&jielongId=%d";

    /**
     * 生成接龙海报
     * @param storeId
     * @param detail
     * @param goodsList
     * @return
     */
    public  String jielongPoster(String  storeId, JielongPage.JielongDetail detail,List<JielongPage.JielongGoods> goodsList) {
        // 生成海报码
        String content = String.format(POSTER_JIELONG_CONTENT, storeId, detail.getJielongId());
        GenUidRequest genUidRequest = new GenUidRequest() {{
            setContent(content);
        }};
        String uid = uidRemoteService.genUid(genUidRequest);
        if (StringUtils.isBlank(uid)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR,"生成海报码出错");
        }
        long ptime = Optional.ofNullable(detail.getPtime()).orElse(detail.getStartTime());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(goodsList)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR,"生成海报码出错");
        }

        JielongPage.JielongGoods cheapGoods = Collections.min(goodsList, Comparator.comparing(JielongPage.JielongGoods::getPrice));
        JielongPictureRequest jielongPictureRequest = new JielongPictureRequest();
        jielongPictureRequest.setQrcodeContent(String.format(jielongPosterUrl, uid));
        jielongPictureRequest.setTitle(detail.getTitle());
        jielongPictureRequest.setPrice(String.valueOf(cheapGoods.getPrice() / 100.0));
        jielongPictureRequest.setImg(detail.getImg());
        jielongPictureRequest.setPtime(WosaiDateTimeUtils.format(ptime, "yyyy-MM-dd") + " 发布");
        return picturePainterRemoteService.jielongPicture(jielongPictureRequest);
    }

    public String jielongSharePicture(String pictureUrl){
        JielongSharePictureRequest jielongSharePictureRequest = new JielongSharePictureRequest();
        jielongSharePictureRequest.setUrl(pictureUrl);
        return picturePainterRemoteService.jielongSharePicture(jielongSharePictureRequest);
    }
}
