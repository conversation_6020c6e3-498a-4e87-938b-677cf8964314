package com.wosai.pantheon.uf4c.service.apisix;


import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.market.painter.api.dto.request.UfoodShare4CRequest;
import com.wosai.pantheon.core.uitem.model.CategoryResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.dto.MultiCategoryRequest;
import com.wosai.pantheon.uf4c.model.dto.RecommendMaterialFindRequest;
import com.wosai.pantheon.uf4c.model.vo.CategoryVo;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.model.vo.RecommendMaterialTuple;
import com.wosai.web.api.ListResult;

import java.util.List;
import java.util.Map;

@JsonRpcService(value = "/rpc/item")
public interface ItemService {
    /**
     * 获取商品详情
     *
     * @param apiRequest
     * @return
     */
    ItemDetailVO getItemDetailById(ApiRequest apiRequest);

    /**
     * 分享接口
     *
     * @param apiRequest
     * @return
     */
    Map share(ApiRequest<UfoodShare4CRequest> apiRequest);

    ListResult<ItemDetailVO> findItemDetails(ApiRequest<ItemFind> apiRequest);

    List<CategoryResult<ItemDetailVO>> findByMultiCategoryId(ApiRequest<MultiCategoryRequest> apiRequest);

    List<ItemDetailVO> hotsale(ApiRequest<Map<String, Object>> apiRequest);

    ListResult<ItemDetailVO> recommend(ApiRequest<ItemFind> apiRequest);

    List<CategoryVo> category(ApiRequest<ItemFind> apiRequest);

    Map<String, Object> miniIndex(ApiRequest<ItemFind> apiRequest);

    List<RecommendMaterialTuple> findRecommendMaterials(ApiRequest<RecommendMaterialFindRequest> apiRequest);

    ListResult<CategoryVo> productTag(ItemFind find);


    void deleteItemTag(String openIds);
}
