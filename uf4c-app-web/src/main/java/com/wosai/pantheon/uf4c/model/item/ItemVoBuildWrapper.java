package com.wosai.pantheon.uf4c.model.item;

import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.smart.goods.common.constant.ProductConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Getter
public class ItemVoBuildWrapper {

    /**
     * 商品详情
     */
    private ItemDetailVO detailVo;

    /**
     * 服务类型
     */
    private Integer serviceType;

    /**
     * 图片显示的尺寸
     * {@link ProductConstant.MediaSpecTypeEnum#getCode()}
     */
    private int imageSizeForShow;

    public ItemVoBuildWrapper(ItemDetailVO detailVo, Integer serviceType) {
        this.detailVo = detailVo;
        this.serviceType = serviceType;
        this.imageSizeForShow = ProductConstant.MediaSpecTypeEnum.RATIO_1_1.getCode();
    }

    public void setImageSizeForShow(Integer imageSize) {
        if (Objects.isNull(imageSize)) {
            return;
        }
        ProductConstant.MediaSpecTypeEnum imageSizeEnum = ProductConstant.MediaSpecTypeEnum.getByCode(imageSize);
        if (Objects.isNull(imageSizeEnum)) {
            return;
        }
        this.imageSizeForShow = imageSizeEnum.getCode();
    }

    /**
     * 根据商户设置的首页展示布局，选择对应的图片尺寸展示
     * @param homeLayoutMccValue
     */
    public void setImageSpecTypeByHomeLayoutSetting(String homeLayoutMccValue) {
        if (StringUtils.isBlank(homeLayoutMccValue)) {
            homeLayoutMccValue = "0";
        }
        switch (homeLayoutMccValue) {
            case "0":
                this.imageSizeForShow = ProductConstant.MediaSpecTypeEnum.RATIO_1_1.getCode();
                break;
            case "1":
                this.imageSizeForShow = ProductConstant.MediaSpecTypeEnum.RATIO_4_3.getCode();
                break;
            case "2":
                this.imageSizeForShow = ProductConstant.MediaSpecTypeEnum.RATIO_16_9.getCode();
                break;
            default:
                break;
        }
    }

    /**
     * 根据商户设置的商品详情页展示布局，选择对应的图片展示
     * @param detailSettingMccValue
     */
    public void setImageSpecTypeByDetailSetting(String detailSettingMccValue) {
        if (StringUtils.isBlank(detailSettingMccValue)) {
            detailSettingMccValue = "1";
        }
        switch (detailSettingMccValue) {
            case "1":
                this.imageSizeForShow = ProductConstant.MediaSpecTypeEnum.RATIO_1_1.getCode();
                break;
            case "2":
                this.imageSizeForShow = ProductConstant.MediaSpecTypeEnum.RATIO_4_3.getCode();
                break;
            case "3":
                this.imageSizeForShow = ProductConstant.MediaSpecTypeEnum.RATIO_16_9.getCode();
                break;
            default:
                break;
        }
    }

}
