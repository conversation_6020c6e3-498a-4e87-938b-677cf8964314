package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.LoveQrcodeRequest;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import com.wosai.smartbiz.base.pojo.RedeemResult;

import java.util.Map;

@JsonRpcService(value = "/rpc/order/v2")
public interface OrderServiceV2 {

    RedeemResult getRedeemResult(ApiRequest<RedeemRequest> apiRequest);


    PayResult pay(ApiRequest<PayRequest> apiRequest);

    PayResult lovePay(ApiRequest<PayRequest> apiRequest);

    Map loveQrCode(ApiRequest<LoveQrcodeRequest> apiRequest);
}
