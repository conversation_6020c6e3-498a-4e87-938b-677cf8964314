package com.wosai.pantheon.uf4c.config;

import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;

import static com.wosai.pantheon.uf4c.util.TraceContextUtils.fake;

@Component
public class PrefixStringRedisSerializer extends StringRedisSerializer {
    private static final String DEFAULT_PREFIX = "";

    private static final String FAKE_PREFIX = "FAKE:" + DEFAULT_PREFIX;

    public PrefixStringRedisSerializer() {
        super();
    }

    public PrefixStringRedisSerializer(Charset charset) {
        super(charset);
    }

    @Override
    public byte[] serialize(String key) {
        String prefix = fake() ? FAKE_PREFIX : DEFAULT_PREFIX;
        return super.serialize(prefix + key);
    }
}
