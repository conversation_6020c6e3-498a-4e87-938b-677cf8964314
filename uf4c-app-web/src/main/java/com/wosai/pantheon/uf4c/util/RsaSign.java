package com.wosai.pantheon.uf4c.util;


import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.web.exception.DataSignException;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.common.util.Digest;
import org.apache.commons.codec.binary.Base64;

import java.io.*;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by xuchmao on 2019/1/9.
 */
public class RsaSign {
    private static final String SIGN_TYPE_RSA = "RSA";
    private static final String SIGN_ALGORITHMS = "SHA256WithRSA";
    private static final int DEFAULT_BUFFER_SIZE = 8192;

    public static String getSign(Map<String, String> params, String key) throws DataSignException {
        return getSign(params, key, Constants.CHARSET_UTF_8);
    }

    public static String getSign(Map<String, String> params, String key, String charset) throws DataSignException {
        String signContent = getSignContent(params);
        return rsaSign(signContent, key, charset);
    }

    public static String getSign(String content, String key, String charset) throws DataSignException {
        return rsaSign(content, key, charset);
    }

    public static boolean check(Map<String, String> params, String key) throws DataSignException {
        return check(params, key, Constants.CHARSET_UTF_8);
    }

    public static boolean check(Map<String, String> params, String key, String charset) throws DataSignException {
        String sign = params.get("sign");
        String content = getSignCheckContent(params);
        return rsaCheckContent(content, sign, key, charset);
    }

    public static boolean check(String content, String sign, String key, String charset) throws DataSignException {
        return rsaCheckContent(content, sign, key, charset);
    }

    public static String rsaSign(String content, String privateKey, String charset) throws DataSignException {
        try {
            PrivateKey priKey = getPrivateKeyFromPKCS8(SIGN_TYPE_RSA,
                    new ByteArrayInputStream(privateKey.getBytes()));

            java.security.Signature signature = java.security.Signature
                    .getInstance(SIGN_ALGORITHMS);

            signature.initSign(priKey);

            if (StringUtil.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }
            byte[] signed = signature.sign();
            return new String(Base64.encodeBase64(signed));
        } catch (InvalidKeySpecException ie) {
            throw new DataSignException("RSA私钥格式不正确，请检查是否正确配置了PKCS8格式的私钥");
        } catch (Exception e) {
            throw new DataSignException("数字签名校验错误");
        }
    }

    private static PrivateKey getPrivateKeyFromPKCS8(String algorithm,
                                              InputStream ins) throws Exception {
        if (ins == null || StringUtil.isEmpty(algorithm)) {
            return null;
        }
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        byte[] encodedKey = readText(ins).getBytes();
        encodedKey = Base64.decodeBase64(encodedKey);
        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
    }

    private static String getSignCheckContent(Map<String, String> params) {
        if (params == null) {
            return null;
        }
        params.remove("sign");
        return getSignContent(params);
    }

    public static boolean rsaCheckContent(String content, String sign, String publicKey,
                                   String charset) throws DataSignException {
        try {
            PublicKey pubKey = getPublicKeyFromX509("RSA",
                    new ByteArrayInputStream(publicKey.getBytes()));

            java.security.Signature signature = java.security.Signature
                    .getInstance(SIGN_ALGORITHMS);
            signature.initVerify(pubKey);
            if (StringUtil.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }

            return signature.verify(Base64.decodeBase64(sign.getBytes()));
        } catch (Exception e) {
            throw new DataSignException();
        }
    }

    public static String getSignContent(Map<String, String> params) {
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getValue() == null) {
                params.remove(entry.getKey());
            }
        }
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); ++i) {
            String key = keys.get(i);
            sb.append(key + "=" + params.get(key) + "&");
        }
        return sb.substring(0, sb.length() - 1);
    }

    private static PublicKey getPublicKeyFromX509(String algorithm,
                                           InputStream ins) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);

        StringWriter writer = new StringWriter();
        io(new InputStreamReader(ins), writer);

        byte[] encodedKey = writer.toString().getBytes();

        encodedKey = Base64.decodeBase64(encodedKey);

        return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
    }

    private  static String readText(InputStream in) throws IOException {
        return readText(in, null, -1);
    }

    private static String readText(InputStream in, String encoding) throws IOException {
        return readText(in, encoding, -1);
    }

    private static String readText(InputStream in, String encoding, int bufferSize)
            throws IOException {
        Reader reader = (encoding == null) ? new InputStreamReader(in) : new InputStreamReader(in,
                encoding);

        return readText(reader, bufferSize);
    }

    private static String readText(Reader reader) throws IOException {
        return readText(reader, -1);
    }

    private static String readText(Reader reader, int bufferSize) throws IOException {
        StringWriter writer = new StringWriter();
        io(reader, writer, bufferSize);
        return writer.toString();
    }

    private static void io(Reader in, Writer out, int bufferSize) throws IOException {
        if (bufferSize == -1) {
            bufferSize = DEFAULT_BUFFER_SIZE >> 1;
        }
        char[] buffer = new char[bufferSize];
        int amount;
        while ((amount = in.read(buffer)) >= 0) {
            out.write(buffer, 0, amount);
        }
    }

    private static void io(Reader in, Writer out) throws IOException {
        io(in, out, -1);
    }

    public static String getUpaySign(Map<String, String> content, String secret, String charset) {
        for (Map.Entry<String, String> entry : content.entrySet()) {
            if (entry.getValue() == null) {
                content.remove(entry.getKey());
            }
        }
        List<String> keys = new ArrayList<>(content.keySet());
        Collections.sort(keys);

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); ++i) {
            String key = keys.get(i);
            sb.append(key + "=" + content.get(key) + "&");
        }
        return getMd5Sign(sb.substring(0, sb.length() - 1), secret, charset);
    }


    public static String getMd5Sign(String content, String secret, String charset) {
        String md5 = "";
        try {
            md5 = Digest.md5((content + "&key="+ secret).getBytes(charset)).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return md5;
    }

    public static void main(String[] args) throws Exception {
        RsaSign rsaSign = new RsaSign();
        String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDptG9gIT3W8NlK\n" +
                "kzFSWZp0NxhX5c2ti1lswg9YruwCyovgPa/VKoYiQ8JbMG+0FKa55e5k3uyCFOm2\n" +
                "CNmX5q8OkmsxoSawS5wh6o0uCDfdnrKlKd5NDTpnbd7xp1SS14YiB0oj52q52fl/\n" +
                "pEPvyHJ67f6Qv7avsMuH7eyZNNShBnJSnBxF12Sn0XfKIVz+TKCXcDi5NRLXpaCo\n" +
                "GEw+StwoOZ3pdlvJQekUWX58AsgUtozNy2B+YJTeJ9h5usZqfjj0m2adq/OD9vkI\n" +
                "we0Q3lLizrXu76CAVtsOq+y7o595/tVC5Kb0EOcRcC9iu7phBw6ZGjFeXqQhAtFM\n" +
                "Dhc8UH6pAgMBAAECggEAD08PgKeMyniqczUjJPbTpEs7n/5gs76OFA6+PJvwv9oO\n" +
                "wZio3PjVIod3wstwdcZoiYLjU946debyzEbWIhotW3IB7ZVV2HSLfLlFmD9XkX/k\n" +
                "yebtNfTo33+V/BRu+NN/aNk7ZRCysAv0SQU0og9qWF0gsNCuIeId4wCV4A9S4l1O\n" +
                "siLb+3BMd4VYZu/I1QAQ9YBTbH7pdJulCotR1W1uESKydGk7qn3MIfRKe/7FVq1w\n" +
                "FqBpND/VF0Z6hGoVCCnMibVqYJGqP8jHmvBVm0nM+wpESPlAxS24v1Pvkk/m8zXR\n" +
                "zL6KfRPrLoTFcFtF0J+pnQXV7naThnZ3JUTnA8oOkQKBgQD1vkwa/ihjk49QjZYl\n" +
                "iCXajxKIFoJhrYcqtwsVraIgm4F5CSjyFtblcU1GLib2gRKBabrJ9nY0424ORrnM\n" +
                "RQDZ/oCoE93oxCWR3TBoI5yWcpVGBlGDJbHRjCcbhamUXrHDeqLs9wuNiEiJWpRD\n" +
                "BJW5eYSyjPtCF7ooHmt73M0vhQKBgQDzdYJjDwQ5UqUHE/ui/aQul+IT0GpryBP8\n" +
                "hQlfySyrLUM67v2Po6lK2i6Z9tclwBR29oz9iRsiMbtpis5Sr220n0h6FOx3EuIy\n" +
                "LJX1iM/xON8EBSY1xwa3/XBt2WZRw4lbVZ+xTglUYZ8nTmr1m68JlqRn9ggWTSVD\n" +
                "SHd54Nmx1QKBgGOk7Bst35aw6LXTE4TYdfDvfqYr6mZNBLfvTNFXfJwYMiGv8yhm\n" +
                "1nBmxtykLixaHfTXuGPYSgocBi5Hw2Luv++k4OpGU/7kOg+xTsWLWx0uDRU9zqON\n" +
                "D1NWryzbv+1nkFEhUxse+VJEZpU7Y07hdwykF4iG3gLouBU4cIDjAVg5AoGBANBO\n" +
                "ONOc45ZQWhcmR+X7c2DSX4NojcXOmRs0kQK91FRaE+320kj2VUFQURM3GnqYUGpB\n" +
                "Jf1ANFEbkCkLtOEWbJ1gs9iQ0opHQ/Z+I4jEGHdim56K8ViOA/elB29V1bAbyAi3\n" +
                "yD0iceUa6dvtwqKHJErl86Fn5x99vmZFOMS1WE5JAoGBAOCoHHwKKHznrrstr9It\n" +
                "LEq1dWf1lUfHSyFGnRrix9Uk++rMWDdDaF0CaCw5EltyIYhGXlncsLpVfGhyu54H\n" +
                "DEU3AgyYMwZPF9Mobz/27GnamupOKqN8YxezBPJ6bTIVtrHwxJS45NjHPdUvke0k\n" +
                "f9W2YJN+YAI+YJEmnP8UnpcX";
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6bRvYCE91vDZSpMxUlma\n" +
                "dDcYV+XNrYtZbMIPWK7sAsqL4D2v1SqGIkPCWzBvtBSmueXuZN7sghTptgjZl+av\n" +
                "DpJrMaEmsEucIeqNLgg33Z6ypSneTQ06Z23e8adUkteGIgdKI+dqudn5f6RD78hy\n" +
                "eu3+kL+2r7DLh+3smTTUoQZyUpwcRddkp9F3yiFc/kygl3A4uTUS16WgqBhMPkrc\n" +
                "KDmd6XZbyUHpFFl+fALIFLaMzctgfmCU3ifYebrGan449Jtmnavzg/b5CMHtEN5S\n" +
                "4s617u+ggFbbDqvsu6Ofef7VQuSm9BDnEXAvYru6YQcOmRoxXl6kIQLRTA4XPFB+\n" +
                "qQIDAQAB";
        String content = "123";

        String sign = rsaSign.rsaSign(content, privateKey, "utf-8");
        System.out.println(sign);

        System.out.println(rsaSign.rsaCheckContent(content, sign, publicKey, "utf-8"));

        System.out.println(publicKey);
    }
}
