package com.wosai.pantheon.uf4c.model.item;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/6/13
 */
@Data
public class RetailItemCursorQueryRes implements Serializable {

    /**
     * 总数
     */
    private Long total;

    /**
     * 下一页的游标
     */
    private String cursor;

    /**
     * 当前页数据
     */
    private List<RetailItemDTO> records;

    public static RetailItemCursorQueryRes emptyRes(long total) {
        RetailItemCursorQueryRes res = new RetailItemCursorQueryRes();
        res.setTotal(total);
        res.setRecords(Collections.emptyList());
        return res;
    }

}
