package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.smart.template.api.interfaces.StoreTemplateRemoteServiceV2;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.merchant.api.StoreTemplateRemoteService;
import com.wosai.market.merchant.dto.template.StoreShareRequest;
import com.wosai.pantheon.core.ufood.model.StoreConfig;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.RangeAreaDto;
import com.wosai.pantheon.uf4c.model.dto.MultiConfigRequest;
import com.wosai.pantheon.uf4c.service.CampusHelper;
import com.wosai.pantheon.uf4c.service.DeliveryAreaHelper;
import com.wosai.pantheon.uf4c.service.apisix.StoreService;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.smartbiz.base.exceptions.ParamException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class StoreServiceImpl implements StoreService {

    @Autowired
    private ConfigRemoteService configRemoteService;
    @Autowired
    private DeliveryAreaHelper deliveryAreaHelper;
    @Autowired
    private CampusHelper campusHelper;
    @Autowired
    private StoreTemplateRemoteService storeTemplateRemoteService;
    @Autowired
    private StoreTemplateRemoteServiceV2 storeTemplateRemoteServiceV2;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Override
    public StoreConfig getStoreConfig(ApiRequest apiRequest) {

        Map queryParam = apiRequest.getQuery();
        String name = MapUtils.getString(queryParam, "name");
        String storeId = MapUtils.getString(queryParam, "store_id");

        if (StringUtils.isBlank(name) || StringUtils.isBlank(storeId)) {
            throw new ParamException("参数异常");
        }

        ConfigResponse configResponse = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, name));
        if (null == configResponse) {
            return null;
        }

        StoreConfig storeConfig = new StoreConfig();
        storeConfig.setStoreId(storeId);
        storeConfig.setName(name);
        storeConfig.setValue(configResponse.getValue());
        storeConfig.setEnabled(configResponse.getEnabled());

        // 这里做个特殊处理，收银机收银需要收银机登录才能正式启用，需要判断配置的启用状态
        if (StringUtils.equals(configResponse.getName(), MccUtils.CASHIER_MODE) && BooleanUtils.isNotTrue(configResponse.getEnabled())) {
            storeConfig.setValue(MccUtils.SWITCH_CLOSE);
        }

        return storeConfig;
    }

    @Override
    public List<ConfigResponse> getMultiStoreConfig(ApiRequest<MultiConfigRequest> apiRequest) {
        MultiConfigRequest request = apiRequest.getBody();
        return Optional.ofNullable(request)
                .map(it -> {
                    String storeId = it.getStoreId();
                    // TODO: 临时适配小程序端的问题，待移除
                    if (null == storeId) {
                        return new ArrayList<ConfigResponse>();
                    }
                    List<FindConfigByNameRequest> findConfigByNameRequestList = it.getKeys().stream()
                            .map(key -> MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, key))
                            .collect(Collectors.toList());

                    List<ConfigResponse> responseList = configRemoteService.batchFindByName(findConfigByNameRequestList);
                    if (CollectionUtils.isEmpty(responseList)) {
                        return responseList;
                    }

                    // 是否有收银机收银的配置
                    ConfigResponse cashierMode = responseList.stream()
                            .filter(p -> StringUtils.equals(p.getName(), MccUtils.CASHIER_MODE))
                            .findFirst()
                            .orElse(null);
                    if (Objects.nonNull(cashierMode) && BooleanUtils.isNotTrue(cashierMode.getEnabled())) {
                        cashierMode.setValue(MccUtils.SWITCH_CLOSE);
                    }

                    return responseList;
                })
                .orElse(null);
    }

    @Override
    public RangeAreaDto getAreas(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();
        String storeId = MapUtils.getString(queryParam, "store_id");
        String apiVersion = Optional.ofNullable(MapUtils.getString(queryParam, "api_version")).orElse("1");
        return deliveryAreaHelper.getAreas(storeId, apiVersion);
    }

    @Override
    public Map<String, Object> getStoreShareInfo(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();
        String storeId = MapUtils.getString(queryParam, "store_id");
        Long templateId = Optional.ofNullable(MapUtils.getLong(queryParam, "template_id")).orElse(100000L);

        if (apolloConfigHelper.getBooleanConfigValueByKey("use.smart.template", false)) {
            com.shouqianba.smart.template.api.dto.template.StoreShareRequest request = new com.shouqianba.smart.template.api.dto.template.StoreShareRequest();
            request.setStoreId(storeId);
            request.setTemplateId(templateId);
            return storeTemplateRemoteServiceV2.getStoreShareInfo(request);
        } else {
            StoreShareRequest request = new StoreShareRequest();
            request.setStoreId(storeId);
            request.setTemplateId(templateId);
            return storeTemplateRemoteService.getStoreShareInfo(request);
        }
    }


    @GetMapping("/profit")
    public Boolean hasTakeoutProfitSharing(@RequestParam("store_id") String storeId) {
        FindConfigByNameRequest configByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TYPE);
        ConfigResponse configResponse = configRemoteService.findByName(configByNameRequest);
        // 配送方式 1-不配送    >1-支持配送
        int deliveryType = (int) MccUtils.getLongValue(configResponse);
        boolean takeoutProfitSharing = false;

        if (deliveryType == 2) {
            // 自配送，查询是否有校园外卖收钱吧配送，有收钱吧配送则有分账
            takeoutProfitSharing = campusHelper.hasProfitSharing(storeId);
        } else if (deliveryType > 3) {
            // 第三方外卖配送，有分账
            takeoutProfitSharing = true;
        }
        return takeoutProfitSharing;
    }

}
