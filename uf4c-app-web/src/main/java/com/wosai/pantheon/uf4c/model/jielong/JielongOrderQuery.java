package com.wosai.pantheon.uf4c.model.jielong;


import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
public class JielongOrderQuery implements Serializable {
    @NotNull
    @Min(0)
    private Integer jielongId;
    @NotNull
    @Min(1)
    private Integer pageSize = 10;
    @NotNull
    private Integer position;
}
