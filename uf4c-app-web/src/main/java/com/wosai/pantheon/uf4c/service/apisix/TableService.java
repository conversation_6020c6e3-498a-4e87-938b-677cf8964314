package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.smartbiz.base.pojo.Result;

import java.util.Map;

@JsonRpcService(value = "/rpc/table")
public interface TableService {
    Boolean updatePeopleNum( ApiRequest request);

    Order getTableOrder(ApiRequest request);

    Boolean addMustOrderItem(String tableId, String storeId, String userName, String userIcon, Integer peopleNum);

    /**
     * 开台、修改人数V2,返回错误码
     * @param request
     * @return
     */
    Result<Boolean> updatePeopleNumV2(ApiRequest request);

    Boolean canContinueOrder(String tableId);
}
