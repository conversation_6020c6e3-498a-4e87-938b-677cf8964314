package com.wosai.pantheon.uf4c.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wosai.data.util.StringUtil;
import com.wosai.market.mcc.api.dto.request.UidContentRequest;
import com.wosai.market.mcc.api.service.UidRemoteService;
import com.wosai.market.merchant.api.CustomerStoreRemoteService;
import com.wosai.market.merchant.dto.StorePhoto;
import com.wosai.market.merchant.dto.customer.request.StoreDetailRequest;
import com.wosai.market.merchant.dto.customer.response.StoreDetailResponse;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.market.trade.modal.SubscribePayRequest;
import com.wosai.market.trade.service.PayService;
import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.market.user.dto.UserInfoDTO;
import com.wosai.market.user.service.UserService;
import com.wosai.pantheon.core.uitem.model.Item;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.*;
import com.wosai.pantheon.order.model.dto.request.*;
import com.wosai.pantheon.order.service.JielongOrderService;
import com.wosai.pantheon.order.service.JielongService;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.uf4c.model.DeliverInfo;
import com.wosai.pantheon.uf4c.model.jielong.*;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.IpUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.util.WeakReferenceCaller;
import com.wosai.pantheon.uf4c.web.exception.BaseException;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.pojo.PreReduceGoodsDTO;
import com.wosai.smartbiz.oms.api.query.PreReduceStockRequest;
import com.wosai.smartbiz.oms.api.services.OrderGoodsStockRpcService;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import com.wosai.smartbiz.payment.api.trade.defs.SubPayWay;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.util.Base64;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;


@Service
@Slf4j
public class JielongHelper {


    private static final long EXPIRE_TIME = 6L;
    private static final String CART_REDIS_KEY_PRE = "com.uf4c.cart:";
    private static final int JIELONG_SERVICE_TYPE = 3;
    private static final String PRE_PAY_EXCLUSIVE_KEY_PATTERN = "pay_exclusive:%s";

    @Value("${jielong.default.avatar}")
    private String avatar;

    @Autowired
    private JielongService jielongService;

    @Autowired
    private JielongOrderService jielongOrderService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PayService payService;

    @Autowired
    private PainterHelper painterHelper;

    @Autowired
    private CustomerStoreRemoteService customerStoreRemoteService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private HashOperations<String, String, JielongCartItem> hashOperations;

    @Autowired
    private UidRemoteService uidRemoteService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrderGoodsStockRpcService orderGoodsStockService;

    @PostConstruct
    public void postConstruct() {
        hashOperations = redisTemplate.opsForHash();
    }


    /**
     * 获取接龙页面信息
     *
     * @param id
     * @return
     */
    public Map<String, Object> queryJielongPage(int id) {
        String userId = ThreadLocalHelper.getUserId();
        //返回值map的参数与JielongPage的参数一样
        Map<String, Object> map = new ConcurrentHashMap<>();
        JielongWithAllDTO jielongDTO = jielongService.getFullById(id);
        if (jielongDTO == null || jielongDTO.getStatus() == JielongStatus.DELETED.getCode() || jielongDTO.getStatus() == JielongStatus.READY.getCode()) {
            //未发布、删除状态的接龙不展示
            return map;
        }
        map.put("jielong_id", id);
        //店铺信息
        StoreDetailRequest storeDetailRequest = new StoreDetailRequest();
        storeDetailRequest.setStoreId(jielongDTO.getStoreId());
        StoreDetailResponse store = customerStoreRemoteService.storeDetailInfo(storeDetailRequest);
        if (store == null) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商家不存在");
        }
        map.put("store_id", jielongDTO.getStoreId());
        map.put("store_name", Optional.ofNullable(store.getStoreName()).orElse(""));
        map.put("store_img", Optional.ofNullable(store.getStorePhoto()).map(StorePhoto::getLogoUrl).orElse(""));
        //接龙信息
        CompletableFuture<Void> detailFuture = CompletableFuture.runAsync(() -> convertToDetail(jielongDTO, map));
        //商品列表
        CompletableFuture<Void> goodsFuture = CompletableFuture.runAsync(() -> goodsList(jielongDTO, map));
        //用户购物车
        CompletableFuture<Void> cartFuture = CompletableFuture.runAsync(() -> getCartForPage(jielongDTO, map, userId));
        //当前用户订单
        CompletableFuture<Void> orderFuture = CompletableFuture.runAsync(() -> userOrders(jielongDTO, map, userId));
        //接龙历史订单
        CompletableFuture<Void> historyFuture = CompletableFuture.runAsync(() -> historyOrders(jielongDTO, map));
        CompletableFuture.allOf(detailFuture, goodsFuture, cartFuture, orderFuture, historyFuture).join();
        //生成接龙海报和分享图片
        posterAndPicture(map);
        return map;
    }

    private void convertToDetail(JielongWithAllDTO jielongDTO, Map map) {
        JielongPage.JielongDetail detail = new JielongPage.JielongDetail();
        try {
            detail.setJielongId(jielongDTO.getJielongId());
            detail.setJielongSn(jielongDTO.getSn());
            detail.setTitle(jielongDTO.getTitle());
            detail.setImg(jielongDTO.getImg());
            detail.setCellphone(jielongDTO.getCellphone());
            detail.setStatus(jielongDTO.getStatus());
            if (detail.getStatus() == 1) {
                long now = System.currentTimeMillis();
                if (now < jielongDTO.getStartTime()) {
                    detail.setStatus(10);
                    map.put("status", "待开始");
                } else {
                    detail.setStatus(11);
                    map.put("status", "进行中");
                }
            } else if (jielongDTO.getStatus() == 2) {
                map.put("status", "暂停中");
            } else if (jielongDTO.getStatus() == 3) {
                map.put("status", "已结束");
            }
            detail.setTotal(jielongDTO.getOrderTotal() == null ? 0 : jielongDTO.getOrderTotal().intValue());
            detail.setStartTime(jielongDTO.getStartTime());
            detail.setEndTime(jielongDTO.getEndTime());
            detail.setDeliveryType(jielongDTO.getDeliveryType());
            detail.setPickupAddress(jielongDTO.getPickupAddress());
            detail.setCtime(jielongDTO.getCtime());
            detail.setPtime(jielongDTO.getPtime());
            List<JielongPage.DescDetail> descList = JSONArray.parseArray(jielongDTO.getDesc(), JielongPage.DescDetail.class);
            detail.setDescList(descList);
        } catch (Exception e) {
            log.warn("获取接龙活动页面的接龙详情信息失败",
                    keyValue("method", "convertToDetail"),
                    keyValue("request", Objects.nonNull(jielongDTO) ? JSON.toJSONString(jielongDTO) : ""),
                    e);
        }
        map.put("detail", detail);

    }

    private void goodsList(JielongWithAllDTO jielongDTO, Map map) {
        List<JielongPage.JielongGoods> goodsList = new ArrayList<>();
        try {
            List<String> spuIds = jielongDTO.getJielongGoodsDTOS().stream().map(JielongGoodsBaseDTO::getSpuId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(spuIds)) {
                ListResult<ItemDto> listResult = itemService.findItemDetailsById(jielongDTO.getStoreId(), spuIds);
                if (CollectionUtils.isNotEmpty(listResult.getRecords())) {
                    goodsList = listResult.getRecords().stream()
                            // 使用ctime正序排序
                            .sorted(Comparator.comparing(o -> o.getItem().getCtime()))
                            .map((itemDto) -> {
                                Item item = itemDto.getItem();
                                JielongPage.JielongGoods jielongGoods = new JielongPage.JielongGoods();
                                jielongGoods.setItemId(item.getId());
                                jielongGoods.setName(item.getName());
                                jielongGoods.setPrice(Optional.ofNullable(item.getPrice()).map(Integer::longValue).orElse(null));
                                jielongGoods.setSku(item.getSku());
                                jielongGoods.setImg(item.getPhotoUrl());
                                jielongGoods.setDescription(item.getDescription());
                                //单次限购数量是群接龙商品特有字段，借用商品的最小起售数量字段保存信息
                                jielongGoods.setMaxSaleNum(item.getMinSaleNum());
                                return jielongGoods;
                            }).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.warn("获取接龙活动页面的商品信息失败",
                    keyValue("method", "goodsList"),
                    keyValue("request", Objects.nonNull(jielongDTO) ? JSON.toJSONString(jielongDTO) : ""),
                    e);
        }
        map.put("goods_list", goodsList);
    }

    private void getCartForPage(JielongWithAllDTO jielongDTO, Map map, String userId) {
        JielongCart cart = new JielongCart();
        try {
            cart = getCart(generateKey(jielongDTO.getStoreId(), jielongDTO.getJielongId(), userId));
        } catch (Exception e) {
            log.warn("获取接龙活动购物车信息失败",
                    keyValue("method", "getCartForPage"),
                    keyValue("request", Objects.nonNull(jielongDTO) ? JSON.toJSONString(jielongDTO) : ""),
                    e);
        }
        map.put("cart", cart);
    }

    private void userOrders(JielongWithAllDTO jielongDTO, Map map, String userId) {
        List<JielongPage.OrderInfo> orderInfos = new ArrayList<>();
        //接龙没有历史订单，则不需要查询用户订单信息
        if (CollectionUtils.isEmpty(jielongDTO.getJielongMiniOrderDTOS())) {
            map.put("user_order_list", orderInfos);
            return;
        }
        try {
            JielongOrderBaseQueryRequest request = new JielongOrderBaseQueryRequest();
            request.setJielongId(jielongDTO.getJielongId());
            request.setUserId(userId);
            PageDTO pageDTO = new PageDTO();
            pageDTO.setPage(1);
            pageDTO.setPageSize(100);
            request.setPageDTO(pageDTO);
            List<JielongOrderDTO> jielongOrders = jielongOrderService.getJielongOrders(request).getRecords();
            while (CollectionUtils.isNotEmpty(jielongOrders)) {
                orderInfos.addAll(jielongOrders.stream().map(this::convertToOrderInfo).collect(Collectors.toList()));
                if (jielongOrders.size() < 100) {
                    break;
                }
                pageDTO.setPage(pageDTO.getPage() + 1);
                jielongOrders = jielongOrderService.getJielongOrders(request).getRecords();
            }
        } catch (Exception e) {
            log.warn("获取接龙活动页面用户订单信息失败",
                    keyValue("method", "userOrders"),
                    keyValue("request", Objects.nonNull(jielongDTO) ? JSON.toJSONString(jielongDTO) : ""),
                    e);
        }
        map.put("user_order_list", orderInfos);
    }

    private JielongPage.OrderInfo convertToOrderInfo(JielongOrderDTO order) {
        if (order == null) {
            return null;
        }
        JielongPage.OrderInfo orderInfo = new JielongPage.OrderInfo();
        orderInfo.setOrderSn(order.getSn());
        orderInfo.setOrderSeq(order.getOrderSeq());
        orderInfo.setStatus(order.getStatus());
        orderInfo.setWxName(order.getUserName());
        orderInfo.setWxIcon(order.getAvatarUrl());
        orderInfo.setAmount(order.getEffectiveAmount());
        orderInfo.setRemark(order.getRemark());
        orderInfo.setCtime(order.getCtime());
        orderInfo.setMtime(order.getMtime());
        OrderAddressDTO orderAddress = order.getOrderAddress();
        if (orderAddress != null) {
            orderInfo.setUserName(orderAddress.getUserName());
            orderInfo.setAddress(orderAddress.getAddress());
            orderInfo.setCellphone(orderAddress.getCellphone());
        }
        List<JielongPage.JielongGoods> goodsList = order.getItemsInfo().stream().map((item) -> {
            JielongPage.JielongGoods jielongGoods = new JielongPage.JielongGoods();
            jielongGoods.setName(item.getName());
            jielongGoods.setNumber(item.getNumber());
            return jielongGoods;
        }).collect(Collectors.toList());
        orderInfo.setGoodsList(goodsList);
        return orderInfo;
    }

    private void historyOrders(JielongWithAllDTO jielongDTO, Map map) {
        List<JielongPage.OrderInfo> historyList = new ArrayList<>();
        try {
            historyList = convertToOrderInfoList(jielongDTO.getJielongMiniOrderDTOS());
        } catch (Exception e) {
            log.warn("获取接龙活动页面历史订单信息失败",
                    keyValue("method", "historyOrders"),
                    keyValue("request", Objects.nonNull(jielongDTO) ? JSON.toJSONString(jielongDTO) : ""),
                    e);
        }
        map.put("history_order_list", historyList);
    }


    private List<JielongPage.OrderInfo> convertToOrderInfoList(List<JielongMiniOrderDTO> jielongOrders) {
        List<JielongPage.OrderInfo> orderInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jielongOrders)) {
            orderInfos = jielongOrders.stream().filter(order -> CollectionUtils.isNotEmpty(order.getItemsInfo())).map((order) -> {
                JielongPage.OrderInfo orderInfo = new JielongPage.OrderInfo();
                orderInfo.setId(order.getId());
                orderInfo.setOrderSeq(order.getOrderSeq());
                orderInfo.setWxName(order.getUserName());
                orderInfo.setWxIcon(order.getAvatarUrl());
                orderInfo.setCellphone(order.getCellphone());
                List<JielongPage.JielongGoods> goodsList = order.getItemsInfo().stream().map((item) -> {
                    JielongPage.JielongGoods jielongGoods = new JielongPage.JielongGoods();
                    jielongGoods.setName(item.getName());
                    jielongGoods.setNumber(item.getNumber());
                    return jielongGoods;
                }).collect(Collectors.toList());
                orderInfo.setGoodsList(goodsList);
                return orderInfo;
            }).collect(Collectors.toList());

        }
        return orderInfos;
    }


    private void posterAndPicture(Map map) {
        String poster = "";
        String sharePicture = "";
        map.put("poster_img", poster);
        map.put("share_img", sharePicture);
        String storeId = (String) map.get("store_id");
        JielongPage.JielongDetail detail = (JielongPage.JielongDetail) map.get("detail");
        List<JielongPage.JielongGoods> goodsList = (List<JielongPage.JielongGoods>) map.get("goods_list");
        try {
            poster = painterHelper.jielongPoster(storeId, detail, goodsList);
            map.put("poster_img", poster);
        } catch (Exception e) {
            log.warn("生成接龙海报失败",
                    keyValue("method", "posterAndPicture"),
                    keyValue("request", Objects.nonNull(map) ? JSON.toJSONString(map) : ""),
                    e);
        }
        try {
            sharePicture = painterHelper.jielongSharePicture(detail.getImg());
            map.put("share_img", sharePicture);
        } catch (Exception e) {
            log.warn("生成接龙分享图片失败",
                    keyValue("method", "posterAndPicture"),
                    keyValue("request", Objects.nonNull(map) ? JSON.toJSONString(map) : ""),
                    e);
        }
    }

    /**
     * 加减购物车
     *
     * @param cartItem
     * @return
     */
    public JielongCart addorReduceCart(JielongCartItem cartItem) {
        Integer num = cartItem.getNumber();
        JielongCart cart;
        if (num >= 0) {
            cart = addCart(cartItem);
        } else {
            cart = reduceCart(cartItem);
        }
        return cart;
    }


    private JielongCart addCart(JielongCartItem cartItem) {
        String cartKey = generateKey(cartItem.getStoreId(), cartItem.getJielongId());
        JielongCartItem existingCreate = hashOperations.get(cartKey, cartItem.getItemId());
        if (existingCreate != null) {
            // 已存在购物车进行修改
            existingCreate.setNumber(existingCreate.getNumber() + cartItem.getNumber());
            //购物车的商品价格不一定准确，以前端传来的价格为准
            existingCreate.setPrice(cartItem.getPrice());
        } else {
            //不存在，创建购物车
            ItemDto itemDto = itemService.getItemDetailV2(cartItem.getStoreId(), cartItem.getItemId(), JIELONG_SERVICE_TYPE);
            if (itemDto == null) {
                throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
            }
            //第一次创建时，使用商品库实时价格
            cartItem.setPrice(Optional.ofNullable(itemDto.getItem().getPrice()).map(Integer::longValue).orElse(null));
            cartItem.setName(itemDto.getItem().getName());
            cartItem.setNumber(1);
            existingCreate = cartItem;
        }
        if (existingCreate.getNumber() <= 0) {
            removeItem(cartItem);
            return getCart(cartKey);
        }
        upsertItem(existingCreate);
        return getCart(cartKey);
    }


    private JielongCart reduceCart(JielongCartItem cartItem) {
        Integer reduceNum = -cartItem.getNumber();
        String cartKey = generateKey(cartItem.getStoreId(), cartItem.getJielongId());
        JielongCartItem existingCreate = hashOperations.get(cartKey, cartItem.getItemId());
        if (existingCreate != null) {
            Integer num = existingCreate.getNumber();
            if (num > reduceNum) {
                existingCreate.setNumber(num - reduceNum);
                existingCreate.setPrice(cartItem.getPrice());
                upsertItem(existingCreate);
            } else {
                removeItem(cartItem);
            }
            redisTemplate.expire(cartKey, EXPIRE_TIME, TimeUnit.HOURS);
        }
        return getCart(cartKey);
    }


    private JielongCart getCart(String cartKey) {
        JielongCart jielongCart = new JielongCart();
        Map<String, JielongCartItem> entries = hashOperations.entries(cartKey);
        if (MapUtils.isEmpty(entries)) {
            redisTemplate.delete(cartKey);
            return jielongCart;
        }
        List<JielongCartItem> list = new ArrayList<>(entries.values());
        long count = list.stream().mapToLong(i -> i.getNumber() * i.getPrice()).sum();
        jielongCart.setRecords(list);
        jielongCart.setPrice(count);
        return jielongCart;
    }

    /**
     * 获取结算页面信息
     *
     * @param request
     * @return
     */
    public PayCheckResponse getCartAndCalcPrice(JielongCheckRequest request) {
        PayCheckResponse payCheckResponse = new PayCheckResponse();
        try {
            //校验接龙活动
            checkJilelong(request.getJielongId(), request.getStoreId());
        } catch (Exception e) {
            payCheckResponse.setReason(e.getMessage());
            return payCheckResponse;
        }
        String cartKey = generateKey(request.getStoreId(), request.getJielongId());
        Map<String, JielongCartItem> entries = hashOperations.entries(cartKey);
        if (MapUtils.isEmpty(entries)) {
            redisTemplate.delete(cartKey);
            payCheckResponse.setReason("购物车无商品");
            return payCheckResponse;
        }
        //校验购物车总价
        List<JielongCartItem> list = new ArrayList<>(entries.values());
        Long amount = 0L;
        for (JielongCartItem cartItem : list) {
            ItemDto itemDto = itemService.getItemDetailV2(request.getStoreId(), cartItem.getItemId(), JIELONG_SERVICE_TYPE);
            try {
                if (itemDto == null) {
                    throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
                }
                Item item = itemDto.getItem();
                Integer number = cartItem.getNumber();

                if (number == null || number <= 0) {
                    throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR);
                }
                if (item.getSku() != null && item.getSku() < number) {
                    removeItem(cartItem);
                    throw new BusinessException(ReturnCode.ITEM_OUT_OF_SKU);
                }
                if (item.getPrice().longValue() != cartItem.getPrice()) {
                    removeItem(cartItem);
                    throw new BusinessException(ReturnCode.ITEM_PRICE_CHANGE);
                }
                //校验单次限购数量
                if (item.getMinSaleNum() != null && number > item.getMinSaleNum()) {
                    removeItem(cartItem);
                    throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR, "超过商品最大限购数量");
                }
                amount = amount + number * item.getPrice();
            } catch (Exception e) {
                payCheckResponse.setReason(e.getMessage());
                return payCheckResponse;
            }
        }
        payCheckResponse.setRecords(list);
        payCheckResponse.setPrice(amount);
        payCheckResponse.setCanPay(true);
        return payCheckResponse;
    }


    private void upsertItem(JielongCartItem cartItem) {
        String cartKey = generateKey(cartItem.getStoreId(), cartItem.getJielongId());
        hashOperations.put(cartKey, cartItem.getItemId(), cartItem);
        redisTemplate.expire(cartKey, EXPIRE_TIME, TimeUnit.HOURS);

    }

    private void removeItem(JielongCartItem cartItem) {
        String cartKey = generateKey(cartItem.getStoreId(), cartItem.getJielongId());
        hashOperations.delete(cartKey, cartItem.getItemId());
        redisTemplate.expire(cartKey, EXPIRE_TIME, TimeUnit.HOURS);
    }

    private String generateKey(String storeId, int jielongId) {
        storeId = storeId.replaceAll("(.*)(\\?$)", "$1");
        return CART_REDIS_KEY_PRE + storeId + ":" + JIELONG_SERVICE_TYPE + ":" + jielongId + ":" + ThreadLocalHelper.getUserId();
    }

    private String generateKey(String storeId, int jielongId, String userId) {
        storeId = storeId.replaceAll("(.*)(\\?$)", "$1");
        return CART_REDIS_KEY_PRE + storeId + ":" + JIELONG_SERVICE_TYPE + ":" + jielongId + ":" + userId;
    }

    /**
     * 分页查询订单
     *
     * @param query
     * @return
     */
    public List<JielongPage.OrderInfo> orderList(@RequestBody JielongOrderQuery query) {
        JielongMiniOrderQueryRequest request = new JielongMiniOrderQueryRequest();
        request.setJielongId(query.getJielongId());
        PageDTO pageDTO = new PageDTO();
        pageDTO.setPage(1);
        pageDTO.setPageSize(query.getPageSize());
        request.setPageDTO(pageDTO);
        request.setLastOrderId(query.getPosition());
        List<JielongMiniOrderDTO> records = jielongOrderService.getJielongMiniOrders(request).getRecords();
        return convertToOrderInfoList(records);
    }

    /**
     * 接龙下单
     *
     * @param payRequest
     * @return
     */
    public PayResult jielongPay(JielongPayRequest payRequest) {
        //1.接龙活动校验
        checkJilelong(payRequest.getJielongId(), payRequest.getStoreId());
        //2.商户校验
        StoreDetailRequest storeDetailRequest = new StoreDetailRequest();
        storeDetailRequest.setStoreId(payRequest.getStoreId());
        StoreDetailResponse store = customerStoreRemoteService.storeDetailInfo(storeDetailRequest);
        if (store == null) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商家不存在");
        }
        //3.组装商品信息
        String cartKey = generateKey(payRequest.getStoreId(), payRequest.getJielongId());
        JielongCart cart = getCart(cartKey);
        if (CollectionUtils.isEmpty(cart.getRecords())) {
            throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR);
        }
        List<OrderItemAddRequest> items = new ArrayList<>();
        List<OrderItemDigest> itemsInfo = new ArrayList<>();
        Long amount = calcGoods(payRequest, cart, items, itemsInfo);
        //4.预扣库存逻辑
        PreReduceStockRequest preReduceStockRequest = new PreReduceStockRequest();
        preReduceStockRequest.setMerchantId(store.getMerchantId());
        preReduceStockRequest.setStoreId(store.getStoreId());
        preReduceStockRequest.setProcessTime(System.currentTimeMillis());
        List<PreReduceGoodsDTO> preReduceGoodsList = items.stream()
                .map(this::convertPreReduceGoods).collect(Collectors.toList());
        preReduceStockRequest.setGoods(preReduceGoodsList);
        CartCheckResultDTO cartCheckResultDTO = orderGoodsStockService.preReduceProductStock(preReduceStockRequest);
        if (!cartCheckResultDTO.isSuccess()) {
            //预扣库存未成，阻塞下单流程。
            redisTemplate.delete(cartKey);
            throw new BusinessException(ReturnCode.ITEM_OUT_OF_SKU);
        }
        //5.清空购物车
        redisTemplate.delete(cartKey);
        //6.组装下单参数
        DeliverInfo deliveryInfo = payRequest.getDeliveryInfo();
        if (Objects.isNull(deliveryInfo) || StringUtils.isEmpty(deliveryInfo.getUserName()) || StringUtils.isEmpty(deliveryInfo.getCellphone())) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "请输入正确的收货人信息");
        }
        SubscribePayRequest subscribePayRequest = buildSubscribePayRequest(payRequest, items, itemsInfo, amount);
        subscribePayRequest.setPreReduceNo(cartCheckResultDTO.getPreReduceNo());
        //7.下单
        return payService.payForSubscribe(subscribePayRequest);
    }


    private void checkJilelong(Integer jielongId, String storeId) {
        JielongWithSumAndGoodsDTO jielongDTO = jielongService.getWithSumById(jielongId);
        if (jielongDTO == null) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "不存在的接龙活动");
        }
        long now = System.currentTimeMillis();
        if (jielongDTO.getStatus() != 1 || now < jielongDTO.getStartTime() || now > jielongDTO.getEndTime()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "该接龙不在活动有效期内");
        }
        if (Objects.nonNull(jielongDTO.getMaxNumber()) && jielongDTO.getMaxNumber() != -1 && jielongDTO.getTotal() >= jielongDTO.getMaxNumber()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "该接龙活动已经拼团成功，停止下单");
        }
        if (!jielongDTO.getStoreId().equals(storeId)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "接龙活动与商家不符");
        }
    }


    private Long calcGoods(JielongPayRequest payRequest, JielongCart cart, List<OrderItemAddRequest> items, List<OrderItemDigest> itemsInfo) {
        Long amount = 0L;
        for (JielongCartItem cartItem : cart.getRecords()) {
            ItemDto itemDto = itemService.getItemDetailV2(payRequest.getStoreId(), cartItem.getItemId(), JIELONG_SERVICE_TYPE);
            if (itemDto == null) {
                throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
            }
            Item item = itemDto.getItem();
            Integer number = cartItem.getNumber();
            if (number == null || number <= 0) {
                throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR);
            }
            if (item.getSku() != null && item.getSku() < number) {
                throw new BusinessException(ReturnCode.ITEM_OUT_OF_SKU);
            }
            fillItems(items, itemsInfo, item, number, payRequest.getJielongId());
            amount = amount + number * item.getPrice();
        }
        return amount;
    }

    private PreReduceGoodsDTO convertPreReduceGoods(OrderItemAddRequest item) {
        PreReduceGoodsDTO goods = new PreReduceGoodsDTO();
        if (item != null) {
            goods.setSaleCount(item.getCount());
            goods.setSpuId(item.getItemId());
            goods.setSpuTitle(item.getName());
            goods.setSpuType(SpuType.PRODUCT);
            goods.setOriginSalePrice(item.getOriginalAmountPer());
            goods.setSkuType(SkuType.SINGLE);
            goods.setUnitType(GoodsUnitTypeEnum.NUMBER);
        }
        return goods;
    }

    private void fillItems(List<OrderItemAddRequest> items, List<OrderItemDigest> itemsInfo, Item
            item, Integer number, Integer jielongId) {
        OrderItemAddRequest itemRequest = new OrderItemAddRequest();
        itemRequest.setPackFee(0L);
        itemRequest.setItemId(item.getId());
        itemRequest.setName(item.getName());
        itemRequest.setCount(new BigDecimal(number));
        itemRequest.setOriginalAmountPer(Long.valueOf(item.getPrice()));
        itemRequest.setEffectiveAmountPer(Long.valueOf(item.getPrice()));
        itemRequest.setCategoryId(jielongId.toString());
        itemRequest.setSpuType(SpuType.PRODUCT);
        itemRequest.setSkuType(SkuType.SINGLE);
        itemRequest.setCategorySort(1);
        itemRequest.setItemSort(1);
        items.add(itemRequest);

        OrderItemDigest orderItemDigest = new OrderItemDigest();
        orderItemDigest.setId(item.getId());
        orderItemDigest.setName(item.getName());
        orderItemDigest.setPrice(item.getPrice());
        orderItemDigest.setNumber(number);
        itemsInfo.add(orderItemDigest);
    }

    private SubscribePayRequest buildSubscribePayRequest(JielongPayRequest
                                                                 payRequest, List<OrderItemAddRequest> items, List<OrderItemDigest> itemsInfo, Long amount) {
        UserContextDTO user = ThreadLocalHelper.getUserNotThrowExcetpion();
        String userId = user.getUserId();
        RLock lock = redissonClient.getLock(String.format(PRE_PAY_EXCLUSIVE_KEY_PATTERN, userId));
        SubscribePayRequest request = new SubscribePayRequest();
        try {
            if (lock.tryLock(-1, 5000, TimeUnit.MILLISECONDS)) {
//                Map tradeTerminal = terminalService.getTerminalByDeviceFingerprint("jjz" + payRequest.getStoreId());
//                request.setTerminalId(BeanUtil.getPropString(tradeTerminal, DaoConstants.ID));
//                request.setTerminalSn(BeanUtil.getPropString(tradeTerminal, Terminal.SN));
                request.setOrderSource(OrderSource.MINI);
                String userName = payRequest.getWxName();
                if (StringUtils.isBlank(userName) || StringUtils.equals(userName, "微信用户")) {
                    //没有微信名称或默认微信名称 使用手机尾号4位数字
                    if (StringUtils.isNotBlank(user.getCellphone()) && user.getCellphone().length() > 4) {
                        userName = "手机尾号" + user.getCellphone().substring(user.getCellphone().length() - 4);
                    } else {
                        userName = "匿名用户";
                    }
                }
                request.setUserName(userName);
                //默认使用小钱钱头像。不使用payRequest.getWxIcon()
                request.setAvatarUrl(avatar);
                request.setWeixinAppId(user.getWeixinAppId());
                request.setPayerUid(user.getThirdpartyUserId());
                request.setUserId(userId);
                request.setChannelUserId(user.getThirdpartyUserId());
                request.setDiscountStrategy(OrderType.JIELONG_ORDER.getMsg());
                request.setScene(ThreadLocalHelper.getScene());
                request.setPayway(PayWay.WECHAT.getCode().toString());
                request.setSubPayway(SubPayWay.MINI.getCode().toString());
                request.setStoreId(payRequest.getStoreId());
                request.setTotalAmount(amount.toString());
                request.setRemark(payRequest.getRemark());
                request.setClientIp(StringUtil.empty(ThreadLocalHelper.getRealIp()) ? IpUtils.getIp() : ThreadLocalHelper.getRealIp());
                request.setPacked(PackType.INSIDE_ORDER);
                request.setPackAmount(0L);
                OrderAddressRequest orderAddressRequest = new OrderAddressRequest();
                BeanUtils.copyProperties(payRequest.getDeliveryInfo(), orderAddressRequest);
                StringBuilder address = new StringBuilder();
                if (StringUtils.isNotBlank(orderAddressRequest.getCity())) {
                    address.append(orderAddressRequest.getCity());
                }
                if (StringUtils.isNotBlank(orderAddressRequest.getDistrict())) {
                    address.append(orderAddressRequest.getDistrict());
                }
                if (StringUtils.isNotBlank(orderAddressRequest.getAddress())) {
                    address.append(orderAddressRequest.getAddress());
                }
                if (StringUtils.isNotBlank(orderAddressRequest.getHouseNumber())) {
                    address.append(orderAddressRequest.getHouseNumber());
                }
                orderAddressRequest.setAddress(address.toString());
                request.setOrderAddress(orderAddressRequest);
                request.setItems(items);
                request.setItemsInfo(itemsInfo);
                request.setJielongId(payRequest.getJielongId());
                if (Objects.equals(payRequest.getPayway(), PayWay.SODEXO.getCode()) && StringUtils.isNotBlank(user.getCellphone())) {
                    // 索迪斯支付时,payerUid为经过base64加密后的手机号
                    String payerUid = Base64.encode(user.getCellphone().getBytes(StandardCharsets.UTF_8));
                    request.setPayerUid(payerUid);
                    request.setChannelUserId(payerUid);
                    request.setPayway(PayWay.SODEXO.getCode().toString());
                }
            } else {
                log.info("用户正在重复下单，已阻止",
                        keyValue("method", "buildSubscribePayRequest"),
                        keyValue("request", Objects.nonNull(payRequest) ? JSON.toJSONString(payRequest) : ""),
                        keyValue("user_id", userId));
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "您已下单，请稍后再试");
            }
        } catch (InterruptedException ex) {
            log.warn("jielong pay error", ex);
            throw new BaseException(ReturnCode.BUSINESS_ERROR, "系统错误，请稍后再试（PRE_PAY_ERROR）");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return request;
    }

    public JielongPage.OrderInfo getOrderBySn(String orderSn) {
        if (StringUtils.isBlank(orderSn)) {
            return null;
        }
        OrderDTO orderDTO = orderService.getOrderBySn(orderSn);
        JielongPage.OrderInfo order = new JielongPage.OrderInfo();
        if (orderDTO != null) {
            order.setOrderSn(orderSn);
            order.setWxName(orderDTO.getUserName());
            order.setWxIcon(orderDTO.getAvatarUrl());
            order.setOrderSeq(orderDTO.getOrderSeq());
            order.setGoodsList(orderDTO.getItemsInfo().stream().map(itemDigestDTO -> {
                JielongPage.JielongGoods jielongGoods = new JielongPage.JielongGoods();
                jielongGoods.setName(itemDigestDTO.getName());
                jielongGoods.setNumber(itemDigestDTO.getNumber());
                return jielongGoods;
            }).collect(Collectors.toList()));
            order.setAmount(orderDTO.getEffectiveAmount());
        }
        return order;
    }


    public void setDataFromUrl(Map map) {
        String url = MapUtil.getString(map, "url", null);
        if (StringUtils.isNotBlank(url)) {
            Map<String, Object> qrParams = CommonUtil.getUrlParams(url);
            String s = MapUtils.getString(qrParams, "s");
            String uid = MapUtils.getString(qrParams, "p");
            if (!"p".equalsIgnoreCase(s) || StringUtils.isBlank(uid)) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "错误的二维码，请确认");
            }
            UidContentRequest uidContentRequest = new UidContentRequest();
            uidContentRequest.setUid(uid);
            try {
                String content = uidRemoteService.content(uidContentRequest);
                Map data = CommonUtil.getUrlParams(content);
                Integer jielongId = MapUtil.getInteger(data, "jielongId", 0);
                if (jielongId > 0) {
                    map.put("jielong_id", jielongId);
                }
            } catch (Exception e) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "错误的二维码，请确认");
            }
        }
    }

    private UserInfoDTO findUserInfo() {
        UserInfoDTO userInfo = WeakReferenceCaller.call(() -> userService.findUserInfo(ThreadLocalHelper.getThirdPartyUserId(), ThreadLocalHelper.getMiniProgramType().name()));
        return userInfo;
    }

}
