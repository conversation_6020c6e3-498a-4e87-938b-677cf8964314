package com.wosai.pantheon.uf4c.gather;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.gather.cache.GatherCacheHelper;
import com.wosai.pantheon.uf4c.model.GatherOrderExtraRequest;
import com.wosai.pantheon.uf4c.model.GatherOrderRequest;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.HttpRequestUtil;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import com.wosai.smartbiz.payment.api.trade.defs.SubPayWay;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component
@Slf4j
public class GatherHelper {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private GatherService gatherService;
    @Autowired
    private OrderHelper orderHelper;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    // 测试环境地址
    private static final String betaUrl = "https://uf4c-app.iwosai.com/api/v1/gather/index?";
    @Autowired
    private GatherCacheHelper gatherCacheHelper;

    @Value("${qrcode.jjzDomain}")
    private String jjzDomain;

    /**
     * 区分生产与测试环境进行处理<br>
     * 此方法目的是在生产环境未发布的情况下，拦截生产环境小程序通过微信预拉取接口来请求数据。<br>
     * 当生产环境发布后，此方法的作用就是测试门店或测试码可以通过生产环境来访问测试环境的数据。
     * <ul>
     * <li>当前环境是测试
     * <ul>
     * <li style="color:red">生产二维码或非测试门店不处理</li>
     * <li>仅处理测试环境二维码</li>
     * <li>仅处理测试门店</li>
     * </ul>
     * </li>
     * <li>当前环境是生产<ul>
     * <li>测试环境码调用测试环境接口</li>
     * <li>测试门店调用测试环境接口</li>
     * </ul>
     * </ul>
     *
     * @param request
     * @return
     */
    public Map<String, Object> dispatcherRequest(GatherRequest request) {
        if (!isBetaEnv()) {
            // 当前为生产环境
            if (isBetaRequest(request)) {
                // 测试环境请求，直接返回空
                return null;
            }
        }
        return gatherService.orderIndex(request);
    }

    private boolean isBetaRequest(GatherRequest request) {
        return isBetaUrl(request.getUrl()) || isBetaStore(request.getStoreId()) || request.isBetaEnv();
    }

    /**
     * 是否是测试环境二维码
     *
     * @param url
     * @return
     */
    public boolean isBetaUrl(String url) {
        return StringUtils.isNotBlank(url) && CommonUtil.isValidURL(url) && !url.contains("m.sqbe.cn") && !url.contains("99zhe.com") && !url.contains("shouqianba.com");
    }

    /**
     * 是否是测试门店
     *
     * @param storeId
     * @return
     */
    public boolean isBetaStore(String storeId) {
        String betaStoreIds = apolloConfigHelper.getStringConfigValueByKey("gatherBetaStoreIds", "");
        return StringUtils.isNotBlank(storeId) && betaStoreIds.contains(storeId);
    }

    /**
     * 当前环境
     *
     * @return
     */
    public boolean isBetaEnv() {
        String gatherEnv = apolloConfigHelper.getStringConfigValueByKey("gatherEnv", "beta");
        return "beta".equalsIgnoreCase(gatherEnv);
    }

    /**
     * beta环境请求
     *
     * @param request
     * @return
     */
    public Map<String, Object> betaIndex(GatherRequest request) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            Map params = JacksonUtil.beanToMap(request, objectMapper);
            String url = betaUrl + CommonUtil.getUrlParamsByMap(params);
            Map<String, Object> dataMap = restTemplate.getForObject(url, Map.class);
            if (MapUtils.isNotEmpty(dataMap)) {
                return (Map<String, Object>) MapUtils.getMap(dataMap, "data");
            }
        } catch (Exception e) {
            log.warn("请求测试环境数据出错：{}", request, e);
        }
        return null;
    }

    /**
     * 查询地址是否为插件地址
     *
     * @param page
     * @return
     */
    public boolean isPackagePage(String page) {
        String pageRouter = apolloConfigHelper.getStringConfigValueByKey("pageRouter", "");
        if (StringUtils.isNotBlank(pageRouter)) {
            Map<String, String> pageMap = JacksonUtil.toBean(pageRouter, new TypeReference<Map<String, String>>() {
            });
            String path = MapUtils.getString(pageMap, page);
            if (StringUtils.isNotBlank(path)) {
                return path.startsWith("/packages");
            }
        }
        return false;
    }

    /**
     * 获取页面路径所对应的page
     *
     * @param path
     * @return
     */
    public String getPage(String path) {
        String pageRouter = apolloConfigHelper.getStringConfigValueByKey("pageRouter", "");
        if (StringUtils.isNotBlank(pageRouter)) {
            Map<String, String> pageMap = JacksonUtil.toBean(pageRouter, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isNotEmpty(pageMap)) {
                String page = null;
                for (Map.Entry entry : pageMap.entrySet()) {
                    if (entry.getValue().toString().equalsIgnoreCase(path)) {
                        page = entry.getKey().toString();
                        break;
                    }
                }
                return page;
            }

        }
        return null;
    }

    /**
     * 获取页面映射地址
     *
     * @param page
     * @return
     */
    public String getPageMapping(String page) {
        String pageRouter = apolloConfigHelper.getStringConfigValueByKey("pageMapping", "");
        if (StringUtils.isNotBlank(pageRouter)) {
            Map<String, String> pageMap = JacksonUtil.toBean(pageRouter, new TypeReference<Map<String, String>>() {
            });
            String mapping = MapUtils.getString(pageMap, page);
            if (StringUtils.isNotBlank(mapping)) {
                return mapping;
            }
        }
        return null;
    }

    public String getFullPath(Map<String, Object> extraMap) {
        Map newMap = new HashMap(extraMap);
        newMap.remove("closing");
        String page = MapUtils.getString(newMap, "page");
        if (StringUtils.isNotBlank(page)) {
            if (page.startsWith("page-")) {
                page = findPage(page);
                newMap.put("page", page);
            }
        }
        String path = findPath(page);
        if (StringUtils.isNotBlank(path)) {
            path = path + "?";
        }
        return path + CommonUtil.getUrlParamsByMap(newMap);
    }

    private String findPage(String page) {
        String pageRouter = apolloConfigHelper.getStringConfigValueByKey("pageMapping", "");
        if (StringUtils.isNotBlank(pageRouter)) {
            Map<String, String> pageMap = JacksonUtil.toBean(pageRouter, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isNotEmpty(pageMap)) {
                for (Map.Entry entry : pageMap.entrySet()) {
                    if (entry.getValue().toString().equalsIgnoreCase(page)) {
                        page = entry.getKey().toString();
                        break;
                    }
                }
                return page;
            }
        }
        return null;
    }

    private String findPath(String page) {
        String pageRouter = apolloConfigHelper.getStringConfigValueByKey("pageRouter", "");
        if (StringUtils.isNotBlank(pageRouter)) {
            Map<String, String> pageMap = JacksonUtil.toBean(pageRouter, new TypeReference<Map<String, String>>() {
            });
            if (MapUtils.isNotEmpty(pageMap)) {
                return MapUtils.getString(pageMap, page);
            }
        }
        return null;
    }


    /**
     * 获取小程序阿波罗配置版本
     *
     * @return
     */
    public String getMpApolloVersion() {
        return apolloConfigHelper.getStringConfigValueByKey("MpApolloVersion", "5.0.0");
    }

    public void processResources(GatherRequest request) {
        if (StringUtils.isBlank(request.getResources())) {
            request.setResources(apolloConfigHelper.getStringConfigValueByKey("GatherDefaultResources", "store,mcc,category,goods,activity,config"));
        }
    }

    /**
     * 是否为门店首页这种特殊的二维码
     *
     * @param request
     * @return
     */
    public boolean isIndexQrcode(GatherRequest request) {
        if (CommonUtil.isValidURL(request.getUrl())) {
            String domains = apolloConfigHelper.getStringConfigValueByKey("forwardToIndexDomain", "");
            if (StringUtils.isBlank(domains)) {
                return false;
            }
            List<String> list = Arrays.asList(domains.split("\n"));
            if (CollectionUtils.isEmpty(list)) {
                return false;
            }
            Map<String, Object> params = CommonUtil.getUrlParams(request.getUrl());
            if (MapUtils.isEmpty(params) || (params.size() == 1 && params.containsKey("scancode_time"))) {
                String[] arr = request.getUrl().split("\\?");
                // jjzDomain.equalsIgnoreCase(arr[0]) || (jjzDomain + "/").equalsIgnoreCase(arr[0]);
                return list.contains(arr[0]);
            }
        }
        return false;
    }

    /**
     * 从header中获取小程序相关信息
     *
     * @param httpServletRequest
     * @param request
     */
    public void setHeaderData(HttpServletRequest httpServletRequest, GatherRequest request) {
        String miniProgramType = httpServletRequest.getHeader("Wosai-MiniProgramType");
        String scene = httpServletRequest.getHeader("Wosai-Scene");
        String acceptLanguage = HttpRequestUtil.getAcceptLanguage(httpServletRequest);
        if (StringUtils.isNotBlank(miniProgramType)) {
            request.setMiniProgramType(miniProgramType);
        }
        if (StringUtils.isNotBlank(scene)) {
            request.setScene(scene);
        }
        if (StringUtils.isNotBlank(acceptLanguage)) {
            request.setAcceptLanguage(acceptLanguage);
        }
        if (allowHeaderTokenLogin()) {
            String token = httpServletRequest.getHeader("Wosai-Token");
            if (StringUtils.isNotBlank(token)) {
                request.setToken(token);
            }
        }
        if (StringUtils.isEmpty(request.getAppid())) {
            request.setAppid(httpServletRequest.getHeader("x-sqb-mp-platform-appid"));
        }
    }

    /**
     * 是否允许使用header中的token进行登录
     *
     * @return false
     */
    private boolean allowHeaderTokenLogin() {
        return apolloConfigHelper.getBooleanConfigValueByKey("AllowHeaderTokenLogin", false);
    }


    public void logHeaders(HttpServletRequest httpServletRequest, GatherRequest request) {
        log.info("聚合接口请求header",
                keyValue("method", "gatherHeader"),
                keyValue("uri", request.getScene()),
                keyValue("request", JSON.toJSONString(getHttpRequestAllHeaders(httpServletRequest))));
    }

    private Map<String, String> getHttpRequestAllHeaders(HttpServletRequest httpServletRequest) {
        Map<String, String> headers = new HashMap<>();
        try {
            Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                String value = httpServletRequest.getHeader(name);
                headers.put(name, value);
            }
        } catch (Exception e) {
        }
        return headers;
    }

    public void fillExtraRequestData(HttpServletRequest httpServletRequest, ApiRequest<GatherOrderExtraRequest> apiRequest) {
        GatherOrderExtraRequest request = apiRequest.getBody();
        String miniProgramType = httpServletRequest.getHeader("Wosai-MiniProgramType");
        if (StringUtils.isNotBlank(miniProgramType)) {
            request.setMiniProgramType(MiniProgramType.get(miniProgramType));
        } else {
            request.setMiniProgramType(MiniProgramType.WECHAT);
        }
        if (Objects.isNull(request.getPayway())) {
            if (request.getMiniProgramType() == MiniProgramType.WECHAT) {
                request.setPayway(PayWay.WECHAT.getCode());
            }
            if (request.getMiniProgramType() == MiniProgramType.ALIPAY) {
                request.setPayway(PayWay.ALIPAY.getCode());
            }
            if (request.getMiniProgramType() == MiniProgramType.UNION_PAY) {
                request.setPayway(PayWay.QUICK_PASS.getCode());
            }
            request.setSubPayway(SubPayWay.MINI.getCode());
        }
    }

    public void fillRequestData(HttpServletRequest httpServletRequest, ApiRequest<GatherOrderRequest> apiRequest) {
        GatherOrderRequest request = apiRequest.getBody();
        request.setType(orderHelper.getOrderType(request.getOrderType()));
        request.setUserId(apiRequest.getUser().getUserId());
        request.setThirdpartyUserId(apiRequest.getUser().getThirdpartyUserId());
        request.setAppid(httpServletRequest.getHeader("x-sqb-mp-platform-appid"));
        if (StringUtils.isBlank(request.getAppid())) {
            request.setAppid(apiRequest.getUser().getWeixinAppId());
        }
        String miniProgramType = httpServletRequest.getHeader("Wosai-MiniProgramType");
        if (StringUtils.isNotBlank(miniProgramType)) {
            request.setMiniProgramType(MiniProgramType.get(miniProgramType));
        } else {
            request.setMiniProgramType(MiniProgramType.WECHAT);
        }
        if (Objects.isNull(request.getDiscountParams().getPayway())) {
            if (request.getMiniProgramType() == MiniProgramType.WECHAT) {
                request.getDiscountParams().setPayway(PayWay.WECHAT.getCode());
            }
            if (request.getMiniProgramType() == MiniProgramType.ALIPAY) {
                request.getDiscountParams().setPayway(PayWay.ALIPAY.getCode());
            }
            if (request.getMiniProgramType() == MiniProgramType.UNION_PAY) {
                request.getDiscountParams().setPayway(PayWay.QUICK_PASS.getCode());
            }
        }
        request.getDiscountParams().setSubPayway(SubPayWay.MINI.getCode());
        request.getDiscountParams().setStoreId(request.getStoreId());
        request.getDiscountParams().setMerchantId(request.getMerchantId());
        request.getDiscountParams().setTableId(Objects.nonNull(request.getTableId()) ? request.getTableId().toString() : "");
        request.getDiscountParams().setDiscountStrategy(request.getType().getMsg());
        request.getDiscountParams().setCompatible(true);
    }

    public void verifyRequestParams(GatherOrderRequest request) {
        if (StringUtils.isBlank(request.getStoreId())) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "门店ID不能为空");
        }
        if (Objects.isNull(request.getType())) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "订单类型不能为空");
        }
        request.setType(orderHelper.getOrderType(request.getOrderType()));
        int serviceType = CommonUtil.getServiceType(request.getType());
        if (serviceType == 0) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "不支持的订单类型");
        }
        request.setServiceType(serviceType);
        if (request.getType() == OrderType.EAT_FIRST_ORDER && request.getTableId() <= 0) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "围餐订单桌台ID无效");
        }
    }

    public void verifyExtraRequestParams(GatherOrderExtraRequest request) {
        if (StringUtils.isBlank(request.getStoreId())) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "门店ID不能为空");
        }
        if (StringUtils.isBlank(request.getStoreSn())) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "门店SN不能为空");
        }
        if (Objects.isNull(request.getMerchantId())) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "商户ID不能为空");
        }
    }
}
