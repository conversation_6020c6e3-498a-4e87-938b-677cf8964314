package com.wosai.pantheon.uf4c.model;

import lombok.Data;

import java.util.Map;

@Data
public class PayParamWrapper {

    /**
     * 储值的scene
     */
    private String storedScene;

    /**
     * 充值方案id
     */
    private String rechargeRuleId;

    /**
     * 是否是储值并支付
     */
    private boolean rechargeAndPay = false;

    /**
     * 充值金额
     */
    private Long rechargeAmount ;

    /**
     * c端收银台传入的参数，原样进行透传
     */
    private Map<String, Object> cashierBizParams;

    /**
     * 优惠签名信息
     */
    private String redeemDigest;
    /**
     * 优惠金额
     */
    private Long totalDiscount;

    /**
     * 二次支付参数
     */
    private String acquiring;

    /**
     * 场景值
     */
    private String sqbPaySource;

    /**
     * 支付请求头中需要存储的字段
     */
    private Map payHeaders;

    /**
     * 操作人
     */
    private String operator;
}
