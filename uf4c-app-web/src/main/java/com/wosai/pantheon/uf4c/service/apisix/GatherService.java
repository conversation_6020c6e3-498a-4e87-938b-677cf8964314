package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.GatherOrderExtraRequest;
import com.wosai.pantheon.uf4c.model.GatherOrderRequest;
import com.wosai.pantheon.uf4c.model.GatherRequest;

import java.util.Map;

@JsonRpcService(value = "/rpc/gather")
public interface GatherService {


    /**
     * 点单页聚合接口
     *
     * @param request
     * @return
     */
    Map<String, Object> index(ApiRequest<GatherRequest> request);

    Map<String, Object> index(GatherRequest request);

    /**
     * 清除点单页聚合接口缓存
     *
     * @param storeId
     */
    void cleanCache(String storeId);

    /**
     * 获取缓存中的数据
     *
     * @param storeId
     * @param serviceType
     * @return
     */
    Map<String, Object> getCacheData(String storeId, Integer serviceType);

    /**
     * 下单页主要数据聚合接口
     *
     * @param request
     * @return
     */
    Map<String, Object> orderMain(ApiRequest<GatherOrderRequest> request);

    /**
     * 下单页额外数据聚合接口
     *
     * @param request
     * @return
     */
    Map<String, Object> orderExtra(ApiRequest<GatherOrderExtraRequest> request);
}
