package com.wosai.pantheon.uf4c.fallbackconfig.server;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.uf4c.gather.GatherBase;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartAndRedeem;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.model.dto.RecommendMaterialFindRequest;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.model.vo.RecommendMaterialTuple;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.smartbiz.base.pojo.RedeemResult;

import java.util.*;

import static com.wosai.pantheon.uf4c.web.exception.ReturnCode.BLOCK_FALLBACK_ERROR;

/**
 * 处理限流和降级的类
 */
public class BlockHandleServer {

    public static List<RecommendMaterialTuple> handleRecommendMaterial(RecommendMaterialFindRequest request, BlockException ex) throws Throwable {
        return new ArrayList<>();
    }


    public static Map<String, Object> handleIndex(GatherRequest request, BlockException ex) throws Throwable {
        Map<String, Object> map = new HashMap<>();
        GatherBase.setClosing(map, Arrays.asList("当前使用人数太多，请稍后再试"));
        return map;
    }


    public static CartAndRedeem handleGetCartAndRedeem(CartsRequest request, BlockException ex) throws Throwable {
        throw new BusinessException(BLOCK_FALLBACK_ERROR);
    }

    public static RedeemResult handleGetRedeemResult(RedeemRequest redeemRequest, BlockException ex) throws Throwable {
        return null;
    }

    public static ItemDetailVO handleGetItemDetailById(Map queryParam, BlockException ex) throws Throwable {
        throw new BusinessException(BLOCK_FALLBACK_ERROR);
    }


    public static Map<String, Object> handleMiniIndex(ItemFind find, BlockException ex) throws Throwable {
        throw new BusinessException(BLOCK_FALLBACK_ERROR);
    }

    public static PayResult handlePay(PayRequest request, BlockException ex) throws Throwable {
        throw new BusinessException(BLOCK_FALLBACK_ERROR);
    }

    public static Cart handleRoundMealList(Map queryParam , BlockException ex) throws Throwable {
        throw new BusinessException(BLOCK_FALLBACK_ERROR);
    }

}
