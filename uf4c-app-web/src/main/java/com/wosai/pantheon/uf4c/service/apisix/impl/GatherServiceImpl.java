package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.gather.GatherHelper;
import com.wosai.pantheon.uf4c.gather.cache.GatherCacheHelper;
import com.wosai.pantheon.uf4c.gather.order.GatherOrderService;
import com.wosai.pantheon.uf4c.model.GatherOrderExtraRequest;
import com.wosai.pantheon.uf4c.model.GatherOrderRequest;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.service.apisix.GatherService;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class GatherServiceImpl implements GatherService {


    @Autowired
    private com.wosai.pantheon.uf4c.gather.GatherService gatherService;
    @Autowired
    private HttpServletRequest httpServletRequest;
    @Autowired
    private GatherHelper gatherHelper;
    @Autowired
    private GatherCacheHelper gatherCacheHelper;
    @Autowired
    private GatherOrderService gatherOrderService;


    @Override
    public Map<String, Object> index(ApiRequest<GatherRequest> apiRequest) {
        GatherRequest request = apiRequest.getBody();
        return index(request);
    }

    @Override
    public Map<String, Object> index(GatherRequest request) {
        request.setReqId(UUID.randomUUID().toString().replaceAll("-", ""));
        //gatherHelper.logHeaders(httpServletRequest, request);
        gatherHelper.setHeaderData(httpServletRequest, request);
        CommonUtil.parseGatherRequestParams(request);
        gatherHelper.processResources(request);
        // 若二维码和门店都为空，则不处理或者仅查询阿波罗配置
        if ("config".equalsIgnoreCase(request.getResources()) || (StringUtils.isBlank(request.getUrl()) && StringUtils.isBlank(request.getStoreId()))) {
            return gatherService.defaultRequest(request);
        }
        if (gatherHelper.isIndexQrcode(request)) {
            return gatherService.pageIndex(request);
        }
        // 根据环境处理请求
        return gatherHelper.dispatcherRequest(request);
    }

    @Override
    public void cleanCache(String storeId) {
        gatherCacheHelper.clean(storeId);
    }

    @Override
    public Map<String, Object> getCacheData(String storeId, Integer serviceType) {
        GatherRequest request = new GatherRequest();
        request.setStoreId(storeId);
        request.setServiceType(serviceType);
        Map<String, Object> dataMap = new HashMap<>();
        gatherCacheHelper.getStoreCache(storeId, dataMap);
        gatherCacheHelper.getGoodsCache(request, dataMap);
        return dataMap;
    }

    @Override
    public Map<String, Object> orderMain(ApiRequest<GatherOrderRequest> apiRequest) {
        gatherHelper.fillRequestData(httpServletRequest, apiRequest);
        gatherHelper.verifyRequestParams(apiRequest.getBody());
        return gatherOrderService.main(apiRequest.getBody(), apiRequest.getUser());
    }

    @Override
    public Map<String, Object> orderExtra(ApiRequest<GatherOrderExtraRequest> apiRequest) {
        gatherHelper.fillExtraRequestData(httpServletRequest, apiRequest);
        gatherHelper.verifyExtraRequestParams(apiRequest.getBody());
        return gatherOrderService.extra(apiRequest.getBody());
    }
}
