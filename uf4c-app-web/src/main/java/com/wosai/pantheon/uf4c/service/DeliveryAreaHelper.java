package com.wosai.pantheon.uf4c.service;

import com.google.common.collect.Lists;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.merchant.api.CampusRemoteService;
import com.wosai.market.merchant.api.CustomerCampusRemoteService;
import com.wosai.market.merchant.api.DeliveryAddressRemoteService;
import com.wosai.market.merchant.api.DeliveryAreaRemoteService;
import com.wosai.market.merchant.dto.AppEdgeRequest;
import com.wosai.market.merchant.dto.address.DeliveryAddressDto;
import com.wosai.market.merchant.dto.apisix.ApiRequest;
import com.wosai.market.merchant.dto.area.DeliveryAreaDto;
import com.wosai.market.merchant.dto.campus.CampusBaseInfo;
import com.wosai.market.merchant.dto.campus.request.StoresCampusListRequest;
import com.wosai.market.merchant.dto.campus.request.UserStructAddressRequest;
import com.wosai.market.merchant.dto.campus.response.UseStructAddressResponse;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.DeliverInfo;
import com.wosai.pantheon.uf4c.model.RangeAreaDto;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Service
@Slf4j
public class DeliveryAreaHelper {
    @Resource
    private ConfigRemoteService configRemoteService;
    @Resource
    private CampusRemoteService campusRemoteService;
    @Resource
    private DeliveryAreaRemoteService deliveryAreaRemoteService;
    @Resource
    private CustomerCampusRemoteService customerCampusRemoteService;
    @Resource
    private DeliveryAddressRemoteService deliveryAddressRemoteService;


    public RangeAreaDto getAreas(String storeId, String apiVersion) {
        try {
            StoresCampusListRequest storeCampusListRequest = new StoresCampusListRequest();
            storeCampusListRequest.setStoreIds(Collections.singletonList(storeId));
            List<CampusBaseInfo> campusBaseInfos = campusRemoteService.storesCampusList(storeCampusListRequest);
            // 是否为校园店铺
            boolean isCampusStore = CollectionUtils.isNotEmpty(campusBaseInfos);
            // 是否仅服务校内
            boolean onlyForCampus = isCampusStore && campusBaseInfos.stream().anyMatch(it -> Objects.equals(it.getDeliveryRange(), 2));
            if (isCampusStore) {
                if (onlyForCampus) {
                    // 按照校区名称进行分组
                    Map<String, List<CampusBaseInfo>> campusMap = campusBaseInfos.stream().collect(Collectors.groupingBy(CampusBaseInfo::getCampusPageName));
                    List<RangeAreaDto.Area> areaList = new ArrayList<>();
                    campusMap.forEach((k, v) -> {
                        v.sort(Comparator.comparing(CampusBaseInfo::getId));
                        CampusBaseInfo campusBaseInfo = v.get(0);
                        RangeAreaDto.Area area = new RangeAreaDto.Area();
                        area.setAddress(campusBaseInfo.getAddress());
                        area.setId(campusBaseInfo.getId());
                        area.setName(campusBaseInfo.getCampusName());
                        area.setCampusPageName(campusBaseInfo.getCampusPageName());
                        area.setLongitude(campusBaseInfo.getLongitude());
                        area.setLatitude(campusBaseInfo.getLatitude());
                        areaList.add(area);
                    });
                    RangeAreaDto areaDto = new RangeAreaDto();
                    areaDto.setEnabled(true);
                    areaDto.setType(RangeAreaDto.TYPE_CAMPUS);
                    areaDto.setAreas(areaList);
                    return areaDto;
                }
            } else {
                Map<String, String> configs = getDeliveryConfigs(storeId);
                String deliveryType = MapUtils.getString(configs, Constants.DELIVERY_TYPE);
                String deliveryRangeType = MapUtils.getString(configs, Constants.DELIVERY_RANGE_TYPE);
                if (Objects.equals("2", deliveryType) && Objects.equals("area", deliveryRangeType)) {
                    AppEdgeRequest appEdgeRequest = new AppEdgeRequest();
                    appEdgeRequest.setStoreId(storeId);
                    List<DeliveryAreaDto> deliveryAreaDtoList = deliveryAreaRemoteService.query(appEdgeRequest);

                    if (CollectionUtils.isNotEmpty(deliveryAreaDtoList)) {
                        List<DeliveryAreaDto> checkedList = deliveryAreaDtoList.stream().filter(DeliveryAreaDto::getChecked).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(checkedList)) {
                            RangeAreaDto areaDto = new RangeAreaDto();
                            areaDto.setEnabled(true);
                            areaDto.setType(RangeAreaDto.TYPE_AREA);
                            areaDto.setAreas(checkedList.stream()
                                    .map(it -> {
                                        RangeAreaDto.Area area = new RangeAreaDto.Area();
                                        area.setAddress(it.getAddress());
                                        area.setId(it.getId());
                                        area.setName(it.getName());
                                        area.setLongitude(it.getLongitude());
                                        area.setLatitude(it.getLatitude());
                                        return area;
                                    }).collect(Collectors.toList()));
                            return areaDto;
                        }
                    }
                } else if (Objects.equals("2", deliveryType) && Objects.equals("address", deliveryRangeType) && Objects.equals(apiVersion, "2")) {
                    AppEdgeRequest appEdgeRequest = new AppEdgeRequest();
                    appEdgeRequest.setStoreId(storeId);
                    List<DeliveryAddressDto> deliveryAddressDtoList = deliveryAddressRemoteService.query(appEdgeRequest);
                    if (CollectionUtils.isNotEmpty(deliveryAddressDtoList)) {
                        List<DeliveryAddressDto> checkedList = deliveryAddressDtoList.stream().filter(DeliveryAddressDto::getChecked).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(checkedList)) {
                            RangeAreaDto areaDto = new RangeAreaDto();
                            areaDto.setEnabled(true);
                            areaDto.setType(RangeAreaDto.TYPE_ADDRESS);
                            areaDto.setAreas(checkedList.stream()
                                    .map(it -> {
                                        RangeAreaDto.Area area = new RangeAreaDto.Area();
                                        area.setAddress(it.getAddress());
                                        area.setId(it.getId());
                                        area.setDeliveryFee(it.getDeliveryFee());
                                        return area;
                                    }).collect(Collectors.toList()));
                            return areaDto;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查询可送区域失败",
                    keyValue("method", "DeliveryAreaHelper.getAreas"),
                    keyValue("arguments", storeId), e);
        }
        return new RangeAreaDto();
    }


    private UseStructAddressResponse getStructAddress(String storeId, Integer campusId) {
        UseStructAddressResponse useStructAddressResponse = null;
        ApiRequest apiRequest = new ApiRequest<>();
        UserStructAddressRequest userStructAddressRequest = new UserStructAddressRequest();
        userStructAddressRequest.setStoreId(storeId);
        userStructAddressRequest.setCampusId(campusId);
        apiRequest.setBody(userStructAddressRequest);
        try {
            useStructAddressResponse = customerCampusRemoteService.useStructuredAddress(apiRequest);
        } catch (Exception e) {
            log.warn("查询可送区域失败",
                    keyValue("method", "getStructAddress"),
                    keyValue("arguments", storeId), e);
        }
        return useStructAddressResponse;
    }


    public void checkDeliveryInfo(String storeId, DeliverInfo deliveryInfo) {
        if (Objects.isNull(deliveryInfo.getAppVersion())) {
            return;
        }
        Long apiVersion = Optional.ofNullable(deliveryInfo.getAppVersion()).orElse(1L);
        RangeAreaDto rangeAreaDto = getAreas(storeId, String.valueOf(apiVersion));
        if (rangeAreaDto.isEnabled()) {
            List<Integer> idList = rangeAreaDto.getAreas().stream().map(RangeAreaDto.Area::getId).collect(Collectors.toList());
            if (RangeAreaDto.TYPE_AREA.equalsIgnoreCase(rangeAreaDto.getType())) {
                if (Objects.isNull(deliveryInfo.getAreaId()) || !idList.contains(deliveryInfo.getAreaId())) {
                    throw new BusinessException(ReturnCode.DELIVERY_AREA_ERROR);
                }
            }
            if (RangeAreaDto.TYPE_CAMPUS.equalsIgnoreCase(rangeAreaDto.getType())) {
                if (Objects.isNull(deliveryInfo.getCampusId()) || !idList.contains(deliveryInfo.getCampusId())) {
                    throw new BusinessException(ReturnCode.DELIVERY_CAMPUS_ERROR);
                }
                UseStructAddressResponse useStructAddressResponse = getStructAddress(storeId, deliveryInfo.getCampusId());
                if (Objects.nonNull(useStructAddressResponse)) {
                    if (useStructAddressResponse.isUseStructuredAddress() && StringUtils.isBlank(deliveryInfo.getAddressCode())) {
                        throw new BusinessException(ReturnCode.DELIVERY_CAMPUS_ADDRESS_ERROR);
                    }
                }
            }
            if (RangeAreaDto.TYPE_ADDRESS.equalsIgnoreCase(rangeAreaDto.getType())) {
                if (Objects.isNull(deliveryInfo.getAddressId()) || !idList.contains(deliveryInfo.getAddressId())) {
                    throw new BusinessException(ReturnCode.DELIVERY_ADDRESS_ERROR);
                }
            }
        }
    }

    private Map<String, String> getDeliveryConfigs(String storeId) {
        FindConfigByNameRequest deliveryTypeRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TYPE);
        FindConfigByNameRequest deliveryRangeTypeRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_RANGE_TYPE);
        List<ConfigResponse> configResponses = configRemoteService.batchFindByName(Lists.newArrayList(deliveryTypeRequest, deliveryRangeTypeRequest));
        if (CollectionUtils.isNotEmpty(configResponses)) {
            return configResponses.stream().collect(Collectors.toMap(ConfigResponse::getName, ConfigResponse::getValue));
        }
        return null;
    }

}
