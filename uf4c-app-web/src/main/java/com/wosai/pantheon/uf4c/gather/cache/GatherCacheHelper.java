package com.wosai.pantheon.uf4c.gather.cache;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.gather.GatherBase;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.vo.CategoryVo;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 聚合接口缓存处理
 */
@Component
@EnableAsync
@Slf4j
public class GatherCacheHelper extends GatherBase {
    private static final String storeKeyPrefix = "store:";
    private static final String goodsKeyPrefix = "goods:";
    private static final String skuKeyPrefix = "sku:";
    private static final String STORE_DATA_MCC = "mcc"; // 门店配置
    private static final String STORE_DATA_MCH_MCC = "mch_mcc"; // 商户配置
    private static final String STORE_DATA_DETAIL = "store"; // 门店详情
    private static final String STORE_DATA_AREA = "areas"; // 可送区域
    private static final String STORE_DATA_THEME = "theme"; // 门店主题
    private static final String STORE_DATA_ACTIVITY = "activity"; // 门店优惠
    private static final String GOODS_DATA_GOODS = "goods"; // 商品
    private static final String GOODS_DATA_CATEGORY = "category"; // 商品分类

    @Autowired
    private GatherRedisUtil gatherRedisUtil;
    @Autowired
    private RedisTemplate gatherRedisTemplate;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    public boolean isCacheEnabled(String storeId) {
        boolean cacheEnable = apolloConfigHelper.getBooleanConfigValueByKey("gatherCacheEnable", false);
        String whitelist = apolloConfigHelper.getStringConfigValueByKey("gatherCacheWhitelist", "");
        // 白名单类型 1-白名单：名单内使用缓存 2-黑名单：不在名单内使用缓存
        long whitelistType = apolloConfigHelper.getLongConfigValueByKey("gatherCacheWhitelistType", 1L);
        return StringUtils.isNotBlank(storeId) && cacheEnable && (StringUtils.isBlank(whitelist) || ((whitelistType == 1) == whitelist.contains(storeId)));
    }

    public void saveDataToCache(GatherRequest request, Map<String, Object> dataMap) {
        try {
            saveStoreToCache(request.getStoreId(), dataMap);
            gatherRedisUtil.setExpire(storeKey(request.getStoreId()));

            //零售门店不缓存商品数据
            if (!request.isRetailStore()) {
                saveGoodsToCache(request.getStoreId(), request.getServiceType(), dataMap);
                setSkuData(request.getStoreId(), dataMap);
                gatherRedisUtil.setExpire(goodsKey(request.getStoreId(), request.getServiceType()));
                gatherRedisUtil.setExpire(skuKey(request.getStoreId()));
            }

            LogUtils.logInfo("保存聚合接口缓存", "saveDataToCache", request);
        } catch (Exception e) {
            LogUtils.logWarn("saveDataToCache", "saveDataToCache", request, e);
        }

    }

    public void saveGoodsToCache(String storeId, Integer serviceType, Map<String, Object> dataMap) {
        if (Objects.nonNull(serviceType) && serviceType > 0) {
            // 门店优惠
            setGoodsData(storeId, serviceType, STORE_DATA_ACTIVITY, MapUtils.getObject(dataMap, STORE_DATA_ACTIVITY));
            // 首页商品
            setGoodsData(storeId, serviceType, GOODS_DATA_GOODS, MapUtils.getObject(dataMap, GOODS_DATA_GOODS));
            // 商品分类
            setGoodsData(storeId, serviceType, GOODS_DATA_CATEGORY, MapUtils.getObject(dataMap, GOODS_DATA_CATEGORY));
        }
    }


    /**
     * 保存门店相关数据到缓存中
     */
    public void saveStoreToCache(String storeId, Map<String, Object> dataMap) {
        if (StringUtils.isNotBlank(storeId) && MapUtils.isNotEmpty(dataMap)) {
            // 门店配置
            setStoreData(storeId, STORE_DATA_MCC, MapUtils.getObject(dataMap, STORE_DATA_MCC));
            // 商户配置
            setStoreData(storeId, STORE_DATA_MCH_MCC, MapUtils.getObject(dataMap, STORE_DATA_MCH_MCC));
            // 门店详情
            setStoreData(storeId, STORE_DATA_DETAIL, MapUtils.getObject(dataMap, STORE_DATA_DETAIL));
            // 主题配置
            //setStoreData(storeId, STORE_DATA_THEME, MapUtils.getObject(dataMap, STORE_DATA_THEME));
            // 可送区域
            //setStoreData(storeId, STORE_DATA_AREA, MapUtils.getObject(dataMap, STORE_DATA_AREA));
        }
    }

    public void clean(String storeId) {
        if (StringUtils.isEmpty(storeId)) {
            return;
        }
        try {
            gatherRedisTemplate.delete(Lists.newArrayList(storeKey(storeId), skuKey(storeId), goodsKey(storeId, 1), goodsKey(storeId, 2)));
        } catch (Exception e) {
            LogUtils.logError("清空缓存出错", "clean", storeId, e);
        }
    }

    public void getStoreCache(String storeId, Map<String, Object> dataMap) {
        durationStart(dataMap, "storeCache");
        Map<String, Object> storeCacheMap = gatherRedisUtil.getHashAllValues(storeKey(storeId));
        durationEnd(dataMap, "storeCache");
        dataMap.putAll(storeCacheMap);
    }

    public void getGoodsCache(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "goodsCache");
        Map<String, Object> goodsCacheMap = gatherRedisUtil.getHashAllValues(goodsKey(request.getStoreId(), request.getServiceType()));
        durationEnd(dataMap, "goodsCache");
        durationStart(dataMap, "skuCache");
        mergeSku(request.getStoreId(), goodsCacheMap);
        durationEnd(dataMap, "skuCache");
        dataMap.putAll(goodsCacheMap);
        dataMap.put("cacheData", true);
    }

    private void setStoreData(String storeId, String field, Object data) {
        if (Objects.isNull(data)) {
            return;
        }
        gatherRedisUtil.saveHashData(storeKey(storeId), field, data);
    }

    private void setGoodsData(String storeId, Integer serviceType, String field, Object data) {
        if (Objects.isNull(data)) {
            //data = new HashMap<String, Object>();
            return;
        }
        if (field.equalsIgnoreCase(GOODS_DATA_CATEGORY)) {
            List<CategoryVo> categoryVoList = (List<CategoryVo>) data;
            try {
                if (CollectionUtils.isNotEmpty(categoryVoList)) {
                    if ("recent".equalsIgnoreCase(categoryVoList.get(0).getId())) {
                        categoryVoList.remove(0);
                    }
                    gatherRedisUtil.saveHashData(goodsKey(storeId, serviceType), field, categoryVoList);
                }

            } catch (Exception ignored) {
                gatherRedisUtil.saveHashData(goodsKey(storeId, serviceType), field, data);
            }
        } else {
            gatherRedisUtil.saveHashData(goodsKey(storeId, serviceType), field, data);
        }
    }

    private void setSkuData(String storeId, Map<String, Object> dataMap) {
        List<CategoryVo> categoryVoList = CommonUtil.transferObject(MapUtils.getObject(dataMap, GOODS_DATA_CATEGORY), new TypeReference<List<CategoryVo>>() {
        });
        Map<String, Object> goods = (Map<String, Object>) MapUtils.getObject(dataMap, GOODS_DATA_GOODS);
        Map<String, Object> skuMap = new HashMap<>();
        List<ItemDetailVO> newList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(categoryVoList)) {
            categoryVoList.forEach(it -> {
                if (CollectionUtils.isNotEmpty(it.getItems())) {
                    newList.addAll(it.getItems());
                }
            });
        }
        if (CollectionUtils.isNotEmpty(newList)) {
            newList.forEach(it -> {
                if (Objects.nonNull(it.getItem().getSku())) {
                    if (Objects.isNull(it.getItem().getSku())) {
                        skuMap.put(it.getItem().getId(), "null");
                    } else {
                        skuMap.put(it.getItem().getId(), it.getItem().getSku().toString());
                    }
                }
            });
        }
        if (MapUtils.getIntValue(goods, "total", 0) > 0) {
            List<List<Map<String, Object>>> pages = (List<List<Map<String, Object>>>) MapUtils.getObject(goods, "pages");
            if (CollectionUtils.isNotEmpty(pages)) {
                pages.forEach(p -> p.forEach(i -> {
                    List<ItemDetailVO> list = CommonUtil.transferObject(MapUtils.getObject(i, "items"), new TypeReference<List<ItemDetailVO>>() {
                    });
                    list.forEach(it -> skuMap.put(it.getItem().getId(), Objects.isNull(it.getItem().getSku()) ? "null" : it.getItem().getSku()));
                }));
            }
        }
        for (Map.Entry<String, Object> mapEntry : skuMap.entrySet()) {
            gatherRedisUtil.saveHashData(skuKey(storeId), mapEntry.getKey(), mapEntry.getValue());
        }
    }

    /**
     * 合并sku信息
     *
     * @param storeId
     * @param goodsMap
     */
    private void mergeSku(String storeId, Map<String, Object> goodsMap) {
        Map<String, Object> skuMap = getSkuCache(storeId);
        if (goodsMap.containsKey(GOODS_DATA_GOODS)) {
            Map<String, Object> goods = (Map<String, Object>) MapUtils.getObject(goodsMap, "goods");
            if (MapUtils.isEmpty(goods)) {
                return;
            }
            List<List<Map<String, Object>>> pages = (List<List<Map<String, Object>>>) MapUtils.getObject(goods, "pages");
            pages.forEach(p -> {
                p.forEach(i -> {
                    List<Map<String, Object>> itemList = (List<Map<String, Object>>) MapUtils.getObject(i, "items");
                    itemList.forEach(it -> {
                        Map<String, Object> itemMap = (Map<String, Object>) MapUtils.getObject(it, "item");
                        String itemId = MapUtils.getString(itemMap, "id");
                        if (Objects.nonNull(MapUtils.getObject(itemMap, "sale_times"))) {
                            List<Map> saleTimes = (List<Map>) MapUtils.getObject(itemMap, "sale_times");
                            for (Map time : saleTimes) {
                                time.put("intime_section", CommonUtil.inTimes(MapUtils.getString(time, "start_time"), MapUtils.getString(time, "end_time")));
                            }
                        }
                        if (skuMap.containsKey(itemId)) {
                            String skuStr = MapUtils.getString(skuMap, itemId);
                            if (StringUtils.isBlank(skuStr) || "null".equalsIgnoreCase(skuStr)) {
                                itemMap.put("sku", null);
                                itemMap.put("out_of_stock", false);
                            } else {
                                int sku = MapUtils.getIntValue(skuMap, itemId, 0);
                                itemMap.put("sku", sku);
                                itemMap.put("out_of_stock", sku <= 0);
                            }
                        }
                    });
                });
            });
        }
        if (goodsMap.containsKey(GOODS_DATA_CATEGORY)) {
            List<Map<String, Object>> categories = (List<Map<String, Object>>) MapUtils.getObject(goodsMap, GOODS_DATA_CATEGORY);
            if (CollectionUtils.isNotEmpty(categories)) {
                Map<String, Object> category = categories.stream().filter(it -> "recent".equalsIgnoreCase(MapUtils.getString(it, "id"))).findFirst().orElse(null);
                if (Objects.nonNull(category)) {
                    categories.remove(category);
                }
            }
            // 从缓存中删除点过的菜分类
            categories.forEach(c -> {
                if (c.containsKey("items")) {
                    List<Map<String, Object>> items = (List<Map<String, Object>>) MapUtils.getObject(c, "items");
                    items.forEach(it -> {
                        Map<String, Object> itemMap = (Map<String, Object>) MapUtils.getObject(it, "item");
                        String itemId = MapUtils.getString(itemMap, "id");
                        if (Objects.nonNull(MapUtils.getObject(itemMap, "sale_times"))) {
                            List<Map> saleTimes = (List<Map>) MapUtils.getObject(itemMap, "sale_times");
                            for (Map time : saleTimes) {
                                time.put("intime_section", CommonUtil.inTimes(MapUtils.getString(time, "start_time"), MapUtils.getString(time, "end_time")));
                            }
                        }
                        if (skuMap.containsKey(itemId)) {
                            String skuStr = MapUtils.getString(skuMap, itemId);
                            if (StringUtils.isBlank(skuStr) || "null".equalsIgnoreCase(skuStr)) {
                                itemMap.put("sku", null);
                                itemMap.put("out_of_stock", false);
                            } else {
                                int sku = MapUtils.getIntValue(skuMap, itemId, 0);
                                itemMap.put("sku", sku);
                                itemMap.put("out_of_stock", sku <= 0);
                            }
                        }
                    });
                }
            });
        }

    }

    public boolean hasStoreCache(String storeId) {
        return gatherRedisUtil.hasKey(storeKey(storeId), "mcc");
    }


    public boolean hasGoodsCache(String storeId, Integer serviceType) {
        return gatherRedisUtil.hasKey(goodsKey(storeId, serviceType), "goods");
    }

    public Map<String, Object> getSkuCache(String storeId) {
        gatherRedisUtil.getHashAllValues(skuKey(storeId));
        return gatherRedisUtil.getHashAllValues(skuKey(storeId));
    }


    private String storeKey(String storeId) {
        return "store:".concat(storeId);
    }


    private String goodsKey(String storeId, Integer serviceType) {
        return "goods:".concat(serviceType.toString()).concat(":").concat(storeId);
    }

    private String skuKey(String storeId) {
        return "sku:".concat(storeId);
    }
}
