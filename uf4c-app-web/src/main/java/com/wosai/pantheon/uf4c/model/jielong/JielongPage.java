package com.wosai.pantheon.uf4c.model.jielong;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 接龙页面
 */
@Data
public class JielongPage implements Serializable {
    Integer jielongId;
    String storeId;
    String storeName;
    String storeImg;
    JielongDetail detail;
    /**
     * 商品列表
     */
    List<JielongGoods> goodsList;
    /**
     * 当前用户购物车
     */
    JielongCart cart;
    /**
     * 当前用户订单列表
     */
    List<OrderInfo> userOrderList;
    /**
     * 接龙历史
     */
    List<OrderInfo> historyOrderList;
    String posterImg;
    String shareImg;

    /**
     * 接龙详情
     */
    @Data
    public static class JielongDetail {
        private Integer jielongId;
        private String jielongSn;
        private String title;
        private List<DescDetail> descList;
        private String img;
        /**
         * 交货方式：1-快递配送 2-到店自提"
         */
        private int deliveryType;
        /**
         * 自提地址
         */
        private String pickupAddress;
        /**
         * -1-删除 0-待发布 1-已发布 2-暂停中 3-已结束"
         */
        private int status;
        private Long startTime;
        private Long endTime;
        /**
         * 客服电话
         */
        private String cellphone;
        /**
         * 支付完成订单数
         */
        private Integer total;
        private Long ctime;
        private Long ptime;
    }


    /**
     * 接龙详情内容
     */
    @Data
    public static class DescDetail {
        /**
         * bigImg,smallImg,text
         */
        String type;
        String text;
        List<String> imgList;
    }

    /**
     * 接龙商品
     */
    @Data
    public static class JielongGoods {
        String itemId;
        String name;
        Long price;
        String img;
        Integer sku;
        Integer number;
        String description;
        /***
         * 单次每人人限购数量
         */
        Integer maxSaleNum;
    }

    /**
     * 订单信息
     */
    @Data
    public static class OrderInfo {
        Integer id;
        String orderSn;
        String orderSeq;
        Integer status;
        Long amount;
        String userId;
        String wxName;
        String wxIcon;
        String userName;
        String cellphone;
        String address;
        String remark;
        List<JielongGoods> goodsList;
        Long ctime;
        Long mtime;
    }
}
