package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.*;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Map;

/**
 * 轻餐的购物车接口
 */
@JsonRpcService(value = "/rpc/cart")
@Validated
public interface SingleCartService {

    /**
     * 添加购物车
     *
     * @param apiRequest
     * @return
     */
    Cart addCart(@Valid ApiRequest<CartItemCreate> apiRequest);

    /**
     * 减少购物车条目的额数量（减一）
     *
     * @param apiRequest
     * @return
     */
    Cart reduceCartItemCount(ApiRequest apiRequest);

    /**
     * 修改购物车商品数量
     *
     * @param apiRequest
     * @return
     */
    Cart setCartItem(@Valid ApiRequest<CartItemCreate> apiRequest);

    /**
     * 修改桌台人数
     *
     * @param apiRequest
     * @return
     */
    Cart setPeopleNum(ApiRequest<Map> apiRequest);

    /**
     * 获取购物车详情
     *
     * @param apiRequest
     * @return
     */
    Cart getCart(ApiRequest<CartItemCreate> apiRequest);

    /**
     * 清空购物车
     *
     * @param apiRequest
     * @return
     */
    Cart resetCart(ApiRequest<CartItemCreate> apiRequest);

    /**
     * 检查购物车条目状态
     *
     * @param apiRequest
     * @return
     */
    CartCheckResultDTO checkItemStatus(ApiRequest<CartItemCreate> apiRequest);

    /**
     * 购物车商品添加或删除加料
     *
     * @param apiRequest
     * @return
     */
    CartAndRedeem addOrReduceMaterial(@Valid ApiRequest<CartItemCreate> apiRequest);

    /**
     * 添加或删除条目，并且返回优惠信息
     *
     * @param apiRequest
     * @return
     */
    CartAndRedeem addCartAndRedeem(@Valid ApiRequest<CartItemCreate> apiRequest);

    /**
     * 批量添加
     *
     * @param apiRequest
     * @return
     */
    CartAndRedeem addCartAndRedeemBatch(@Valid ApiRequest<CartItemCreateBatch> apiRequest);

    /**
     * 批量添加购物车,通过传入的 spu_id 和 sku_id 自动加购 1 份商品
     *
     * @param apiRequest
     * @return
     */
    CartAndRedeem addCartAndRedeemBySpuIds(@Valid ApiRequest<SpecSpuCartItemCreate> apiRequest);


    void transCart(@Valid ApiRequest apiRequest);


    /**
     * 查询购物车数据时，同步返回优惠信息
     *
     * @param apiRequest
     * @return
     */
    CartAndRedeem getCartAndRedeem(@Valid ApiRequest<CartsRequest> apiRequest);


    /**
     * 添加或删除条目，并且返回优惠信息V2, 增加桌台状态校验
     *
     * @param apiRequest
     * @return
     */
    CartAndRedeem addCartAndRedeemV2(@Valid ApiRequest<CartItemCreate> apiRequest);

    /**
     * 批量添加V2，增加桌台状态校验
     *
     * @param apiRequest
     * @return
     */
    CartAndRedeem addCartAndRedeemBatchV2(@Valid ApiRequest<CartItemCreateBatch> apiRequest);

}
