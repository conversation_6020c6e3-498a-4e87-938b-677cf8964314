package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.service.apisix.CashierService;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.user.user.dto.req.MainCashierBaseReq;
import com.wosai.smartbiz.user.user.dto.vo.MainCashierCheckOnlineVO;
import com.wosai.smartbiz.user.user.services.MainCashierRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

import static com.wosai.pantheon.uf4c.util.TraceContextUtils.fake;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class CashierServiceImpl implements CashierService {
    @Autowired
    private MainCashierRegisterService mainCashierRegisterService;


    @Override
    public boolean isOnline(ApiRequest apiRequest){

        Map queryParam = apiRequest.getQuery();

        String merchantId = MapUtils.getString(queryParam, "merchant_id");
        String storeId = MapUtils.getString(queryParam, "store_id");

        if (StringUtils.isBlank(storeId)){
            throw new ParamException("门店信息不能为空");
        }
        if (StringUtils.isBlank(merchantId)){
            throw new ParamException("商户信息不能为空");
        }


        //检查主收银是否在线
        MainCashierBaseReq checkReq = new MainCashierBaseReq();
        checkReq.setMerchantId(merchantId);
        checkReq.setStoreId(storeId);
        MainCashierCheckOnlineVO checkOnlineVO;
        checkOnlineVO = mainCashierRegisterService.checkMainCashierOnline(checkReq);
        return checkOnlineVO.getOnline();
//        return false;

    }
}
