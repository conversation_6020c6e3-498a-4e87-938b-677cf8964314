package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.MerchantInfo;
import com.wosai.pantheon.uf4c.model.dto.MultiConfigRequest;
import com.wosai.pantheon.uf4c.service.apisix.MerchantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * created by shij on 2019/4/1
 */
@RestController
@RequestMapping(path = "/api/v1/merchants", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class MerchantController {


    @Autowired
    private MerchantService merchantService;


    @RequestMapping("/info")
    @ResponseBody
    public MerchantInfo getMerchantInfo(@RequestParam Map queryParam) {
        return merchantService.getMerchantInfo(ApiRequest.buildGetRequest(queryParam));

    }

    @PostMapping("/multiConfigs")
    @ResponseBody
    public List<ConfigResponse> getMerchantConfig(@RequestBody MultiConfigRequest request) {
        return merchantService.getMerchantConfig(new ApiRequest<>(request));
    }

    @GetMapping("/isCollection")
    public Boolean isCollection(@RequestParam Map queryParam) {
        return merchantService.isCollection(ApiRequest.buildGetRequest(queryParam));
    }
}
