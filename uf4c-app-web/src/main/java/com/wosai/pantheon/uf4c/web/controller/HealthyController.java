package com.wosai.pantheon.uf4c.web.controller;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/11/22.
 * <p>
 * 注意: 不要、不要、千万不要删除，这个是作为接入bingo的必要条件
 */
@RestController
@RequestMapping("")
public class HealthyController {

    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public
    @ResponseBody
    String healthy() {
        return "success"; //可以自定义逻辑来验证系统已经启动成功
    }

    private static Config config = ConfigService.getAppConfig();

    @GetMapping("/apollo/test")
    String apolloTest() {
        return config.getProperty("apollo.test", "");
    }

}