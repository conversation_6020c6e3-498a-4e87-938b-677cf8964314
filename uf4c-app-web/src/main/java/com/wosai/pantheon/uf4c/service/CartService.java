package com.wosai.pantheon.uf4c.service;

import com.google.common.collect.Lists;
import com.wosai.market.enums.ProductTypeEnum;
import com.wosai.pantheon.core.uitem.model.*;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.MaterialAddSourceEnum;
import com.wosai.pantheon.order.enums.SpuType;
import com.wosai.pantheon.uf4c.apisix.UserContext;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.Cart.Record;
import com.wosai.pantheon.uf4c.model.CartAndRedeem;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.CartItemCreate.Attribute;
import com.wosai.pantheon.uf4c.model.CartItemCreate.Item;
import com.wosai.pantheon.uf4c.model.CartItemCreate.Material;
import com.wosai.pantheon.uf4c.model.CartItemCreate.Spec;
import com.wosai.pantheon.uf4c.model.dto.RetailExtendItemDTO;
import com.wosai.pantheon.uf4c.model.vo.RecommendMaterial;
import com.wosai.pantheon.uf4c.service.apisix.TableService;
import com.wosai.pantheon.uf4c.util.EntityConvert;
import com.wosai.pantheon.uf4c.util.HttpRequestUtil;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.NtagHelper;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.enums.DealTypeEnum;
import com.wosai.smartbiz.oms.api.enums.ErrorTipWayEnum;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.pojo.CartInfoDTO;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartGoodsDTO;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartItemDTO;
import com.wosai.smartbiz.oms.api.query.CartSyncQuery;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * created by shij on 2019/7/26
 */
@Slf4j
@Service
public class CartService {
    private static final String CART_REDIS_KEY_PRE = "com.uf4c.cart:";

    private static final String CART_REDIS_PEOPLE_KEY_PRE = "com.uf4c.cart:people:";
    private static final long expireTime = 6L;
    private final RedisTemplate<String, Object> redisTemplate;
    private final HashOperations<String, String, CartItemCreate> hashOperations;
    @Autowired
    ItemService itemService;

    @Autowired
    com.wosai.smartbiz.oms.api.services.CartService roundMealCartService;


    @Autowired
    ItemHelper itemHelper;

    @Autowired
    CartHelper cartHelper;

    @Autowired
    MaterialHelper materialHelper;
    @Autowired
    private BrandActivityHelper brandActivityHelper;
    @Autowired
    private HttpServletRequest httpServletRequest;
    @Autowired
    private TranslationConvertService translationConvertService;

    @Value("${senable.ntag.upload:false}")
    private boolean enableNtagUpload;

    @Autowired
    private TableService tableService;

    public CartService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.hashOperations = redisTemplate.opsForHash();
    }


    public Cart addCart(CartItemCreate create, OrderMealTypeEnum mealType, DealTypeEnum dealType) {


        if (CollectionUtil.isNotEmpty(create.getMaterials())) {
            //这里如果数量为空，后面会有问题的。但是现在因为小程序问题，无法直接报错， 暂时兼容处理下。
            List<Material> newMaterials = create.getMaterials().stream().filter(m -> m.getNumber() != null).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(newMaterials)) {
                create.setMaterials(newMaterials);
            } else {
                create.setMaterials(null);
            }
        }



        if (mealType == OrderMealTypeEnum.SINGLE) {
            String cartKey = generateKey(create.getStoreId(), create.getServiceType());
            String itemUid;
            // 若有品牌活动信息，处理相关数据
            // 此处位置不要随便变更，因为处理完的数据需要用来计算itemUid
            if (StringUtil.isBlank(create.getItemUid()) && Objects.nonNull(create.getBrandActProduct()) && !create.isFromBrandAuto()) {
                brandActivityHelper.processBrandCartItem(create);
            }
            if (create.getItemUid() != null) {
                itemUid = create.getItemUid();
            } else {
                itemUid = cartHelper.generateItemUid(create);
            }


            CartItemCreate existingCreate;
            if (hashOperations.hasKey(cartKey, itemUid)) {
                // 已存在购物车进行修改
                existingCreate = hashOperations.get(cartKey, itemUid);
                // 当请求中的版本号低于redis中数据的版本号时，不进行更新操作
                if (create.isCover() && create.getClientVersion() > 0) {
                    if (create.getClientVersion() < existingCreate.getClientVersion()) {
                        return getCartByUid(cartKey);
                    }
                }
                existingCreate.setCover(create.isCover());
                existingCreate.setClientVersion(create.getClientVersion());
                if (dealType == DealTypeEnum.ADD) {
                    Integer num = existingCreate.getItem().getNumber();
                    if (num < 0) {
                        throw new BusinessException(ReturnCode.BUSINESS_ERROR, "参数错误，商品数量不能小于0");
                    }
                    if (create.isCover()) {
                        if (create.getItem().getNumber() > 0) {
                            existingCreate.getItem().setNumber(create.getItem().getNumber());
                        }

                    } else {
                        if (create.getItem() != null && create.getItem().getNumber() != null && create.getItem().getNumber() > 1) {
                            existingCreate.getItem().setNumber(num + create.getItem().getNumber());
                        } else {
                            existingCreate.getItem().setNumber(++num);
                        }
                    }
                } else {
                    Integer num = create.getItem() != null ? create.getItem().getNumber() : create.getNumber();
                    if (num <= 0) {
                        removeItem(existingCreate, mealType);
                        return getCartByUid(cartKey);
                    }
                    existingCreate.getItem().setNumber(num);
                }

                if (existingCreate.getItem().getMinSaleNum() != null) {
                    if (existingCreate.getItem().getNumber() < existingCreate.getItem().getMinSaleNum()) {
                        existingCreate.getItem().setNumber(existingCreate.getItem().getMinSaleNum());
                    }
                }


                if (!StringUtils.isEmpty(create.getCategoryId())) {
                    existingCreate.getItem().setCategoryId(create.getCategoryId());
                }


            } else {
                Integer num = create.getItem() != null ? create.getItem().getNumber() : create.getNumber();
                if (num <= 0) {
                    return getCartByUid(cartKey);
                }
                if (!org.apache.commons.lang3.StringUtils.equalsIgnoreCase(Constants.BRAND_PRODUCT_SOURCE_BRAND, create.getBrandProductSource())) {
                    // 商品来源不是品牌就需要补充信息
                    // 不存在，创建
                    create = checkAndFillItemInfo(create);
                }

                EntityConvert.generateAttachInfo(create);
                create.setItemUid(itemUid);
                if (create.getCtime() == null) {
                    create.setCtime(System.currentTimeMillis());
                }
                if (create.getItem().getMinSaleNum() != null) {
                    if (create.getItem().getNumber() == null || create.getItem().getNumber() < create.getItem().getMinSaleNum()) {
                        create.getItem().setNumber(create.getItem().getMinSaleNum());
                    }
                }

                existingCreate = create;

            }

            getAndSaveNtagInfo(existingCreate);

            // 本地购物车去掉购物车库存校验
            if (!existingCreate.isCover() && existingCreate.getItem().getSku() != null) {
                if (existingCreate.getItem().getNumber() > existingCreate.getItem().getSku()) {
                    int existingNum = existingCreate.getItem().getSku() >= 0 ? existingCreate.getItem().getSku() : 0;
                    throw new BusinessException(ReturnCode.BUSINESS_ERROR, "仅剩余" + existingNum + "份,无法继续加购");
                }
            }
            if (create.isCover() && create.getItem().getNumber() == 0) {
                hashOperations.delete(cartKey, itemUid);
            } else {
                upsertItem(itemUid, existingCreate, OrderMealTypeEnum.SINGLE);
            }

            return getCartByUid(cartKey);
        } else {

            if (StringUtil.isBlank(create.getTableId()) || "null".equalsIgnoreCase(create.getTableId())) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "门店二维码不支持先下单后付款场景，请联系服务员");
            }

            getAndSaveNtagInfo(create);

            CartSyncQuery cartSyncQuery = new CartSyncQuery();
            cartSyncQuery.setTableId(create.getTableId());
            cartSyncQuery.setDealType(dealType);

            cartSyncQuery.setUserId(ThreadLocalHelper.getUserId());
            cartSyncQuery.setUserIcon(create.getUserIcon());
            cartSyncQuery.setUserName(create.getUserName());
            if (StringUtils.isEmpty(create.getId()) && StringUtils.isEmpty(create.getItemUid())) {
                create = checkAndFillItemInfo(create);
            }
            cartSyncQuery.setGoods(com.wosai.pantheon.uf4c.util.CartHelper.convert2RoundGoods(create));
            Result<CartInfoDTO> result = roundMealCartService.addOrReduceCartItem(cartSyncQuery);
            if (!result.isSuccess()) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
            }
            // 翻译购物车返回数据
            String acceptLanguage = HttpRequestUtil.getAcceptLanguage(httpServletRequest);
            translationConvertService.convertRoundCartItems(result.getData(), create.getStoreId(), acceptLanguage);
            Cart cart =  com.wosai.pantheon.uf4c.util.CartHelper.convertCart(result.getData());
            if(!tableService.canContinueOrder(create.getTableId())){
                cart.setCheckResult(Result.StatusInfo.error(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getCode(), ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getMessage()));
            }
            return cart;
        }
    }


    private void getAndSaveNtagInfo(CartItemCreate existingCreate) {
        Map ntagInfo = NtagHelper.getNtagInfo();
        if(Objects.nonNull(ntagInfo) && enableNtagUpload){
            Map extraInfo = existingCreate.getExtraInfo();
            if (extraInfo  == null){
                extraInfo = new HashMap<>();
            }
            extraInfo.put("alipayNOrderInfo", ntagInfo);
            existingCreate.setExtraInfo(extraInfo);
        }
    }


    public CartItemCreate checkAndFillItemInfo(CartItemCreate cartItemCreate) {
        // 不存在，创建
        ItemDto itemDto = itemHelper.getItemDtoById(cartItemCreate.getStoreId(), cartItemCreate.getItem().getId(), cartItemCreate.getServiceType());
        if (itemDto == null) {
            throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
        }
        // 本地购物车不校验售卖时段
        if (!cartItemCreate.isCover() && !ItemHelper.curTimeForSale(itemDto)) {
            //当前时段不可售
            throw new BusinessException(ReturnCode.ITEM_NOT_SALE_IN_SECTION);
        }
        cartItemCreate.getItem().setSku(itemDto.getItem().getSku());
        cartItemCreate.getItem().setOutOfStock(itemDto.getItem().getOutOfStock());
        cartItemCreate.getItem().setCategoryId(itemDto.getItem().getCategoryId());
        cartItemCreate.getItem().setIsMultiple(itemDto.getItem().getSpecNum());
        cartItemCreate.setCategoryId(itemDto.getItem().getCategoryId());
        cartItemCreate.getItem().setUnit(itemDto.getItem().getUnit());
        cartItemCreate.getItem().setMinSaleNum(itemDto.getItem().getMinSaleNum());
        cartItemCreate.getItem().setCategorySort(itemDto.getItem().getCategorySort());
        cartItemCreate.getItem().setDisplayOrder(itemDto.getItem().getDisplayOrder());

        // 翻译需求改造
        convertItemNamesToOriginalNames(cartItemCreate, itemDto);
        if (itemDto instanceof RetailExtendItemDTO) {
            RetailExtendItemDTO dto = (RetailExtendItemDTO) itemDto;
            if (MapUtils.isNotEmpty(dto.getSkuConvert())) {
                cartItemCreate.setSkuConvert(dto.getSkuConvert());
            }
        }
        if (Objects.equals(cartItemCreate.getItem().getSpuType(), SpuType.PACKAGE.name())) {
            //必选分组内的商品价格必须都为0
            if (cartItemCreate.getPackageItems() != null) {
                cartItemCreate.getPackageItems().stream()
                        .filter(packageItem -> StringUtils.isEmpty(packageItem.getPackageGroupId()))
                        .forEach(packageItem -> packageItem.getItem().setPrice(0));
            }
            cartItemCreate.getItem().setPrice(cartItemCreate.getItem().getPrice() + ItemHelper.getPackageGoodsTotalPrice(cartItemCreate));
        }
        return cartItemCreate;
    }

    private void convertItemNamesToOriginalNames(CartItemCreate cartItemCreate, ItemDto itemDto) {
        try {
            // 由于翻译需要处理的字段
            String spuType = cartItemCreate.getItem().getSpuType();
            List<CartItemCreate> allCartItems = new ArrayList<>();
            allCartItems.add(cartItemCreate);
            if (ProductTypeEnum.PACKAGE.name().equals(spuType)) {
                // 套餐商品各字段购物车存储需要使用原始名称
                List<CartItemCreate> packageItems = cartItemCreate.getPackageItems();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(packageItems)) {
                    allCartItems.addAll(packageItems);
                }
            }

            List<ItemDto> itemDtoList = flatItemDto(itemDto);
            Map<String, com.wosai.pantheon.core.uitem.model.Item> originalItemMap = getItemMap(itemDtoList);
            Map<String, ItemSpec> originalSpecMap = getSpecMap(itemDtoList);
            Map<String, com.wosai.pantheon.core.uitem.model.Material> originalMaterialMap = getMaterialMap(itemDtoList);
            Map<String, Map<String, String>> origunalAttributeMap = getAttributeMap(itemDtoList);

            convertItemNamesToOriginalNames(allCartItems, originalItemMap);
            convertSpecNamesToOriginalNames(allCartItems, originalSpecMap);
            convertMaterialNamesToOriginalNames(allCartItems, originalMaterialMap);
            convertAttributeNamesToOriginalNames(allCartItems, origunalAttributeMap);
        } catch (Exception e) {
            LogUtils.logWarnWithError("convertItemNamesToOriginalNames", "convertItemNamesToOriginalNames", cartItemCreate, e);
        }
    }

    private List<ItemDto> flatItemDto(ItemDto itemDto) {
        List<ItemDto> itemDtoS = new ArrayList<>();
        itemDtoS.add(itemDto);
        if (!Objects.equals(ProductTypeEnum.PACKAGE.name(), itemDto.getItem().getItemType())) {
            return itemDtoS;
        }
        List<ItemDto> packageMustOrderProducts = itemDto.getPackageMustOrderProducts();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(packageMustOrderProducts)) {
            itemDtoS.addAll(packageMustOrderProducts);
        }
        List<PackageOptionalGroup> packageOptionalGroups = itemDto.getPackageOptionalGroups();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(packageOptionalGroups)) {
            packageOptionalGroups.forEach(group -> {
                List<ItemDto> products = group.getProducts();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(products)) {
                    itemDtoS.addAll(products);
                }
            });
        }
        return itemDtoS;
    }

    private Map<String, com.wosai.pantheon.core.uitem.model.Item> getItemMap(List<ItemDto> itemDtoList) {
        return itemDtoList.stream()
                .map(ItemDto::getItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(com.wosai.pantheon.core.uitem.model.Item::getId, it -> it, (v1, v2) -> v1));
    }

    private Map<String, ItemSpec> getSpecMap(List<ItemDto> itemDtoList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemDtoList)) {
            return new HashMap<>();
        }
        return itemDtoList.stream()
                .filter(it -> org.apache.commons.collections4.CollectionUtils.isNotEmpty(it.getSpecs()))
                .flatMap(it -> it.getSpecs().stream())
                .collect(Collectors.toMap(ItemSpec::getId, it -> it, (v1, v2) -> v1));
    }

    private Map<String, com.wosai.pantheon.core.uitem.model.Material> getMaterialMap(List<ItemDto> itemDtoList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemDtoList)) {
            return new HashMap<>();
        }
        return itemDtoList.stream()
                .filter(it -> org.apache.commons.collections4.CollectionUtils.isNotEmpty(it.getMaterials()))
                .flatMap(it -> it.getMaterials().stream())
                .collect(Collectors.toMap(com.wosai.pantheon.core.uitem.model.Material::getId, it -> it, (v1, v2) -> v1));
    }

    private Map<String, Map<String, String>> getAttributeMap(List<ItemDto> itemDtoList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemDtoList)) {
            return new HashMap<>();
        }
        List<AttributeDto> attributeDtoS = itemDtoList.stream()
                .filter(it -> org.apache.commons.collections4.CollectionUtils.isNotEmpty(it.getAttributes()))
                .flatMap(it -> it.getAttributes().stream())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attributeDtoS)) {
            return new HashMap<>();
        }

        Map<String, Map<String, String>> result = new HashMap<>();
        attributeDtoS.forEach(it -> {
            List<AttributeOptionDto> options = it.getOptions();
            String title = it.getTitle();
            if (CollectionUtils.isEmpty(options)) {
                return;
            }
            options.forEach(option -> {
                Map<String, String> map = new HashMap<>();
                map.put("title", title);
                map.put("name", option.getName());
                result.put(option.getId(), map);
            });
        });
        return result;
    }

    private void convertItemNamesToOriginalNames(List<CartItemCreate> cartItemCreates, Map<String, com.wosai.pantheon.core.uitem.model.Item> originalItemMap) {
        cartItemCreates.forEach(it -> {
            Item item = it.getItem();
            if (item == null) {
                return;
            }
            String id = item.getId();
            com.wosai.pantheon.core.uitem.model.Item origunalItem = originalItemMap.get(id);
            if (origunalItem == null) {
                return;
            }
            it.getItem().setName(origunalItem.getName());
        });
    }
    private void convertSpecNamesToOriginalNames(List<CartItemCreate> cartItemCreates, Map<String, ItemSpec> originalSpecMap) {
        cartItemCreates.forEach(it -> {
            Spec spec = it.getSpec();
            if (spec == null) {
                return;
            }
            String id = spec.getId();
            ItemSpec originalSpec = originalSpecMap.get(id);
            if (originalSpec == null) {
                return;
            }
            it.getSpec().setName(originalSpec.getName());
        });
    }

    private void convertMaterialNamesToOriginalNames(List<CartItemCreate> cartItemCreates, Map<String, com.wosai.pantheon.core.uitem.model.Material> originalMaterialMap) {
        cartItemCreates.forEach(it -> {
            List<Material> materials = it.getMaterials();
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(materials)) {
                return;
            }
            materials.forEach(material -> {
                String id = material.getId();
                com.wosai.pantheon.core.uitem.model.Material originalMaterial = originalMaterialMap.get(id);
                if (originalMaterial == null) {
                    return;
                }
                material.setName(originalMaterial.getName());
            });
        });
    }

    private void convertAttributeNamesToOriginalNames(List<CartItemCreate> cartItemCreates, Map<String, Map<String, String>> originalAttributeMap) {
        cartItemCreates.forEach(it -> {
            List<Attribute> attributes = it.getAttributes();
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(attributes)) {
                return;
            }
            attributes.forEach(attribute -> {
                String id = attribute.getId();
                Map<String, String> attributeMap = originalAttributeMap.get(id);
                if (MapUtils.isEmpty(attributeMap)) {
                    return;
                }
                attribute.setName(attributeMap.get("name"));
                attribute.setTitle(attributeMap.get("title"));
            });
        });
    }




    public Cart reduceRoundMealCart(CartItemCreate request) {
        CartSyncQuery cartSyncQuery = new CartSyncQuery();

        if(!tableService.canContinueOrder(request.getTableId())){
            Cart cart = new Cart();
            cart.setCheckResult(Result.StatusInfo.error(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getCode(), ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getMessage()));
            return cart;
        }

        cartSyncQuery.setTableId(request.getTableId());
        cartSyncQuery.setDealType(DealTypeEnum.REDUCE);

        cartSyncQuery.setUserId(ThreadLocalHelper.getUserId());
        cartSyncQuery.setUserIcon(request.getUserIcon());
        cartSyncQuery.setUserName(request.getUserName());

        cartSyncQuery.setGoods(com.wosai.pantheon.uf4c.util.CartHelper.convert2RoundGoods(request));

        Result<CartInfoDTO> result = roundMealCartService.addOrReduceCartItem(cartSyncQuery);
        if (!result.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
        }
        return com.wosai.pantheon.uf4c.util.CartHelper.convertCart(result.getData());
    }

    /**
     * 购物车条目数量减少
     *
     * @param itemUid
     * @param storeId
     * @param serviceType
     * @return
     */
    public Cart reduceCartItemCount(String itemUid, String storeId, Integer serviceType) {
        return reduceCartItemCount(itemUid, storeId, serviceType, 1);
    }

    public Cart reduceCartItemCount(String itemUid, String storeId, Integer serviceType, int reduceNum) {
        storeId = storeId.replaceAll("(.*)(\\?$)", "$1");

        String cartKey = generateKey(storeId, serviceType);

        // count>1减少1个数量， count<1直接删除
        CartItemCreate existingCreate = hashOperations.get(cartKey, itemUid);
        if (existingCreate != null) {

            if (existingCreate.isOpenTableMustOrder() && !existingCreate.isOpenTableItemEditable()) {
                //开台必点，并且不可编辑
                return getCartByUid(cartKey);
            }

            Integer num = existingCreate.getItem().getNumber();
            if (num > reduceNum) {
                existingCreate.getItem().setNumber(num - reduceNum);
                if (existingCreate.getItem().getMinSaleNum() != null && existingCreate.getItem().getNumber() < existingCreate.getItem().getMinSaleNum()) {
                    //小于最低加购数量了，删除这个商品
                    hashOperations.delete(cartKey, itemUid);
                    redisTemplate.expire(cartKey, expireTime, TimeUnit.HOURS);
                } else {
                    if (existingCreate.getQuotaCount() != null && existingCreate.getItem().getNumber() <= existingCreate.getQuotaCount()) {
                        EntityConvert.generateAttachInfo(existingCreate);
                    }
                    hashOperations.put(cartKey, itemUid, existingCreate);
                }
            } else {
                hashOperations.delete(cartKey, itemUid);
            }
            redisTemplate.expire(cartKey, expireTime, TimeUnit.HOURS);
            increaseCartVersion(cartKey);
        }
        return getCartByUid(cartKey);
    }

    private void removeItem(CartItemCreate cartItem, OrderMealTypeEnum mealTypeEnum, boolean cleanMustOrder) {
        if (mealTypeEnum == OrderMealTypeEnum.ROUND_MEAL) {
            CartSyncQuery cartSyncQuery = new CartSyncQuery();
            cartSyncQuery.setItemUid(cartItem.getItemUid());
            cartSyncQuery.setTableId(cartItem.getTableId());
            cartSyncQuery.setCleanMustOrder(cleanMustOrder);
            roundMealCartService.deleteItem(cartSyncQuery);
        } else {
            String cartKey = generateKey(cartItem.getStoreId(), cartItem.getServiceType());
            CartItemCreate existingCreate = hashOperations.get(cartKey, cartItem.getItemUid());
            if (!cleanMustOrder) {
                if (existingCreate != null && existingCreate.isOpenTableMustOrder() && !existingCreate.isOpenTableItemEditable()) {
                    //开台必点，并且不可编辑
                    return;
                }
            }
            hashOperations.delete(cartKey, cartItem.getItemUid());
            redisTemplate.expire(cartKey, expireTime, TimeUnit.HOURS);
            increaseCartVersion(cartKey);
        }
    }

    private void removeItem(CartItemCreate cartItem, OrderMealTypeEnum mealTypeEnum) {

        removeItem(cartItem, mealTypeEnum, false);
    }

    public void removeItem(String storeId, String tableId, String itemUid, Integer serviceType, OrderMealTypeEnum mealTypeEnum) {
        if (mealTypeEnum == OrderMealTypeEnum.ROUND_MEAL) {
            CartSyncQuery cartSyncQuery = new CartSyncQuery();
            cartSyncQuery.setItemUid(itemUid);
            cartSyncQuery.setTableId(tableId);
            cartSyncQuery.setCleanMustOrder(false);
            roundMealCartService.deleteItem(cartSyncQuery);
        } else {
            String cartKey = generateKey(storeId, serviceType);
            CartItemCreate existingCreate = hashOperations.get(cartKey, itemUid);
            if (existingCreate != null && existingCreate.isOpenTableMustOrder() && !existingCreate.isOpenTableItemEditable()) {
                //开台必点，并且不可编辑
                return;
            }
            hashOperations.delete(cartKey, itemUid);
            redisTemplate.expire(cartKey, expireTime, TimeUnit.HOURS);
            increaseCartVersion(cartKey);
        }
    }

    private void upsertItem(String itemUid, CartItemCreate item, OrderMealTypeEnum orderMealTypeEnum) {
        upsertItem(itemUid, item, orderMealTypeEnum, false);
    }

    private void upsertItem(String itemUid, CartItemCreate item, OrderMealTypeEnum orderMealTypeEnum, boolean editMustorderItem) {
        if (orderMealTypeEnum == OrderMealTypeEnum.ROUND_MEAL) {

            CartSyncQuery cartSyncQuery = new CartSyncQuery();
            cartSyncQuery.setTableId(item.getTableId());
            cartSyncQuery.setItemUid(item.getItemUid());
            cartSyncQuery.setCleanMustOrder(editMustorderItem);
            cartSyncQuery.setGoods(com.wosai.pantheon.uf4c.util.CartHelper.convert2RoundGoods(item));
            roundMealCartService.updateAttributesAndMaterials(cartSyncQuery);
        } else {
            String cartKey = generateKey(item.getStoreId(), item.getServiceType());
            CartItemCreate existingCreate = hashOperations.get(cartKey, itemUid);
            if (!editMustorderItem) {
                if (existingCreate != null && existingCreate.isOpenTableMustOrder() && !existingCreate.isOpenTableItemEditable()) {
                    //开台必点，并且不可编辑
                    return;
                }
            }
            hashOperations.put(cartKey, itemUid, item);
            increaseCartVersion(cartKey);
            redisTemplate.expire(cartKey, expireTime, TimeUnit.HOURS);
        }
    }

    public Cart getRoundCart(String storeId, String tableId, boolean fillRecommendMaterial) {
        if (StringUtil.isBlank(tableId) || "null".equalsIgnoreCase(tableId)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "门店二维码不支持先下单后付款场景，请联系服务员");
        }
        CartSyncQuery query = new CartSyncQuery();
        query.setTableId(tableId);
        query.setVersion(0L);
        Result<CartInfoDTO> oldCartResult = roundMealCartService.checkAndGetCartInfo(query);
        if (!oldCartResult.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, oldCartResult.getErrorMsg());
        }
        CartInfoDTO oldCart = oldCartResult.getData();
        try {

            if (fillRecommendMaterial && org.apache.commons.lang3.StringUtils.isNotBlank(storeId)) {
                List<ShoppingCartItemDTO> cartItems = oldCart.getShoppingCartItems();
                if (!CollectionUtils.isEmpty(cartItems)) {
                    Set<String> itemIds = cartItems.stream()
                            .map(ShoppingCartItemDTO::getShoppingCartGoods)
                            .map(ShoppingCartGoodsDTO::getSpuId)
                            .collect(Collectors.toSet());
                    Map<String, List<RecommendMaterial>> itemIdToRmsMap = materialHelper.recommendMap(storeId, new ArrayList<>(itemIds));

                    CartSyncQuery cartSyncQuery = new CartSyncQuery();
                    cartSyncQuery.setTableId(tableId);
                    cartSyncQuery.setBatchGoods(Lists.newArrayList());

                    for (ShoppingCartItemDTO cartItem : cartItems) {
                        ShoppingCartGoodsDTO goods = cartItem.getShoppingCartGoods();

                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(goods.getMaterials())
                                || Optional.ofNullable(goods.getSaleCount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
                            goods.setRecommendMaterials(null);
                        } else {
                            List<RecommendMaterial> tmpRms = MapUtils.getObject(itemIdToRmsMap, goods.getSpuId(), null);
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tmpRms)) {

                                List<ShoppingCartGoodsDTO.RecommendMaterial> roundRms = tmpRms.stream().map(m -> {
                                    ShoppingCartGoodsDTO.RecommendMaterial tmp = new ShoppingCartGoodsDTO.RecommendMaterial();
                                    tmp.setId(m.getId());
                                    tmp.setName(m.getName());
                                    tmp.setPrice(m.getPrice());
                                    tmp.setNumber(0);
                                    tmp.setSource(MaterialAddSourceEnum.ORDER_SUBMIT_RECOMMEND.getCode());
                                    tmp.setSelected(false);
                                    return tmp;
                                }).limit(3).collect(Collectors.toList());

                                goods.setRecommendMaterials(roundRms);
                            } else {
                                goods.setRecommendMaterials(null);
                            }
                        }
                        if (goods.getRecommendMaterials() != null) {
                            cartSyncQuery.getBatchGoods().add(goods);
                        }
                    }
                    if (!CollectionUtils.isEmpty(cartSyncQuery.getBatchGoods())) {
                        roundMealCartService.batchUpdateRecommendMaterials(cartSyncQuery);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("fill recommend material error", e);
        }
        String acceptLanguage = HttpRequestUtil.getAcceptLanguage(httpServletRequest);
        translationConvertService.convertRoundCartItems(oldCart, storeId, acceptLanguage);
        return com.wosai.pantheon.uf4c.util.CartHelper.convertCart(oldCart);
    }


    public Cart getCart(String storeId, Integer serviceType, String tableId, OrderMealTypeEnum mealType, boolean fillRecommendMaterial) {
        Cart cart = null;
        switch (mealType) {
            case SINGLE:
                String cartId = this.generateKey(storeId, serviceType);
                cart = this.getSingleCart(storeId, cartId, fillRecommendMaterial);
                break;
            case ROUND_MEAL:
                cart = this.getRoundCart(storeId, tableId, fillRecommendMaterial);
                break;
            default:
                break;
        }
        return cart;
    }

    public Cart getCart(String storeId, Integer serviceType, String tableId, OrderMealTypeEnum mealType) {
        return this.getCart(storeId, serviceType, tableId, mealType, false);
    }


    public void resetCart(String storeId, Integer serviceType, boolean cleanMustOrderItem) {
        resetCart(storeId, serviceType, cleanMustOrderItem, true);
    }

    public void resetCart(String storeId, Integer serviceType, boolean cleanMustOrderItem, boolean cleanTablePeopleNum) {
        if (cleanMustOrderItem) {
            String cartKey = generateKey(storeId, serviceType);
            redisTemplate.delete(cartKey);
            if (cleanTablePeopleNum) {
                redisTemplate.delete(generateCartPeopleNumKey(storeId, serviceType));
            }
            increaseCartVersion(cartKey);
        } else {
            String cartKey = generateKey(storeId, serviceType);
            Map<String, CartItemCreate> entries = hashOperations.entries(cartKey);

            if (CollectionUtils.isEmpty(entries)) {
                return;
            }
            entries.forEach((k, v) -> {
                if (!v.isOpenTableMustOrder()) {
                    //不是开台必点，那么删除掉
                    hashOperations.delete(cartKey, k);
                }
                if (v.isOpenTableMustOrder() && v.isOpenTableItemEditable()) {
                    //开台必点，但是可以编辑
                    hashOperations.delete(cartKey, k);
                }
            });
            increaseCartVersion(cartKey);
        }

    }


    public CartCheckResultDTO checkItemStatus(String tableId, String storeId, Integer serviceType, OrderMealTypeEnum orderMealTypeEnum) {
        CartCheckResultDTO cartCheckResult = new CartCheckResultDTO();
        List<CartCheckResultDTO.Item> invalid = new ArrayList<>();
        List<CartCheckResultDTO.Item> price = new ArrayList<>();
        List<CartCheckResultDTO.Item> checkFailList = new ArrayList<>();


        Map<String, CartItemCreate> entries = getCartItemsMap(storeId, tableId, serviceType, orderMealTypeEnum);

        Map<String, Integer> spuCountMap = new HashMap<>();

        if (entries == null) {
            if (orderMealTypeEnum == OrderMealTypeEnum.ROUND_MEAL) {
                throw new BusinessException(ReturnCode.ORDER_CONCURRENT);
            } else {
                throw new BusinessException(ReturnCode.CART_IS_EMPTY);
            }
        }

        List<CartItemCreate> records = entries.values().stream().collect(Collectors.toList());

        //排个序， 商品要按照顺序删除（开台必点在最前面，最先加购的在前面）
        Collections.sort(records, (o1, o2) -> {
            if (o1.isOpenTableMustOrder() && !o2.isOpenTableMustOrder()) {
                return -1;
            }
            if (o2.isOpenTableMustOrder() && !o1.isOpenTableMustOrder()) {
                return 1;
            }
            return o1.getCtime().compareTo(o2.getCtime());
        });

        records.forEach((v) -> {
            CartCheckResultDTO.Item item = validateItem(v, storeId, serviceType, orderMealTypeEnum, spuCountMap);
            if (item != null) {
                if (item.getChangeType().equals(Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID)) {
                    invalid.add(item);
                } else if (item.getChangeType().equals(Constants.CartCheckItemChangeType.CHANGE_TYPE_PRICE)) {
                    price.add(item);
                }
                checkFailList.add(item);
            } else {
                com.wosai.pantheon.uf4c.util.CartHelper.addSpuCount(spuCountMap, v.getItem().getId(), v.getItem().getNumber());
            }
        });
        cartCheckResult.setSuccess(CollectionUtil.isEmpty(checkFailList));
        cartCheckResult.setErrorTipWay(ErrorTipWayEnum.POPUP);
        cartCheckResult.setInvalid(invalid);
        cartCheckResult.setPrice(price);
        cartCheckResult.setCheckFailList(checkFailList);
        return cartCheckResult;
    }


    public List<CartItemCreate> getItemCreateList(String storeId, String tableId, Integer serviceType, OrderMealTypeEnum mealType) {

        Map<String, CartItemCreate> map = getCartItemsMap(storeId, tableId, serviceType, mealType);
        if (map == null) {
            return null;
        }
        List<CartItemCreate> list = map.values().stream().sorted(Comparator.comparing(CartItemCreate::getItemUid)).collect(Collectors.toList());
        // 处理加价购购物商品合并（商品属于门店的情况下才处理）
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(it -> it.isBrandAct() && Objects.equals(it.getBrandProductSource(), Constants.BRAND_PRODUCT_SOURCE_STORE))) {
            brandActivityHelper.mergeBrandActivityProduct(list);
        }
        return list;
    }

    /**
     * 2024-03-22
     * 与 {@link  #getItemCreateList}作用相同，但是不合并加价购商品。
     * 因为前端展示加价购商品和原商品spuId虽然相同，但itemuid不同，需要做两条数据展示。这里让优惠方计算后返回给前端明确的优惠信息
     */
    public List<CartItemCreate> getItemCreateList4CartRedeem(String storeId, String tableId, Integer serviceType, OrderMealTypeEnum mealType) {

        Map<String, CartItemCreate> map = getCartItemsMap(storeId, tableId, serviceType, mealType);
        if (map == null) {
            return null;
        }
        List<CartItemCreate> list = map.values().stream().sorted(Comparator.comparing(CartItemCreate::getItemUid)).collect(Collectors.toList());

        return list;
    }

    public Map<String, CartItemCreate> getCartItemsMap(String storeId, String tableId, Integer serviceType, OrderMealTypeEnum mealType) {
        Map<String, CartItemCreate> entries = null;
        if (mealType == OrderMealTypeEnum.ROUND_MEAL) {
            CartSyncQuery cartSyncQuery = new CartSyncQuery();

            cartSyncQuery.setTableId(tableId);
            Result<CartInfoDTO> cartInfoDTOResult = roundMealCartService.checkAndGetCartInfo(cartSyncQuery);
            if (cartInfoDTOResult.isSuccess()) {
                CartInfoDTO cartInfoDTO = cartInfoDTOResult.getData();

                List<ShoppingCartItemDTO> shoppingCartItems = cartInfoDTO.getShoppingCartItems();
                if (!CollectionUtils.isEmpty(shoppingCartItems)) {
                    entries = new HashMap<>();
                    for (ShoppingCartItemDTO item : cartInfoDTO.getShoppingCartItems()) {
                        ShoppingCartGoodsDTO goods = item.getShoppingCartGoods();
                        if (goods.getSaleCount().compareTo(BigDecimal.ZERO) > 0) {
                            CartItemCreate create = convert2ItemCreate(goods, storeId, tableId);
                            entries.put(goods.getItemUid(), create);
                        }

                    }
                }
            }
        } else {
            String cartKey = generateKey(storeId, serviceType);
            entries = hashOperations.entries(cartKey);
        }
        return entries;
    }

    /**
     * 添加开台必点商品
     *
     * @param cartItemCreates
     * @return
     */
    public Cart addMustOrderItems(String storeId, Integer serviceType, List<CartItemCreate> cartItemCreates) {

        String cartKey = generateKey(storeId, serviceType);

        Map<String, CartItemCreate> entries = hashOperations.entries(cartKey);

        if (!CollectionUtils.isEmpty(entries)) {
            entries.forEach((k, v) -> {
                if (v.isOpenTableMustOrder()) {
                    hashOperations.delete(cartKey, k);
                    redisTemplate.expire(cartKey, expireTime, TimeUnit.HOURS);
                    increaseCartVersion(cartKey);
                }
            });
        }

        Long i = 1L;
        Long curTime = System.currentTimeMillis();
        for (CartItemCreate item : cartItemCreates) {
            String cartItemKey = cartHelper.generateItemUid(item);
            item.setItemUid(cartItemKey);
            item.setCtime(curTime + i);
            upsertItem(cartItemKey, item, OrderMealTypeEnum.SINGLE);
            i++;
        }
        return getCartByUid(cartKey);
    }

    /**
     * 获取轻餐购物车
     *
     * @param storeId
     * @param cartId
     * @param fillRecommendMaterial 是否补充推荐加料
     * @return
     */
    public Cart getSingleCart(String storeId, String cartId, boolean fillRecommendMaterial) {

        Map<String, CartItemCreate> entries = hashOperations.entries(cartId);
        if (Objects.isNull(entries)) {
            return null;
        }
        // 补充推荐加料
        if (fillRecommendMaterial && org.apache.commons.lang3.StringUtils.isNotBlank(storeId)) {
            // 先排序，再处理
            List<CartItemCreate> records = new ArrayList<>(entries.values());
            if (!CollectionUtil.isEmpty(records)) {
                records.sort((o1, o2) -> {
                    if (o1.isOpenTableMustOrder() && !o2.isOpenTableMustOrder()) {
                        return 1;
                    }
                    if (o2.isOpenTableMustOrder() && !o1.isOpenTableMustOrder()) {
                        return -1;
                    }
                    return o2.getCtime().compareTo(o1.getCtime());
                });

                Set<String> itemIds = records.stream()
                        .map(CartItemCreate::getItem)
                        .map(Item::getId)
                        .collect(Collectors.toSet());
                Map<String, List<RecommendMaterial>> itemIdToRmsMap = materialHelper.recommendMap(storeId, new ArrayList<>(itemIds));
                for (CartItemCreate record : records) {
                    String tmpItemId = record.getItem().getId();
                    // 有加料，不需要处理
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(record.getMaterials())) {
                        record.setRecommendMaterials(null);
                    } else if (record.isBrandAct()) {
                        // 微信加价购，不需要处理
                        record.setRecommendMaterials(null);
                    } else {
                        List<RecommendMaterial> tmpRms = MapUtils.getObject(itemIdToRmsMap, tmpItemId, null);
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tmpRms)) {
                            record.setRecommendMaterials(tmpRms.stream().map(p -> {
                                CartItemCreate.RecommendMaterial q = new CartItemCreate.RecommendMaterial();
                                q.setId(p.getId());
                                q.setName(p.getName());
                                q.setPrice(p.getPrice().longValue());
                                q.setNumber(0);
                                q.setSource(MaterialAddSourceEnum.ORDER_SUBMIT_RECOMMEND.getCode());
                                q.setSelected(false);
                                return q;
                            }).limit(3).collect(Collectors.toList()));
                        } else {
                            record.setRecommendMaterials(null);
                        }
                    }
                    entries.put(record.getItemUid(), record);
                }
                hashOperations.putAll(cartId, entries);
                increaseCartVersion(cartId);
            }
        }

        // 翻译购物车数据
        try {
            String acceptLanguage = acceptLanguage();
            ArrayList<CartItemCreate> cartItemCreates = new ArrayList<>(entries.values());
            if (org.apache.commons.lang3.StringUtils.isBlank(storeId) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(cartItemCreates)) {
                storeId = cartItemCreates.get(0).getStoreId();
            }
            translationConvertService.convertCartItems(cartItemCreates, storeId, acceptLanguage);
        } catch (Exception e) {
            LogUtils.logWarnWithError("翻译购物车数据异常", "getSingleCart", storeId, e);
        }

        return this.singleCartToCart(cartId, entries);
    }

    private String acceptLanguage() {
        String acceptLanguage = HttpRequestUtil.getAcceptLanguage(httpServletRequest);
        if (Objects.isNull(acceptLanguage)) {
            try {
                UserContext userContext = (UserContext) ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO();
                acceptLanguage = userContext.getXSmartAcceptLanguage();
            } catch (Exception e) {
                return null;
            }
        }
        return acceptLanguage;
    }

    public Cart singleCartToCart(String uid, Map<String, CartItemCreate> entries) {
        Cart cart = new Cart();
        List<Record> records = new ArrayList<>();


        cart.setPeopleNum(getCartPeopleNum(uid));

        cart.setVersion(getCartVersion(uid));


        Map<String, Integer> spuCountMap = new HashMap<>();


        entries.forEach((k, v) -> {
            Item item = v.getItem();
            Integer num = item.getNumber();

            Record record = new Record();
            record.setId(k);
            record.setItemId(item.getId());
            record.setSpecId(Optional.ofNullable(v.getSpec()).map(spec -> spec.getId()).orElse(null));
            record.setName(item.getName());

            record.setPrice(item.getPrice().longValue() * num);

            record.setNum(num);
            record.setAttachedInfo(item.getAttachedInfo());
            record.setStatus(item.getStatus());
            record.setCtime(v.getCtime());
            record.setCategoryId(item.getCategoryId());
            record.setUrl(Optional.ofNullable(item.getUrl()).orElse(item.getPhotoUrl()));
            record.setSku(item.getSku());
            record.setOutOfStock(item.getOutOfStock());

            record.setMinSaleNum(item.getMinSaleNum());

            record.setSpuType(item.getSpuType());

            record.setOpenTableMustOrder(v.isOpenTableMustOrder());
            record.setOpenTableMustOrderEditable(v.isOpenTableItemEditable());

            try {
                com.wosai.pantheon.uf4c.util.CartHelper.addSpuCount(spuCountMap, item.getId(), num);

                if (CollectionUtil.isNotEmpty(v.getPackageItems())) {
                    v.getPackageItems().stream().forEach(packageItem -> {
                        com.wosai.pantheon.uf4c.util.CartHelper.addSpuCount(spuCountMap, packageItem.getItem().getId(), packageItem.getItem().getNumber());
                    });
                }
            } catch (Exception ex) {
                log.warn("calc cart spu count error", ex);
            }

            record.setMaterials(cartHelper.toSingleMealCartMaterials(v.getMaterials()));
            record.setRecommendMaterials(cartHelper.toSingleCartRecommendMaterials(v.getRecommendMaterials()));
            record.setAttributes(cartHelper.toSingleMealCartAttributes(v.getAttributes()));

            record.setClientVersion(v.getClientVersion());
            // 获取购物车的时候将加价购名称替换
            record.setBrandAct(v.isBrandAct());
            if (v.isBrandAct() && Objects.nonNull(v.getBrandActProduct())) {
                record.setName(v.getBrandActProduct().getName());
                record.setUrl(v.getBrandActProduct().getUrl());
                record.setBrandAcdProductId(v.getBrandActProduct().getProductId());
            }

            records.add(record);

            cart.setTotal(cart.getTotal() + num);
            cart.setTotalPrice(cart.getTotalPrice() + record.getPrice());
        });

        cart.setSpuCountMap(spuCountMap);

        Collections.sort(records, (o1, o2) -> {
            //加价购商品排在最后
            if (o1.isBrandAct() && !o2.isBrandAct()) {
                return 1;
            } else if (!o1.isBrandAct() && o2.isBrandAct()) {
                return -1;
            }
            if (o1.isOpenTableMustOrder() && !o2.isOpenTableMustOrder()) {
                return 1;
            }
            if (o2.isOpenTableMustOrder() && !o1.isOpenTableMustOrder()) {
                return -1;
            }
            return o2.getCtime().compareTo(o1.getCtime());
        });

        cart.setRecords(records);

        cartHelper.limitRecommendMaterial(records);

        return cart;
    }


    public Cart getCartByUid(String uid) {
        return this.getSingleCart(null, uid, false);
    }

    private CartCheckResultDTO.Item validateItem(CartItemCreate cartItem, String storeId, int service_type, OrderMealTypeEnum mealType, Map<String, Integer> spuCountMap) {
        if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(Constants.BRAND_PRODUCT_SOURCE_BRAND, cartItem.getBrandProductSource())) {
            return null;
        }
        Item item = cartItem.getItem();
        ItemDto itemDto = itemHelper.getItemDtoById(storeId, item.getId(), service_type);
        String photoUrl = null;
        if (itemDto != null) {
            if (StringUtil.isNotBlank(itemDto.getItem().getPhotoUrl())) {
                String[] arr = itemDto.getItem().getPhotoUrl().split(",");
                photoUrl = arr[0];
            }
        }

        // 判断零售称重商品库存转换是否发生变化
        if (itemDto instanceof RetailExtendItemDTO) {
            RetailExtendItemDTO dto = (RetailExtendItemDTO) itemDto;
            if (!Objects.equals(MapUtils.getDouble(dto.getSkuConvert(), "per_takeout_weight", 0d),
                    MapUtils.getDouble(cartItem.getSkuConvert(), "per_takeout_weight", 0d))) {
                removeItem(cartItem, mealType, true);
                return new CartCheckResultDTO.Item(item.getName(), item.getAttachedInfo(), "分量变更", item.getNumber(), Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID);
            }
        }

        Result<Boolean> isItemSalebleResult = itemHelper.isItemSalebleInMiniProgram(itemDto, cartItem, service_type, spuCountMap);

        if (!isItemSalebleResult.isSuccess()) {
            removeItem(cartItem, mealType, true);
            return new CartCheckResultDTO.Item(item.getName(), item.getAttachedInfo(), isItemSalebleResult.getErrorMsg(), item.getNumber(), Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID);
        }

        Spec spec = cartItem.getSpec();
        List<Material> materials = cartItem.getMaterials();
        List<ItemSpec> dbSpecs = itemDto.getSpecs();
        List<com.wosai.pantheon.core.uitem.model.Material> dbMaterials = itemDto.getMaterials();
        Integer dbPrice = 0;

        if (CollectionUtil.isEmpty(dbSpecs) && spec != null) {
            // 商品信息失效
            // 删除商品
            removeItem(cartItem, mealType, true);
            return new CartCheckResultDTO.Item(item.getName(), item.getAttachedInfo(), "失效", item.getNumber(), Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID);
        }
        if (!CollectionUtils.isEmpty(dbSpecs) && spec != null) {
            ItemSpec itemSpec = dbSpecs.stream().filter(s -> s.getId().equalsIgnoreCase(spec.getId())).findFirst().orElse(null);
            if (itemSpec == null) {
                // 商品信息失效
                // 删除商品
                removeItem(cartItem, mealType, true);
                return new CartCheckResultDTO.Item(item.getName(), item.getAttachedInfo(), "失效", item.getNumber(), Constants.CartCheckItemChangeType.CHANGE_TYPE_INVALID);
            }
            dbPrice += dbSpecs.stream()
                    .filter(s -> s.getId().equals(spec.getId()))
                    .map(ItemSpec::getPrice)
                    .findFirst()
                    .orElse(0);
        } else {
            dbPrice += itemDto.getItem().getPrice();
        }

        if (CollectionUtil.isNotEmpty(cartItem.getAttributes())) {
            List<AttributeDto> attributes = itemDto.getAttributes();
            if (CollectionUtil.isEmpty(attributes)) {
                cartItem.setAttributes(null);
            } else {
                Map<String, Attribute> cartItemAttributeMap = cartItem.getAttributes().stream().collect(Collectors.toMap(Attribute::getId, Function.identity(), (key1, key2) -> key2));
                List<Attribute> finalAttributeList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(itemDto.getAttributes())) {
                    itemDto.getAttributes().forEach(attr -> {
                        if (!CollectionUtils.isEmpty(attr.getOptions())) {
                            attr.getOptions().forEach(option -> {
                                if (cartItemAttributeMap.containsKey(option.getId())) {
                                    Attribute attribute = new Attribute();
                                    attribute.setId(option.getId());
                                    attribute.setName(option.getName());
                                    attribute.setTitle(attr.getTitle());
                                    finalAttributeList.add(attribute);
                                }
                            });
                        }
                    });
                }
                cartItem.setAttributes(finalAttributeList);
            }
        }

        //  redhat2022-06-13号调整备注:加料删除、加料售罄都需要提示"商品价格变动"
        boolean materialsChange = false;
        if (!CollectionUtils.isEmpty(materials)) {
            if (CollectionUtils.isEmpty(dbMaterials)) {
                cartItem.setMaterials(null);
                // 加料发生改变
                materialsChange = true;
            } else {
                // 过滤已售罄的加料
                dbMaterials = dbMaterials.stream()
                        .filter(p -> Objects.equals(p.getStatus(), 1))
                        .collect(Collectors.toList());
                List<com.wosai.pantheon.core.uitem.model.Material> finalDbMaterials = dbMaterials;

                List<CartItemCreate.Material> newList = materials.stream()
                        .filter(m -> finalDbMaterials.stream()
                                .anyMatch(dm -> dm.getId().equalsIgnoreCase(m.getId())))
                        .collect(Collectors.toList());
                if (materials.size() != newList.size()) {
                    cartItem.setMaterials(newList);
                    // 加料发生改变
                    materialsChange = true;
                }
            }
        }

        // 更新购物车加料名称、价格信息
        if (!CollectionUtils.isEmpty(dbMaterials) && !CollectionUtils.isEmpty(materials)) {
            Map<String, com.wosai.pantheon.core.uitem.model.Material> map = dbMaterials
                    .stream()
                    .collect(Collectors.toMap(com.wosai.pantheon.core.uitem.model.Material::getId, material -> material, (k1, k2) -> k1));

            // 更新加料信息至最新
            cartItem.getMaterials().forEach(p -> {
                com.wosai.pantheon.core.uitem.model.Material modifyMaterial = map.get(p.getId());
                if (Objects.nonNull(modifyMaterial)) {
                    p.setName(modifyMaterial.getName());
                    p.setPrice(modifyMaterial.getPrice().longValue());
                }
            });

            dbPrice += materials.stream()
                    .map(m ->
                            Optional.ofNullable(map.get(m.getId()))
                                    .map(p -> p.getPrice() * m.getNumber())
                                    .orElse(0))
                    .reduce(0, Integer::sum);
        }

        String attachedInfo = EntityConvert.generateAttachInfo(cartItem);

        //套餐不在这里检查商品价格
        if (!Objects.equals(cartItem.getItem().getSpuType(), SpuType.PACKAGE.name())) {
            // redhat2022-06-13号调整备注: 如果加料价格标记为true, 或者购物车商品计算总价和数据库商品计算总价不一致，则需要提示价格变动
            if (materialsChange || !Objects.equals(dbPrice, item.getPrice())) {
                item.setPrice(dbPrice);
                cartItem.getItem().setAttachedInfo(attachedInfo);
                upsertItem(cartItem.getItemUid(), cartItem, mealType, true);
                // 商品价格发生变动
                return new CartCheckResultDTO.Item(item.getName(), item.getAttachedInfo(), "价格变动", item.getNumber(), Constants.CartCheckItemChangeType.CHANGE_TYPE_PRICE);
            }
        }
        cartItem.getItem().setUrl(photoUrl);
        cartItem.getItem().setAttachedInfo(attachedInfo);
        // 更新单位以及计价单位类型
        //TODO 按产品要求，暂不对计价单位类型变更做校验
        cartItem.getItem().setUnit(itemDto.getItem().getUnit());
        cartItem.getItem().setUnitType(itemDto.getItem().getUnitType());
        cartItem.getItem().setCategorySort(itemDto.getItem().getCategorySort());
        cartItem.getItem().setDisplayOrder(itemDto.getItem().getDisplayOrder());

        upsertItem(cartItem.getItemUid(), cartItem, mealType, true);

        return null;
    }

    /**
     * 把购物车转移给当前用户
     *
     * @param formerUserId
     * @param storeId
     * @param serviceType
     */
    public void transCart(String formerUserId, String storeId, int serviceType) {
        if (StringUtils.isEmpty(formerUserId) || StringUtils.isEmpty(storeId)) {
            //参数错误， 没有前置用户，直接返回
            return;
        }
        if (StringUtil.equals(formerUserId, ThreadLocalHelper.getUserId())) {
            //两个用户一样，不用做任何操作
            return;
        }

        String newCartKey = generateKey(storeId, serviceType);
        String oldCartKey = generateKey(storeId, serviceType, formerUserId);


        if (!redisTemplate.hasKey(oldCartKey)) {
            //老购物车不存在了， 不做任何操作
            return;
        }
        if (redisTemplate.hasKey(newCartKey)) {
            //新购物车已经存在了， 不做任何操作
            redisTemplate.delete(newCartKey);
        }

        Map<String, CartItemCreate> entries = hashOperations.entries(oldCartKey);


        hashOperations.putAll(newCartKey, entries);

        redisTemplate.expire(newCartKey, expireTime, TimeUnit.HOURS);

        //删掉老购物车
        redisTemplate.delete(oldCartKey);

        return;
    }


    public String generateKey(String storeId, int serviceType) {
        return generateKey(storeId, serviceType, ThreadLocalHelper.getUserId());
    }

    public String generateKey(String storeId, int serviceType, String userId) {
        storeId = storeId.replaceAll("(.*)(\\?$)", "$1");
        if (serviceType == 1 || serviceType == 2) {
            return CART_REDIS_KEY_PRE + storeId + ":" + serviceType + ":" + userId;
        } else {
            return CART_REDIS_KEY_PRE + storeId + ":" + userId;
        }
    }

    public Long getCartVersion(String uid) {

        String cartKey = generateCartVersionKey(uid);
        Object versionObj = redisTemplate.opsForValue().get(cartKey);
        if (versionObj == null) {
            return System.currentTimeMillis();
        }
        if (versionObj instanceof Integer) {
            return Long.valueOf((Integer) versionObj);
        }
        if (versionObj instanceof Long) {
            return (Long) versionObj;
        }
        return (Long) versionObj;
    }

    public Long increaseCartVersion(String cartKey) {

        Long curTimeAsVerison = System.currentTimeMillis();
        String cartVersionKey = generateCartVersionKey(cartKey);
        redisTemplate.opsForValue().set(cartVersionKey, curTimeAsVerison, expireTime, TimeUnit.HOURS);

        return curTimeAsVerison;
    }

    public Long increaseCartVersion(String storeId, int serviceType) {
        String cartKey = generateCartVersionKey(generateKey(storeId, serviceType));
        Long curTimeAsVerison = System.currentTimeMillis();
        redisTemplate.opsForValue().set(cartKey, curTimeAsVerison, expireTime, TimeUnit.HOURS);

        return curTimeAsVerison;
    }

    public Integer getCartPeopleNum(String uid) {

        String cartKey = generateCartPeopleNumKey(uid);
        Object peopleNumObj = redisTemplate.opsForValue().get(cartKey);
        if (peopleNumObj == null) {
            return null;
        }
        return (Integer) peopleNumObj;
    }

    public Integer getCartPeopleNum(String storeId, int serviceType) {
        String cartKey = generateCartPeopleNumKey(storeId, serviceType);

        Object peopleNumObj = redisTemplate.opsForValue().get(cartKey);
        if (peopleNumObj == null) {
            return null;
        }
        return (Integer) peopleNumObj;
    }

    public void setCartPeopleNum(String storeId, int serviceType, Integer peopleNum) {
        String cartKey = generateCartPeopleNumKey(storeId, serviceType);

        redisTemplate.opsForValue().set(cartKey, peopleNum, expireTime, TimeUnit.HOURS);
    }

    public String generateCartPeopleNumKey(String storeId, int serviceType) {
        return generateCartPeopleNumKey(generateKey(storeId, serviceType));
    }

    public String generateCartPeopleNumKey(String uid) {
        return uid + ":people:num";
    }

    public String generateCartVersionKey(String uid) {
        return uid + ":version";
    }


    public CartItemCreate convert2ItemCreate(ShoppingCartGoodsDTO goods, String storeId, String tableId) {
        return convert2ItemCreate(goods, storeId, tableId, false);
    }

    public CartItemCreate convert2ItemCreate(ShoppingCartGoodsDTO goods, String storeId, String tableId, boolean isPackageSubGoods) {
        CartItemCreate cartItemCreate = new CartItemCreate();

        cartItemCreate.setItemUid(goods.getItemUid());
        cartItemCreate.setTableId(tableId);
        cartItemCreate.setStoreId(storeId);

        cartItemCreate.setCtime(goods.getCtime());

        CartItemCreate.Item cartItem = new CartItemCreate.Item();
        cartItem.setCategoryId(goods.getCategoryId());
        cartItem.setNumber(goods.getSaleCount().intValue());
        cartItem.setId(goods.getSpuId());
        cartItem.setName(goods.getSpuTitle());
        cartItem.setPhotoUrl(goods.getMainImageUrl());
        cartItem.setUnit(goods.getSaleUnit());
        cartItem.setPrice(goods.getOriginSalePrice().intValue());

        cartItem.setSpuType(Optional.ofNullable(goods.getSpuType()).orElse(SpuType.PRODUCT).name());

        cartItem.setCategorySort(goods.getCategorySort());
        cartItem.setDisplayOrder(goods.getSort());

        cartItemCreate.setItem(cartItem);

        cartItem.setAttachedInfo(goods.getAttachedInfo());

        if (!isPackageSubGoods && Objects.equals(cartItem.getSpuType(), SpuType.PACKAGE.name())) {
            cartItemCreate.setPackageItems(Optional.ofNullable(goods.getPackageGoods())
                    .map(packageGoods -> packageGoods.stream().map(i -> convert2ItemCreate(i, storeId, tableId, true)).collect(Collectors.toList()))
                    .orElse(null));
        }

        cartItemCreate.setPackageGroupId(goods.getPackageGroupId());
        cartItemCreate.setPackageGroupName(goods.getPackageGroupName());

        if (!StringUtils.isEmpty(goods.getSkuId())) {
            CartItemCreate.Spec spec = new CartItemCreate.Spec();

            spec.setId(goods.getSkuId());
            spec.setName(goods.getSkuTitle());
            spec.setPrice(Integer.valueOf(goods.getOriginSalePrice().toString()));

            cartItemCreate.setSpec(spec);
        }

        cartItemCreate.setAttributes(
                Optional.ofNullable(goods.getRecipes())
                        .map(attributes -> attributes.stream()
                                .map(it -> {
                                    Attribute attribute = new Attribute();
                                    attribute.setId(it.getId());
                                    attribute.setTitle(it.getTitle());
                                    attribute.setName(it.getName());
                                    attribute.setSeq(it.getSeq());
                                    return attribute;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null));
        cartItemCreate.setMaterials(
                Optional.ofNullable(goods.getMaterials())
                        .map(materials -> materials.stream()
                                .map(m -> {
                                    Material material = new Material();
                                    material.setId(m.getId());
                                    material.setName(m.getName());
                                    material.setNumber(m.getNumber());
                                    material.setPrice(m.getPrice());
                                    material.setSource(m.getSource());
                                    return material;
                                })
                                .collect(Collectors.toList()))
                        .orElse(null));
        cartItemCreate.setRecommendMaterials(
                Optional.ofNullable(goods.getRecommendMaterials()).map(ms -> ms.stream().map(m -> {
                    CartItemCreate.RecommendMaterial nm = new CartItemCreate.RecommendMaterial();
                    nm.setId(m.getId());
                    nm.setName(m.getName());
                    nm.setNumber(m.getNumber());
                    nm.setPrice(m.getPrice());
                    nm.setSource(m.getSource());
                    nm.setSelected(m.isSelected());
                    return nm;
                }).collect(Collectors.toList())).orElse(null)
        );
        return cartItemCreate;
    }

    /**
     * 购物车单条记录修改加料信息
     *
     * @param changeRecord
     * @return
     */
    public Cart addOrRemoveMaterial(CartItemCreate changeRecord) {
        if (Objects.isNull(changeRecord) || org.apache.commons.lang3.StringUtils.isBlank(changeRecord.getItemUid())) {
            throw new BusinessException(ReturnCode.CART_RECORD_NOT_EXIST);
        }
        OrderMealTypeEnum mealType = changeRecord.getMealType();
        String itemUid = changeRecord.getItemUid();
        String storeId = changeRecord.getStoreId();
        Integer serviceType = changeRecord.getServiceType();
        String tableId = changeRecord.getTableId();
        CartItemCreate existRecord = null;

        if (OrderMealTypeEnum.SINGLE == mealType) {
            String cartKey = generateKey(storeId, serviceType);
            if (hashOperations.hasKey(cartKey, itemUid)) {
                existRecord = hashOperations.get(cartKey, itemUid);
            }
        }

        if (OrderMealTypeEnum.ROUND_MEAL == mealType) {
            CartSyncQuery cartSyncQuery = new CartSyncQuery();
            cartSyncQuery.setTableId(tableId);
            cartSyncQuery.setItemUid(itemUid);
            ShoppingCartGoodsDTO goodsDTO = Optional.ofNullable(roundMealCartService.getSingleCartGood(cartSyncQuery))
                    .map(Result::getData)
                    .orElse(null);
            if (Objects.nonNull(goodsDTO)) {
                existRecord = this.convert2ItemCreate(goodsDTO, storeId, tableId);
            }
        }
        if (Objects.isNull(existRecord)) {
            return this.getCart(storeId, serviceType, tableId, mealType);
        }

        String oldItemUid = existRecord.getItemUid();
        cartHelper.addOrRemoveMaterial(existRecord, changeRecord);
        this.replaceItem(storeId, serviceType, mealType, tableId, oldItemUid, existRecord);

        return this.getCart(storeId, serviceType, tableId, mealType);
    }

    public void replaceItem(String storeId, Integer serviceType, OrderMealTypeEnum mealType, String tableId, String oldItemUid, CartItemCreate newItem) {
        switch (mealType) {
            case SINGLE:
                String cartId = this.generateKey(storeId, serviceType);
                if (!org.apache.commons.lang3.StringUtils.equals(oldItemUid, newItem.getItemUid())) {
                    hashOperations.delete(cartId, oldItemUid);
                }
                hashOperations.put(cartId, newItem.getItemUid(), newItem);
                redisTemplate.expire(cartId, expireTime, TimeUnit.HOURS);
                increaseCartVersion(cartId);
                break;
            case ROUND_MEAL:
                CartSyncQuery cartSyncQuery = new CartSyncQuery();
                cartSyncQuery.setTableId(tableId);
                cartSyncQuery.setRemoveItemUid(oldItemUid);
                newItem.setItemUid(null);
                ShoppingCartGoodsDTO replacer = com.wosai.pantheon.uf4c.util.CartHelper.convert2RoundGoods(newItem);
                replacer.setItemUid(null);
                cartSyncQuery.setGoods(replacer);
                roundMealCartService.replaceItem(cartSyncQuery);
                break;
            default:
                break;
        }
    }

    /**
     * 合并处理购物车
     * 1.若原购物车为空，自动加购
     * 2.若购物车不为空，判断品牌商品是否已加入，未加则自动加购
     *
     * @param cart
     */
    public Cart processBrandAutoCarts(Cart cart, String storeId, Integer serviceType) {
        try {
            List<CartItemCreate> autoItems = brandActivityHelper.processAutoCart(storeId, serviceType, ThreadLocalHelper.getUserId(), ThreadLocalHelper.getMiniProgramType().name(), ThreadLocalHelper.getAppid());
            if (CollectionUtils.isEmpty(autoItems)) {
                return cart;
            }
            if (Objects.isNull(cart)) {
                for (CartItemCreate it : autoItems) {
                    cart = addCart(it, OrderMealTypeEnum.SINGLE, DealTypeEnum.ADD);
                }
            } else {
                for (CartItemCreate it : autoItems) {
                    if (cart.getRecords().stream().noneMatch(r -> Objects.equals(r.getItemId(), it.getItem().getId()))) {
                        cart = addCart(it, OrderMealTypeEnum.SINGLE, DealTypeEnum.ADD);
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.logWarn("自动加购加价购商品出错", "processBrandAutoCarts", storeId, e);
        }
        return cart;
    }
}
