package com.wosai.pantheon.uf4c.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.InvocationListener;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.upay.common.util.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MethodArgumentNotValidException;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wosai.pantheon.uf4c.util.TraceContextUtils.fake;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * <AUTHOR>
 * @date 2020-03-11
 */
@Component
public class JsonRpcLogAspect implements InvocationListener {
    //日志
    private static final Logger logger = LoggerFactory.getLogger(JsonRpcLogAspect.class);

    @Override
    public void willInvoke(Method method, List<JsonNode> arguments) {
    }

    @Override
    public void didInvoke(Method method, List<JsonNode> arguments, Object result, Throwable t, long duration) {
        try {
            String argumentsStr = JacksonUtil.toJsonString(arguments);
            String resultStr = JacksonUtil.toJsonString(result);


            Map<String, Object> logMap = new HashMap<>();

            String userId = null;
            if (ThreadLocalHelper.getRequestContextThreadLocal().get() != null) {
                if (ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO() != null) {
                    userId = ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO().getUserId();
                    logMap.put("user_id", userId);
                }

                logMap.put("ip", ThreadLocalHelper.getRequestContextThreadLocal().get().getRealIp());
            }
            logMap.put("cost", duration);

            if (t != null) {
                if (t instanceof ParamException || t instanceof BusinessException || t instanceof com.wosai.pantheon.uf4c.web.exception.BusinessException){
                    logger.warn(com.wosai.pantheon.uf4c.util.JacksonUtil.toJsonString(logMap),
                            keyValue("class", method.getDeclaringClass().getName()),
                            keyValue("method", method.getName()),
                            keyValue("arguments", argumentsStr),
                            keyValue("result", resultStr),
                            keyValue("user_id", userId),
                            keyValue("uri", method.getName()),
                            keyValue("fake", fake()),
                            keyValue("exception_type", t.getClass().getName()),
                            keyValue("exception_msg", t.getMessage()));
                } else if (t instanceof MethodArgumentNotValidException){
                    logger.warn(com.wosai.pantheon.uf4c.util.JacksonUtil.toJsonString(logMap),
                            keyValue("class", method.getDeclaringClass().getName()),
                            keyValue("method", method.getName()),
                            keyValue("arguments", argumentsStr),
                            keyValue("result", resultStr),
                            keyValue("user_id", userId),
                            keyValue("uri", method.getName()),
                            keyValue("fake", fake()),
                            keyValue("exception_type", t.getClass().getName()),
                            keyValue("exception_msg", t.getMessage()));
                }else {
                    logger.error(com.wosai.pantheon.uf4c.util.JacksonUtil.toJsonString(logMap),
                            keyValue("class", method.getDeclaringClass().getName()),
                            keyValue("method", method.getName()),
                            keyValue("arguments", argumentsStr),
                            keyValue("result", resultStr),
                            keyValue("user_id", userId),
                            keyValue("uri", method.getName()),
                            keyValue("fake", fake()),
                            t
                    );
                }
            } else {
                logger.info(com.wosai.pantheon.uf4c.util.JacksonUtil.toJsonString(logMap),
                        keyValue("class", method.getDeclaringClass().getName()),
                        keyValue("method", method.getName()),
                        keyValue("arguments", argumentsStr),
                        keyValue("result", resultStr),
                        keyValue("user_id", userId),
                        keyValue("uri", method.getName()),
                        keyValue("fake", fake())
                );
            }
        } catch (Exception e) {
        }
    }
}
