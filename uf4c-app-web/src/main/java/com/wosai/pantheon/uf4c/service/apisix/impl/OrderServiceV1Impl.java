package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.market.mcc.api.dto.request.BatchFindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.pantheon.order.enums.OrderStatus;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.order.model.dto.OrderDTO;
import com.wosai.pantheon.order.model.dto.OrderRedeemDTO;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.order.utils.OrderUtil;
import com.wosai.pantheon.printer.enums.TemplateType;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.constant.UpayConstant;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.DeliverInfo;
import com.wosai.pantheon.uf4c.model.DeliveryInfoWrapper;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.PackFeeWrapper;
import com.wosai.pantheon.uf4c.model.dto.DeliverFeeRequest;
import com.wosai.pantheon.uf4c.model.dto.OrderPrintRequest;
import com.wosai.pantheon.uf4c.service.CampusHelper;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.service.UprintService;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV1;
import com.wosai.pantheon.uf4c.util.EntityConvert;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.util.TimeSplit;
import com.wosai.smart.goods.common.exception.BizErrorException;
import com.wosai.smart.goods.common.model.ReturnCode;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.query.ProductStockRequest;
import com.wosai.smartbiz.oms.api.services.OrderGoodsStockRpcService;
import com.wosai.smartbiz.oms.api.services.OrderRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class OrderServiceV1Impl implements OrderServiceV1 {
    @Autowired
    private OrderHelper orderHelper;

    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderRpcService orderRpcService;

    @Autowired
    private UprintService uprintService;

    @Autowired
    private OrderGoodsStockRpcService orderGoodsStockService;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private CampusHelper campusHelper;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;


    @Override
    public Long getPackAmount(ApiRequest apiRequest) {

        Map queryParam = apiRequest.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");
        String type = MapUtils.getString(queryParam, "type");

        if (StringUtils.isBlank(type)) {
            throw new ParamException("参数type不能为空");
        }

        if (StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        OrderType orderType = orderHelper.getOrderType(type);

        PackFeeWrapper wrapper = orderHelper.computePackAmount(storeId, orderType, null);
        return Optional.ofNullable(wrapper).map(item -> item.getPackFee()).orElse(0L);
    }

    @Override
    public Map getDeliveryFee(ApiRequest<DeliverFeeRequest> apiRequest) {
        LogUtils.logInfo("请求计算配送费", "requestDeliveryFee", apiRequest.getBody());
        DeliverFeeRequest request = apiRequest.getBody();
        request.setDeliveryInfo(campusHelper.fillCampusInfo(request.getStoreId(), request.getDeliveryInfo()));
        DeliveryInfoWrapper deliveryInfoWrapper = orderHelper.getDeliveryFee(request);
        DeliverInfo deliverInfo = deliveryInfoWrapper.getDeliverInfo();

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("delivery_fee", deliverInfo.getDeliveryFee());
        resultMap.put("reduction_amount", deliverInfo.getReductionAmount());

        // 查询统一收银台查询所需的相关参数：tradeApp：交易应用、amountComposition：金额构成
        // 在这个接口聚合此逻辑是因为按照产品交互，用户更改了地址之后，需要重新获取配送费。
        // 若门店使用第三方配送或收钱吧配送时，配送费变化会导致tradeApp与amountComposition变更
        // 为了减少前端调用次数，在获取配送费接口里进行此数据逻辑的聚合
        Map<String, Object> cashierParams = orderHelper.getCashierBizParams(request.getStoreId(), OrderType.TAKE_OUT_ORDER, deliverInfo, true);
        resultMap.putAll(cashierParams);
        return resultMap;
    }

    @Override
    public List<TimeSplit.TimeResult> getTimeSplit(ApiRequest apiRequest) {

        Map queryParam = apiRequest.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");

        if (StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        ConfigResponse businessTimeConfig = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TIMES));
        String businessTime = Optional.ofNullable(businessTimeConfig).map(ConfigResponse::getValue).orElse("");
        ConfigResponse presetTypeConfig = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.PRESET_DAYS));
        String days = Optional.ofNullable(presetTypeConfig).map(ConfigResponse::getValue).orElse(Constants.DEFAULT_PRE_DAYS);
        List<TimeSplit.TimeResult> bookTimes = TimeSplit.getBookTimes(businessTime, JSONArray.parseArray(days, Integer.class), Constants.DEFAULT_COOK_TIME, false);
        if (CollectionUtils.isNotEmpty(bookTimes) && bookTimes.get(0).getTimes().get(0) == -1L) {
            bookTimes.get(0).getTimes().set(0, TimeSplit.getNowAfter15Date().getTime());
        }
        return bookTimes;
    }

    @Override
    public List<TimeSplit.TimeResult> getBookTimeSplit(ApiRequest apiRequest) {

        Map queryParam = apiRequest.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");
        String orderType = MapUtils.getString(queryParam, "order_type");

        if (StringUtils.isBlank(storeId)) {
            throw new ParamException("门店信息不能为空");
        }

        if (StringUtils.isBlank(orderType)) {
            throw new ParamException("订单类型不能为空");
        }

        BatchFindConfigByNameRequest request = new BatchFindConfigByNameRequest(AppId.UFOOD, OwnerType.STORE_ID, storeId, Constants.BOOK_ORDER_CONFIGS);
        List<ConfigResponse> configResponses = configRemoteService.batchFindByNames(request);
        Map<String, String> configMap = configResponses.stream().collect(Collectors.toMap(ConfigResponse::getName, ConfigResponse::getValue));
        String mccDays = null;
        int addTime = 0;
        boolean onlyBookOrder = false;
        String customizedSettingTime = null;
        if (OrderType.TAKE_OUT_ORDER.getMsg().equalsIgnoreCase(orderType)) {
            String preStatus = Optional.ofNullable(configMap.get(Constants.TAKEOUT_PRE_STATUS)).orElse("0");
            String deliveryType = Optional.ofNullable(configMap.get(Constants.DELIVERY_TYPE)).orElse("1");
            onlyBookOrder = MapUtils.getIntValue(configMap, Constants.TAKEOUT_ONLY_BOOK_ORDER, 0) == 1;
            if ("1".equals(preStatus) && "2".equals(deliveryType)) {

                //仅打开了外卖预定单开关和自配送商家，才能返回预定时间列表
                mccDays = Optional.ofNullable(configMap.get(Constants.TAKEOUT_PRE_DAYS)).orElse(Constants.DEFAULT_PRE_DAYS);
                addTime = Constants.DEFAULT_COOK_TIME + Constants.DEFAULT_DELIVERY_TIME;

                //自定义时间点、自定义出餐时间、自定义配送时间
                customizedSettingTime = configMap.get(Constants.CUSTOMIZED_TAKEOUT_TIME);
                if (MapUtils.getInteger(configMap, Constants.CUSTOMIZED_DELIVERY_TIME_COST) != null
                        && MapUtils.getInteger(configMap, Constants.CUSTOMIZED_COOK_TIME_COST) != null) {
                    addTime = MapUtils.getIntValue(configMap, Constants.CUSTOMIZED_DELIVERY_TIME_COST) + MapUtils.getIntValue(configMap, Constants.CUSTOMIZED_COOK_TIME_COST);
                }
            } else {
                //其他情况，只返回立即送达。
                return TimeSplit.onlyImmediateOrder();
            }
        } else if (OrderType.PRE_ORDER.getMsg().equalsIgnoreCase(orderType)) {
            mccDays = Optional.ofNullable(configMap.get(Constants.PRESET_DAYS)).orElse(Constants.DEFAULT_PRE_DAYS);
            addTime = Constants.DEFAULT_COOK_TIME;
            onlyBookOrder = MapUtils.getIntValue(configMap, Constants.PRE_ONLY_BOOK_ORDER, 0) == 1;

            //自定义时间点、自定义出餐时间
            customizedSettingTime = configMap.get(Constants.CUSTOMIZED_PRESET_TIME);
            if (MapUtils.getInteger(configMap, Constants.CUSTOMIZED_COOK_TIME_COST) != null) {
                addTime = MapUtils.getIntValue(configMap, Constants.CUSTOMIZED_COOK_TIME_COST);
            }
        }
        String businessTime = Optional.ofNullable(configMap.get(Constants.DELIVERY_TIMES)).orElse("");
        List<Integer> days = JSONArray.parseArray(mccDays, Integer.class);
        if (apolloConfigHelper.getBooleanConfigValueByKey("presettime.customized.flag", false) && StringUtils.isNotBlank(customizedSettingTime)) {
            return TimeSplit.getCustomBookTimes(businessTime, days, customizedSettingTime, addTime);
        }
        return TimeSplit.getBookTimes(businessTime, days, addTime, onlyBookOrder);
    }

    @Override
    public String notify(ApiRequest<Map> apiRequest) {

        Map request = apiRequest.getBody();
        String orderSn = MapUtils.getString(request, "order_sn");

        OrderDTO orderDTO = orderService.getOrderBySn(orderSn);
        if (orderDTO == null) {
            return "error";
        }
        orderDTO.setRedeems(OrderUtil.mergeRedeemsByMkssRule(orderDTO.getRedeems(), OrderRedeemDTO::getExtra, OrderRedeemDTO::setName, OrderRedeemDTO::setDiscountAmount));

        Order order = EntityConvert.convertOrderDTO4Print(orderDTO);

        String nextOrderStatus = BeanUtil.getPropString(request, UpayConstant.ORDER_STATUS);

        if (OrderStatus.BOOKORDER_WAIT_FOR_NOTIFY.name().equalsIgnoreCase(nextOrderStatus)) {
            //预订单在接单时和提醒时都打印，此时为接单打印，不减库存
            try {
                combineOrderWithPrintControlParam(order, orderDTO);
                uprintService.sendPrintJob(order);
            } catch (Exception e) {
                log.warn("sn={} 支付成功，但打印机调用异常", orderSn, e);
            }
            return "success";
        }

        if (UpayConstant.ORDER_STATUS_PAID.equalsIgnoreCase(nextOrderStatus)) {
            // 打印订单
            try {
                if (OrderType.JIELONG_ORDER != orderDTO.getOrderType()) {
                    //接龙订单不打印，小程序下单/自营外卖/到店自取 按配置控制打印参数
                    combineOrderWithPrintControlParam(order, orderDTO);

                    uprintService.sendPrintJob(order);
                }
            } catch (Exception e) {
                log.warn("sn={} 支付成功，但打印机调用异常", orderSn, e);
            }
            return "success";
        }

        if ("REFUNDED".equals(nextOrderStatus) || "PARTIAL_REFUNDED".equals(nextOrderStatus)) {
            try {
                //加库存
                ProductStockRequest stockRequest = new ProductStockRequest();
                stockRequest.setMerchantId(order.getMerchantId());
                stockRequest.setStoreId(order.getStoreId());
                stockRequest.setItems(orderDTO.getItems());
                Boolean stockResult = orderGoodsStockService.addProductStock(stockRequest);
                if (!stockResult) {
                    log.warn("sn={} 退款恢复库存失败", orderSn);
                }
            } catch (Exception ex) {
                log.warn("sn={} 退款恢复库存异常", orderSn, ex);
            }
            return "success";
        }
        return "";
    }

    /**
     * 根据配置&传参控制是否打印结账单&发票，
     *
     * @param order    订单
     * @param orderDTO 订单dto
     */
    private void combineOrderWithPrintControlParam(Order order, OrderDTO orderDTO) {
        boolean printBill = true;
        boolean printInvoiceInBill = false;

        List<ConfigResponse> configResponses = queryMccPrintConfig(order);
        if (CollectionUtils.isNotEmpty(configResponses)) {
            for (ConfigResponse cfg : configResponses) {
                if (StringUtils.equals(cfg.getName(), Constants.PrintConstants.BILL_DEFAULT_PRINT_SWITCH)) {
                    if (OrderType.TAKE_OUT_ORDER.equals(orderDTO.getOrderType()) || OrderType.PRE_ORDER.equals(orderDTO.getOrderType())){
                        //自营外卖/到店自取，需要打印结账单
                        printBill = true;
                    }else {
                        printBill = StringUtils.equalsIgnoreCase(cfg.getValue(), "true");
                    }
                } else if (StringUtils.equals(cfg.getName(), Constants.PrintConstants.TAKEOUT_BILL_PRINT_INVOICE)) {
                    //根据配置判断是否打印，扫码轻餐结账单中不含发票
                    if ((OrderType.TAKE_OUT_ORDER.equals(orderDTO.getOrderType()) || OrderType.PRE_ORDER.equals(orderDTO.getOrderType()))
                            && cfg.getValue() != null) {
                        List<String> invoicePrintCfg = JSON.parseArray(cfg.getValue(), String.class);
                        printInvoiceInBill = CollectionUtils.isNotEmpty(invoicePrintCfg) && invoicePrintCfg.contains("smart");
                    }
                }
            }
        }

        if (printBill) {
            //需要打印结账单，判断发票二维码是否需要打印
            if (printInvoiceInBill){
                order.setCheckInvoice(true);

                Result<String> invoiceUrlResult = orderRpcService.genInvoiceUrl(orderDTO.getSn());
                if (!invoiceUrlResult.isSuccess() || StringUtils.isBlank(invoiceUrlResult.getData())) {
                    log.warn("sn={} 生成发票链接失败", orderDTO.getSn());
                    order.setCheckInvoice(false);
                }else {
                    order.setBillWithInvoiceUrl(invoiceUrlResult.getData());
                }
            }else{
                order.setCheckInvoice(false);
                //不置空invoiceUrl，避免影响开票小票打印
                //order.setInvoiceUrl(null);
            }
        }else {
            order.setExcludeTemplateTypes(Lists.newArrayList(TemplateType.TEMPLATE_BILL.name()));
        }
        log.debug("combineOrderWithPrintControlParam order.checkInvoice:{}, order.billWithInvoiceUrl:{},order.excludeTemplateTypes：{} order.sn:{}", order.isCheckInvoice(),order.getBillWithInvoiceUrl(),order.getExcludeTemplateTypes(), order.getSn());
    }

    private List<ConfigResponse> queryMccPrintConfig(Order order) {
        BatchFindConfigByNameRequest findConfigByNameRequest = new BatchFindConfigByNameRequest();
        findConfigByNameRequest.setNames(Arrays.asList(Constants.PrintConstants.BILL_DEFAULT_PRINT_SWITCH, Constants.PrintConstants.TAKEOUT_BILL_PRINT_INVOICE));
        findConfigByNameRequest.setOwnerType(OwnerType.STORE_ID.getOwnerType());
        findConfigByNameRequest.setOwnerId(order.getStoreId());
        findConfigByNameRequest.setAppId(AppId.UFOOD.getAppId());

        List<ConfigResponse> response;
        try {
            response = configRemoteService.batchFindByNames(findConfigByNameRequest);
            if (response == null) {
                throw new ParamException("mcc 查询打印管控配置失败");
            }
        } catch (Exception e) {
            // 远程调用失败/查无相关信息，默认均需要打印
            log.warn("queryMccPrintConfig failed :orderId:{},req is:{}, msg:{}", order.getSn(), findConfigByNameRequest, e.getMessage());
            return null;
        }
        return response;
    }

    @Override
    public String printByTemplate(ApiRequest<OrderPrintRequest> apiRequest) {

        OrderPrintRequest request = apiRequest.getBody();
        try {
            OrderDTO orderDTO = orderService.getOrderBySn(request.getOrderSn());
            if (orderDTO == null) {
                return "error";
            }
            Order order = EntityConvert.convertOrderDTO4Print(orderDTO);

            Map<String, Object> params = new HashMap<>();
            params.put("data", order);
            params.put("templateType", request.getTemplateType());

            uprintService.printByTemplate(params);
            return "success";
        } catch (Exception e) {
            log.warn("sn={} 打印机调用异常", request.getOrderSn(), e);
            return "error";
        }
    }

    @Override
    public String print(ApiRequest<OrderPrintRequest> apiRequest) {
        OrderPrintRequest request = apiRequest.getBody();
        try {
            OrderDTO orderDTO = orderService.getOrderBySn(request.getOrderSn());
            if (orderDTO == null) {
                return "error";
            }
            Order order = EntityConvert.convertOrderDTO4Print(orderDTO);

            uprintService.sendPrintJob(order);
            return "success";
        } catch (Exception e) {
            log.warn("sn={} 打印机调用异常", request.getOrderSn(), e);
            return "error";
        }
    }

    @Override
    public Map getCashierParams(ApiRequest<Map> apiRequest) {
        Map request = apiRequest.getQuery();
        String storeId = MapUtils.getString(request, "store_id");
        String orderType = MapUtils.getString(request, "order_type");
        boolean packed = MapUtils.getBooleanValue(request, "packed", false);
        if (StringUtils.isAnyBlank(storeId, orderType)) {
            throw new BizErrorException(ReturnCode.INVALID_PARAM_EXCEPTION);
        }
        OrderType type = orderHelper.getOrderType(orderType);
        if (OrderType.PRE_ORDER == type || OrderType.TAKE_OUT_ORDER == type) {
            // 自取必然有打包费
            packed = true;
        }
        return orderHelper.getCashierBizParams(storeId, type, null, packed);
    }


}
