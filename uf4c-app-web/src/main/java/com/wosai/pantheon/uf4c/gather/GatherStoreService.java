package com.wosai.pantheon.uf4c.gather;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shouqianba.smart.template.api.interfaces.StoreTemplateRemoteServiceV2;
import com.shouqianba.smart.template.api.interfaces.TemplateRemoteServiceV2;
import com.wosai.app.model.req.QueryDraftReq;
import com.wosai.app.model.req.QueryRenovationsReq;
import com.wosai.app.model.resp.StoreRenovationResp;
import com.wosai.app.service.StoreRenovationService;
import com.wosai.market.mcc.api.dto.request.BatchFindByOwnerRequest;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.dto.response.ConfigValue;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.merchant.api.AlipayStoreActivityRemoteService;
import com.wosai.market.merchant.api.CustomerStoreRemoteService;
import com.wosai.market.merchant.api.StoreTemplateRemoteService;
import com.wosai.market.merchant.api.TemplateRemoteService;
import com.wosai.market.merchant.dto.alipay.AlipayStoreActivityDTO;
import com.wosai.market.merchant.dto.customer.request.StoreDetailRequest;
import com.wosai.market.merchant.dto.customer.response.StoreDetailResponse;
import com.wosai.market.merchant.dto.template.MainTemplateDetail;
import com.wosai.market.merchant.dto.template.TemplateDetailRequest;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.gather.cache.GatherCacheHelper;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.RangeAreaDto;
import com.wosai.pantheon.uf4c.model.StoreCampusInfo;
import com.wosai.pantheon.uf4c.service.CampusHelper;
import com.wosai.pantheon.uf4c.service.DeliveryAreaHelper;
import com.wosai.pantheon.uf4c.util.GatherTraceRunnable;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
@Service
public class GatherStoreService extends GatherBase {

    @Autowired
    private CustomerStoreRemoteService customerStoreRemoteService;
    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    AlipayStoreActivityRemoteService alipayStoreActivityRemoteService;

    //    @Autowired
//    private VersionConfigRemoteService versionConfigRemoteService;
    @Autowired
    private GatherHelper gatherHelper;
    @Autowired
    private CampusHelper campusHelper;
    @Autowired
    private StoreRenovationService storeRenovationService;
    private static final List<String> pageIds = Lists.newArrayList("p0o2sfaiwk21", "xgaugooilxg7");

    @Autowired
    private DeliveryAreaHelper deliveryAreaHelper;
    @Autowired
    private GatherCacheHelper gatherCacheHelper;

    @Autowired
    private Config mpVersionConfig;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    // 异步线程超时时间
    @Value("${service.async_timeout}")
    private int ASYNC_TIMEOUT;

    // 是否查询外部活动配置
    @Value("${gather.query_outer_activity:false}")
    private boolean queryOuterActivity;

    @Resource
    private TemplateRemoteService templateRemoteService;
    @Resource
    private TemplateRemoteServiceV2 templateRemoteServiceV2;

    @Autowired
    private StoreTemplateRemoteService storeTemplateRemoteService;
    @Autowired
    private StoreTemplateRemoteServiceV2 storeTemplateRemoteServiceV2;

    /**
     * 加载门店配置
     *
     * @param request 请求数据
     * @param dataMap 统一的数据返回对象
     */
    public void loadStoreData(GatherRequest request, Map<String, Object> dataMap) {
        List<CompletableFuture<Void>> list = new ArrayList<>();
        // 查询阿波罗配置
        list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryApolloConfig(request, dataMap)), storeExecutor));
        // 查询可送区域
        list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> getAreas(request, dataMap)), storeExecutor));
        if (apolloConfigHelper.getBooleanConfigValueByKey("theme.query.enable", false)) {
            // 查询门店主题配置
            list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryStoreThemes(request, dataMap)), storeExecutor));
        }
        // 查询门店模板
        if (apolloConfigHelper.getBooleanConfigValueByKey("template.query.enable", true)) {
            long random = new Random().nextInt(1000);
            if (random < apolloConfigHelper.getLongConfigValueByKey("smart.template.traffic.percent", 1000L)) {
                list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryTemplateV2(request, dataMap)), storeExecutor));
            } else {
                list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryTemplate(request, dataMap)), storeExecutor));
            }
        }

        // 查询门店外部活动配置
        if (MiniProgramType.ALIPAY.name().equals(request.getMiniProgramType()) && queryOuterActivity) {
            list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryStoreOuterActivity(request, dataMap)), storeExecutor));
        }
        if (gatherCacheHelper.isCacheEnabled(request.getStoreId()) && !request.isCacheUpdate() && gatherCacheHelper.hasStoreCache(request.getStoreId())) {
            // 查询外卖分账
            list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> hasTakeoutProfitSharing(request.getStoreId(), dataMap)), storeExecutor));
//            // 查询支持储值卡的场景信息
//            list.add(within(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> supportCardPay(request.getStoreId(), dataMap)), storeExecutor), ASYNC_TIMEOUT, TimeUnit.MILLISECONDS)
//                    .exceptionally(throwable -> {
//                        log.warn("查询储值卡可支付场景超时");
//                        setError(dataMap, ReturnCode.SUPPORT_CARD_PAY_ERROR);
//                        durationEnd(dataMap, "supportCardPay");
//                        return null;
//                    }));
            gatherCacheHelper.getStoreCache(request.getStoreId(), dataMap);
        } else {
            if (request.needStore()) {
                // 查询门店信息
                list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryStoreInfo(request, dataMap)), storeExecutor));
            }
            if (request.needMcc()) {
                // 门店配置
                list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryStoreMcc(request, dataMap)), storeExecutor));
                // 商户配置
                list.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> queryMerchantMcc(request, dataMap)), storeExecutor));
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            CompletableFuture<Void> storeFuture = CompletableFuture.allOf(list.toArray(new CompletableFuture[0]));
            storeFuture.join();
            if (StringUtils.isBlank(request.getMerchantId())) {
                // 非扫码进入时，通过终端信息取不到merchantId时的一种补偿处理
                Map<String, Object> storeMap = (Map<String, Object>) MapUtils.getMap(dataMap, "store");
                String merchantId = MapUtils.getString(storeMap, "merchantId");
                request.setMerchantId(merchantId);
            }
            if (!dataMap.containsKey("mch_mcc")) {
                queryMerchantMcc(request, dataMap);
            }
            // 判断是否零售门店
            Map<String, Object> mccMap = (Map<String, Object>) MapUtils.getMap(dataMap, "mch_mcc");
            if (MapUtils.isNotEmpty(mccMap)) {
                boolean flag = MapUtils.getString(mccMap, Constants.MERCHANT_TYPE, Constants.CATERING).equals(Constants.RETAIL);
                request.setRetailStore(flag);
                Map<String, Object> storeMap = (Map<String, Object>) MapUtils.getMap(dataMap, "store");
                if (MapUtils.isNotEmpty(storeMap)) {
                    storeMap.put("retailStore", flag);
                }
            }
        }
    }


    private void queryStoreOuterActivity(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryStoreOuterActivity");
        if (StringUtils.isBlank(request.getStoreId())) {
            return;
        }
        try {
            log.info("查询门店外部活动配置,storeId=" + request.getStoreId(), keyValue("method", "GatherQueryStoreOuterActivity"), keyValue("req_id", request.getReqId()));
            AlipayStoreActivityDTO alipayStoreActivityDTO = alipayStoreActivityRemoteService.queryActivities(request.getStoreId());
            if (Objects.nonNull(alipayStoreActivityDTO) && Objects.nonNull(alipayStoreActivityDTO.getAlipayShopId())) {
                dataMap.put("outerActivity", new HashMap<String, Object>() {{
                    put("alipayMerchantActivity", new HashMap<Object, Object>() {{
                        put("shopId", alipayStoreActivityDTO.getAlipayShopId());
                        put("activities", alipayStoreActivityDTO.getActivityTypes());
                    }});
                }});
            }
        } catch (Exception e) {
            log.warn("查询门店外部活动配置：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            setError(dataMap, ReturnCode.GET_PAY_CHANNEL_ACTIVITY_ERROR);
        } finally {
            durationEnd(dataMap, "queryStoreOuterActivity");
        }
    }

    /**
     * 查询门店信息
     *
     * @param request
     * @param dataMap
     */
    private void queryStoreInfo(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryStoreInfo");
        if (StringUtils.isBlank(request.getStoreId())) {
            return;
        }
        try {
            log.info("查询门店信息,storeId=" + request.getStoreId(),
                    keyValue("method", "GatherQueryStoreInfo"), keyValue("req_id", request.getReqId()));
            StoreDetailRequest storeDetailRequest = new StoreDetailRequest();
            storeDetailRequest.setStoreId(request.getStoreId());
            StoreDetailResponse storeDetailInfo = customerStoreRemoteService.storeDetailInfo(storeDetailRequest);
            request.setMerchantId(storeDetailInfo.getMerchantId());
            // 为了适配前端现有的数据格式，将下划线转换为驼峰
            dataMap.put("store", JacksonUtil.beanToMap(storeDetailInfo, new ObjectMapper()));
            //queryMerchantMcc(request, dataMap);
        } catch (Exception e) {
            log.warn("查询门店信息失败：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            setError(dataMap, ReturnCode.STORE_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "queryStoreInfo");
        }
    }

    /**
     * 查询小程序阿波罗配置
     *
     * @param dataMap
     */
    public void queryApolloConfig(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryApolloConfig");
        try {
            if (!dataMap.containsKey("config")) {
                logInfo("查询阿波罗", "GatherQueryApollo", request);
//                ConfigRequest configRequest = new ConfigRequest();
//                configRequest.setVersion(StringUtils.isNotBlank(request.getVersion()) ? request.getVersion() : gatherHelper.getMpApolloVersion());
//                String config = versionConfigRemoteService.findConfigByVersion(configRequest);
                String version = StringUtils.isNotBlank(request.getVersion()) ? request.getVersion() : gatherHelper.getMpApolloVersion();
                String config = mpVersionConfig.getProperty(version, null);
                dataMap.put("config", JSON.parse(config));
            }
        } catch (Exception e) {
            log.warn("查询阿波罗失败：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            setError(dataMap, ReturnCode.APOLLO_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "queryApolloConfig");
        }
    }

    /**
     * 查询门店MCC配置
     *
     * @param request
     * @param dataMap
     */
    private void queryStoreMcc(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryStoreMcc");
        //TODO 过滤掉前端不需要的配置
        if (StringUtils.isBlank(request.getStoreId())) {
            return;
        }
        try {
            logInfo("查询门店mcc配置信息", "GatherQueryStoreMcc", request);
            Map mccMap = batchFindMcc(request.getStoreId(), OwnerType.STORE_ID);
            //queryStoreExtraMcc(request, mccMap);
            dataMap.put("mcc", mccMap);
        } catch (Exception e) {
            log.warn("商户配置查询出错：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            setError(dataMap, ReturnCode.MCC_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "queryStoreMcc");
        }
    }

    /**
     * 查询商户配置
     *
     * @param request
     * @param dataMap
     */
    private void queryMerchantMcc(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryMerchantMcc");
        //TODO 过滤掉前端不需要的配置
        try {
            if (StringUtils.isBlank(request.getMerchantId())) {
                return;
            }
            logInfo("查询商户mcc配置信息", "GatherQueryMerchantMcc", request);
            dataMap.put("mch_mcc", batchFindMcc(request.getMerchantId(), OwnerType.MERCHANT_ID));
        } catch (Exception e) {
            log.warn("商户配置查询出错：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            setError(dataMap, ReturnCode.MCC_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "queryMerchantMcc");

        }

    }

    /**
     * 查询门店额外的配置信息
     *
     * @param request
     * @param mccMap
     */
    private void queryStoreExtraMcc(GatherRequest request, Map<String, Object> mccMap) {
        hasTakeoutProfitSharing(request.getStoreId(), mccMap);
    }

    /**
     * 查询门店是否有外卖分账
     * 1.门店不配送时，若入驻校园，且配送方式为收钱吧配送，则有外卖分账
     * 2.门店为第三方配送，则有外卖分账
     *
     * @param storeId
     * @param dataMap
     */
    public void hasTakeoutProfitSharing(String storeId, Map<String, Object> dataMap) {
        FindConfigByNameRequest configByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TYPE);
        ConfigResponse configResponse = configRemoteService.findByName(configByNameRequest);
        // 配送方式 1-不配送    >1-支持配送
        int deliveryType = (int) MccUtils.getLongValue(configResponse);
        boolean takeoutProfitSharing = false;

        if (deliveryType == 2) {
            // 自配送，查询是否有校园外卖收钱吧配送，有收钱吧配送则有分账
            takeoutProfitSharing = campusHelper.hasProfitSharing(storeId);
        } else if (deliveryType > 3) {
            // 第三方外卖配送，有分账
            takeoutProfitSharing = true;
        }
        dataMap.put("takeoutProfitSharing", takeoutProfitSharing);
    }

    /**
     * 各场景支持储值卡支付判断
     *
     * @param storeId
     * @param dataMap
     */
    public void supportCardPay(String storeId, Map<String, Object> dataMap) {
        if (!apolloConfigHelper.getBooleanConfigValueByKey("support.card.pay.process.enabled", false)) {
            // 阿波罗控制是否执行储值卡支持场景逻辑处理
            dataMap.put("supportCardPay", "");
            return;
        }

        durationStart(dataMap, "supportCardPay");
        Map<String, Boolean> supports = new HashMap<>();
        supports.put(OrderType.SUBSCRIBE_ORDER.getMsg(), true);
        supports.put(OrderType.TAKE_OUT_ORDER.getMsg(), false);
        supports.put(OrderType.PRE_ORDER.getMsg(), false);

        try {
            // 定义变量：是否允许使用储值
            boolean allowTakeoutOrder = true, allowPreOrder = true;
            // 查询门店配送方式
            FindConfigByNameRequest configByNameRequest = MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.DELIVERY_TYPE);
            ConfigResponse configResponse = configRemoteService.findByName(configByNameRequest);
            // 配送方式 1-不配送    >1-支持配送
            int deliveryType = (int) MccUtils.getLongValue(configResponse);
            // 查询在门店校内相关信息
            StoreCampusInfo campusInfo = campusHelper.getStoreInCampusInfo(storeId);
            if (Objects.nonNull(campusInfo)) {
                if (deliveryType > 2 || (campusInfo.isInCampus() && (campusInfo.isHasCampusFee() || campusInfo.isHasCampusDelivery()))) {
                    // 使用第三方配送，禁用储值支持
                    // 校内门店使用收钱吧配送或有流量服务费，禁用储值支付
                    allowTakeoutOrder = false;
                }
                if (campusInfo.isInCampus() && campusInfo.isHasCampusFee()) {
                    // 校内店铺且有流量服务费，禁用储值支付
                    allowPreOrder = false;
                }
                supports.put(OrderType.TAKE_OUT_ORDER.getMsg(), allowTakeoutOrder);
                supports.put(OrderType.PRE_ORDER.getMsg(), allowPreOrder);
            }

        } catch (Exception e) {
            setError(dataMap, ReturnCode.SUPPORT_CARD_PAY_ERROR);
            logWarn("查询储值卡支持场景出错：" + e.getMessage(), "supportCardPay", storeId, e);
        } finally {
            durationEnd(dataMap, "supportCardPay");
        }
        dataMap.put("supportCardPay", supports);
    }


    /**
     * 查询门店或商户下所有的MCC配置
     *
     * @param ownerId
     * @param ownerType
     * @return
     */
    private Map batchFindMcc(String ownerId, OwnerType ownerType) throws Exception {
        BatchFindByOwnerRequest batchFindByOwnerRequest = new BatchFindByOwnerRequest();
        batchFindByOwnerRequest.setAppId(AppId.UFOOD.getAppId());
        batchFindByOwnerRequest.setOwnerType(ownerType.getOwnerType());
        batchFindByOwnerRequest.setOwnerId(ownerId);
        try {
            List<ConfigValue> configValues = configRemoteService.batchFindByOwner(batchFindByOwnerRequest);
            if (CollectionUtils.isNotEmpty(configValues)) {
                resetScanOrderPayTypeByMealType(ownerType, configValues);
                storeCashierModeConfigChange(ownerType, configValues);
                return configValues.stream().collect(Collectors.toMap(ConfigValue::getName, ConfigValue::getValue, (k1, k2) -> k1));
            }
        } catch (Exception e) {
            log.warn("查询mcc配置出错：{}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 区分轻餐围餐并重置扫码点单支付设置的值
     * 轻餐的话，点单支付配置为"order_and_pay"
     * 围餐的话，点单支付配置按配置的值展示
     *
     * @param ownerType
     * @param configValues
     */
    private void resetScanOrderPayTypeByMealType(OwnerType ownerType, List<ConfigValue> configValues) {
        //仅门店
        if (Objects.equals(ownerType, OwnerType.STORE_ID)) {
            //获取就餐类型
            Optional<String> mealTypeOptional = configValues.stream()
                    .filter(it -> Objects.equals(it.getName(), MccUtils.MEAL_TYPE) && BooleanUtils.isTrue(it.getEnabled()))
                    .map(ConfigValue::getValue).findFirst();
            //不存在，不处理，返回默认
            if (!mealTypeOptional.isPresent()) {
                return;
            }
            //获取扫码点单支付方式
            Optional<ConfigValue> scanOrderPayTypeOptional = configValues.stream()
                    .filter(it -> Objects.equals(it.getName(), MccUtils.SCAN_ORDER_PAY_TYPE) && BooleanUtils.isTrue(it.getEnabled())).findFirst();
            //轻餐设置点单+支付
            if (StringUtils.equals(mealTypeOptional.get(), MccUtils.SINGLE)) {
                scanOrderPayTypeOptional.ifPresent((item) -> {
                    item.setValue(MccUtils.ORDER_AND_PAY);
                });
            }
        }

    }


    private void storeCashierModeConfigChange(OwnerType ow, List<ConfigValue> configValues) {
        if (Objects.equals(ow, OwnerType.STORE_ID)) {
            for (ConfigValue it : configValues) {
                if (Objects.equals(it.getName(), MccUtils.CASHIER_MODE) && BooleanUtils.isNotTrue(it.getEnabled())) {
                    it.setValue("0");
                    return;
                }
            }
        }
    }

    /**
     * 查询门店主题
     *
     * @param request
     * @param dataMap
     */
    private void queryStoreThemes(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryStoreThemes");
        if (StringUtils.isBlank(request.getStoreId())) {
            return;
        }
        try {
            List<StoreRenovationResp> renovationResp;
            if (StringUtils.isNotBlank(request.getDraftId())) {
                QueryDraftReq queryDraftReq = new QueryDraftReq();
                queryDraftReq.setDraft_id(request.getDraftId());
                renovationResp = storeRenovationService.getDraft(queryDraftReq);
            } else {
                QueryRenovationsReq queryRenovationsReq = new QueryRenovationsReq();
                queryRenovationsReq.setQuery_store_id(request.getStoreId());
                // 只有开启主题同步时，才会查询两个页面
                if (apolloConfigHelper.getBooleanConfigValueByKey("theme.sync.enable", false)) {
                    queryRenovationsReq.setPage_ids(Arrays.asList("p0o2sfaiwk21", "xgaugooilxg7"));
                } else {
                    queryRenovationsReq.setPage_ids(Collections.singletonList("p0o2sfaiwk21"));
                }
                renovationResp = storeRenovationService.getStoreRenovations(queryRenovationsReq);
            }
            dataMap.put("theme", renovationResp);
        } catch (Exception e) {
            log.warn("门店主题查询出错：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            //setError(dataMap, ReturnCode.STORE_THEMES_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "queryStoreThemes");
        }
    }

    private void queryTemplate(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryTemplate");
        if (StringUtils.isBlank(request.getStoreId())) {
            return;
        }
        try {
            MainTemplateDetail mainTemplateDetail;
            if (!Objects.isNull(request.getTemplateId())) {
                // spa预览
                TemplateDetailRequest templateDetailRequest = new TemplateDetailRequest();
                templateDetailRequest.setTemplateId(request.getTemplateId());
                templateDetailRequest.setVersion(request.getTemplateVersion());
                mainTemplateDetail = templateRemoteService.mainDetail(templateDetailRequest);
            } else if (!Objects.isNull(request.getPreviewId())) {
                // 收钱吧app点击预览，获取门店模板的草稿预览， 根据storeId查询门店的【模板草稿信息】
                mainTemplateDetail = storeTemplateRemoteService.getByDraftId(request.getPreviewId());
            } else {
                // C端小程序获取门店模板，根据storeId查询门店的【模板信息】
                mainTemplateDetail = storeTemplateRemoteService.getByStoreId(request.getStoreId());
            }
            dataMap.put("template", mainTemplateDetail);
        } catch (Exception e) {
            log.warn("门店模板查询出错：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            //setError(dataMap, ReturnCode.STORE_THEMES_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "queryTemplate");
        }
    }

    private void queryTemplateV2(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "queryTemplate");
        if (StringUtils.isBlank(request.getStoreId())) {
            return;
        }
        try {
            com.shouqianba.smart.template.api.dto.template.MainTemplateDetail mainTemplateDetail;
            if (!Objects.isNull(request.getTemplateId())) {
                // spa预览
                com.shouqianba.smart.template.api.dto.template.TemplateDetailRequest templateDetailRequest = new com.shouqianba.smart.template.api.dto.template.TemplateDetailRequest();
                templateDetailRequest.setTemplateId(request.getTemplateId());
                templateDetailRequest.setVersion(request.getTemplateVersion());
                mainTemplateDetail = templateRemoteServiceV2.mainDetail(templateDetailRequest);
            } else if (!Objects.isNull(request.getPreviewId())) {
                // 收钱吧app点击预览，获取门店模板的草稿预览， 根据storeId查询门店的【模板草稿信息】
                mainTemplateDetail = storeTemplateRemoteServiceV2.getByDraftId(request.getPreviewId());
            } else {
                // C端小程序获取门店模板，根据storeId查询门店的【模板信息】
                mainTemplateDetail = storeTemplateRemoteServiceV2.getByStoreId(request.getStoreId());
            }
            dataMap.put("template", mainTemplateDetail);
        } catch (Exception e) {
            log.warn("门店模板查询出错：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            //setError(dataMap, ReturnCode.STORE_THEMES_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "queryTemplate");
        }
    }

    private void getAreas(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "getAreas");
        if (StringUtils.isBlank(request.getStoreId())) {
            return;
        }
        try {
            Integer apiVersion = Optional.ofNullable(request.getApiVersion()).orElse(1);
            RangeAreaDto deliveryAreaDto = deliveryAreaHelper.getAreas(request.getStoreId(), String.valueOf(apiVersion));
            dataMap.put("areas", deliveryAreaDto);
        } catch (Exception e) {
            log.warn("查询可送区域出错：reqId:{},{}", request.getReqId(), e.getMessage(), e);
            setError(dataMap, ReturnCode.DELIVERY_AREA_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "getAreas");
        }

    }
}
