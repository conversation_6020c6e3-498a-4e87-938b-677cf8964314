package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartAndRedeem;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.query.CartSyncQuery;
import com.wosai.smartbiz.oms.api.vos.CartVersionVO;

/**
 * 围餐的购物车服务
 */
@JsonRpcService(value = "/rpc/cart/round")
public interface RoundMealCartService {

    /**
     * 添加购物车
     * @param request
     * @return
     */
    Cart addCart(ApiRequest<CartItemCreate> request) ;

    Cart setCartItem(ApiRequest<CartItemCreate> request) ;

    Cart reduceCart(ApiRequest<CartItemCreate> request) ;

    Cart cleanCart(ApiRequest apiRequest) ;

    CartCheckResultDTO checkItemStatus(ApiRequest apiRequest);

    Cart list(ApiRequest apiRequest) ;

    boolean setCartMustOrderEditable(ApiRequest<CartsRequest> apiRequest);

    CartAndRedeem listAndRedeem(ApiRequest<CartsRequest> apiRequest) ;

    /**
     * 获取收银机版本，返回开台标识
     * @param apiRequest
     * @return
     */
    Result<CartVersionVO> getCartVersion(ApiRequest<CartSyncQuery> apiRequest);
}
