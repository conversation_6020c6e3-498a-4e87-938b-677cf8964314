package com.wosai.pantheon.uf4c.service;

import com.alibaba.fastjson.JSON;
import com.wosai.market.mcc.api.dto.request.StringConfigQueryRequest;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.trade.modal.RedeemCheckControll;
import com.wosai.market.trade.modal.SubscribePayRequest;
import com.wosai.market.trade.service.PayService;
import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.OrderDTO;
import com.wosai.pantheon.order.model.dto.request.OrderItemAddRequest;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.order.utils.OrderUtil;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.PackFeeWrapper;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.EntityConvert;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.MoneyUtil;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.oms.api.query.table.TableQueryRequest;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import com.wosai.smartbiz.payment.api.trade.defs.SubPayWay;
import com.wosai.upay.activity.model.ActivityOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
@Service
public class RedeemService {
    @Autowired
    private OrderHelper orderHelper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CartService cartService;

    @Autowired
    private PayService payService;

    @Autowired
    private TableRpcServiceV2 tableRpcServiceV2;


    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private ConfigRemoteService configRemoteService;

    public RedeemResult getRedeemResult(RedeemRequest redeemRequest, UserContextDTO user) {
        if (apolloConfigHelper.getServiceDegradeConfig().isRedeemQueryDegrade()) {
            return new RedeemResult();
        }
        OrderMealTypeEnum mealType = OrderMealTypeEnum.SINGLE;
        if (Objects.equals(redeemRequest.getDiscountStrategy(), OrderType.EAT_FIRST_ORDER.getMsg()) || Objects.equals(redeemRequest.getDiscountStrategy(), OrderType.PAY_FIRST_TABLE_ORDER.getMsg())) {
            if (Objects.equals(redeemRequest.getDiscountStrategy(), OrderType.PAY_FIRST_TABLE_ORDER.getMsg()) && StringUtils.isNotBlank(redeemRequest.getTableId())) {
                TableQueryRequest tableQueryRequest = new TableQueryRequest();
                tableQueryRequest.setTableId(redeemRequest.getTableId());
                Result<String> tableQueryResult = tableRpcServiceV2.getTableOrderNo(tableQueryRequest);
                if (tableQueryResult.isSuccess()) {
                    redeemRequest.setSn(tableQueryResult.getData());
                }
            }
            //优惠那边不区分轻餐围餐
            redeemRequest.setDiscountStrategy(OrderType.SUBSCRIBE_ORDER.getMsg());
            redeemRequest.setFromCart(false);
            mealType = OrderMealTypeEnum.ROUND_MEAL;

        } else if (Objects.equals(redeemRequest.getDiscountStrategy(), OrderType.PAY_FIRST_ORDER.getMsg())) {
            redeemRequest.setDiscountStrategy(ActivityOrder.GOODS_NAME_CASHIER_ORDER);
        }

        OrderType orderType = orderHelper.getOrderType(redeemRequest.getDiscountStrategy());
        int serviceType = 0;
        if (redeemRequest.getCompatible()) {
            serviceType = CommonUtil.getServiceType(orderType);
        }

        String sn = Optional.ofNullable(redeemRequest.getSn()).orElse(redeemRequest.getOrderSn());
        List<CartItemCreate> itemCreates = null;
        Order order = null;
        //禁止使用储值优惠折扣
        boolean banGiftCartDiscount = false;
        if (CollectionUtils.isNotEmpty(redeemRequest.getRedeemItems())) {
            itemCreates = redeemRequest.getRedeemItems();
            banGiftCartDiscount = redeemRequest.isBanGiftCartDiscount();
            redeemRequest.setDiscountStrategy(OrderType.SUBSCRIBE_ORDER.getMsg());
            redeemRequest.setFromCart(false);
        } else {
            if (org.apache.commons.lang.StringUtils.isNotBlank(sn)) {
                //提交订单页,继续加菜不用检查优惠
                if(redeemRequest.isFromSubmitOrder()){
                    return new RedeemResult();
                }

                OrderDTO orderDTO = orderService.getOrderBySnForRedeem(sn);
                if (orderDTO == null) {
                    throw new BusinessException(ReturnCode.ORDER_FIND_FAIL);
                }
                if (orderDTO.getOrderType() == OrderType.PAY_FIRST_ORDER) {
                    redeemRequest.setDiscountStrategy(Constants.CASHIER_DISCOUNT_STRATEGY);
                }

               if(!needToCheckRedeem(orderDTO)){
                   return new RedeemResult();
               }
                if (orderDTO.getOrderType() == OrderType.PAY_FIRST_TABLE_ORDER) {
                    //先付后吃的情况下，需要判断是否享受优惠
                    redeemRequest.setDiscountStrategy(OrderType.SUBSCRIBE_ORDER.getMsg());
                    StringConfigQueryRequest stringConfigQueryRequest = new StringConfigQueryRequest();
                    stringConfigQueryRequest.setOwnerId(orderDTO.getStoreId());
                    stringConfigQueryRequest.setName("discount_strategy");
                    stringConfigQueryRequest.setAppId(AppId.UFOOD.getAppId());
                    stringConfigQueryRequest.setOwnerType(OwnerType.STORE_ID.getOwnerType());
                    stringConfigQueryRequest.setDefaultValue("only_once");
                    String configValue = configRemoteService.getStringConfig(stringConfigQueryRequest);
                    if (StringUtils.isNotBlank(configValue) && configValue.equals("only_once")) {
                        if (noLongerEnjoyRedeem(orderDTO)) {
                            return new RedeemResult();
                        }
                    }
                    itemCreates = cartService.getItemCreateList(redeemRequest.getStoreId(), redeemRequest.getTableId(), serviceType, mealType);
                } else {
                    order = EntityConvert.convertOrderDTO(orderDTO);

                    if (orderDTO != null && CollectionUtils.isNotEmpty(orderDTO.getRedeems())) {
                        //如果存在本地优惠，那么不能使用储值折扣
                        banGiftCartDiscount = orderDTO.getRedeems().stream().filter(item -> Objects.equals(item.getDiscountType(), OrderRedeemDiscountType.CASHIER_ELIMINATE.getType())
                                || Objects.equals(item.getDiscountType(), OrderRedeemDiscountType.CASHIER_TEMP_DISCOUNT.getType())).findAny().isPresent();
                    }
                    itemCreates = order.getItems();
                }


            } else {
                itemCreates = cartService.getItemCreateList4CartRedeem(redeemRequest.getStoreId(), redeemRequest.getTableId(), serviceType, mealType);
            }
        }
        SubscribePayRequest subscribePayRequest = new SubscribePayRequest();
        if (order != null && CollectionUtils.isNotEmpty(order.getRedeemDetails())) {
            Long totalGiftGoodsDiscount = order.getRedeemDetails().stream()
                    .filter(item -> item.getSubType() == OrderRedeemDiscountType.GIFT_GOODS.getType().intValue())
                    .mapToLong(RedeemResult.RedeemDetail::getDiscountAmount)
                    .sum();
            if (totalGiftGoodsDiscount !=null){
                if (Long.valueOf(redeemRequest.getTotalAmount()) <= totalGiftGoodsDiscount){
                    return addLocalRedeems(null,order);
                }else{
                    subscribePayRequest.setTotalAmount(String.valueOf(Long.valueOf(redeemRequest.getTotalAmount()) - totalGiftGoodsDiscount));
                }
            }else{
                subscribePayRequest.setTotalAmount(redeemRequest.getTotalAmount());
            }


        }else{
            subscribePayRequest.setTotalAmount(redeemRequest.getTotalAmount());
        }


        subscribePayRequest.setPayerUid(user.getThirdpartyUserId());
        subscribePayRequest.setUserId(user.getUserId());
        subscribePayRequest.setStoreId(redeemRequest.getStoreId());
        subscribePayRequest.setMerchantId(redeemRequest.getMerchantId());
        subscribePayRequest.setDiscountStrategy(redeemRequest.getDiscountStrategy());
        subscribePayRequest.setTerminalSn(redeemRequest.getTerminalSn());
        subscribePayRequest.setCombinedPayment(redeemRequest.getCombinedPayment());
        subscribePayRequest.setMkCustomInfo(redeemRequest.getMkCustomInfo());
        subscribePayRequest.setActivityBizExt(buildActivityBizExt(redeemRequest.getStoreId(),orderType,Long.valueOf(redeemRequest.getTotalAmount()),redeemRequest.getUsingPayTools()));

        RedeemCheckControll redeemCheckControll = new RedeemCheckControll();
        if (banGiftCartDiscount) {
            redeemCheckControll.setBanGiftCardDiscount(true);
        } else {
            redeemCheckControll.setBanGiftCardDiscount(false);
            redeemCheckControll.setFromCart(redeemRequest.isFromCart());
            redeemCheckControll.setInterestId(redeemRequest.getRechargeInterestId());
        }

        subscribePayRequest.setRedeemCheckControll(redeemCheckControll);

        if (redeemRequest.isRechargeAndPay()) {
            subscribePayRequest.setPayway(PayWay.CARD.getCode() + "");
            subscribePayRequest.setSubPayway(SubPayWay.BARCODE.getCode() + "");
        } else {
            subscribePayRequest.setPayway(redeemRequest.getPayway() + "");
            subscribePayRequest.setSubPayway(redeemRequest.getSubPayway() + "");
        }


        subscribePayRequest.setItems(
                Optional.ofNullable(itemCreates)
                        .map(items -> items.stream()
                                .filter(i -> {
                                    if (TagUtil.hasTag(i.getItem().getItemTag(), OrderGoodsTagEnum.GIFT_FOOD.getValue())) {
                                        return false;
                                    }
                                    if (CollectionUtils.isEmpty(redeemRequest.getRedeemItems())) {
                                        if (i.getProcessStatus() != null) {
                                            if (i.getProcessStatus() == GoodsProcessStatus.ACCEPTED || i.getProcessStatus() == GoodsProcessStatus.PART_RETURNED) {
                                                return true;
                                            } else {
                                                return false;
                                            }
                                        }
                                    }
                                    return true;
                                })
                                .map(i -> OrderHelper.convertCartItem2CreateRequest(i))
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        // 补充打包费信息
        if (!redeemRequest.isFromCart() && (Optional.ofNullable(redeemRequest.getPacked()).orElse(false) || OrderType.TAKE_OUT_ORDER == orderType
                || OrderType.PRE_ORDER == orderType)) {
            PackFeeWrapper wrapper = orderHelper.computePackAmount(redeemRequest.getStoreId(), orderType, itemCreates);
            if (wrapper != null) {
                // 整单打包费
                if (Objects.equals(wrapper.getPackFeeType(), 0)) {
                    subscribePayRequest.setOrderTag(TagUtil.addTag(0L, OrderTagEnum.ORDER_PACKAMOUNT.getValue()));
                    subscribePayRequest.setPackAmount(wrapper.getPackFee());
                }
                // 商品打包费
                if (Objects.equals(wrapper.getPackFeeType(), 1)) {
                    Map<String, Integer> packFeeMap = wrapper.getPackFeeMap();
                    if (CollectionUtils.isNotEmpty(subscribePayRequest.getItems()) && MapUtils.isNotEmpty(packFeeMap)) {
                        for (OrderItemAddRequest item : subscribePayRequest.getItems()) {
                            Integer i = packFeeMap.get(item.getItemId());
                            if (i != null && i > 0) {
                                item.setPackFee(Long.valueOf(i));
                            }
                        }
                    }
                }
            }
        }

        RedeemResult redeemResult = payService.getRedeemResultForSubscribe(subscribePayRequest);
        if (order != null && CollectionUtils.isNotEmpty(order.getRedeemDetails())) {
            redeemResult = addLocalRedeems(redeemResult,order);
        }
        return redeemResult;
    }
    private RedeemResult addLocalRedeems(RedeemResult redeemResult, Order order){
        Long totalDiscount = order.getRedeemDetails().stream().mapToLong(RedeemResult.RedeemDetail::getDiscountAmount).sum();
        if (redeemResult == null) {
            redeemResult = new RedeemResult();
        }
        redeemResult.setTotalDiscount(Optional.ofNullable(redeemResult.getTotalDiscount()).orElse(0L) + totalDiscount);
        if (CollectionUtils.isEmpty(redeemResult.getRedeemDetails())) {
            redeemResult.setRedeemDetails(new ArrayList<>());
        }
        redeemResult.getRedeemDetails().addAll(order.getRedeemDetails());
        return redeemResult;
    }

    public Map buildActivityBizExt(String storeId,OrderType orderType,Long totalAmount,Object usingPayTools){
        if (usingPayTools == null){
            return null;
        }
        Map<String, Object> acquiringInfo = new HashMap<>();
        String tradeApp = orderHelper.getTradeApp(storeId,orderType);
        acquiringInfo.put("tradeApp",tradeApp);
        acquiringInfo.put("usingPayTools",usingPayTools);
        acquiringInfo.put("acquiringAmount",totalAmount);

        Map<String, Object> map = new HashMap<>();
        map.put("acquiringInfo",acquiringInfo);
        return map;
    }


    /**
     * 加购并优惠的优惠检查request构建
     *
     * @param cartItemCreate
     * @param cart
     * @return
     */
    public static RedeemRequest buildRedeemRequest(CartItemCreate cartItemCreate, Cart cart, boolean fromCart) {
        RedeemRequest redeemRequest = buildRedeemRequest(cartItemCreate);

        redeemRequest.setTotalAmount(String.valueOf(cart.getTotalPrice()));

        //设置来自于购物车
        redeemRequest.setFromCart(fromCart);
        return redeemRequest;
    }

    public static RedeemRequest buildRedeemRequest(CartItemCreate cartItemCreate) {
        RedeemRequest redeemRequest = new RedeemRequest();
        redeemRequest.setDiscountStrategy(cartItemCreate.getDiscountStrategy());
        redeemRequest.setCompatible(Optional.ofNullable(cartItemCreate.getCompatible()).orElse(true));
        redeemRequest.setOrderSn(cartItemCreate.getOrderSn());
        redeemRequest.setStoreId(cartItemCreate.getStoreId());
        redeemRequest.setMerchantId(cartItemCreate.getMerchantId());
        redeemRequest.setTableId(cartItemCreate.getTableId());
        redeemRequest.setTerminalSn(cartItemCreate.getTerminalSn());
        redeemRequest.setPayway(cartItemCreate.getPayway());
        redeemRequest.setSubPayway(cartItemCreate.getSubPayway());
        redeemRequest.setMkCustomInfo(cartItemCreate.getMkCustomInfo());

        return redeemRequest;
    }


    public RedeemResult getRedeemResult(CartItemCreate cartItemCreate, Cart cart, boolean fromCart, UserContextDTO user) {
        try {
            return getRedeemResult(buildRedeemRequest(cartItemCreate, cart, fromCart), user);
        } catch (Exception e) {
            log.warn("加购后计算优惠异常",
                    keyValue("method", "addCartAndGetRedeem"),
                    keyValue("message", JSON.toJSONString(cartItemCreate)),
                    e);
            return null;
        }
    }


    public RedeemResult getRedeemResult(CartsRequest cartsRequest, Cart cart, boolean fromCart, UserContextDTO user) {
        try {
            RedeemRequest redeemRequest = new RedeemRequest();
            redeemRequest.setDiscountStrategy(cartsRequest.getServiceTypeName());
            redeemRequest.setCompatible(true);
            redeemRequest.setOrderSn(cartsRequest.getOrderSn());
            redeemRequest.setStoreId(cartsRequest.getStoreId());
            redeemRequest.setMerchantId(cartsRequest.getMerchantId());
            redeemRequest.setTableId(cartsRequest.getTableId());
            redeemRequest.setTerminalSn(cartsRequest.getTerminalSn());
            redeemRequest.setPayway(cartsRequest.getPayway());
            redeemRequest.setSubPayway(cartsRequest.getSubPayway());
            redeemRequest.setMkCustomInfo(cartsRequest.getMkCustomInfo());
            redeemRequest.setTotalAmount(String.valueOf(cart.getTotalPrice()));
            redeemRequest.setFromCart(fromCart);
            redeemRequest.setRechargeAndPay(cartsRequest.isRechargeAndPay());
            redeemRequest.setRechargeInterestId(cartsRequest.getRechargeInterestId());

            return getRedeemResult(redeemRequest, user);
        } catch (Exception e) {
            log.warn("获取购物车后计算优惠异常",
                    keyValue("method", "getCartAndGetRedeem"),
                    keyValue("message", JSON.toJSONString(cartsRequest)),
                    e);
            return null;
        }
    }

    /**
     * 需要检查营销优惠
     * @param orderDTO
     * @return
     */
    public boolean needToCheckRedeem(OrderDTO orderDTO) {
        if(orderDTO == null){
            return false;
        }
        if(orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER) {
            if (TagUtil.hasTag(orderDTO.getOrderTag(), OrderTagEnum.EVER_FRONT_COUPON_PAID.getValue())) {
                return !noLongerEnjoyRedeem(orderDTO);
            }
            Long receiveAmount = MoneyUtil.getNotNullAmount(orderDTO.getReceiveAmount());
            if(receiveAmount == 0){
                //首次支付的零元商品可以继续检查优惠
                return !TagUtil.hasTag(orderDTO.getOrderTag(), OrderTagEnum.ZERO_PAID_WITH_REDEEM.getValue());
            }
            //其他情况不需要检查优惠
            return false;
        }
        return true;
    }

    /**
     * @description 后续不再享受优惠
     * @param orderDTO
     * @return
     **/
    private boolean noLongerEnjoyRedeem(OrderDTO orderDTO) {
        if (TagUtil.hasTag(orderDTO.getOrderTag(), OrderTagEnum.EVER_FRONT_COUPON_PAID.getValue())) {
            if (CollectionUtils.isEmpty(orderDTO.getRedeems())) {
                return false;
            }
            //是否含有除了前置券优惠以外的优惠
            return orderDTO.isHasOthersRedeemsExcludeFront();
        }
        return MoneyUtil.getNotNullAmount(orderDTO.getMerchantDiscountTotalAmount()) > 0;
    }

}
