package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.uf4c.model.jielong.*;
import com.wosai.pantheon.uf4c.service.JielongHelper;
import com.wosai.pantheon.uf4c.service.RedeemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(path = "/api/v1/jielong", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class JielongController {


    @Autowired
    CartController cartController;

    @Autowired
    private RedeemService redeemService;

    @Autowired
    private JielongHelper jielongHelper;


    /**
     * 通过接龙id查询接龙首页
     *
     * @param jielongId
     * @return
     */
    @GetMapping("/query")
    public Map<String, Object> queryJielongInfo(@RequestParam("jielong_id") Integer jielongId) {
        if (jielongId == null || jielongId <= 0) {
            return null;
        }
        return jielongHelper.queryJielongPage(jielongId);
    }

    /**
     * 加减购物车
     *
     * @param create
     * @return
     */
    @PostMapping("/addCart")
    public JielongCart addCart(@RequestBody @Valid JielongCartItem create) {
        return jielongHelper.addorReduceCart(create);
    }


    /**
     * 下单页面校验
     *
     * @param request
     * @return
     */
    @PostMapping("/check")
    public PayCheckResponse check(@RequestBody @Valid JielongCheckRequest request) {
        return jielongHelper.getCartAndCalcPrice(request);
    }


    /**
     * 群接龙订单下单
     *
     * @param request
     * @return
     */
    @PostMapping("/pay")
    public PayResult pay(@RequestBody @Valid JielongPayRequest request) {
        return jielongHelper.jielongPay(request);
    }

    /**
     * 群接龙页面分页查询历史订单
     *
     * @param request
     * @return
     */
    @PostMapping("/orderList")
    public List<JielongPage.OrderInfo> orderList(@RequestBody @Valid JielongOrderQuery request) {
        return jielongHelper.orderList(request);
    }

    /**
     * 支付成功后，用orderSn查订单信息
     *
     * @param orderSn
     * @return
     */
    @GetMapping("/getOrderBySn")
    public JielongPage.OrderInfo getOrderBySn(@RequestParam("order_sn") String orderSn) {
        return jielongHelper.getOrderBySn(orderSn);
    }


}
