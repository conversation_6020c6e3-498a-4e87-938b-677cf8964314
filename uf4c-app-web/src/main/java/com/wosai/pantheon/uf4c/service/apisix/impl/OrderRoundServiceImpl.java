package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.smart.translation.manager.common.utils.LogUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.market.jjz.dataworks.api.UfoodFeeService;
import com.wosai.market.mcc.api.dto.request.BooleanConfigQueryRequest;
import com.wosai.market.mcc.api.dto.request.StringConfigQueryRequest;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.tethys.api.dto.request.storedCard.AchieveRedeemCodeRequest;
import com.wosai.market.tethys.api.dto.response.storedCardResponse.RedeemCode;
import com.wosai.market.tethys.api.service.StoredCardRemoteService;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.market.trade.modal.SharingAndTerminalChooseRequest;
import com.wosai.market.trade.modal.SharingAndTerminalChooseResponse;
import com.wosai.market.trade.modal.upayGateway.PrePayResult;
import com.wosai.market.trade.service.PayService;
import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.OrderRedeemDTO;
import com.wosai.pantheon.order.model.dto.v2.OrderGoodsDTO;
import com.wosai.pantheon.order.model.dto.v2.OrderMainDTO;
import com.wosai.pantheon.order.service.v2.OrderGoodsService;
import com.wosai.pantheon.order.service.v2.OrderMainService;
import com.wosai.pantheon.order.service.v2.OrderRedeemV2Service;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.dto.InitRequest;
import com.wosai.pantheon.uf4c.model.dto.PayFirstRoundOrderPayRequest;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import com.wosai.pantheon.uf4c.model.payfirsttableorder.ItemsAndRedeem;
import com.wosai.pantheon.uf4c.service.CartService;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.service.RedeemService;
import com.wosai.pantheon.uf4c.service.TranslationConvertService;
import com.wosai.pantheon.uf4c.service.apisix.OrderRoundService;
import com.wosai.pantheon.uf4c.service.apisix.TableService;
import com.wosai.pantheon.uf4c.util.*;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.MoneyUtil;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.oms.api.domain.HsPreCreateOrderVO;
import com.wosai.smartbiz.oms.api.enums.MiniSource;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.oms.api.pojo.*;
import com.wosai.smartbiz.oms.api.query.CartSyncQuery;
import com.wosai.smartbiz.oms.api.query.table.TableQueryRequest;
import com.wosai.smartbiz.oms.api.services.OrderRpcService;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import com.wosai.smartbiz.payment.api.trade.defs.SubPayWay;
import com.wosai.smartbiz.payment.api.trade.entity.UpayProfitSharing;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.util.Base64;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class OrderRoundServiceImpl implements OrderRoundService {

    @Autowired
    private PrePayUtil prePayUtil;
    @Autowired
    private com.wosai.smartbiz.oms.api.services.CartService roundMealCartService;
    @Autowired
    private CartService cartService;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private OrderRpcService orderRpcService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private StoredCardRemoteService storedCardRemoteService;
    @Autowired
    private TableRpcServiceV2 tableRpcService;
    @Autowired
    private ConfigRemoteService configRemoteService;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private PayService marketTradePayService;
    @Autowired
    private OrderMainService awesomeOrderOrderMainService;

    @Autowired
    private OrderGoodsService orderGoodsService;

    @Autowired
    private RedeemService redeemService;

    @Autowired
    private TableRpcServiceV2 tableRpcServiceV2;

    @Autowired
    private OrderRedeemV2Service orderRedeemV2Service;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private TranslationConvertService translationConvertService;

    @Autowired
    private TableService tableService;

    @Value("${callback.profitSharingCallback}")
    private String profitSharingCallback;

    @Value("${mini.zero.pay.gray.enable:false}")
    private boolean miniZeroPayEnable;

    @Value("${senable.ntag.upload:false}")
    private boolean enableNtagUpload;


    @Override
    @SneakyThrows
    public OrderMainWrapper init(ApiRequest<InitRequest> apiRequest) {
        InitRequest request = apiRequest.getBody();
        Integer serviceType = (null != request.getServiceType()) ? request.getServiceType() : 0;

        if(!tableService.canContinueOrder(request.getTableId())){
            throw new BusinessException(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE);
        }

        //判断你是否需要验证购物车商品,如果不需要，说明是旧版接口，旧版接口有单独check方法，这里就不用再次check了
        if (request.isCheckCartItem()) {
            if (org.apache.commons.lang3.StringUtils.isBlank(request.getTableId())) {
                throw new ParamException("桌台信息不能为空");
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(request.getStoreId())) {
                throw new ParamException("门店信息不能为空");
            }
            //检查购物车商品状态
            CartCheckResultDTO cartCheckResult = cartService.checkItemStatus(request.getTableId(), request.getStoreId(), serviceType, OrderMealTypeEnum.ROUND_MEAL);
            if (!cartCheckResult.isSuccess()) {
                return OrderMainWrapper.builder()
                        .cartCheckResult(cartCheckResult)
                        .build();
            }
        }

        // 检查必选商品
        List<CartItemCreate> items = cartService.getItemCreateList(request.getStoreId(), request.getTableId(), null, OrderMealTypeEnum.ROUND_MEAL);
        orderHelper.checkMustCategory(items, request.getStoreId(), OrderType.EAT_FIRST_ORDER);

        Map store = storeService.getStore(request.getStoreId());

        BooleanConfigQueryRequest booleanConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, request.getStoreId(), Constants.CASHIER_MODE_CONFIG_KEY, false);
        boolean isCashierMode = configRemoteService.getBooleanConfig(booleanConfigQueryRequest);

        com.wosai.smartbiz.oms.api.query.PayRequest payRequest = new com.wosai.smartbiz.oms.api.query.PayRequest();
        payRequest.setTableId(request.getTableId());
        payRequest.setTableNo(request.getTableNo());

        payRequest.setOrderSource(OrderSource.MINI);

        if (ThreadLocalHelper.getMiniProgramType() == MiniProgramType.ALIPAY) {
            payRequest.setMiniSource(MiniSource.ALIPAY);
        } else {
            payRequest.setMiniSource(MiniSource.WECHAT);
        }

        payRequest.setUserId(ThreadLocalHelper.getUserId());
        payRequest.setIsRoundMeal(true);
        payRequest.setPreReduceStock(true);

        payRequest.setRemark(request.getRemark());
        payRequest.setTotalAmount(request.getTotalAmount());

        if (isCashierMode) {
            payRequest.setCashierMode(YesNoEnum.Y);
        } else {
            payRequest.setCashierMode(YesNoEnum.N);
        }

        payRequest.setUserName(request.getUserName());
        payRequest.setUserIcon(request.getUserIcon());
        // 订单初始化的时候的消费者信息
        Map<Object, Object> extraMap = new HashMap<>();
        UserContextDTO user = ThreadLocalHelper.getUserNotThrowExcetpion();
        if (user != null) {
            extraMap.put("initialChannelUserId", user.getThirdpartyUserId());
            String initialAppId = Optional.ofNullable(user.getWeixinAppId())
                    .orElseGet(user::getAlipayAppId);
            if (initialAppId != null) {
                extraMap.put("initialAppId", initialAppId);
            }
        }
        Map alipayNOrderInfo = NtagHelper.getNtagInfo();
        if(Objects.nonNull(alipayNOrderInfo) && enableNtagUpload){
            extraMap.put("alipayNOrderInfo", alipayNOrderInfo);
        }
        payRequest.setExtraInfo(extraMap);

        // 查商户信息
        payRequest.setBiz_store_id(request.getStoreId());
        payRequest.setStoreSn(BeanUtil.getPropString(store, Store.SN));
        payRequest.setStoreName(BeanUtil.getPropString(store, Store.NAME));
        payRequest.setMerchant_id(BeanUtil.getPropString(store, Store.MERCHANT_ID));
        payRequest.setMerchantSn(BeanUtil.getPropString(store, "merchant_sn"));


        List<ShoppingCartGoodsDTO> goodsList = request.getItems().stream()
                .map(item -> CartHelper.convert2RoundGoods(item)).collect(Collectors.toList());
        payRequest.setGoods(goodsList);
        // 初始化订单
        Result<OrderMainWrapper> result = orderRpcService.init(payRequest);
        if (!result.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
        }

        OrderMainWrapper orderMainWrapper = result.getData();
        if (null != orderMainWrapper.getCartCheckResult() && !orderMainWrapper.getCartCheckResult().isSuccess()) {
            List<CartCheckResultDTO.Item> checkFailList = orderMainWrapper.getCartCheckResult().getCheckFailList();
            //清空购物车商品
            if (CollectionUtils.isNotEmpty(checkFailList)) {
                checkFailList.stream().forEach(item -> {
                    cartService.removeItem(request.getStoreId(), request.getTableId(), item.getItemUid(), serviceType, OrderMealTypeEnum.ROUND_MEAL);
                });
            }

            //这里预扣库存后需要兼容老版本接口返回，老版本接口只需要给错误提示就行，新版本需要返回CartCheckResultDTO对象
            if (!request.isCheckCartItem()) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商品库存不足，请重新选购");
            }
        }

        return orderMainWrapper;
    }

    @Override
    public boolean cancelBatchGoods4PayFirstRoundOrder(PayFirstRoundOrderPayRequest request) {
        Result<Boolean> result = orderRpcService.cancelBatchGoods4PayFirstRoundOrder(request.getSn(),request.getBatchNo());
        if (!result.isSuccess()){
            throw new com.wosai.smartbiz.base.exceptions.BusinessException(result.getErrorMsg());
        }

        return result.getData();
    }

    @Override
    public ItemsAndRedeem getOrderGoodsAndRedeem(PayFirstRoundOrderPayRequest request) {

        String acceptLanguage = HttpRequestUtil.getAcceptLanguage(httpServletRequest);
        ItemsAndRedeem itemsAndRedeem = new ItemsAndRedeem();
        UserContextDTO user = ThreadLocalHelper.getUser();
        String sn = Optional.ofNullable(request.getSn()).orElse(request.getOrderSn());

        if (StringUtils.isEmpty(sn)){
            String tableId = request.getTableId();
            if (StringUtils.isEmpty(tableId)){
                throw new ParamException("订单号和桌台信息不能同时为空");
            }
            TableQueryRequest tableQueryRequest = new TableQueryRequest();
            tableQueryRequest.setTableId(request.getTableId());
            Result<String> tableNoResult = tableRpcServiceV2.getTableOrderNo(tableQueryRequest);
            if (!tableNoResult.isSuccess()){
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, tableNoResult.getErrorMsg());
            }
            sn = tableNoResult.getData();
        }
        //用来存储最终返回给前端的商品信息（不包含购物车中的商品）
        List<CartItemCreate> finalItems = new ArrayList<>();
        //用来存储所有的商品信息（包含购物车中的商品）
        List<CartItemCreate> allItems = new ArrayList<>();
        Long curTime = System.currentTimeMillis();
        //这种情况是首次下单，没有订单号，也没有桌台信息，那么就是购物车的数据
        if (StringUtils.isEmpty(request.getBatchNo()) && !request.isAllPay()){
            //如果不是某一批次的二次支付，那么购物车的数据也是要展示的
            List<CartItemCreate> items = null;

            CartSyncQuery cartSyncQuery = new CartSyncQuery();
            cartSyncQuery.setTableId(request.getTableId());
            Result<CartInfoDTO> cartInfoDTOResult = roundMealCartService.checkAndGetCartInfo(cartSyncQuery);
            if (cartInfoDTOResult.isSuccess()) {
                CartInfoDTO cartInfoDTO = cartInfoDTOResult.getData();
                translationConvertService.convertRoundCartItems(cartInfoDTO, request.getStoreId(), acceptLanguage);
                itemsAndRedeem.setCart(com.wosai.pantheon.uf4c.util.CartHelper.convertCart(cartInfoDTO));

                List<ShoppingCartItemDTO> shoppingCartItems = cartInfoDTO.getShoppingCartItems();
                if (!org.springframework.util.CollectionUtils.isEmpty(shoppingCartItems)) {
                    items = new ArrayList<>();
                    for (ShoppingCartItemDTO item : cartInfoDTO.getShoppingCartItems()) {
                        ShoppingCartGoodsDTO goods = item.getShoppingCartGoods();
                        if (goods.getSaleCount().compareTo(BigDecimal.ZERO) > 0) {
                            CartItemCreate create =cartService.convert2ItemCreate(goods,request.getStoreId(),request.getTableId());
                            items.add(create);
                        }

                    }
                }
            }
            if (CollectionUtils.isNotEmpty(items)){

                items.stream().forEach(item -> {
                    item.setOrderTime(curTime);
                    BigDecimal saleCount = Optional.ofNullable(item.getItem().getNumberDecimal()).orElse(new BigDecimal(item.getItem().getNumber()));
                    saleCount = saleCount.subtract(Optional.ofNullable(item.getItem().getReturnNumber()).map(returnNumber -> new BigDecimal(returnNumber)).orElse(BigDecimal.ZERO));
                    item.getItem().setTotalAmount(saleCount.multiply(new BigDecimal(item.getItem().getPrice())).setScale(0, RoundingMode.HALF_UP).longValue());
                });
                allItems.addAll(items);
            }
        }

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(sn)){

            Result<List<OrderGoodsDTO>> goodsQueryResult = orderGoodsService.selectByOrderSnAndRefPayType(sn,GoodsRefPayType.PAY);

            if (!goodsQueryResult.isSuccess()){
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, goodsQueryResult.getErrorMsg());
            }

            List<OrderGoodsDTO> orderGoodsDTOList = goodsQueryResult.getData();

            if (CollectionUtils.isNotEmpty(orderGoodsDTOList)){
                //收银机端未支付的商品是必须要展示的
                //如果指定了批次号，那么需要展示指定批次号的商品
                finalItems.addAll(orderGoodsDTOList.stream()
                        .filter(goods -> {
                            if (TagUtil.hasTag(goods.getGoodsTag(), OrderGoodsTagEnum.PAY_FIRST_TABLE_ORDER_NOT_PAY_GOODS.getValue())){
                                return true;
                            }

                            if (goods.getProcessStatus() == GoodsProcessStatus.NOT_ACCEPTED){
                                return true;
                            }

                            return false;
                        })
                        .map(item -> EntityConvert.convertOrderItemDto2CartItemCreate(item,null,false))
                        .collect(Collectors.toList()));

                allItems.addAll(finalItems);
            }

           

        }

        Long notReportedDiscountAmount = 0L;
        boolean isNTagInfoSaved = false;
        if (CollectionUtils.isNotEmpty(allItems)){
            if (org.apache.commons.lang3.StringUtils.isNotBlank(sn)) {
                //已经存在订单的情况下，判断下是否可以继续享受优惠
                Result<OrderMainDTO> orderMainDTOResult = awesomeOrderOrderMainService.getBySn(sn);
                if (orderMainDTOResult.isSuccess()) {
                    OrderMainDTO orderMainDTO = orderMainDTOResult.getData();
                    StringConfigQueryRequest stringConfigQueryRequest = new StringConfigQueryRequest();
                    stringConfigQueryRequest.setOwnerId(orderMainDTO.getStoreId());
                    stringConfigQueryRequest.setName("discount_strategy");
                    stringConfigQueryRequest.setAppId(AppId.UFOOD.getAppId());
                    stringConfigQueryRequest.setOwnerType(OwnerType.STORE_ID.getOwnerType());
                    stringConfigQueryRequest.setDefaultValue("only_once");
                    String configValue = configRemoteService.getStringConfig(stringConfigQueryRequest);
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(configValue) && configValue.equals("only_once")) {
                        if (noLongerEnjoyRedeem(orderMainDTO)) {
                            itemsAndRedeem.setRedeem(new RedeemResult());
                            // 只允许享受一次优惠的,已经享受过优惠了,这里显式告知不要使用优惠了
                            itemsAndRedeem.setDisableRedeem(Boolean.TRUE);
                        }
                    }
                    isNTagInfoSaved = TagUtil.hasTag(orderMainDTO.getOrderTag(), OrderTagEnum.N_TAG_INFO_SAVED.getValue());
                }
            }

            if (itemsAndRedeem.getRedeem() == null) {

                List<CartItemCreate> redeemItems = allItems.stream()
                        .map(item -> {
                            if (item.getProcessStatus() == GoodsProcessStatus.RETURNED){
                                return null;
                            }
                            if (TagUtil.hasTag(item.getItem().getItemTag(),OrderGoodsTagEnum.GIFT_FOOD.getValue())){
                                return null;
                            }
                            BigDecimal saleCount = Optional.ofNullable(item.getItem().getNumberDecimal()).orElse(new BigDecimal(item.getItem().getNumber()));
                            saleCount = saleCount.subtract(Optional.ofNullable(item.getItem().getReturnNumber()).map(BigDecimal::new).orElse(BigDecimal.ZERO));
                            Long totalAmount = saleCount.multiply(new BigDecimal(item.getItem().getPrice())).setScale(0, RoundingMode.HALF_UP).longValue();
                            item.getItem().setTotalAmount(totalAmount);

                            return item;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(redeemItems)) {

                    RedeemRequest redeemRequest = RedeemService.buildRedeemRequest(allItems.get(0));

                    redeemRequest.setDiscountStrategy(OrderType.PAY_FIRST_TABLE_ORDER.getMsg());
                    redeemRequest.setRedeemItems(redeemItems);
                    redeemRequest.setMerchantId(request.getMerchantId());
                    redeemRequest.setStoreId(request.getStoreId());
                    Long totalAmount = redeemItems.stream().mapToLong(item -> item.getItem().getTotalAmount()).sum();
                    redeemRequest.setTotalAmount(String.valueOf(totalAmount));
                    redeemRequest.setPayway(request.getPayWay());
                    redeemRequest.setMkCustomInfo(request.getMkCustomInfo());
                    redeemRequest.setUsingPayTools(request.getUsingPayTools());

                    RedeemResult redeemResult = redeemService.getRedeemResult(redeemRequest, user);
                    itemsAndRedeem.setRedeem(redeemResult);
                }
            }
            

            if (CollectionUtils.isNotEmpty(finalItems)){
                //只有存在未支付的商品时，才取未上报的优惠
                Result<List<OrderRedeemDTO>> redeemResult = orderRedeemV2Service.getOrderRedeemListByOrderSn(sn);
                if (redeemResult.isSuccess() && CollectionUtils.isNotEmpty(redeemResult.getData())) {
                    //未上报的优惠信息
                    List<OrderRedeemDTO> notReportedRedeems = redeemResult.getData().stream()
                            .filter(redeem -> org.apache.commons.lang3.StringUtils.isEmpty(MapUtils.getString(redeem.getExtra(), com.wosai.pantheon.order.constant.Constants.OrderRedeemExtraKey.EXTRA_INFO_ITEM_RELATE_PAY_CLIENT_SN)))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(notReportedRedeems)) {
                        if (request.isNeedLocalRedeems()) {
                            List<RedeemResult.RedeemDetail> notReportedRedeemDetails = notReportedRedeems.stream().map(item -> {
                                RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                                redeemDetail.setName(item.getName());
                                redeemDetail.setType(item.getType());
                                redeemDetail.setMessage(item.getName());
                                redeemDetail.setDiscountAmount(item.getDiscountAmount());
                                redeemDetail.setSubType(item.getDiscountType());
                                return redeemDetail;
                            }).collect(Collectors.toList());
                            RedeemResult redeem = itemsAndRedeem.getRedeem();
                            Long totalDiscount = notReportedRedeemDetails.stream().mapToLong(RedeemResult.RedeemDetail::getDiscountAmount).sum();
                            if (redeem == null) {
                                redeem = new RedeemResult();
                            }
                            redeem.setTotalDiscount(Optional.ofNullable(redeem.getTotalDiscount()).orElse(0L) + totalDiscount);
                            if (org.apache.commons.collections.CollectionUtils.isEmpty(redeem.getRedeemDetails())) {
                                redeem.setRedeemDetails(new ArrayList<>());
                            }
                            redeem.getRedeemDetails().addAll(notReportedRedeemDetails);

                            itemsAndRedeem.setRedeem(redeem);
                        }else{
                            notReportedDiscountAmount += notReportedRedeems.stream().mapToLong(OrderRedeemDTO::getDiscountAmount).sum();
                        }
                    }
                }
            }

        }

        if(org.apache.commons.lang3.StringUtils.isNotBlank(sn) && !isNTagInfoSaved && enableNtagUpload){
            Map alipayNOrderInfo = NtagHelper.getNtagInfo();
            if(alipayNOrderInfo != null ){
                //如果存在支付宝N订单号，但是没有保存N订单号信息，那么需要保存
                awesomeOrderOrderMainService.saveNTagInfo(sn, alipayNOrderInfo);
            }
        }

        if(!org.springframework.util.CollectionUtils.isEmpty(finalItems)){
            //先付后吃， 需要返回商品的分组信息
            Map<Long, List<CartItemCreate>> batchMap = finalItems.stream().collect(Collectors.groupingBy(item -> Optional.ofNullable(item.getOrderTime()).orElse(0L)));
            String finalSn = sn;
            List<Order.GoodsBatchInfo> goodsBatchInfos = batchMap.entrySet().stream().map(entry -> {
                Order.GoodsBatchInfo goodsBatchInfo = new Order.GoodsBatchInfo();
                if (entry.getKey() != null && entry.getKey() > 0) {
                    goodsBatchInfo.setOrderTime(entry.getKey());
                }


                CartItemCreate item = entry.getValue().get(0);
                goodsBatchInfo.setSn(finalSn);
                goodsBatchInfo.setName(MapUtils.getString(item.getExtraInfo(), com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.EXTRA_KEY_USER_NAME));
                goodsBatchInfo.setIcon(MapUtils.getString(item.getExtraInfo(), com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.EXTRA_KEY_USER_ICON));

                goodsBatchInfo.setRemark(MapUtils.getString(item.getExtraInfo(), com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.EXTRA_KEY_GOODS_BATCH_REMARK));

                if (item.getProcessStatus() == GoodsProcessStatus.NOT_ACCEPTED) {
                    goodsBatchInfo.setStatusDescription("");
                    goodsBatchInfo.setAllowCancel(true);
                } else if (item.getProcessStatus() == GoodsProcessStatus.PAYED_BUT_NOT_ACCEPTED) {
                    goodsBatchInfo.setStatusDescription("已下单，等待商家接单");
                }else if (item.getProcessStatus() == GoodsProcessStatus.ACCEPT_FAILED || item.getProcessStatus() == GoodsProcessStatus.REJECTED) {
                    goodsBatchInfo.setStatusDescription("已下单，商家接单失败");
                }else if (item.getProcessStatus() == GoodsProcessStatus.CANCELED) {
                    goodsBatchInfo.setStatusDescription("已取消");
                }else if (item.getProcessStatus() == null) {
                    goodsBatchInfo.setStatusDescription("待下单");
                }else if (item.getProcessStatus() == GoodsProcessStatus.ACCEPTED) {
                    if (TagUtil.hasTag(item.getItem().getItemTag(),OrderGoodsTagEnum.PAY_FIRST_TABLE_ORDER_NOT_PAY_GOODS.getValue())
                            && !TagUtil.hasTag(item.getItem().getItemTag(),OrderGoodsTagEnum.GIFT_FOOD.getValue())){
                        goodsBatchInfo.setStatusDescription("待支付");
                    }else {
                        goodsBatchInfo.setStatusDescription("商家已接单，请等候上菜");
                    }
                }else{
                    goodsBatchInfo.setStatusDescription("商家已接单，请等候上菜");
                }
                return goodsBatchInfo;
            }).collect(Collectors.toList());
            itemsAndRedeem.setGoodsBatchInfos(goodsBatchInfos);
        }

        Long totalAmount = 0L;
        if (CollectionUtils.isNotEmpty(allItems)){
            totalAmount = allItems.stream().mapToLong(item -> item.getItem().getTotalAmount()).sum();
        }
        itemsAndRedeem.setOriginalAmount(totalAmount);
        itemsAndRedeem.setTradeApp(orderHelper.getTradeApp4RoundMeal(request.getTradeScene()));
        itemsAndRedeem.setAmountComposition(orderHelper.getRoundMealAmountComposition(totalAmount));
        itemsAndRedeem.setNeedPayAmount(totalAmount - Optional.ofNullable(itemsAndRedeem.getRedeem()).map(redeemResult -> redeemResult.getTotalDiscount()).orElse(0L) - notReportedDiscountAmount );
        itemsAndRedeem.setItems(finalItems);
        try {
            translationConvertService.convertCartItems(finalItems, request.getStoreId(), acceptLanguage);
        } catch (Exception e) {
            LogUtils.logWarnWithError("convertCartItems error", "convertCartItems", finalItems, e);
        }

        return itemsAndRedeem;

    }

    @Override
    public PayResult addGoodsAndPay(ApiRequest<PayFirstRoundOrderPayRequest> apiRequest) {
        PayFirstRoundOrderPayRequest request = apiRequest.getBody();

        Integer serviceType = (null != request.getServiceType()) ? request.getServiceType() : 0;

        if (request.isAddAndPay() || (StringUtils.isEmpty(request.getBatchNo()) && !request.isAllPay())) {
            //判断你是否需要验证购物车商品,如果不需要，说明是旧版接口，旧版接口有单独check方法，这里就不用再次check了
            if (request.isCheckCartItem()) {
                if (org.apache.commons.lang3.StringUtils.isBlank(request.getTableId())) {
                    throw new ParamException("桌台信息不能为空");
                }
                if (org.apache.commons.lang3.StringUtils.isBlank(request.getStoreId())) {
                    throw new ParamException("门店信息不能为空");
                }
                //检查购物车商品状态
                CartCheckResultDTO cartCheckResult = cartService.checkItemStatus(request.getTableId(), request.getStoreId(), serviceType, OrderMealTypeEnum.ROUND_MEAL);
                if (!cartCheckResult.isSuccess()) {
                    PayResult payResult = new PayResult();
                    payResult.setCartCheckResult(cartCheckResult);
                    return payResult;
                }


            }

        }

        if (request.isRechargeAndPay()) {
            request.setPayWay(Integer.valueOf(ThreadLocalHelper.getMiniProgramType().getCode()));
        }

        PayWay payWay = getByCode(request.getPayWay());

        if (payWay == null) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "支付方式错误");
        }
        if (payWay == PayWay.CASH || payWay == PayWay.ACCOUNTING){
           throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "支付方式不能为现金/记账");
        }



        BooleanConfigQueryRequest cashierModeRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, request.getStoreId(), Constants.CASHIER_MODE_CONFIG_KEY, false);
        boolean isCashierMode = configRemoteService.getBooleanConfig(cashierModeRequest);


        UserContextDTO user = ThreadLocalHelper.getUser();
        com.wosai.smartbiz.oms.api.query.PayRequest payRequest = new com.wosai.smartbiz.oms.api.query.PayRequest();

        String sn = Optional.ofNullable(request.getSn()).orElse(request.getOrderSn());


        if (StringUtils.isEmpty(sn)){
            TableQueryRequest tableQueryRequest = new TableQueryRequest();
            tableQueryRequest.setTableId(request.getTableId());
            Result<String> orderQueryResult = tableRpcServiceV2.getTableOrderNo(tableQueryRequest);
            if (orderQueryResult.isSuccess()){
                sn = orderQueryResult.getData();
            }
        }


        String merchantId = null;
        String tableId = request.getTableId();
        if (StringUtils.isEmpty(sn)){
            checkMustCategory(request,null);
            Map store = storeService.getStore(request.getStoreId());
            // 查商户信息
            payRequest.setBiz_store_id(request.getStoreId());
            payRequest.setStoreSn(BeanUtil.getPropString(store, Store.SN));
            payRequest.setStoreName(BeanUtil.getPropString(store, Store.NAME));
            payRequest.setMerchant_id(BeanUtil.getPropString(store, Store.MERCHANT_ID));
            payRequest.setMerchantSn(BeanUtil.getPropString(store, "merchant_sn"));
            merchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
        }else{
            Result<OrderMainDTO> orderMainDTOResult = awesomeOrderOrderMainService.getBySn(sn);
            if (!orderMainDTOResult.isSuccess()) {
                throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, orderMainDTOResult.getErrorMsg());
            }

            OrderMainDTO orderMainDTO = orderMainDTOResult.getData();

            if (orderMainDTO == null) {
                throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "待支付订单不存在");
            }

            if (TagUtil.hasTag(orderMainDTO.getOrderTag(),OrderTagEnum.PAY_FIRST_ORDER_PART_PAY.getValue())){
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "请联系服务员在收银系统上完成剩余应付金额收款后再下单～");
            }

            if(CollectionUtils.isEmpty(orderMainDTO.getItemsInfo())){
                checkMustCategory(request,sn);
            }
            merchantId = orderMainDTO.getMerchantId();
            tableId = orderMainDTO.getTableId();
        }

        if(!tableService.canContinueOrder(tableId)){
            throw new BusinessException(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE);
        }

        payRequest.setBatchNo(request.getBatchNo());
        payRequest.setAllPay(request.isAllPay());

        payRequest.setTableId(request.getTableId());
        payRequest.setTableNo(request.getTableNo());

        payRequest.setOrderNo(sn);
        payRequest.setOrderVersion(request.getOrderVersion());


        payRequest.setUserName(request.getUserName());
        payRequest.setUserIcon(request.getUserIcon());

        payRequest.setMkCustomInfo(request.getMkCustomInfo());
        payRequest.setDiscountDigest(request.getRedeemDigest());
        payRequest.setDiscountDigestAmount(request.getTotalDiscount());

        payRequest.setCashierBizParams(request.getCashierBizParams());


        payRequest.setUserId(user.getUserId());
        payRequest.setSubAppId(user.getWeixinAppId());
        payRequest.setAlipayAppId(user.getAlipayAppId());
        String payerUid;
        if (payWay == PayWay.SODEXO && org.apache.commons.lang.StringUtils.isNotBlank(user.getCellphone())) {
            // 索迪斯支付时,payerUid为经过base64加密后的手机号
            payerUid = Base64.encode(user.getCellphone().getBytes(StandardCharsets.UTF_8));
        } else {
            payerUid = user.getThirdpartyUserId();
        }
        payRequest.setPayerUid(payerUid);
        payRequest.setOrderSource(OrderSource.MINI);
        payRequest.setTradeScene(request.getTradeScene());

        if (ThreadLocalHelper.getMiniProgramType() == MiniProgramType.ALIPAY) {
            payRequest.setMiniSource(MiniSource.ALIPAY);
        } else {
            payRequest.setMiniSource(MiniSource.WECHAT);
        }

        if (isCashierMode) {
            payRequest.setCashierMode(YesNoEnum.Y);
        } else {
            payRequest.setCashierMode(YesNoEnum.N);
        }

        // 订单初始化的时候的消费者信息
        Map<Object, Object> extraMap = new HashMap<>();
        if (user != null) {
            extraMap.put("initialChannelUserId", user.getThirdpartyUserId());
            String initialAppId = Optional.ofNullable(user.getWeixinAppId())
                    .orElseGet(user::getAlipayAppId);
            if (initialAppId != null) {
                extraMap.put("initialAppId", initialAppId);
            }
        }
        payRequest.setExtraInfo(extraMap);

        payRequest.setIsRoundMeal(true);
        payRequest.setRoundMealType("PAY_FIRST_TABLE_ORDER");
        payRequest.setPreReduceStock(true);
        payRequest.setRemark(request.getRemark());
        payRequest.setBiz_store_id(request.getStoreId());

        payRequest.setClientSn(request.getClientSn());

        payRequest.setClientIp(StringUtil.empty(ThreadLocalHelper.getRequestContextThreadLocal().get().getRealIp()) ? IpUtils.getIp() : ThreadLocalHelper.getRequestContextThreadLocal().get().getRealIp());



        if (CollectionUtils.isNotEmpty(request.getItems())) {
            List<ShoppingCartGoodsDTO> goodsList = request.getItems().stream()
                    .map(item -> CartHelper.convert2RoundGoods(item)).collect(Collectors.toList());
            payRequest.setGoods(goodsList);
        }else{
            if (StringUtils.isEmpty(request.getBatchNo()) && !request.isAllPay()){
                //说明这是购物车和收银机商品的混合支付页面
                List<CartItemCreate> items = cartService.getItemCreateList(request.getStoreId(), request.getTableId(), null, OrderMealTypeEnum.ROUND_MEAL);
                if (CollectionUtils.isNotEmpty(items)) {
                    List<ShoppingCartGoodsDTO> goodsList = items.stream()
                            .map(item ->{
                                ShoppingCartGoodsDTO shoppingCartGoodsDTO = CartHelper.convert2RoundGoods(item);

                                if (item.getItem() != null){
                                    shoppingCartGoodsDTO.setSaleCount(new BigDecimal(item.getItem().getNumber()));
                                }
                                return shoppingCartGoodsDTO;
                            }).collect(Collectors.toList());
                    payRequest.setGoods(goodsList);
                }
            }
        }

        SharingAndTerminalChooseRequest sharingAndTerminalChooseRequest = new SharingAndTerminalChooseRequest();
        sharingAndTerminalChooseRequest.setMerchantId(merchantId);
        sharingAndTerminalChooseRequest.setStoreId(request.getStoreId());
        sharingAndTerminalChooseRequest.setOrderType(OrderType.EAT_FIRST_ORDER.getMsg());
        sharingAndTerminalChooseRequest.setAppId(user.getWeixinAppId());
        sharingAndTerminalChooseRequest.setTerminalSn(request.getTerminalSn());
        sharingAndTerminalChooseRequest.setTradeScene(request.getTradeScene());
        SharingAndTerminalChooseResponse sharingAndTerminalChooseResponse = marketTradePayService.sharingAndTerminalChoose(sharingAndTerminalChooseRequest);
        Map tradeTerminal = null;
        if (sharingAndTerminalChooseResponse != null) {
            tradeTerminal = sharingAndTerminalChooseResponse.getTerminal();
            payRequest.setMmp(sharingAndTerminalChooseResponse.isMmp());

            if (CollectionUtils.isNotEmpty(sharingAndTerminalChooseResponse.getCharge())) {

                UpayProfitSharing upayProfitSharing = new UpayProfitSharing();

                upayProfitSharing.setSharingFlag(UpayProfitSharing.SHARING_FLAG_ENABLE);
                upayProfitSharing.setSharingNotifyUrl(profitSharingCallback);
                upayProfitSharing.setCharge(sharingAndTerminalChooseResponse.getCharge().stream().map(charge -> {
                    UpayProfitSharing.Charge sharingCharge = new UpayProfitSharing.Charge();
                    sharingCharge.setChargeFlag(charge.getCharge_flag());
                    sharingCharge.setChargeParams(charge.getCharge_params());
                    return sharingCharge;
                }).collect(Collectors.toList()));
                payRequest.setProfitSharing(upayProfitSharing);
            }

        }

        payRequest.setTerminalSn(BeanUtil.getPropString(tradeTerminal, Terminal.SN));
        payRequest.setTerminalId(BeanUtil.getPropString(tradeTerminal, DaoConstants.ID));
        payRequest.setTerminalKey(BeanUtil.getPropString(tradeTerminal, Terminal.CURRENT_SECRET));


        Result<OrderMainWrapper> result = null;

        if(!StringUtils.isEmpty(ThreadLocalHelper.getSqbPaySource())){
            extraMap.put(Constants.SQB_PAY_SOURCE, ThreadLocalHelper.getSqbPaySource()) ;
        }
        if(!MapUtils.isEmpty(ThreadLocalHelper.getPayHeaders())){
            extraMap.put(Constants.PAY_HEADERS, ThreadLocalHelper.getPayHeaders()) ;
        }
        payRequest.setExtraInfo(extraMap);

        if (request.isRechargeAndPay()) {
            payRequest.setPayWay(payWay);
            payRequest.setSubPayWay(SubPayWay.MINI);
            payRequest.setRechargeAmount(request.getRechargeAmount());
            payRequest.setRechargeRuleId(request.getRechargeRuleId());
            payRequest.setRechargeAndPay(true);

        } else {
        
            if (payWay == PayWay.GIFT_CARD || payWay == PayWay.CARD) {
                if(request.getCashierBizParams() != null){
                    payRequest.setPayWay(payWay);
                }else{
                    try {
                        AchieveRedeemCodeRequest achieveRedeemCodeRequest = new AchieveRedeemCodeRequest();
                        achieveRedeemCodeRequest.setChannelUserId(user.getThirdpartyUserId());
                        achieveRedeemCodeRequest.setStoreId(request.getStoreId());
                        achieveRedeemCodeRequest.setUserId(user.getUserId());
                        RedeemCode redeemCode = storedCardRemoteService.achieveRedeemCodeOnlyForCardPayway(achieveRedeemCodeRequest);
    
                        if (redeemCode == null || StringUtils.isEmpty(redeemCode.getAuth_code())) {
                            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "无法找到对应的储值卡信息，无法使用储值卡进行支付");
                        }
                        payRequest.setBarcode(redeemCode.getAuth_code());
                    } catch (Exception ex) {
                        throw new BusinessException(ReturnCode.BUSINESS_ERROR, "无法获取到对应的储值卡信息，无法使用储值卡进行支付");
                    }
                }

            } else {
                payRequest.setPayWay(payWay);
            }
            
        }
        //统一收银台接入后， 储值并支付也通过这个入口进来
        result = orderRpcService.addGoodsAndPay(payRequest);

        if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorCode(), result.getErrorMsg());
        }
        return convert2PayResult(result.getData().getPreCreateOrderVO());
    }

    private void checkMustCategory(PayFirstRoundOrderPayRequest request,String sn) {
        //说明还没有下单成功
        if (request.isAddAndPay() || (StringUtils.isEmpty(request.getBatchNo()) && !request.isAllPay())) {

            List<String> waitCheckGoodsCategoryIds = new ArrayList<>();

            if (!StringUtils.isEmpty(sn)) {
                //这里要判断已经下单的商品中是否有必选商品
                Result<List<OrderGoodsDTO>> goodsDTOSResult = orderGoodsService.selectByOrderSnAndRefPayType(sn, GoodsRefPayType.PAY);
                if (goodsDTOSResult.isSuccess() && CollectionUtils.isNotEmpty(goodsDTOSResult.getData())) {

                    Predicate<OrderGoodsDTO> predicate = goods -> {
                        if (goods.getProcessStatus() == GoodsProcessStatus.REFUNDED || goods.getProcessStatus() == GoodsProcessStatus.RETURNED
                                || goods.getProcessStatus() == GoodsProcessStatus.CANCELED || goods.getProcessStatus() == GoodsProcessStatus.REJECTED) {
                            return false;
                        }

                        if (TagUtil.hasTag(goods.getGoodsTag(), OrderGoodsTagEnum.PAY_FIRST_TABLE_ORDER_NOT_PAY_GOODS.getValue())) {
                            return true;
                        }


                        if (goods.getProcessStatus() == GoodsProcessStatus.NOT_ACCEPTED) {
                            return true;
                        }

                        return false;
                    };


                    //找到未付款的收银机下单的商品,这些金额也要加上去
                    Predicate<OrderGoodsDTO> finalPredicate = predicate;
                    waitCheckGoodsCategoryIds.addAll(goodsDTOSResult.getData().stream()
                            .filter(orderGoodsDTO -> finalPredicate.test(orderGoodsDTO))
                            .map(OrderGoodsDTO::getCategoryId)
                            .distinct()
                            .collect(Collectors.toList()));
        
                }
            }

            // 检查必选商品
            List<CartItemCreate> items = cartService.getItemCreateList(request.getStoreId(), request.getTableId(), null, OrderMealTypeEnum.ROUND_MEAL);
            if (!CollectionUtils.isEmpty(items)) {
                List<String> categoryIds = items.stream().map(ic -> ic.getItem().getCategoryId()).distinct().collect(Collectors.toList());
                waitCheckGoodsCategoryIds.addAll(categoryIds);
            
            }
            if (CollectionUtils.isNotEmpty(waitCheckGoodsCategoryIds)) {
            
                orderHelper.checkMustCategorys(waitCheckGoodsCategoryIds, request.getStoreId(), OrderType.EAT_FIRST_ORDER);
            }
        }

    }

    @Override
    @SneakyThrows
    public OrderMainWrapper addGoods(ApiRequest<InitRequest> apiRequest) {

        InitRequest request = apiRequest.getBody();
        Integer serviceType = (null != request.getServiceType()) ? request.getServiceType() : 0;

        if(!tableService.canContinueOrder(request.getTableId())){
            throw new BusinessException(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE);
        }

        //判断你是否需要验证购物车商品,如果不需要，说明是旧版接口，旧版接口有单独check方法，这里就不用再次check了
        if (request.isCheckCartItem()) {
            if (org.apache.commons.lang3.StringUtils.isBlank(request.getTableId())) {
                throw new ParamException("桌台信息不能为空");
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(request.getStoreId())) {
                throw new ParamException("门店信息不能为空");
            }
            CartCheckResultDTO cartCheckResult = cartService.checkItemStatus(request.getTableId(), request.getStoreId(), serviceType, OrderMealTypeEnum.ROUND_MEAL);
            if (!cartCheckResult.isSuccess()) {
                return OrderMainWrapper.builder()
                        .cartCheckResult(cartCheckResult)
                        .build();
            }
        }

        BooleanConfigQueryRequest booleanConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, request.getStoreId(), Constants.CASHIER_MODE_CONFIG_KEY, false);
        boolean isCashierMode = configRemoteService.getBooleanConfig(booleanConfigQueryRequest);

        Map store = storeService.getStore(request.getStoreId());

        com.wosai.smartbiz.oms.api.query.PayRequest payRequest = new com.wosai.smartbiz.oms.api.query.PayRequest();
        payRequest.setTableId(request.getTableId());
        payRequest.setTableNo(request.getTableNo());
        payRequest.setOrderNo(request.getOrderSn());


        payRequest.setOrderSource(OrderSource.MINI);
        if (ThreadLocalHelper.getMiniProgramType() == MiniProgramType.ALIPAY) {
            payRequest.setMiniSource(MiniSource.ALIPAY);
        } else {
            payRequest.setMiniSource(MiniSource.WECHAT);
        }
        payRequest.setUserId(ThreadLocalHelper.getUserId());
        payRequest.setIsRoundMeal(true);
        payRequest.setPreReduceStock(true);

        if (isCashierMode) {
            payRequest.setCashierMode(YesNoEnum.Y);
        } else {
            payRequest.setCashierMode(YesNoEnum.N);
        }

        payRequest.setRemark(request.getRemark());

        payRequest.setUserName(request.getUserName());
        payRequest.setUserIcon(request.getUserIcon());

        // 查商户信息
        payRequest.setBiz_store_id(request.getStoreId());
        payRequest.setStoreSn(BeanUtil.getPropString(store, Store.SN));
        payRequest.setStoreName(BeanUtil.getPropString(store, Store.NAME));
        payRequest.setMerchant_id(BeanUtil.getPropString(store, Store.MERCHANT_ID));
        payRequest.setMerchantSn(BeanUtil.getPropString(store, "merchant_sn"));


        List<ShoppingCartGoodsDTO> goodsList = request.getItems().stream()
                .map(item -> CartHelper.convert2RoundGoods(item)).collect(Collectors.toList());
        payRequest.setGoods(goodsList);
        // 围餐加菜
        Result<OrderMainWrapper> result = orderRpcService.addRemoveGoods(payRequest);
        if (!result.isSuccess()) {
            throw new BusinessException(result.getErrorCode(), result.getErrorMsg());
        }

        OrderMainWrapper orderMainWrapper = result.getData();
        if (null != orderMainWrapper.getCartCheckResult() && !orderMainWrapper.getCartCheckResult().isSuccess()) {
            List<CartCheckResultDTO.Item> checkFailList = orderMainWrapper.getCartCheckResult().getCheckFailList();
            //清空购物车商品
            if (CollectionUtils.isNotEmpty(checkFailList)) {
                checkFailList.stream().forEach(item -> {
                    cartService.removeItem(request.getStoreId(), request.getTableId(), item.getItemUid(), serviceType, OrderMealTypeEnum.ROUND_MEAL);
                });
            }

            //这里预扣库存后需要兼容老版本接口返回，老版本接口只需要给错误提示就行，新版本需要返回CartCheckResultDTO对象
            if (!request.isCheckCartItem()) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商品库存不足，请重新选购");
            }
        }

        return orderMainWrapper;
    }

    @Override
    @SneakyThrows
    public PayResult roundPay(ApiRequest<PayRequest> apiRequest) {
        PayRequest request = apiRequest.getBody();

        if (request.isRechargeAndPay()) {
            request.setPayWay(Integer.valueOf(ThreadLocalHelper.getMiniProgramType().getCode()));
        }
        PayWay payWay = getByCode(request.getPayWay());

        if (payWay == null) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "支付方式错误");
        }
        if (payWay == PayWay.CASH || payWay == PayWay.ACCOUNTING){
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "支付方式不能为现金/记账");
        }
        Result<OrderMainDTO> orderMainDTOResult = awesomeOrderOrderMainService.getBySn(request.getSn());
        if (!orderMainDTOResult.isSuccess()) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, orderMainDTOResult.getErrorMsg());
        }
        OrderMainDTO orderMainDTO = orderMainDTOResult.getData();
        if (orderMainDTO == null) {
            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "待支付订单不存在");
        }

        if(!tableService.canContinueOrder(orderMainDTO.getTableId())){
            throw new BusinessException(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE);
        }

        UserContextDTO user = ThreadLocalHelper.getUser();
        com.wosai.smartbiz.oms.api.query.PayRequest payRequest = new com.wosai.smartbiz.oms.api.query.PayRequest();
        payRequest.setMkCustomInfo(request.getMkCustomInfo());
        payRequest.setCashierBizParams(request.getCashierBizParams());
        payRequest.setOrderNo(request.getSn());
        payRequest.setOrderVersion(request.getOrderVersion());
        payRequest.setUserName(request.getUserName());
        payRequest.setUserIcon(request.getUserIcon());
        payRequest.setDiscountDigest(request.getRedeemDigest());
        payRequest.setDiscountDigestAmount(request.getTotalDiscount());
        payRequest.setUserId(user.getUserId());
        payRequest.setSubAppId(user.getWeixinAppId());
        payRequest.setAlipayAppId(user.getAlipayAppId());
        String payerUid;
        if (payWay == PayWay.SODEXO && org.apache.commons.lang.StringUtils.isNotBlank(user.getCellphone())) {
            // 索迪斯支付时,payerUid为经过base64加密后的手机号
            payerUid = Base64.encode(user.getCellphone().getBytes(StandardCharsets.UTF_8));
        } else {
            payerUid = user.getThirdpartyUserId();
        }
        payRequest.setPayerUid(payerUid);
        payRequest.setOrderSource(OrderSource.MINI);
        payRequest.setTradeScene(request.getTradeScene());
        payRequest.setIsRoundMeal(true);
        payRequest.setPreReduceStock(true);
        payRequest.setBiz_store_id(request.getStoreId());
        payRequest.setUc_user_id(request.getOperator());
        payRequest.setOperator(request.getOperator());
        payRequest.setClientSn(request.getClientSn());
        payRequest.setClientIp(StringUtil.empty(ThreadLocalHelper.getRequestContextThreadLocal().get().getRealIp()) ? IpUtils.getIp() : ThreadLocalHelper.getRequestContextThreadLocal().get().getRealIp());

        Map extraMap = new HashMap();

        SharingAndTerminalChooseRequest sharingAndTerminalChooseRequest = new SharingAndTerminalChooseRequest();
        sharingAndTerminalChooseRequest.setMerchantId(orderMainDTO.getMerchantId());
        sharingAndTerminalChooseRequest.setStoreId(orderMainDTO.getStoreId());
        sharingAndTerminalChooseRequest.setOrderType(OrderType.EAT_FIRST_ORDER.getMsg());
        sharingAndTerminalChooseRequest.setAppId(user.getWeixinAppId());
        sharingAndTerminalChooseRequest.setTerminalSn(request.getTerminalSn());
        sharingAndTerminalChooseRequest.setTradeScene(request.getTradeScene());
        SharingAndTerminalChooseResponse sharingAndTerminalChooseResponse = marketTradePayService.sharingAndTerminalChoose(sharingAndTerminalChooseRequest);

        Map tradeTerminal = null;

        if (sharingAndTerminalChooseResponse != null) {
            tradeTerminal = sharingAndTerminalChooseResponse.getTerminal();
            payRequest.setMmp(sharingAndTerminalChooseResponse.isMmp());

            if (CollectionUtils.isNotEmpty(sharingAndTerminalChooseResponse.getCharge())) {
                UpayProfitSharing upayProfitSharing = new UpayProfitSharing();
                upayProfitSharing.setSharingFlag(UpayProfitSharing.SHARING_FLAG_ENABLE);
                upayProfitSharing.setSharingNotifyUrl(profitSharingCallback);
                upayProfitSharing.setCharge(sharingAndTerminalChooseResponse.getCharge().stream().map(charge -> {
                    UpayProfitSharing.Charge sharingCharge = new UpayProfitSharing.Charge();
                    sharingCharge.setChargeFlag(charge.getCharge_flag());
                    sharingCharge.setChargeParams(charge.getCharge_params());
                    return sharingCharge;
                }).collect(Collectors.toList()));
                payRequest.setProfitSharing(upayProfitSharing);
            }
        }

        payRequest.setTerminalSn(BeanUtil.getPropString(tradeTerminal, Terminal.SN));
        payRequest.setTerminalId(BeanUtil.getPropString(tradeTerminal, DaoConstants.ID));
        payRequest.setTerminalKey(BeanUtil.getPropString(tradeTerminal, Terminal.CURRENT_SECRET));
        Result<OrderMainWrapper> result = null;

        if(!StringUtils.isEmpty(ThreadLocalHelper.getSqbPaySource())){
           extraMap.put(Constants.SQB_PAY_SOURCE, ThreadLocalHelper.getSqbPaySource()) ;
        }
        if(!MapUtils.isEmpty(ThreadLocalHelper.getPayHeaders())){
            extraMap.put(Constants.PAY_HEADERS, ThreadLocalHelper.getPayHeaders()) ;
        }
        payRequest.setExtraInfo(extraMap);
        if(orderMainDTO.getOriginalAmount() == 0
                ||(request.getTotalDiscount() != null
                && orderMainDTO.getOriginalAmount() - request.getTotalDiscount() == 0)){

            if(!miniZeroPayEnable){
                throw new BusinessException(ReturnCode.SYSTEM_EXCEPTION, "0元订单暂不支持下单");
            }

            payWay = PayWay.CASH;
            payRequest.setZeroPay(true);
            payRequest.setChannelId(4L);
        }

        //打印控制
        payRequest.setCheckPrintBill(request.getCheckPrintBill());
        payRequest.setCheckInvoice(request.getCheckInvoice());

        if (request.isRechargeAndPay()) {
            payRequest.setPayWay(payWay);
            payRequest.setSubPayWay(SubPayWay.MINI);
            payRequest.setRechargeAmount(request.getRechargeAmount());
            payRequest.setRechargeRuleId(request.getRechargeRuleId());

            result = orderRpcService.rechargeAndPay(payRequest);
        } else {
            if (payWay == PayWay.GIFT_CARD || payWay == PayWay.CARD) {
                if(request.getCashierBizParams() != null){
                    payRequest.setPayWay(payWay);
                }else{
                    try {
                        AchieveRedeemCodeRequest achieveRedeemCodeRequest = new AchieveRedeemCodeRequest();
                        achieveRedeemCodeRequest.setChannelUserId(user.getThirdpartyUserId());
                        achieveRedeemCodeRequest.setStoreId(request.getStoreId());
                        achieveRedeemCodeRequest.setUserId(user.getUserId());
                        RedeemCode redeemCode = storedCardRemoteService.achieveRedeemCodeOnlyForCardPayway(achieveRedeemCodeRequest);
    
                        if (redeemCode == null || StringUtils.isEmpty(redeemCode.getAuth_code())) {
                            throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "无法找到对应的储值卡信息，无法使用储值卡进行支付");
                        }
                        payRequest.setBarcode(redeemCode.getAuth_code());
                    } catch (Exception ex) {
    
                        if (apolloConfigHelper.isInTempChargeErrorDowngradeWhitelist(orderMainDTO.getMerchantId()) && "充值账户余额不足，请充值使用".equals(ex.getMessage())){
                            PayWay resetPayway = getByCode(Integer.valueOf(ThreadLocalHelper.getMiniProgramType().getCode()));
                            payRequest.setPayWay(resetPayway);
                        }else{
                            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "无法获取到对应的储值卡信息，无法使用储值卡进行支付");
                        }
                    }
                }
                

            } else {
                payRequest.setPayWay(payWay);
            }
            result = orderRpcService.pay(payRequest);
        }

        if (!result.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
        }
        return convert2PayResult(result.getData().getPreCreateOrderVO());
    }



    private String getStringValue(Long amount) {
        if (amount == null) {
            return null;
        }
        return String.valueOf(amount);
    }

    private PayResult convert2PayResult(HsPreCreateOrderVO preCreateOrderVO) {
        PayResult payResult = new PayResult();
        //兼容之前的前端程序，这里clientSn也返回sn
        payResult.setClientSn(preCreateOrderVO.getOrderSn());
        payResult.setSn(preCreateOrderVO.getOrderSn());
        payResult.setTotalAmount(getStringValue(preCreateOrderVO.getTotalAmount()));
        payResult.setNetAmount(getStringValue(preCreateOrderVO.getNetAmount()));
        payResult.setTotalDiscount(getStringValue(preCreateOrderVO.getDiscountAmount()));
        if (preCreateOrderVO.getWapPayRequest() != null) {
            PrePayResult.biz_response.data.WapPayRequest wapPayRequest = new PrePayResult.biz_response.data.WapPayRequest();
            wapPayRequest.setAppId(preCreateOrderVO.getWapPayRequest().getAppId());
            wapPayRequest.setNonceStr(preCreateOrderVO.getWapPayRequest().getNonceStr());
            wapPayRequest.setPackage(preCreateOrderVO.getWapPayRequest().getPackageStr());
            wapPayRequest.setPaySign(preCreateOrderVO.getWapPayRequest().getPaySign());
            wapPayRequest.setSignType(preCreateOrderVO.getWapPayRequest().getSignType());
            wapPayRequest.setTimeStamp(preCreateOrderVO.getWapPayRequest().getTimeStamp());
            wapPayRequest.setTradeNO(preCreateOrderVO.getWapPayRequest().getTradeNO());
            wapPayRequest.setEncrypt_data(preCreateOrderVO.getWapPayRequest().getEncryptData());
            payResult.setWapPayRequest(wapPayRequest);
        }

        if (preCreateOrderVO.getRiskInfo() != null) {
            payResult.setHasRisk(preCreateOrderVO.getRiskInfo().isHashRisk());
            payResult.setAction(preCreateOrderVO.getRiskInfo().getAction());
            payResult.setRiskId(preCreateOrderVO.getRiskInfo().getRiskId());
        }
        payResult.setAcquiring(preCreateOrderVO.getAcquiringResult());
        return payResult;
    }

    

    @Override
    @SneakyThrows
    public Boolean canclePayLock(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();

        String tableId = org.apache.commons.collections.MapUtils.getString(queryParam, "table_id");

        if (org.apache.commons.lang3.StringUtils.isBlank(tableId)) {
            throw new ParamException("桌台信息不能为空");
        }

        TableQueryRequest tableQueryRequest = new TableQueryRequest();
        tableQueryRequest.setTableId(tableId);
        Result<Boolean> cancelResult = tableRpcService.cancelPayLock(tableQueryRequest);
        if (!cancelResult.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, cancelResult.getErrorMsg());
        }
        return cancelResult.getData();
    }

    PayWay getByCode(Integer payway) {
        for (PayWay payWay : PayWay.values()) {
            if (Objects.equals(payWay.getCode(), payway)) {
                return payWay;
            }
        }
        return null;
    }

    /**
     * @description 后续不再享受优惠
     * @param orderMainDTO
     * @return
     **/
    private boolean noLongerEnjoyRedeem(OrderMainDTO orderMainDTO) {
        if (TagUtil.hasTag(orderMainDTO.getOrderTag(), OrderTagEnum.EVER_FRONT_COUPON_PAID.getValue())) {
            return MoneyUtil.getNotNullAmount(orderMainDTO.getMerchantDiscountTotalAmount()) > 0
                    && (!Objects.equals(MoneyUtil.getNotNullAmount(orderMainDTO.getMerchantDiscountTotalAmount()), MoneyUtil.getNotNullAmount(orderMainDTO.getCouponDiscountAmount())));
        }
        return MoneyUtil.getNotNullAmount(orderMainDTO.getMerchantDiscountTotalAmount()) > 0;
    }

//    private Map getTerminal(String terminalSn, String storeId) {
//        Map terminal = null;
//        if (org.apache.commons.lang.StringUtils.isEmpty(terminalSn)) {
//            terminal = terminalService.getTerminalByDeviceFingerprint("jjz" + storeId);
//        } else {
//            terminal = terminalService.getTerminalBySn(terminalSn);
//        }
//        return terminal;
//    }

//    private Map getDiscountTerminal(String terminalSn, String storeId, String orderType) {
//        Map terminal = null;
//        if (!orderType.equals(MarketTradeConstant.orderType.PRE_ORDER.getMsg())
//                && !orderType.equals(MarketTradeConstant.orderType.SUBSCRIBE_ORDER.getMsg())
//                && !orderType.equals(MarketTradeConstant.orderType.TAKE_OUT_ORDER.getMsg())
//                && !orderType.equals(MarketTradeConstant.orderType.EAT_FIRST_ORDER.getMsg())
//        ) {
//            return this.getTerminal(terminalSn, storeId);
//        }
//
//        UfoodFeeCheckResponse ufoodFeeCheckResponse = ufoodFeeService.ufoodFeeCheck(storeId);
//        if (!ufoodFeeCheckResponse.isOldStore()) {
//            //新增商户
//            if (ufoodFeeCheckResponse.isUseFreeFee()) {
//                //零费率终端
//                terminal = getTerminalMap(storeId, MarketTradeConstant.terminalConfig.JJZUFOODDISCOUNTFEE.getConfigName());
//            } else {
//                //新终端 使用时 浮动费率 extended 字段sqb_scene传 service_fee_up
//                //文档https://confluence.wosai-inc.com/pages/viewpage.action?pageId=527598120
//                terminal = getTerminalMap(storeId, MarketTradeConstant.terminalConfig.JJZUFOODFEE.getConfigName());
//            }
//        } else {
//            //老商户优惠费率终端
//            terminal = getTerminalMap(storeId, MarketTradeConstant.terminalConfig.JJZDISCOUNTFEE.getConfigName());
//        }
//
//        return Optional.ofNullable(terminal).orElse(this.getTerminal(terminalSn, storeId));
//    }

//    private Map getTerminalMap(String storeId, String prefix) {
//        Map terminal = terminalService.getTerminalByDeviceFingerprint(prefix + storeId);
//        if (Objects.nonNull(terminal)) {
//            terminal.put("terminalDiscountType", prefix);
//            return terminal;
//        }
//        return null;
//    }


}
