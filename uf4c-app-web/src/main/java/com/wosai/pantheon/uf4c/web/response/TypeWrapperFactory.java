package com.wosai.pantheon.uf4c.web.response;


import java.util.HashMap;
import java.util.Map;

public class TypeWrapperFactory {

    public static Object create(Object o) {
        if (o instanceof Boolean || o instanceof Integer || o instanceof Long) {
            // 包装原始类型
            Map<String, Object> response = new HashMap<>();
            response.put("result", o);
            return response;
        }
        return o;
    }
}
