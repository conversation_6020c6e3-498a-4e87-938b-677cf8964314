package com.wosai.pantheon.uf4c.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: FuL
 * @Date 2020/11/2
 */
public class TimeSplit {
    private static final int MINUTES_INTERVAL = 20;

    private static final long ONE_MINUTE = 60 * 1000;

    @SneakyThrows
    public static void main(String[] args) {
//        List<TimeResult> times = getBookTimes("[{\"startTime\":\"00:00\",\"endTime\":\"04:00\"},{\"startTime\":\"18:00\",\"endTime\":\"24:00\"}]", Arrays.stream(new int[]{0,1}).boxed().collect(Collectors.toList()), 35);
//        List<TimeResult> times = getBookTimes("{\"1\":[{\"endTime\":\"02:00\",\"startTime\":\"00:00\"},{\"endTime\":\"05:00\",\"startTime\":\"04:00\"},{\"endTime\":\"08:00\",\"startTime\":\"06:00\"}],\"2\":[{\"endTime\":\"11:00\",\"startTime\":\"09:00\"},{\"endTime\":\"16:00\",\"startTime\":\"15:00\"},{\"endTime\":\"18:00\",\"startTime\":\"17:00\"}],\"3\":[{\"endTime\":\"22:00\",\"startTime\":\"00:00\"}],\"4\":[{\"endTime\":\"18:00\",\"startTime\":\"17:00\"}],\"5\":[{\"endTime\":\"16:00\",\"startTime\":\"11:00\"},{\"endTime\":\"17:00\",\"startTime\":\"16:00\"},{\"endTime\":\"19:00\",\"startTime\":\"18:00\"}],\"6\":[],\"7\":[{\"endTime\":\"01:00\",\"startTime\":\"00:00\"},{\"endTime\":\"03:00\",\"startTime\":\"02:00\"},{\"endTime\":\"10:00\",\"startTime\":\"04:00\"}]}", Arrays.stream(new int[]{0,1, 2,3,4,5,6,7}).boxed().collect(Collectors.toList()), 15, false);
        List<TimeResult> times  = getCustomBookTimes("{\"1\":[{\"endTime\":\"18:00\",\"startTime\":\"10:00\"}],\"2\":[{\"endTime\":\"18:00\",\"startTime\":\"10:00\"}],\"3\":[{\"endTime\":\"18:00\",\"startTime\":\"10:00\"}],\"4\":[{\"endTime\":\"18:00\",\"startTime\":\"10:00\"}],\"5\":[{\"endTime\":\"18:00\",\"startTime\":\"10:00\"}],\"6\":[{\"endTime\":\"18:00\",\"startTime\":\"10:00\"}],\"7\":[{\"endTime\":\"18:00\",\"startTime\":\"10:00\"}]}", Arrays.stream(new int[]{0,1, 2,3,4,5,6,7}).boxed().collect(Collectors.toList()), "[\"08:00\",\"11:30\",\"15:00\"]",25);
        times.forEach(tr -> {
            System.out.println(tr.getLabel());
            tr.times.forEach(t -> System.out.println(t == -1 ? "立即配送" : WosaiDateTimeUtils.format(t, "MM-dd HH:mm")));
        });
    }


    public static List<TimeResult> getTimes(String businessTimes, int days) {

        List<Map<String, String>> timeRangeList = new ArrayList<>();
        if (StringUtil.isBlank(businessTimes) || businessTimes.equalsIgnoreCase("[]")) {
            // 营业时间为空，视为全天营业
            Map<String, String> map = new HashMap<>();
            map.put("startTime", "00:00");
            map.put("endTime", "24:00");
            timeRangeList.add(map);
        } else {
            // 取出营业时间
            timeRangeList = JacksonUtil.toBean(businessTimes, new TypeReference<List<Map<String, String>>>() {
            });
        }
        List<Long> resultTimes = new ArrayList<>();
        Date now = getNowAfter15Date();
        for (int i = 0; i < days + 1; i++) {
            // 获取日期组显示的名称
            // 分割好时间
            List<Long> timeSplitList = new ArrayList<>();
            if (timeRangeList != null) {
                for (Map<String, String> map : timeRangeList) {
                    // 获取Date格式的营业时间起止
                    Date[] dates = getDate(map.get("startTime"), map.get("endTime"), i);
                    if (i == 0 && new Date().getTime() > dates[1].getTime()) {
                        continue;
                    }

                    // 分隔时间段
                    List<Long> dateSplits = split(dates[0], dates[1]);
                    if (CollectionUtil.isNotEmpty(dateSplits)) {
                        timeSplitList.addAll(dateSplits);
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(timeSplitList)) {
                if (i == 0) {
                    // 对当前时间与营业时间进行如下判断
                    // 1.当前时间在营业时间内，则预估时间=当前时间+15min，并且该订单为立即取单
                    // 2.当前时间在营业时间外，则预估时间=营业时间起+15min，并且该订单时预订单
                    if (now.getTime() < timeSplitList.get(0)) {
                        // 如果取单时间小于分割好的最大时间
                        resultTimes.addAll(timeSplitList);
                    } else {
                        if (CollectionUtil.isNotEmpty(timeRangeList)) {
                            if ("24:00".equalsIgnoreCase(timeRangeList.get(timeRangeList.size() - 1).get("endTime"))) {
                                if (days == 0) {
                                    // 仅当天，若营业时间截止24:00，则当前允许最大时间为23:59
                                    if (now.getTime() < timeToDate("23:59", 0).getTime()) {
                                        resultTimes.add(now.getTime());
                                        resultTimes.add(timeToDate("23:59", 0).getTime());
                                    } else {
                                        if (!isSameDay(now, timeToDate("23:59", 0))) {
                                            resultTimes.add(timeToDate("23:59", 0).getTime());
                                        } else {
                                            resultTimes.add(now.getTime());
                                        }

                                    }
                                } else {
                                    // 若非仅当天，且营业时间截止24:00,第二天从00:00开始
                                    if (now.getTime() < timeToDate("00:00", 1).getTime()) {
                                        resultTimes.add(now.getTime());
                                        //resultTimes.add(timeToDate("23:59", 0).getTime());
                                    } else {
                                        resultTimes.add(timeToDate("00:00", 1).getTime());
                                    }
                                }
                            } else {
                                if (now.getTime() > timeSplitList.get(timeSplitList.size() - 1)) {
                                    resultTimes.add(timeSplitList.get(timeSplitList.size() - 1));
                                } else {
                                    resultTimes.add(now.getTime());
                                }
                            }
                        }
                        long finalMinTime = now.getTime();
                        resultTimes.addAll(timeSplitList.stream().filter(t -> t > finalMinTime).collect(Collectors.toList()));
                    }
                } else {
                    if ("24:00".endsWith(timeRangeList.get(timeRangeList.size() - 1).get("endTime"))) {
                        // 这种情况代表上一天的营业时间为24:00，则需要从00:00开始
                        if (now.getTime() < timeToDate("00:00", i).getTime()) {
                            timeSplitList.add(0, timeToDate("00:00", i).getTime());
                        }
                    }
                    resultTimes.addAll(timeSplitList);
                }
            }
        }
        // 存放最终返回前端的结果
        List<TimeResult> times = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resultTimes)) {
            resultTimes.sort(Long::compareTo);
            LinkedHashMap<String, List<Long>> groupMap = resultTimes.stream()
                    .collect(Collectors.groupingBy(t -> WosaiDateTimeUtils.format(t, "yyyy-MM-dd"), LinkedHashMap::new, Collectors.toList()));
            groupMap.forEach((k, v) -> times.add(new TimeResult(getLabel(afterNowDays(toDate(k, "yyyy-MM-dd"))), v)));
        }
        return times;
    }


    public static List<TimeResult> getBookTimes(String businessTimes, List<Integer> days, Integer addTime, boolean onlyBookOrder) {
        if (CollectionUtil.isEmpty(days)) {
            return null;
        }
        days.sort(Integer::compareTo);
        List<Map<String, String>> timeRangeList = new ArrayList<>();
        Map<String, List<Map<String, String>>> timeRangeMap = JacksonUtil.toBean(businessTimes, new TypeReference<TreeMap<String, List<Map<String, String>>>>() {
        });
        int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();
        List<Map<String, String>> todayTimeRange = timeRangeMap.get(String.valueOf(dayOfWeek));

        List<Long> resultTimes = new ArrayList<>();
        Date now = getSoonDate(addTime);

        Long curBusinessMax = 0L;
        Long todayEndTime = 0L;
        for (int i = days.get(0); i < days.get(days.size() - 1) + 1; i++) {
            // 获取日期组显示的名称
            // 分割好时间
            List<Long> timeSplitList = new ArrayList<>();
            int day = LocalDate.now().plusDays(i).getDayOfWeek().getValue();
            timeRangeList = timeRangeMap.get(String.valueOf(day));
//            if (timeRangeList != null) {
            if (CollectionUtils.isNotEmpty(timeRangeList)) {
                for (Map<String, String> map : timeRangeList) {
                    // 获取Date格式的营业时间起止
                    Date[] dates = getDate(map.get("startTime"), map.get("endTime"), i);
                    if (i == 0 && new Date().getTime() > dates[1].getTime()) {
                        continue;
                    }
                    if (i == 0 && curBusinessMax == 0) {
                        curBusinessMax = dates[1].getTime();
                    }
                    if (i == 0) {
                        todayEndTime = dates[1].getTime();
                    }
                    // 分隔时间段
                    List<Long> dateSplits = splitWithAddTime(dates[0], dates[1], addTime);
                    if (CollectionUtil.isNotEmpty(dateSplits)) {
                        timeSplitList.addAll(dateSplits);
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(timeSplitList)) {
                if (i == 0) {
                    // 对当前时间与营业时间进行如下判断
                    // 1.当前时间在营业时间内，则预估时间=当前时间+15min，并且该订单为立即取单
                    // 2.当前时间在营业时间外，则预估时间=营业时间起+15min，并且该订单时预订单
                    if (now.getTime() < timeSplitList.get(0)) {
                        // 如果取单时间小于分割好的最小时间
                        resultTimes.addAll(timeSplitList);
                    } else {
                        if (CollectionUtil.isNotEmpty(timeRangeList)) {
                            if ("24:00".equalsIgnoreCase(timeRangeList.get(timeRangeList.size() - 1).get("endTime"))) {
                                if (days.size() == 1 && days.get(0) == 0) {
                                    // 仅当天，若营业时间截止24:00，则当前允许最大时间为23:59
                                    if (now.getTime() < timeToDate("23:59", 0).getTime()) {
                                        resultTimes.add(now.getTime());
//                                        resultTimes.add(timeToDate("23:59", 0).getTime());
                                    } else {
                                        if (!isSameDay(now, timeToDate("23:59", 0))) {
//                                            快接近关门时间了，空着，最后会补一个立即送达
//                                            resultTimes.add(timeToDate("23:59", 0).getTime());
                                        } else {
                                            resultTimes.add(now.getTime());
                                        }

                                    }
                                } else {
                                    // 若非仅当天，且营业时间截止24:00,第二天从00:00开始
                                    if (now.getTime() < timeToDate("00:00", 1).getTime()) {
                                        resultTimes.add(now.getTime());
                                    } else {
                                        resultTimes.add(timeToDate("00:00", 1).getTime());
                                    }
                                }
                            } else {
                                if (now.getTime() > timeSplitList.get(timeSplitList.size() - 1)) {
                                    //快接近关门时间了，空着，最后会补一个立即送达
                                    //resultTimes.add(timeSplitList.get(timeSplitList.size() - 1));
                                } else {
                                    resultTimes.add(now.getTime());
                                }
                            }
                        }
                        long finalMinTime = now.getTime();
                        resultTimes.addAll(timeSplitList.stream().filter(t -> t > finalMinTime).collect(Collectors.toList()));
                    }
                } else {
                    int preDay = Objects.equals(day, 1) ? 7 : (day - 1);
                    List<Map<String, String>> preTimeRangeList = timeRangeMap.get(String.valueOf(preDay));
                    if (CollectionUtil.isNotEmpty(preTimeRangeList)) {
                        String endTime = preTimeRangeList.get(preTimeRangeList.size() - 1).get("endTime");
                        if (Objects.nonNull(endTime) && "24:00".endsWith(endTime)) {
                            // 这种情况代表上一天的营业时间为24:00，则需要从00:00开始
                            if (now.getTime() < timeToDate("00:00", i).getTime()) {
                                timeSplitList.add(0, timeToDate("00:00", i).getTime());
                            }
                        }
                    }
                    resultTimes.addAll(timeSplitList);
                }
            }
        }
        // 存放最终返回前端的结果
        List<TimeResult> times = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resultTimes)) {
            resultTimes.sort(Long::compareTo);
            LinkedHashMap<String, List<Long>> groupMap = resultTimes.stream()
                    .collect(Collectors.groupingBy(t -> WosaiDateTimeUtils.format(t, "yyyy-MM-dd"), LinkedHashMap::new, Collectors.toList()));
            groupMap.forEach((k, v) -> times.add(new TimeResult(getLabel(afterNowDays(toDate(k, "yyyy-MM-dd"))), v)));
            if (getLabel(0).equals(times.get(0).getLabel())) {
                //如果第一列是今天
                if (times.get(0).getTimes().get(0) == now.getTime()) {
//                    System.out.println("尽快送达时间" + WosaiDateTimeUtils.format(times.get(0).getTimes().get(0), "MM-dd HH:mm"));
                    //如果最近的一个时间是计算出的立即送达时间。1、-要把该时间设置为 -1,表示即时单；2、要把[now-第一段结束时间]后面的时间都设置为最近的整点时间+20*n
                    ArrayList<Long> list = new ArrayList<>();
                    if (!onlyBookOrder) {
                        list.add(-1L);
                    }

                    SimpleDateFormat mm = new SimpleDateFormat("mm");
                    int i = 0;
                    if (Integer.parseInt(mm.format(now)) % 5 != 0) {
                        i = 5 - (Integer.parseInt(mm.format(now)) % 5);
                    }
                    Date recentTime = new Date(now.getTime() + (long) i * ONE_MINUTE);
                    long timeStamp = recentTime.getTime() + 20 * ONE_MINUTE;
                    while (timeStamp < curBusinessMax) {
                        list.add(timeStamp);
                        timeStamp = timeStamp + 20 * ONE_MINUTE;
                    }
                    for (Long l : times.get(0).getTimes()) {
                        if (l != now.getTime() && l >= curBusinessMax) {
                            list.add(l);
                        }
                    }
                    times.get(0).setTimes(list);
                }
            } else {
                //如果今天预订单关了，如果还在营业时间内，则可以显示立即送达
                if (todayTimeRange != null) {
                    if (todayTimeRange.size() > 0 && todayTimeRange.stream().anyMatch(it -> isInDate(
                            BeanUtil.getPropString(it, "startTime"),
                            BeanUtil.getPropString(it, "endTime")))
                    ) {
                        // 可接受当天订单时，才增加立即送达时间
                        if (!onlyBookOrder) {
                            times.add(0, new TimeResult(getLabel(0), new ArrayList<Long>() {{
                                add(-1L);
                            }}));
                        }

                    }
                }
            }
        }
        //补全最后接近关门时间，空着的立即送达逻辑
        if (times.size() == 0 && System.currentTimeMillis() < todayEndTime) {
            return onlyImmediateOrder();
        }
        return times;
    }

    private static String getLabel(int days) {
        Date date = addDays(new Date(), days);
        StringBuilder sb = new StringBuilder();
        switch (days) {
            case 0:
                sb.append("今天").append("(").append(getWeekOfDate(date)).append(")");
                break;
            case 1:
                sb.append("明天").append("(").append(getWeekOfDate(date)).append(")");
                break;
            default:
                String dateStr = formatDateTime(date, "MM月dd日");
                sb.append(dateStr).append("(").append(getWeekOfDate(date)).append(")");
                break;
        }
        return sb.toString();
    }

    private static String getWeekOfDate(Date date) {
        String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    private static boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) && cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }

    private static Date[] getDate(String startTimeStr, String endTimeStr, int days) {
        String nowDateStr = formatDateTime(new Date(), "yyyy-MM-dd ");
        Date[] dates = new Date[2];
        if (endTimeStr.equalsIgnoreCase("24:00")) {
            endTimeStr = "23:59";
        }
        startTimeStr = nowDateStr + startTimeStr;
        endTimeStr = nowDateStr + endTimeStr;
        Date startDate = toDate(startTimeStr);
        Date endDate = toDate(endTimeStr);
        dates[0] = addDays(startDate, days);
        dates[1] = addDays(endDate, days);
        return dates;
    }

    private static Date timeToDate(String timeStr, int days) {
        String nowDateStr = formatDateTime(new Date(), "yyyy-MM-dd ");
        String dateStr = nowDateStr + timeStr;
        Date date = toDate(dateStr);
        return addDays(date, days);
    }

    private static List<Long> split(Date startTime, Date endTime) {
        List<Long> res = new ArrayList<>();
        Date tempDate = addMinute(startTime, MINUTES_INTERVAL);
        while (true) {
            if (tempDate.getTime() <= endTime.getTime()) {
                res.add(tempDate.getTime());
                tempDate = addMinute(tempDate, MINUTES_INTERVAL);
            } else {
                break;
            }
        }
        return res;
    }

    private static List<Long> splitWithAddTime(Date startTime, Date endTime, int addTime) {
        List<Long> res = new ArrayList<>();
        Date tempDate = addMinute(startTime, addTime);
        while (true) {
            if (tempDate.getTime() <= endTime.getTime()) {
                res.add(tempDate.getTime());
                tempDate = addMinute(tempDate, MINUTES_INTERVAL);
            } else {
                break;
            }
        }
        return res;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeResult {
        private String label;
        private List<Long> times;
    }


    private static Date addDays(Date date, int days) {
        return add(date, Calendar.DAY_OF_MONTH, days);
    }


    public static Date addMinute(Date date, int minute) {
        return add(date, Calendar.MINUTE, minute);
    }

    public static Date getNowAfter15Date() {
        String time = WosaiDateTimeUtils.format(new Date().getTime(), "yyyy-MM-dd HH:mm");
        Date now = toDate(time);
        return addMinute(now, Constants.DEFAULT_COOK_TIME);
    }

    //获取尽快送时间
    public static Date getSoonDate(Integer addTime) {
        String time = WosaiDateTimeUtils.format(new Date().getTime(), "yyyy-MM-dd HH:mm");
        Date now = toDate(time);
        return addMinute(now, addTime);
    }


    private static Date add(final Date date, final int calendarField, final int amount) {
        final Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }


    @SneakyThrows
    public static Date toDate(String dateStr) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return simpleDateFormat.parse(dateStr);
    }

    @SneakyThrows
    public static int afterNowDays(Date date) {
        Date nowDate = toDate(formatDateTime(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        return (int) (date.getTime() - nowDate.getTime()) / (1000 * 3600 * 24);
    }

    @SneakyThrows
    public static Date toDate(String dateStr, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.parse(dateStr);
    }

    private static String formatDateTime(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
    }


    /**
     * 判断当前时间是否在给定时间区间内。[08:00,21:00]闭区间
     * 传入时间为字符串 HH:mm 格式
     *
     * @param startTime 开始时间 08:00
     * @param endTime   结束时间 21:00
     * @return
     */
    public static boolean isInDate(String startTime, String endTime) {
        return isInDate4PresetTime(startTime, endTime, System.currentTimeMillis());
    }

    /**
     * 判断预定时间是否在给定时间区间内。
     * 传入时间为字符串 HH:mm 格式
     *
     * @param startTime  开始时间 08:00
     * @param endTime    结束时间 21:00
     * @param presetTime 预定时间，毫秒时间戳
     * @return
     */
    public static boolean isInDate4PresetTime(String startTime, String endTime, long presetTime) {
        LocalTime now = LocalDateTime.ofInstant(Instant.ofEpochMilli(presetTime), ZoneId.of("+8")).toLocalTime();
        LocalTime start = LocalTime.parse(startTime, DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime end = LocalTime.parse(endTime, DateTimeFormatter.ofPattern("HH:mm"));
        if (start.isAfter(end)) {
            //[22:00,03:00] 跨天营业时间。比较逻辑为 now>=start || now <=end
            return now.compareTo(start) >= 0 || now.compareTo(end) <= 0;
        } else if (end.isAfter(start)) {
            //[09:00,18:00] 当日内营业时间。比较逻辑为 now>=start && now <=end
            return now.compareTo(start) >= 0 && now.compareTo(end) <= 0;
        } else {
            //[00:00,24:00] 会被替换为 [00:00,00:00]
            //开始时间和结束时间相同。全天营业，直接返回ture
            return true;
        }
    }

    public static List<TimeResult> onlyImmediateOrder() {
        List<TimeResult> times = new ArrayList<>();
        times.add(0, new TimeResult(getLabel(0), new ArrayList<Long>() {{
            add(-1L);
        }}));
        return times;
    }

    /**
     * 根据自定义时间点，获取预定时间
     * @param businessTimes
     * @param days
     * @param customizedSettingTime
     * @return
     */
    public static List<TimeResult> getCustomBookTimes(String businessTimes, List<Integer> days, String customizedSettingTime,Integer addTime) {
        if (CollectionUtil.isEmpty(days)) {
            return new ArrayList<>();
        }
        days.sort(Integer::compareTo);
        List<Map<String, String>> timeRangeList = new ArrayList<>();
        Map<String, List<Map<String, String>>> timeRangeMap = JacksonUtil.toBean(businessTimes, new TypeReference<TreeMap<String, List<Map<String, String>>>>() {
        });
        List<Long> resultTimes = new ArrayList<>();
        Date now = addMinute(new Date(), addTime);
        for (int i = days.get(0); i < days.get(days.size() - 1) + 1; i++) {
            // 获取日期组显示的名称
            // 分割好时间
            int day = LocalDate.now().plusDays(i).getDayOfWeek().getValue();
            timeRangeList = timeRangeMap.get(String.valueOf(day));
//            if (timeRangeList != null) {
            if (CollectionUtils.isNotEmpty(timeRangeList)) {
                for (Map<String, String> map : timeRangeList) {
                    // 获取Date格式的营业时间起止
                    Date[] dates = getDate(map.get("startTime"), map.get("endTime"), i);
                    if (i == 0 && System.currentTimeMillis() > dates[1].getTime()) {
                        continue;
                    }

                    // 分隔时间段
                    List<Long> dateSplits = dateSplitsByCustomized(dates[0], dates[1], customizedSettingTime, now);
                    if (CollectionUtil.isNotEmpty(dateSplits)) {
                        resultTimes.addAll(dateSplits);
                    }
                }
            }
        }
        // 存放最终返回前端的结果
        List<TimeResult> times = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resultTimes)) {
            resultTimes.sort(Long::compareTo);
            LinkedHashMap<String, List<Long>> groupMap = resultTimes.stream()
                    .collect(Collectors.groupingBy(t -> WosaiDateTimeUtils.format(t, "yyyy-MM-dd"), LinkedHashMap::new, Collectors.toList()));
            groupMap.forEach((k, v) -> times.add(new TimeResult(getLabel(afterNowDays(toDate(k, "yyyy-MM-dd"))), v)));
        }
        return times;
    }

    /**
     * 计算在开始时间和结束时间内，有效的自定义时间点，返回毫秒时间戳
     * customizedSettingTime = ["12:00","15:30","18:00"]
     * @param start 开始时间。不一定是今天
     * @param end 结束时间。不一定是今天
     * @param customizedSettingTime
     * @return 毫秒时间戳
     */
    private static List<Long> dateSplitsByCustomized(Date start, Date end, String customizedSettingTime,Date now) {
        List<Long> validTimes = new ArrayList<>();
        List<String> settings = JSON.parseArray(customizedSettingTime, String.class);
        for (String settingTime : settings) {
            String nowDayStr = formatDateTime(start, "yyyy-MM-dd ");
            String timeStr = nowDayStr + settingTime;
            Date timePoint = toDate(timeStr);
            if (!timePoint.before(now) && !timePoint.before(start) && !timePoint.after(end)) {
                validTimes.add(timePoint.getTime());
            }
        }
        return validTimes;
    }

}
