package com.wosai.pantheon.uf4c.config;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.ErrorData;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.common.exception.*;
import com.wosai.market.trade.exception.MarketTradeException;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.BaseException;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ErrorCode;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.web.api.exception.UnknownException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Optional;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Component
@Slf4j
public class JsonRpcErrorResolver implements ErrorResolver {
    @Override
    public ErrorResolver.JsonError resolveError(Throwable throwable, Method method, List<JsonNode> list) {
        //valid错误
        String throwableName = throwable.getClass().getName();
        ErrorData defaultErrorData = new ErrorData(throwableName, throwable.getMessage());
        CommonException commonException = null;
        try {
            // exception => response
            if (throwable instanceof MethodArgumentNotValidException) {
                logErrorOnlyMessage(method,list,"MethodArgumentNotValidException");
                FieldError fieldError = ((MethodArgumentNotValidException) throwable).getBindingResult().getFieldError();
                if (fieldError == null) {
                    return new ErrorResolver.JsonError(CommonException.CODE_INVALID_PARAMETER, ((MethodArgumentNotValidException) throwable).getBindingResult().getGlobalError().getDefaultMessage(),defaultErrorData);
                }
                return new ErrorResolver.JsonError(CommonException.CODE_INVALID_PARAMETER, fieldError.getField() + fieldError.getDefaultMessage(),defaultErrorData);
            } else if (throwable instanceof MarketTradeException) {
                logErrorFullStack(method,list,throwable);
                return new ErrorResolver.JsonError(CommonException.CODE_BIZ_EXCEPTION, throwable.getMessage(),defaultErrorData);
            } else if (throwable instanceof ParamException) {
                logErrorOnlyMessage(method,list, ReturnCode.INVALID_PARAM_EXCEPTION.name());
                return new ErrorResolver.JsonError(CommonException.CODE_INVALID_PARAMETER, throwable.getMessage(),defaultErrorData);
            }else if (throwable instanceof BusinessException){
                logErrorOnlyMessage(method,list, ReturnCode.BUSINESS_ERROR.name());
                BusinessException exception = (BusinessException) throwable;

                return new ErrorResolver.JsonError(Optional.ofNullable(convertErrorCode(exception.getCode())).orElse(ErrorCode.BUSINESS_ERROR.value()), throwable.getMessage(),defaultErrorData);
            }else if (throwable instanceof UnknownException) {
                logErrorOnlyMessage(method,list, ReturnCode.BUSINESS_ERROR.name());
                return new ErrorResolver.JsonError(CommonException.CODE_BIZ_EXCEPTION, throwable.getMessage(),defaultErrorData);
            }else if (throwable instanceof BaseException) {
                logErrorFullStack(method,list,throwable);
                return new ErrorResolver.JsonError(CommonException.CODE_BIZ_EXCEPTION, throwable.getMessage(),defaultErrorData);
            } else {
                logErrorFullStack(method,list,throwable);
                if (StringUtils.isNotBlank(throwable.getMessage())){
                    return new ErrorResolver.JsonError(CommonException.CODE_BIZ_EXCEPTION,throwable.getMessage(),defaultErrorData);
                }
                return new ErrorResolver.JsonError(CommonException.CODE_BIZ_EXCEPTION,throwable.getMessage(),defaultErrorData);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        if (commonException != null) {
            return new ErrorResolver.JsonError(commonException.getCode(), commonException.getMessage(), defaultErrorData);
        }
        return new ErrorResolver.JsonError(CommonException.CODE_UNKNOWN_ERROR, Optional.ofNullable(throwable.getMessage()).orElse(CommonException.getCodeDesc(CommonException.CODE_UNKNOWN_ERROR)), defaultErrorData);
    }

    private String getUri(Method method){
        return method.getDeclaringClass().getAnnotation(JsonRpcService.class).value() + "/" + method.getName();
    }


    private Integer convertErrorCode(String code){
        if (StringUtils.isBlank(code)){
            return null;
        }
        try{
            Integer codeInt = Integer.valueOf(code);
            return codeInt;
        }catch (Exception ex){
            return null;
        }
    }

    private void logErrorFullStack(Method method ,List<JsonNode> params, Throwable e){

        String userId = Optional.ofNullable(ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO()).map(requestContext -> requestContext.getUserId()).orElse("");
        log.error(String.format("Exception raised",
                        keyValue("params",JSON.toJSONString(params))),
                keyValue("user_id",userId),
                keyValue("uri",getUri(method)),e);
    }

    private void logErrorOnlyMessage(Method method ,List<JsonNode> params, String erroryType){

        String userId = Optional.ofNullable(ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO()).map( requestContext -> requestContext.getUserId()).orElse("");
        log.error(String.format("Exception raised",
                        keyValue("params", JSON.toJSONString(params))),
                keyValue("user_id",userId),
                keyValue("uri",getUri(method)),
                keyValue("error_type",erroryType));
    }
}
