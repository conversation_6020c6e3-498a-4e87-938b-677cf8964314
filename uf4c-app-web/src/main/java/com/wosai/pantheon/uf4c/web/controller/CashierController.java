package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.service.apisix.CashierService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(path = "/api/v1/cashier", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@RequiredArgsConstructor
public class CashierController {

    @Autowired
    private CashierService cashierService;

    @RequestMapping("/check/online")
    @ResponseBody
    public boolean isOnline(@RequestParam Map queryParam){
        return cashierService.isOnline(ApiRequest.buildGetRequest(queryParam));
    }
}
