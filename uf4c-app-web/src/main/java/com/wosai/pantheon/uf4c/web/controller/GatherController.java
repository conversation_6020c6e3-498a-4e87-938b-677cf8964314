package com.wosai.pantheon.uf4c.web.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.fallbackconfig.server.BlockHandleServer;
import com.wosai.pantheon.uf4c.model.GatherOrderExtraRequest;
import com.wosai.pantheon.uf4c.model.GatherOrderRequest;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.service.apisix.GatherService;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@Slf4j
@RequestMapping(path = "/api/v1/gather", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class GatherController {

    @Autowired
    GatherService gatherService;


    /**
     * 点单页聚合接口
     *
     * @param request
     * @return
     */
    @GetMapping("/index")
    public Map<String, Object> index(GatherRequest request) {
        return indexTemporary(request);
    }

    /**
     * 2024-03-06
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/GatherController/index",blockHandlerClass = BlockHandleServer.class,blockHandler = "handleIndex")
    private Map<String, Object> indexTemporary(GatherRequest request) {
        return gatherService.index(new ApiRequest<>(request));
    }


    /**
     * 下单页聚合接口（主要数据）
     *
     * @param request
     * @return
     */
    @GetMapping("/order/main")
    public Map<String, Object> orderMain(GatherOrderRequest request) {
        return gatherService.orderMain(new ApiRequest<>(request));
    }

    /**
     * 下单页聚合接口（额外数据）
     *
     * @param request
     * @return
     */
    @GetMapping("/order/extra")
    public Map<String, Object> orderExtra(GatherOrderExtraRequest request) {
        return gatherService.orderExtra(new ApiRequest<>(request));
    }


    /**
     * 点单页聚合接口
     */
    @GetMapping("/cleanCache")
    public String cleanCache(String storeId) {
        try {
            gatherService.cleanCache(storeId);
        } catch (Exception ignored) {

        }
        return "success";
    }
}
