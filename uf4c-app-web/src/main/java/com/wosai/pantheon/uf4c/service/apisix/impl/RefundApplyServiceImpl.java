package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.fasterxml.jackson.core.type.TypeReference;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.model.MerchantUserPushSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSidePushService;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.push.api.bean.PushIotBizSoundRequest;
import com.wosai.app.push.api.service.IPushMqttService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.data.util.CollectionUtil;
import com.wosai.market.data.events.api.AppNotifyService;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.merchant.api.CampusRemoteService;
import com.wosai.pantheon.order.constant.Constants;
import com.wosai.pantheon.order.enums.OrderSource;
import com.wosai.pantheon.order.enums.OrderStatus;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.order.model.dto.v2.OrderMainDTO;
import com.wosai.pantheon.order.pojo.OrderGoods4Refund;
import com.wosai.pantheon.order.pojo.OrderRefundApplyDTO;
import com.wosai.pantheon.order.pojo.RefundApplyItemInfo;
import com.wosai.pantheon.order.service.OrderRefundApplyService;
import com.wosai.pantheon.order.service.apisix.Uf4cApisixOrderService;
import com.wosai.pantheon.order.service.v2.OrderMainService;
import com.wosai.pantheon.order.utils.RefundApplyHelper;
import com.wosai.pantheon.printer.enums.PrintSourceType;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.RefundApplyRequest;
import com.wosai.pantheon.uf4c.service.UprintService;
import com.wosai.pantheon.uf4c.service.apisix.RefundApplyService;
import com.wosai.pantheon.uf4c.util.*;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.apisix.uf4capp.Uf4cAppApiRequest;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.domain.RefundGoodsDTO;
import com.wosai.smartbiz.oms.api.domain.RefundResultWrapper;
import com.wosai.smartbiz.oms.api.query.RefundRequest;
import com.wosai.smartbiz.oms.api.services.OrderRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class RefundApplyServiceImpl implements RefundApplyService {

    @Autowired
    OrderMainService awesomeOrderMainService;

    @Autowired
    CampusRemoteService merchantCampusService;

    @Autowired
    OrderRefundApplyService orderRefundApplyService;

    @Autowired
    Uf4cApisixOrderService uf4cApisixOrderService;

    @Autowired
    OrderRpcService orderRpcService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private ClientSidePushService clientSidePushService;

    @Autowired
    private IPushMqttService iPushMqttService;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private AppNotifyService appNotifyService;

    @Autowired
    private UprintService uprintService;

    @Value("${refund.apply.push.aop-code}")
    private String aopCode;

    @Value("${refund.apply.push.notice-aop-code}")
    private String noticeAopCode;

    @Value("${refund.apply.push.cancel-takeout.template-id}")
    private String cancelTakeoutTemplateId;

    @Value("${refund.apply.push.cancel-pre.template-id}")
    private String cancelPreTemplateId;

    @Value("${refund.apply.push.refund-takeout.template-id}")
    private String refundTakeoutTemplateId;

    @Value("${refund.apply.push.refund-pre.template-id}")
    private String refundPreTemplateId;

    @Value("${refund.apply.push.refund-revoke.template-id}")
    private String refundRevokeTemplateId;

    @Autowired
    MerchantUserServiceV2 merchantUserServiceV2;


    // 语音播报
    @Value("${refund.apply.voice.cancel-takeout}")
    private String voiceCancelTakeout;
    @Value("${refund.apply.voice.cancel-pre}")
    private String voiceCancelPre;
    @Value("${refund.apply.voice.refund-takeout}")
    private String voiceRefundTakeout;
    @Value("${refund.apply.voice.refund-pre}")
    private String voiceRefundPre;


    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private Config appConfig;

    ExecutorService executorService = Executors.newFixedThreadPool(2);

    private static final String CAMPUS_APPLY_REFUND_EXPIRE_MILLS_CONFIG_KEY = "APPLY_REFUND_EXPIRE_MILLS_CONFIG_KEY";
    private static final String NON_CAMPUS_APPLY_REFUND_EXPIRE_MILLS_CONFIG_KEY = "NON_CAMPUS_APPLY_REFUND_EXPIRE_MILLS_CONFIG_KEY";

    @Override
    public boolean cancel(ApiRequest<Map> request) {
        Map map = request.getBody();

        String sn = MapUtils.getString(map, "sn");

        if (StringUtils.isBlank(sn)) {
            throw new ParamException("订单号不能为空");
        }

        Result<OrderMainDTO> result = awesomeOrderMainService.getBySn(sn);
        if (!result.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
        }

        OrderRefundApplyDTO refundApplyDTO = orderRefundApplyService.selectBySn(sn);
        if (refundApplyDTO != null) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "不支持的操作");
        }

        OrderMainDTO orderMain = result.getData();

        //判断订单是否存在
        if (orderMain == null) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "订单不存在");
        }
        //判断订单类型
        if (!(orderMain.getOrderType() == OrderType.TAKE_OUT_ORDER || orderMain.getOrderType() == OrderType.PRE_ORDER)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "当前订单类型不支持无责取消");
        }

        if (!(orderMain.getStatus() == OrderStatus.PAID || orderMain.getStatus() == OrderStatus.RECEIVED
                || orderMain.getStatus() == OrderStatus.WAIT_TO_SEND_DELIVER || orderMain.getStatus() == OrderStatus.BOOKORDER_WAIT_FOR_NOTIFY)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "当前订单状态不支持无责取消");
        }

        //判断是否是自己的订单
        if (!StringUtils.equals(orderMain.getUserId(), ThreadLocalHelper.getUserId())) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "你无权取消当前订单");
        }

        if (orderMain.getReceiveAmount() == 0){
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "0元订单暂不支持退款");
        }

        //判断订单的接单时间
        Long acceptTime = Optional.ofNullable(MapUtils.getLong(orderMain.getExtra(), Constants.OrderExtraKey.EXTRA_KEY_ACCEPT_TIME)).orElse(orderMain.getPayTime().getTime());


        //时间超过一分钟， 则不允许取消，并返回一个特定的错误码（这个错误码前端会使用，不要随便更改）
        if (System.currentTimeMillis() - acceptTime > 1000 * 60) {
            throw new BusinessException(ReturnCode.ORDER_CANCEL_EXPIRED);
        }

        Uf4cAppApiRequest<Map> uf4cAppApiRequest = new Uf4cAppApiRequest<>();
        Map queryMap = new HashMap();
        queryMap.put("sn", orderMain.getSn());
        queryMap.put("merchant_id", orderMain.getMerchantId());
        queryMap.put("order_type", Optional.ofNullable(orderMain.getOrderType()).map(OrderType::getCode).orElse(null));
        uf4cAppApiRequest.setBody(queryMap);
        List<OrderGoods4Refund> refundGoodsList = uf4cApisixOrderService.findGoods4Refund(uf4cAppApiRequest);


        OrderRefundApplyDTO orderRefundApplyDTO = new OrderRefundApplyDTO();
        orderRefundApplyDTO.setSn(orderMain.getSn());
        orderRefundApplyDTO.setApplyReason("顾客1分钟内无责取消订单");
        orderRefundApplyDTO.setMerchantId(orderMain.getMerchantId());
        orderRefundApplyDTO.setStoreId(orderMain.getStoreId());
        orderRefundApplyDTO.setOrderSeq(orderMain.getOrderSeq());
        orderRefundApplyDTO.setUserId(orderMain.getUserId());
        orderRefundApplyDTO.setApplyStatus(RefundApplyHelper.OrderRefundApplyConstants.APPLY_STATUS_INIT);
        orderRefundApplyDTO.setApplyTime(System.currentTimeMillis());
        orderRefundApplyDTO.setExpireTime(orderRefundApplyDTO.getApplyTime());
        orderRefundApplyDTO.setOrderType(orderMain.getOrderType());
        orderRefundApplyDTO.setNotified(YesNoEnum.N);
        orderRefundApplyDTO.setRefundType(RefundApplyHelper.OrderRefundApplyConstants.REFUND_TYPE_CANCEL);
        orderRefundApplyDTO.setRefundAmount(orderMain.getReceiveAmount());

        if (CollectionUtils.isNotEmpty(refundGoodsList)) {
            RefundRequest refundRequest = new RefundRequest();
            refundRequest.setSn(orderMain.getSn());
            refundRequest.setRefundGoods(refundGoodsList.stream().map(item -> {
                RefundGoodsDTO refundGoodsDTO = new RefundGoodsDTO();
                refundGoodsDTO.setId(item.getId());
                refundGoodsDTO.setRefundCount(item.getCount());
                return refundGoodsDTO;
            }).collect(Collectors.toList()));

            Result<RefundResultWrapper> refundResultWrapperResult = orderRpcService.getRefundAmount(refundRequest);

            if (!refundResultWrapperResult.isSuccess()) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, refundResultWrapperResult.getErrorMsg());
            }

            RefundResultWrapper wrapper = refundResultWrapperResult.getData();
            orderRefundApplyDTO.setRefundItems(refundGoodsList.stream().map(item -> {
                RefundApplyItemInfo refundApplyItemInfo = new RefundApplyItemInfo();
                refundApplyItemInfo.setId(item.getId());
                refundApplyItemInfo.setName(item.getName());
                refundApplyItemInfo.setRefundCount(item.getCount());
                refundApplyItemInfo.setAttachInfo(item.getAttachInfo());
                refundApplyItemInfo.setMainImage(item.getMainImage());

                if (CollectionUtils.isNotEmpty(wrapper.getRefundGoods())) {
                    Long refundAmount = wrapper.getRefundGoods().stream().filter(refundGood -> StringUtils.equals(item.getId(), refundGood.getId()))
                            .findFirst().map(goods -> goods.getRefundAmount()).orElse(null);
                    refundApplyItemInfo.setRefundAmount(refundAmount);

                }
                return refundApplyItemInfo;
            }).collect(Collectors.toList()));
        }


        orderRefundApplyService.upsert(orderRefundApplyDTO);

        //打印取消的小票
        PrintSourceType sourceType;
        if(com.wosai.pantheon.uf4c.constant.Constants.RETAIL.equals(MapUtil.getString(orderMain.getExtra(), com.wosai.pantheon.uf4c.constant.Constants.MERCHANT_INDUSTRY_TYPE))){
            sourceType = PrintSourceType.RETAIL;
        } else {
            sourceType = PrintSourceType.SMART;
        }
        uprintService.printRefundCancelTemplate(orderRefundApplyDTO, orderMain.getStoreName(), sourceType);

        Map<String, Object> data = new HashMap<>();
        data.put("storeName", orderMain.getStoreName());
        data.put("orderSeq", orderMain.getOrderSeq());
        data.put("orderSn", orderMain.getSn());
        data.put("storeId", orderMain.getStoreId());

        if (orderMain.getOrderType() == OrderType.TAKE_OUT_ORDER) {
            sendNoticeAndPush(orderMain.getMerchantId(), orderMain.getStoreId(), data, cancelTakeoutTemplateId);
            sendVoice(orderMain.getStoreSn(), voiceCancelTakeout);
        } else {
            sendNoticeAndPush(orderMain.getMerchantId(), orderMain.getStoreId(), data, cancelPreTemplateId);
            sendVoice(orderMain.getStoreSn(), voiceCancelPre);
        }

        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setSn(sn);
        refundRequest.setRefundAmount(orderRefundApplyDTO.getRefundAmount());
        refundRequest.setOrderSource(OrderSource.MINI);
        refundRequest.setRefundReason("接单1分钟内，顾客取消订单系统自动通过");
        //不打印退款单、退菜单
        refundRequest.setNotifyBackKitchen(false);
        refundRequest.setPrintRefund(false);

        if (CollectionUtils.isNotEmpty(refundGoodsList)) {
            refundRequest.setRefundGoods(refundGoodsList.stream().map(item -> {
                RefundGoodsDTO refundGoodsDTO = new RefundGoodsDTO();
                refundGoodsDTO.setId(item.getId());
                refundGoodsDTO.setRefundCount(item.getCount());
                return refundGoodsDTO;
            }).collect(Collectors.toList()));
        }

        refundRequest.setRefundApplyAgreeType(RefundApplyHelper.OrderRefundApplyConstants.REFUND_PROCESS_TYPE_AUTO);

        Result<Boolean> refundResult = orderRpcService.refundAll(refundRequest);


        WeakReferenceCaller.callReturnVoid(() -> appNotifyService.sendRefundApplyUpdateEvent(orderMain.getMerchantId(), orderMain.getStoreId()));

        if (refundResult.isSuccess()) {
            return true;
        } else {
            throw new com.wosai.smartbiz.base.exceptions.BusinessException(refundResult.getErrorMsg());
        }
    }

    @Override
    public boolean revokeApply(ApiRequest<Map> request) {
        Map map = request.getBody();

        String sn = MapUtils.getString(map, "sn");

        if (StringUtils.isBlank(sn)) {
            throw new ParamException("订单号不能为空");
        }

        Result<OrderMainDTO> result = awesomeOrderMainService.getBySn(sn);
        if (!result.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
        }

        OrderMainDTO orderMain = result.getData();

        //判断是否是自己的订单
        if (!StringUtils.equals(orderMain.getUserId(), ThreadLocalHelper.getUserId())) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "你无权操作当前订单");
        }

        boolean revokeApplySuccess = orderRefundApplyService.revokeApply(sn);
        if (revokeApplySuccess) {
            WeakReferenceCaller.callReturnVoid(() -> {
                // 撤销成功后去进行服务通知和推送
                Map<String, Object> data = new HashMap<>();
                data.put("storeName", orderMain.getStoreName());
                data.put("orderSeq", orderMain.getOrderSeq());
                data.put("orderSn", orderMain.getSn());
                data.put("storeId", orderMain.getStoreId());
                data.put("orderType", "自取");
                if (orderMain.getOrderType() == OrderType.TAKE_OUT_ORDER) {
                    data.put("orderType", "外卖");
                }
                sendNoticeAndPush(orderMain.getMerchantId(), orderMain.getStoreId(), data, refundRevokeTemplateId);
                OrderRefundApplyDTO orderRefundApplyDTO = orderRefundApplyService.selectBySn(orderMain.getSn());
                //打印撤销申请单
                uprintService.printRefundApplyRevokeTemplate(orderRefundApplyDTO, orderMain.getStoreName());
            });
        }


        return revokeApplySuccess;
    }

    @Override
    public boolean refundApply(ApiRequest<RefundApplyRequest> apiRequest) {


        RefundApplyRequest request = apiRequest.getBody();

        if (StringUtils.isBlank(request.getSn())) {
            throw new ParamException("订单号不能为空");
        }

        if (CollectionUtils.isEmpty(request.getGoods())) {
            throw new ParamException("请选择要退款的商品");
        }

        request.setGoods(request.getGoods().stream().filter(item -> item.getRefundCount() != null && item.getRefundCount() > 0).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(request.getGoods())) {
            throw new ParamException("请选择要退款的商品");
        }

        if (StringUtils.isBlank(request.getReason())) {
            throw new ParamException("请选择退款原因");
        }


        Result<OrderMainDTO> result = awesomeOrderMainService.getBySn(request.getSn());
        if (!result.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
        }

        OrderMainDTO orderMain = result.getData();

        //判断订单是否存在
        if (orderMain == null) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "订单不存在");
        }
        //判断订单类型
        if (!(orderMain.getOrderType() == OrderType.TAKE_OUT_ORDER || orderMain.getOrderType() == OrderType.PRE_ORDER)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "当前订单类型不支持退款申请");
        }

        OrderRefundApplyDTO refundApplyDTO = orderRefundApplyService.selectBySn(request.getSn());
        if (refundApplyDTO != null && !StringUtils.equals(refundApplyDTO.getApplyStatus(), RefundApplyHelper.OrderRefundApplyConstants.APPLY_STATUS_REVOKED)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "您已经申请过退款或者取消订单，无法重新申请退款");
        }


        //判断是否是自己的订单
        if (!StringUtils.equals(orderMain.getUserId(), ThreadLocalHelper.getUserId())) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "你无权操作当前订单");
        }

        //判断订单的接单时间
        Long acceptTime = Optional.ofNullable(MapUtils.getLong(orderMain.getExtra(), Constants.OrderExtraKey.EXTRA_KEY_ACCEPT_TIME)).orElse(orderMain.getPayTime().getTime());
        Long finishTime = Optional.ofNullable(MapUtils.getLong(orderMain.getExtra(), Constants.OrderExtraKey.EXTRA_KEY_FINISH_TIME)).orElse(orderMain.getMtime().getTime());
        boolean refundApplyEnabled = RefundApplyHelper.refundApplyEnabled(orderMain.getStatus(), acceptTime, finishTime);

        if (!refundApplyEnabled) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "当前订单不能申请退款");
        }


        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setSn(request.getSn());
        refundRequest.setRefundGoods(request.getGoods());

        Result<RefundResultWrapper> refundResultWrapperResult = orderRpcService.getRefundAmount(refundRequest);

        if (!refundResultWrapperResult.isSuccess()) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, refundResultWrapperResult.getErrorMsg());
        }

        RefundResultWrapper wrapper = refundResultWrapperResult.getData();


        boolean partRefund = false;

        if (wrapper.getRefundAmount() < orderMain.getReceiveAmount()) {
            partRefund = true;
        }

        if (partRefund && orderMain.getStatus() != OrderStatus.FINISHED) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "进行中订单不能申请部分退款");
        }


        ConfigResponse businessTimes = configRemoteService.findByName(MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, orderMain.getStoreId(), com.wosai.pantheon.uf4c.constant.Constants.DELIVERY_TIMES));
        Map<String, List<Map<String, String>>> timeMaps = Optional.ofNullable(businessTimes)
                .map(it -> JacksonUtil.toBean(it.getValue(), new TypeReference<TreeMap<String, List<Map<String, String>>>>() {
                })).orElse(null);

        OrderRefundApplyDTO orderRefundApplyDTO = new OrderRefundApplyDTO();
        orderRefundApplyDTO.setSn(orderMain.getSn());
        orderRefundApplyDTO.setOrderSeq(orderMain.getOrderSeq());
        orderRefundApplyDTO.setApplyReason(request.getReason());
        orderRefundApplyDTO.setRefundItems(request.getGoods().stream().map(item -> {
            RefundApplyItemInfo refundApplyItemInfo = new RefundApplyItemInfo();
            refundApplyItemInfo.setId(item.getId());
            refundApplyItemInfo.setName(item.getName());
            refundApplyItemInfo.setRefundCount(item.getRefundCount());
            refundApplyItemInfo.setAttachInfo(item.getAttachInfo());
            refundApplyItemInfo.setMainImage(item.getMainImage());

            if (CollectionUtils.isNotEmpty(wrapper.getRefundGoods())) {
                Long refundAmount = wrapper.getRefundGoods().stream().filter(refundGood -> StringUtils.equals(item.getId(), refundGood.getId()))
                        .findFirst().map(goods -> goods.getRefundAmount()).orElse(null);
                refundApplyItemInfo.setRefundAmount(refundAmount);

            }

            return refundApplyItemInfo;
        }).collect(Collectors.toList()));
        orderRefundApplyDTO.setMerchantId(orderMain.getMerchantId());
        orderRefundApplyDTO.setStoreId(orderMain.getStoreId());
        orderRefundApplyDTO.setUserId(orderMain.getUserId());
        orderRefundApplyDTO.setApplyStatus(RefundApplyHelper.OrderRefundApplyConstants.APPLY_STATUS_INIT);
        orderRefundApplyDTO.setApplyTime(System.currentTimeMillis());

        orderRefundApplyDTO.setExpireTime(getExpireTime(orderMain.getStoreId(), timeMaps));


        orderRefundApplyDTO.setOrderType(orderMain.getOrderType());
        orderRefundApplyDTO.setNotified(YesNoEnum.N);
        orderRefundApplyDTO.setRefundType(RefundApplyHelper.OrderRefundApplyConstants.REFUND_TYPE_APPLY);
        orderRefundApplyDTO.setRefundAmount(wrapper.getRefundAmount());
        orderRefundApplyDTO.setPackAmount(wrapper.getPackAmount());
        orderRefundApplyDTO.setDeliveryAmount(wrapper.getDeliverAmount());
        orderRefundApplyDTO.setApplyProof(request.getProofs());
        orderRefundApplyDTO.setPartRefund(partRefund);

        Map<String, Object> data = new HashMap<>();
        data.put("storeName", orderMain.getStoreName());
        data.put("orderSeq", orderMain.getOrderSeq());
        data.put("orderSn", orderMain.getSn());
        data.put("storeId", orderMain.getStoreId());
        if (orderMain.getOrderType() == OrderType.TAKE_OUT_ORDER) {
            sendNoticeAndPush(orderMain.getMerchantId(), orderMain.getStoreId(), data, refundTakeoutTemplateId);
            sendVoice(orderMain.getStoreSn(), voiceRefundTakeout);
        } else {
            sendNoticeAndPush(orderMain.getMerchantId(), orderMain.getStoreId(), data, refundPreTemplateId);
            sendVoice(orderMain.getStoreSn(), voiceRefundPre);
        }


        boolean applyResult = orderRefundApplyService.upsert(orderRefundApplyDTO);

        if (applyResult) {
            PrintSourceType sourceType;
            if(com.wosai.pantheon.uf4c.constant.Constants.RETAIL.equals(MapUtil.getString(orderMain.getExtra(), com.wosai.pantheon.uf4c.constant.Constants.MERCHANT_INDUSTRY_TYPE))){
                sourceType = PrintSourceType.RETAIL;
            } else {
                sourceType = PrintSourceType.SMART;
            }
            uprintService.printRefundApplyTemplate(orderRefundApplyDTO, orderMain.getStoreName(),sourceType);
        }

        WeakReferenceCaller.callReturnVoid(() -> appNotifyService.sendRefundApplyUpdateEvent(orderMain.getMerchantId(), orderMain.getStoreId()));

        return applyResult;
    }

    private Long getExpireMills(String storeId) {
        Long campusExpireMills = apolloConfigHelper.getLongConfigValueByKey(CAMPUS_APPLY_REFUND_EXPIRE_MILLS_CONFIG_KEY, 90 * 60 * 1000L);
        Long nonCampusExpireMills = apolloConfigHelper.getLongConfigValueByKey(NON_CAMPUS_APPLY_REFUND_EXPIRE_MILLS_CONFIG_KEY, 240 * 60 * 1000L);
        Boolean isCampusStore = WeakReferenceCaller.call(() -> merchantCampusService.storeInAnyCampusByStoreId(storeId));

        if (isCampusStore == null || !isCampusStore) {
            //不是校园门店
            return nonCampusExpireMills;
        } else {
            return campusExpireMills;
        }
    }

    private long calculateRefundTime(long currentTime, long durationTime, Map<String, List<Map<String, String>>> timeMaps) {
        LocalDateTime currentDateTime = Instant.ofEpochMilli(currentTime).atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime refundTime = currentDateTime;
        long remainingTime = durationTime;
        if (Objects.isNull(timeMaps)) {
            refundTime = refundTime.plusNanos(remainingTime * 1_000_000);
            return refundTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        }
        DayOfWeek currentDay = refundTime.getDayOfWeek();
        int count = 0;
        while (remainingTime > 0 && count < 50) {
            count++;
            List<Map<String, String>> businessHours = timeMaps.get(String.valueOf(currentDay.getValue()));
            if (!businessHours.isEmpty()) {
                boolean isBusinessTimeFound = false;
                for (Map<String, String> businessHour : businessHours) {
                    LocalDateTime startTime = LocalDateTime.of(refundTime.toLocalDate(), parseTime(businessHour.get("startTime")));
                    LocalDateTime endTime = LocalDateTime.of(refundTime.toLocalDate(), parseTime(businessHour.get("endTime")));
                    if (refundTime.isBefore(startTime)) {
                        refundTime = startTime;
                    }
                    if (refundTime.isAfter(endTime)) {
                        continue;
                    }
                    long timeInterval = Duration.between(refundTime, endTime).toMillis();
                    if (timeInterval >= remainingTime) {
                        refundTime = refundTime.plusNanos(remainingTime * 1_000_000);
                        isBusinessTimeFound = true;
                        break;
                    } else {
                        remainingTime -= timeInterval;
                    }
                }
                if (isBusinessTimeFound) {
                    break;
                }
            }
            refundTime = getNextBusinessDayStartTime(refundTime, currentDay.plus(1), timeMaps);
            currentDay = refundTime.getDayOfWeek();
            if (refundTime.isEqual(currentDateTime)) {
                // 如果找不到合适的营业时间段，退出循环并返回当前时间+延迟时间
                refundTime = refundTime.plusNanos(durationTime * 1_000_000);
                break;
            }
        }
        return refundTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    private LocalDateTime getNextBusinessDayStartTime(LocalDateTime currentDateTime, DayOfWeek nextBusinessDay, Map<String, List<Map<String, String>>> timeMaps) {
        DayOfWeek currentDay = nextBusinessDay;
        int dayCount = 1;
        while (dayCount <= 7) {
            List<Map<String, String>> businessHours = timeMaps.get(String.valueOf(currentDay.getValue()));
            if (!businessHours.isEmpty()) {
                String startTime = businessHours.get(0).get("startTime");
                LocalDate nextBusinessDateTime = currentDateTime.toLocalDate().plusDays(dayCount);
                return LocalDateTime.of(nextBusinessDateTime, parseTime(startTime));
            }
            currentDay = currentDay.plus(1);
            dayCount++;
        }
        return currentDateTime;
    }

    private LocalTime parseTime(String time) {
        if (time.equals("24:00")) {
            return LocalTime.MAX;
        } else {
            return LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm"));
        }
    }

    public Long getExpireTime(String storeId, Map<String, List<Map<String, String>>> timeMaps) {
        long expireMills = getExpireMills(storeId);
        Boolean refundApplySwitch = appConfig.getBooleanProperty("refundApplySwitch", Boolean.TRUE);
        if (!refundApplySwitch) {
            return expireMills;
        }
        return calculateRefundTime(System.currentTimeMillis(), expireMills, timeMaps);
    }

    private void sendNoticeAndPush(String merchantId, String storeId, Map noticeData, String templateId) {
        try {

            QueryMerchantUserReq queryMerchantUserReq = new QueryMerchantUserReq();
            queryMerchantUserReq.setMerchant_id(merchantId);
            queryMerchantUserReq.setStore_id(storeId);
            queryMerchantUserReq.setContain_partner(true);
            List<UcMerchantUserInfo> merchantUserInfos = merchantUserServiceV2.getMerchantUser(queryMerchantUserReq);
            if (CollectionUtils.isNotEmpty(merchantUserInfos)) {
                merchantUserInfos.stream().forEach(user -> {
                    executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            MerchantUserPushSendModel merchantUserPushSendModel = new MerchantUserPushSendModel() {{
                                setDevCode(aopCode);
                                setTemplateCode(templateId);
                                setMerchantUserId(user.getMerchant_user_id());
                                setData(CollectionUtil.hashMap("storeId", storeId));
                                setTimestamp(System.currentTimeMillis());
                            }};

                            clientSidePushService.sendToMerchantUser(merchantUserPushSendModel);
                            log.info("[send user push] content:{}", JSON.toJSONString(merchantUserPushSendModel));

                            MerchantUserNoticeSendModel merchantUserPushSendModelNotice = new MerchantUserNoticeSendModel() {{
                                setDevCode(aopCode);
                                setTemplateCode(templateId);
                                setMerchantUserId(user.getMerchant_user_id());
                                setData(noticeData);
                                setTimestamp(System.currentTimeMillis());
                            }};

                            clientSideNoticeService.sendToMerchantUser(merchantUserPushSendModelNotice);
                            log.info("[send user notice] content:{}", JSON.toJSONString(merchantUserPushSendModelNotice));
                        }
                    });
                });

            }

        } catch (Exception ex) {
            log.warn("发送消息推送失败 -> {}", noticeData, keyValue("method", "sendPush"), ex);
        }
    }

    private void sendVoice(String storeSn, String voice) {
        PushIotBizSoundRequest pushIotBizSoundRequest = new PushIotBizSoundRequest();
        pushIotBizSoundRequest.setStoreSn(storeSn);
        pushIotBizSoundRequest.setVoice(voice);
        pushIotBizSoundRequest.setBizCode("1");
        try {
            iPushMqttService.pushBizSoundToSoundBox(pushIotBizSoundRequest);
            log.info("发送4G音箱语音播报", keyValue("method", "sendVoice"), keyValue("arguments", storeSn + ":" + voice));
        } catch (Exception e) {
            log.warn("发送4G音箱语音播报失败 -> {}", storeSn, keyValue("method", "sendVoice"), e);
        }
    }
}
