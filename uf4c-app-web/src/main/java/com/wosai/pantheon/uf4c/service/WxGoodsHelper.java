package com.wosai.pantheon.uf4c.service;

import com.wosai.market.mcc.api.dto.request.FindWhiteListByOwnerIdRequest;
import com.wosai.market.mcc.api.dto.response.WhiteListResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.enums.WhiteListBusinessType;
import com.wosai.market.mcc.api.service.WhiteListRemoteService;
import com.wosai.market.merchant.api.WxGoodsSubsidyRemoteService;
import com.wosai.market.merchant.dto.WxGoodsSubsidyDTO;
import com.wosai.market.merchant.dto.request.BaseRequest;
import com.wosai.pantheon.core.uitem.model.Item;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.model.TimeSection;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.GoodsUnitTypeEnum;
import com.wosai.pantheon.order.enums.SkuType;
import com.wosai.pantheon.order.enums.SpuType;
import com.wosai.pantheon.order.model.dto.request.OrderItemAddRequest;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.dto.WxGoodsSubsidy;
import com.wosai.pantheon.uf4c.model.vo.WxActProductVO;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信加价购：
 * 1.查询门店活动列表
 * 2.校验加购商品有效
 * 2024-03-05 完全停用
 */
@Slf4j
@Deprecated
public class WxGoodsHelper {
    @Autowired
    private WxGoodsSubsidyRemoteService wxGoodsSubsidyRemoteService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private WhiteListRemoteService whiteListRemoteService;

    public List<WxGoodsSubsidyDTO> queryStoreActs(String storeId) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setStoreId(storeId);
        return wxGoodsSubsidyRemoteService.queryByStoreId(baseRequest);
    }

    public void checkEnable(List<WxGoodsSubsidy> wxGoods, String storeId) {
        if (CollectionUtils.isEmpty(wxGoods)) {
            return;
        }
        Map<String, WxGoodsSubsidyDTO> enableActs = queryStoreActs(storeId).stream().collect(Collectors.toMap(WxGoodsSubsidyDTO::getActId, o -> o));
        for (WxGoodsSubsidy wxGoodsSubsidy : wxGoods) {
            String actId = wxGoodsSubsidy.getActId();
            if (enableActs.containsKey(actId)) {
                WxGoodsSubsidyDTO wxGoodsSubsidyDTO = enableActs.get(actId);
                wxGoodsSubsidy.setSpuId(wxGoodsSubsidyDTO.getSpuId());
                wxGoodsSubsidy.setActGoodsName(wxGoodsSubsidyDTO.getActGoodsName());
            } else {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "加价购商品不在活动期间内，请刷新重试");
            }
        }
    }


    /**
     * 预下单时把微信加价购商品转换为OrderItemAddRequest，包括库存校验。
     *
     * @param storeId
     * @param wxGoods
     * @return
     */
    public List<OrderItemAddRequest> buildItemAddRequest(String storeId, List<WxGoodsSubsidy> wxGoods, List<CartItemCreate> itemCreates) {
        Map<String, Integer> existsCartSku = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemCreates)) {
            itemCreates.forEach(it -> {
                CartItemCreate.Item item = it.getItem();
                existsCartSku.put(item.getId(), item.getNumber());
            });
        }
        Map<String, WxGoodsSubsidy> wxGoodsMap = wxGoods.stream().collect(Collectors.toMap(WxGoodsSubsidy::getSpuId, o -> o));
        ListResult<ItemDto> itemDtos = itemService.findItemDetailsById(storeId, new ArrayList<>(wxGoodsMap.keySet()));
        ArrayList<OrderItemAddRequest> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(itemDtos.getRecords())) {
            if (itemDtos.getRecords().size() != wxGoods.size()) {
                throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
            }
            for (ItemDto itemDto : itemDtos.getRecords()) {
                //校验商品库存等
                Item item = itemDto.getItem();
                Integer number = wxGoodsMap.get(item.getId()).getNumber();
                if (number == null || number <= 0) {
                    throw new BusinessException(ReturnCode.ORDER_PARAMS_ERROR);
                }

                if (itemDto.getItem().getSku() != null && itemDto.getItem().getSku() < (number + MapUtils.getInteger(existsCartSku, item.getId(), 0))) {
                    throw new BusinessException(ReturnCode.ITEM_OUT_OF_SKU);
                }
                // 校验商品的服务类型
                if (itemDto.getItem().getServiceType() == 1) {
                    throw new BusinessException(ReturnCode.ITEM_NOT_FOR_SALE);
                }
                //组装订单商品对象
                OrderItemAddRequest addRequest = new OrderItemAddRequest();
                addRequest.setPackFee(item.getPackFee().longValue());
                addRequest.setReflect(item.getId());
                addRequest.setItemId(item.getId());
                addRequest.setName(item.getName());
                addRequest.setCount(new BigDecimal(number));
                Long price = Long.valueOf(item.getPrice());
                addRequest.setOriginalAmountPer(price);
                addRequest.setEffectiveAmountPer(price);
                addRequest.setUrl(item.getPhotoUrl());
                addRequest.setCategoryId(item.getCategoryId());
                addRequest.setSpuType(SpuType.PRODUCT);
                addRequest.setCategorySort(item.getCategorySort());
                addRequest.setItemSort(item.getDisplayOrder());
                addRequest.setSkuType(SkuType.SINGLE);
                addRequest.setSaleUnit(item.getUnit());
                addRequest.setUnitType(Objects.nonNull(item.getUnitType()) && 1 == item.getUnitType() ? GoodsUnitTypeEnum.WEIGHT : GoodsUnitTypeEnum.NUMBER);
                list.add(addRequest);
            }
        }
        return list;
    }

    /**
     * 提供给优惠计算，把微信加价购商品转换为OrderItemAddRequest。不进行库存校验。
     *
     * @param wxGoods
     * @param storeId
     * @return
     */
    public List<OrderItemAddRequest> convertItemAddRequest(List<WxGoodsSubsidy> wxGoods, String storeId) {
        checkEnable(wxGoods, storeId);
        Map<String, WxGoodsSubsidy> wxGoodsMap = wxGoods.stream().collect(Collectors.toMap(WxGoodsSubsidy::getSpuId, o -> o));
        ListResult<ItemDto> itemDtos = itemService.findItemDetailsById(storeId, new ArrayList<>(wxGoodsMap.keySet()));
        ArrayList<OrderItemAddRequest> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(itemDtos.getRecords())) {
            if (itemDtos.getRecords().size() != wxGoods.size()) {
                throw new BusinessException(ReturnCode.ITEM_FIND_FAIL);
            }
            for (ItemDto itemDto : itemDtos.getRecords()) {
                Item item = itemDto.getItem();
                Integer number = wxGoodsMap.get(item.getId()).getNumber();
                //组装订单商品对象
                OrderItemAddRequest addRequest = new OrderItemAddRequest();
                addRequest.setPackFee(item.getPackFee().longValue());
                addRequest.setReflect(item.getId());
                addRequest.setItemId(item.getId());
                addRequest.setName(item.getName());
                addRequest.setCount(new BigDecimal(number));
                Long price = Long.valueOf(item.getPrice());
                addRequest.setOriginalAmountPer(price);
                addRequest.setEffectiveAmountPer(price);
                addRequest.setUrl(item.getPhotoUrl());
                addRequest.setCategoryId(item.getCategoryId());
                addRequest.setSpuType(SpuType.PRODUCT);
                addRequest.setCategorySort(item.getCategorySort());
                addRequest.setItemSort(item.getDisplayOrder());
                addRequest.setSkuType(SkuType.SINGLE);
                addRequest.setSaleUnit(item.getUnit());
                addRequest.setUnitType(Objects.nonNull(item.getUnitType()) && 1 == item.getUnitType() ? GoodsUnitTypeEnum.WEIGHT : GoodsUnitTypeEnum.NUMBER);
                list.add(addRequest);
            }
        }
        return list;
    }


    public Map findWxStoreInfo(String storeId) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setStoreId(storeId);
        return wxGoodsSubsidyRemoteService.getStoreWxInfo(baseRequest);
    }

    /**
     * 查询微信加价购活动相关信息，排除商品库存不够的活动
     *
     * @param storeId
     * @return
     */
    public Map getStoreActs(String storeId) {
        //商家白名单校验
        if (!checkWhitelist(storeId)) {
            return null;
        }
        //查询门店有效活动列表
        List<WxGoodsSubsidyDTO> wxGoodsSubsidyList = queryStoreActs(storeId);
        if (CollectionUtils.isNotEmpty(wxGoodsSubsidyList)) {
            Map<String, WxGoodsSubsidyDTO> map = wxGoodsSubsidyList.stream().collect(Collectors.toMap(WxGoodsSubsidyDTO::getSpuId, o -> o));
            List<String> spuIdList = wxGoodsSubsidyList.stream().map(WxGoodsSubsidyDTO::getSpuId).collect(Collectors.toList());
            ListResult<ItemDto> items = itemService.findItemDetailsById(storeId, spuIdList);
            if (CollectionUtil.isNotEmpty(items.getRecords())) {
                ArrayList<WxActProductVO> products = new ArrayList<>();
                //处理活动商品
                for (ItemDto itemDto : items.getRecords()) {
                    Item item = itemDto.getItem();
                    if (Objects.nonNull(item.getSku()) && itemDto.getItem().getSku() <= 0) {
                        //商品库存不足，不展示该商品的加价购活动
                        continue;
                    }
                    if (!ItemHelper.curTimeForSale(itemDto)) {
                        //不在售卖时间段，不展示该商品的加价购活动
                        continue;
                    }
                    if (map.get(item.getId()) != null) {
                        WxActProductVO wxActProductVO = new WxActProductVO();
                        wxActProductVO.setActId(map.get(item.getId()).getActId());
                        wxActProductVO.setPrice(item.getPrice() == null ? null : item.getPrice().longValue());
                        wxActProductVO.setProductName(item.getName());
                        products.add(wxActProductVO);
                    }
                }
                if (CollectionUtils.isNotEmpty(products)) {
                    Map wxStoreInfo = findWxStoreInfo(storeId);
                    if (org.apache.commons.collections4.MapUtils.isNotEmpty(wxStoreInfo)) {
                        HashMap<String, Object> hashMap = new HashMap<>();
                        hashMap.put("subStoreId", wxStoreInfo.get("subStoreId"));
                        hashMap.put("subMchId", wxStoreInfo.get("subMchId"));
                        hashMap.put("actIds", products.stream().map(WxActProductVO::getActId).collect(Collectors.toList()));
                        hashMap.put("products", products);
                        return hashMap;
                    }
                }
            }
        }
        return null;
    }


    /**
     * 检查门店是否在加价购白名单内
     *
     * @param storeId
     * @return
     */
    public boolean checkWhitelist(String storeId) {
        FindWhiteListByOwnerIdRequest request = new FindWhiteListByOwnerIdRequest();
        request.setAppId(AppId.JJZ_WHITE_LIST.getAppId());
        request.setBusinessType(WhiteListBusinessType.WX_GOODS_SUBSIDY.getBusinessType());
        request.setOwnerType(OwnerType.STORE_ID.getOwnerType());
        request.setOwnerId(storeId);
        WhiteListResponse whiteListResponse = whiteListRemoteService.findByOwnerId(request);
        return Objects.nonNull(whiteListResponse);
    }
}
