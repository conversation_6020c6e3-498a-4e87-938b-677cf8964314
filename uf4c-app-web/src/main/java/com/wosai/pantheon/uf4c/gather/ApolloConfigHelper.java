package com.wosai.pantheon.uf4c.gather;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class ApolloConfigHelper {

    private HotsaleBlackList hotsaleBlackList;

    public static final Config apolloConfig = ConfigService.getAppConfig();

    @Value("${hotsale.blacklist}")
    private String hotsaleBlackListStr;


    @Value("${temp.charge.error.downgrade.whitelist}")
    private Set<String> tempChargeErrorDowngradeWhitelist;

    private ServiceDegradeConfig serviceDegradeConfig;

    private Map<String, String> payHeaderConfigMap;


    public boolean isInTempChargeErrorDowngradeWhitelist(String merchantId) {
        if (tempChargeErrorDowngradeWhitelist != null) {
            return tempChargeErrorDowngradeWhitelist.contains(merchantId);
        }
        return false;
    }

    public ServiceDegradeConfig getServiceDegradeConfig() {
        if (serviceDegradeConfig == null) {
            serviceDegradeConfig = new ServiceDegradeConfig();
        }
        return serviceDegradeConfig;
    }

    public void setServiceDegradeConfig(ServiceDegradeConfig serviceDegradeConfig) {
        this.serviceDegradeConfig = serviceDegradeConfig;
    }


    public Map<String, String> getPayHeaderConfigMap() {
        if(payHeaderConfigMap == null){
            return new HashMap<String, String >();
        }
        return payHeaderConfigMap;
    }

    public void setPayHeaderConfigMap(Map<String, String> payHeaderConfigMap) {
        this.payHeaderConfigMap = payHeaderConfigMap;
    }


    public String getStringConfigValueByKey(String key, String defaultValue) {
        return apolloConfig.getProperty(key, defaultValue);
    }

    public Boolean getBooleanConfigValueByKey(String key, Boolean defaultValue) {
        return apolloConfig.getBooleanProperty(key, defaultValue);
    }

    public Long getLongConfigValueByKey(String key, Long defaultValue) {
        return apolloConfig.getLongProperty(key, defaultValue);
    }

    public HotsaleBlackList getHotsaleBlackList() {

        if (hotsaleBlackList != null && hotsaleBlackList.isValid()) {
            return hotsaleBlackList;
        }


        HotsaleBlackList hotsaleBlackList = null;
        if (StringUtils.isBlank(hotsaleBlackListStr)) {
            hotsaleBlackList = new HotsaleBlackList();
        } else {
            hotsaleBlackList = JSON.parseObject(hotsaleBlackListStr, HotsaleBlackList.class);
        }
        hotsaleBlackList.setGenerateTime(System.currentTimeMillis());
        this.hotsaleBlackList = hotsaleBlackList;
        return this.hotsaleBlackList;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ServiceDegradeConfig implements Serializable {
        /**
         * 热销商品降级
         */
        private boolean hotsaleDegrade = false;

        /**
         * 推荐商品降级
         */
        private boolean recommendGoodsDegrade = false;

        /**
         * 推荐加料降级
         */
        private boolean recommendMaterialDegrade = false;

        /**
         * 优惠分类降级
         */
        private boolean discountCategoryDegrade = false;

        /**
         * 点过的菜降级
         */
        private boolean recentItemsDegrade = false;

        /**
         * 优惠查询降级
         */
        private boolean redeemQueryDegrade = false;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HotsaleBlackList implements Serializable {
        /**
         * 精确匹配列表
         */
        private List<String> accurateList;

        /**
         * 模糊匹配列表
         */
        private List<String> fuzzyList;

        /**
         * 最小价格,被过滤的商品单价小于该价格即被过滤
         */
        private int minPrice;

        /**
         * 本地配置的创建时间
         */
        private Long generateTime;

        private boolean isValid() {
            if (generateTime == null) {
                return false;
            }
            if (System.currentTimeMillis() - 5 * 60 * 1000 > generateTime) {
                return false;
            }
            return true;
        }
    }


    public static void main(String[] args) {
        System.out.println(JSON.toJSONString(new ServiceDegradeConfig()));
    }

}
