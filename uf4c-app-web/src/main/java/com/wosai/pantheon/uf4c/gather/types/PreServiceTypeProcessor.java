package com.wosai.pantheon.uf4c.gather.types;

import com.wosai.market.mcc.api.constant.MerchantKeys;
import com.wosai.market.mcc.api.constant.StoreActivatedKeys;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.CodeScene;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ServiceTypeData;
import com.wosai.pantheon.uf4c.util.MccUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class PreServiceTypeProcessor extends AbstractServiceProcessor {
    @Override
    public ServiceTypeData.ServiceItem process(GatherRequest request, Map<String, Object> dataMap) {
        ServiceTypeData.ServiceItem serviceItem = new ServiceTypeData.ServiceItem();
        serviceItem.setSort(3);
        serviceItem.setName("到店自取");
        serviceItem.setServiceType(1);
        serviceItem.setServiceTypeName(OrderType.PRE_ORDER.getMsg());
        serviceItem.setShortName("自取");
        serviceItem.setActive(isActive(request, dataMap));
        Map<String, String> mccMap = (Map<String, String>) MapUtils.getMap(dataMap, "mcc");
        Map<String, String> mchMap = (Map<String, String>) MapUtils.getMap(dataMap, "mch_mcc");
        boolean isRetail = Objects.equals(Constants.RETAIL, MapUtils.getString(mchMap, MerchantKeys.MERCHANT_TYPE));
        boolean takeoutActivated = MccUtils.getBooleanValue(mccMap, StoreActivatedKeys.RETAIL_TAKEOUT_ACTIVATED, false);
        // 自营外卖营业状态
        boolean onTakeoutBusiness = MccUtils.getBooleanValue(mccMap, Constants.TAKEOUT_BUSINESS_STATUS, false);
        // 到店自取营业状态
        boolean onPreBusiness = MccUtils.getBooleanValue(mccMap, Constants.PRESET_BUSINESS_STATUS, true);
        if (isRetail) {
            return takeoutActivated && onTakeoutBusiness && onPreBusiness ? serviceItem : null;
        } else {
            return onTakeoutBusiness && onPreBusiness ? serviceItem : null;
        }
    }

    @Override
    public boolean isActive(GatherRequest request, Map<String, Object> dataMap) {
        Map<String, String> mccMap = (Map<String, String>) MapUtils.getMap(dataMap, "mcc");
        Map<String, String> extraMap = (Map<String, String>) MapUtils.getMap(dataMap, "extra");
        boolean onTakeoutBusiness = MccUtils.getBooleanValue(mccMap, Constants.TAKEOUT_BUSINESS_STATUS, false);
        int deliveryType = MccUtils.getIntValue(mccMap, Constants.DELIVERY_TYPE);
        String mealShareType = MapUtils.getString(extraMap, "mealShareType");
        String scene = MapUtils.getString(extraMap, "scene", "");
        String goodsId = MapUtils.getString(extraMap, "goodsId");
        // 外卖营业状态开启，但是配送方式为：不配送，此时，外卖不可用
        boolean noTakeout = onTakeoutBusiness && deliveryType == 1;
        if (noTakeout) {
            // 外卖海报
            if (StringUtils.isNotBlank(scene) && "poster".equalsIgnoreCase(mealShareType)) {
                return true;
            }
            // 校园外卖进入
            if (StringUtils.isNotBlank(request.getFrom()) && Constants.From.CAMPUS.equalsIgnoreCase(request.getFrom())) {
                return true;
            }
            // 商品分享码
            if (CodeScene.getCodeScene(scene) == CodeScene.P && StringUtils.isNotBlank(goodsId)) {
                return true;
            }
            // 校园外卖进入
            if (Objects.nonNull(request.getCampusId()) && request.getCampusId() > 0) {
                return true;
            }
        }
        // 再来一单
        return OrderType.PRE_ORDER.getMsg().equalsIgnoreCase(request.getBuyAgainOrderType());
    }
}
