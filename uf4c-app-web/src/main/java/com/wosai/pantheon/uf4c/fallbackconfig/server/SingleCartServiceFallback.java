package com.wosai.pantheon.uf4c.fallbackconfig.server;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.pantheon.uf4c.service.apisix.GatherService;
import com.wosai.pantheon.uf4c.service.apisix.SingleCartService;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;

import java.lang.reflect.Method;
import java.util.HashMap;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;
import static com.wosai.pantheon.uf4c.web.exception.ReturnCode.BLOCK_FALLBACK_ERROR;
import static com.wosai.pantheon.uf4c.web.exception.ReturnCode.SYSTEM_EXCEPTION;

public class SingleCartServiceFallback extends JsonRPCFallbackDefine {

    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[] {
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("addCartAndRedeemBatch").or(named("getCart"));
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        throw new BusinessException(BLOCK_FALLBACK_ERROR);
                    }
                },
                ElementMatchers::any
        };
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(SingleCartService.class);
    }

    @Override
    public JsonRPCFallbackDefine.Provider getProvider() {
        return JsonRPCFallbackDefine.Provider.SERVER;
    }
}
