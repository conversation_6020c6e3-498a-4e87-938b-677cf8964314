package com.wosai.pantheon.uf4c.util;

import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.util.JacksonUtil;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.URI;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

public class HttpUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);
    public static final int DEFAULT_CONNECTION_TIMEOUT = 3000;
    public static final int DEFAULT_READ_TIMEOUT = 15000;

    public static Map post(String url, Map<String, Object> request) throws Exception {
        return post(url, request, DEFAULT_CONNECTION_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }

    public static HttpClient client = createSSLDefaultClient();

    public static String execute(HttpRequestBase requestBase) throws IOException {
        HttpResponse response = client.execute(requestBase);
        String responseStr = EntityUtils.toString(response.getEntity(), "utf-8");
        return responseStr;
    }

    public static Map post(String url, Map<String, Object> request, int connectionTimeOut, int readTimeOut) throws Exception {
        try {
            logger.info("request url {}, data {}", url, request);
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-type", (new StringBuilder()).append("application/json").toString());
            httpPost.setHeader("Accept", new StringBuffer("text/xml,text/html,application/json;charset=").append("utf-8").toString());
            httpPost.setHeader("Cache-Control", "no-cache");

            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectionTimeOut).setSocketTimeout(readTimeOut).build();
            httpPost.setConfig(requestConfig);

            StringEntity entity = new StringEntity(JacksonUtil.toJsonString(request), "utf-8");
            httpPost.setEntity(entity);
            HttpResponse response = client.execute(httpPost);
            String responseStr = EntityUtils.toString(response.getEntity(), "utf-8");
            logger.info("response {}", responseStr);
            return JacksonUtil.toBean(responseStr, Map.class);
        } catch (Exception e) {
            throw new Exception("请求异常", e);
        }
    }

    public static Map get(String baseUrl, Map params) throws Exception {
        return get(baseUrl, params, DEFAULT_CONNECTION_TIMEOUT, DEFAULT_READ_TIMEOUT);
    }

    public static Map get(String baseUrl, Map<String, String> params, int connectionTimeOut, int readTimeOut) throws Exception {
        try {
            logger.info("request url {}, params {}", baseUrl, params);
            HttpClient client = createSSLDefaultClient();
            HttpGet httpGet = new HttpGet(buildURI(baseUrl, params));
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectionTimeOut).setSocketTimeout(readTimeOut).build();
            httpGet.setConfig(requestConfig);
            HttpResponse response = client.execute(httpGet);
            String responseStr = EntityUtils.toString(response.getEntity(), "utf-8");
            logger.info("response {}", responseStr);
            return JacksonUtil.toBean(responseStr, Map.class);
        } catch (Exception e) {
            throw new Exception("请求异常", e);
        }
    }

    public static String buildURI(String baseUrl, Map<String, String> params) throws Exception {
        if (params == null) {
            return baseUrl;
        }
        URI uri = new URI(baseUrl);
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (!StringUtil.empty(value)) {
                uri = new URIBuilder(uri).addParameter(key, value).build();
            }
        }
        return uri.toString();
    }

    public static CloseableHttpClient createSSLDefaultClient() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                //信任所有
                public boolean isTrusted(X509Certificate[] chain,
                                         String authType) throws CertificateException {
                    return true;
                }
            }).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (KeyManagementException e) {
            logger.error("", e);
        } catch (NoSuchAlgorithmException e) {
            logger.error("", e);
        } catch (KeyStoreException e) {
            logger.error("", e);
        }
        return HttpClients.createDefault();
    }

    public static void main(String[] args) {
        try {
            String baseUrl = "https://xzg.shouqianba.com/api/trend/dashboard";
            Map params = CollectionUtil.hashMap("beginStr", "2018-06-10 00:00", "endStr", "2018-06-11 00:00");
            System.out.println(buildURI(baseUrl, params));
            get(baseUrl, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
