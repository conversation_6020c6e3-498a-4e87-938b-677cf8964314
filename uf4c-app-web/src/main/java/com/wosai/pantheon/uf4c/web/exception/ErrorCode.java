package com.wosai.pantheon.uf4c.web.exception;

public enum ErrorCode {
    OK(10000),
    UNKNOWN(10401),
    // 必填参数未传，参数类型错误，报文格式错误...
    BAD_REQUEST(10101),
    // 参数值非法
    INVALID_REQUEST(10102),
    // 通过二维码ID查询不到对应门店
    UNRECOGNIZED_QR_CODE(10999),

    // 业务异常
    BUSINESS_ERROR(20100),

    // 支付宝/微信未授权
    UNAUTHORIZED(40001),
    // 内部系统异常
    INTERNAL_SYSTEM_ERROR(50000),
    // 外部系统异常
    EXTERNAL_SYSTEM_ERROR(50003),

    // core-business
    CORE_BUSINESS_SERVICE_NETWORK_ERROR(60100),
    CORE_BUSINESS_SERVICE_SERVER_ERROR(60100),

    // upay-qrcode
    UPAY_QR_CODE_SERVICE_NETWORK_ERROR(70100),
    UPAY_QR_CODE_SERVICE_SERVER_ERROR(70101),

    // 支付网关连接异常
    UPAY_GATEWAY_NETWORK_ERROR(80100),
    // 支付网关响应异常
    UPAY_GATEWAY_SERVER_ERROR(80101),
    // 微信OAuth异常
    WEIXIN_OAUTH_ERROR(90100),
    // 微信连接异常
    WEIXIN_NETWORK_ERROR(90101),
    // 微信响应异常
    WEIXIN_SERVER_ERROR(90102),
    // 支付宝OAuth异常
    ALIPAY_OAUTH_ERROR(90200),
    // 支付宝连接异常
    ALIPAY_NETWORK_ERROR(90201),
    // 支付宝响应异常
    ALIPAY_SERVER_ERROR(90202),

    TRADE_PARAMS_ACTIVITY_KEY_NOT_SET(90900),
    TRADE_PARAMS_NOT_SET(90902),
    TRADE_PARAMS_WEIXIN_APP_ID_OR_SECRET_NOT_SET(90903),
    TRADE_PARAMS_NOT_FOUND(90904);

    private final int value;

    ErrorCode(int value) {
        this.value = value;
    }

    public int value() {
        return value;
    }

    public static ErrorCode valueOf(int value) {
        if (value >= 20100 && value < 40000) {
            return BUSINESS_ERROR;
        }
        switch (value) {
            case 10101:
                return BAD_REQUEST;
            case 10102:
                return INVALID_REQUEST;
            case 40001:
                return UNAUTHORIZED;
            case 50000:
                return INTERNAL_SYSTEM_ERROR;
            case 50003:
                return EXTERNAL_SYSTEM_ERROR;
            case 80100:
                return UPAY_GATEWAY_NETWORK_ERROR;
            case 80101:
                return UPAY_GATEWAY_SERVER_ERROR;
            case 90100:
                return WEIXIN_OAUTH_ERROR;
            case 90200:
                return ALIPAY_OAUTH_ERROR;
            default:
                return UNKNOWN;
        }
    }

}
