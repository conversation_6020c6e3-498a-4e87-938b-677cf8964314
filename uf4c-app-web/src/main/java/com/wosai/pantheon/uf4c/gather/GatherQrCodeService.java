package com.wosai.pantheon.uf4c.gather;

import com.wosai.data.bean.BeanUtil;
import com.wosai.market.mcc.api.dto.request.UidContentRequest;
import com.wosai.market.mcc.api.dto.response.UidConfigDTO;
import com.wosai.market.mcc.api.service.UidRemoteService;
import com.wosai.market.merchant.exception.MerchantServerException;
import com.wosai.pantheon.uf4c.constant.CodeScene;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.constant.MpPageSceneEnums;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.QRCodeUtils;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.smartbiz.gds.enums.TableStatusEnum;
import com.wosai.smartbiz.gds.enums.config.MealTypeValueEnum;
import com.wosai.smartbiz.gds.request.QrcodeInfoRequest;
import com.wosai.smartbiz.gds.service.QrCodeRpcService;
import com.wosai.upay.qrcode.service.QrcodeService;
import jodd.net.URLDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.wosai.market.merchant.enums.MerchantErrorCode.QRCODE_NOT_FOUND;

@Slf4j
@Service
public class GatherQrCodeService extends GatherBase {

    @Value("${qrcode.jjzRegex}")
    private String jjzRegex;
    // 收钱吧二维码域名
    @Value("${qrcode.sqbRegex}")
    private String sqbRegex;
    // 扫码点单二维码地址域名
    @Value("${qrcode.jjzQrCodeDomainPrefix}")
    private String jjzQrCodeDomainPrefix;

    @Autowired
    private QrcodeService qrCodeService;
    @Autowired
    private UidRemoteService uidRemoteService;
    @Autowired
    private QrCodeRpcService qrCodeRpcService;

    /**
     * 二维码解析
     *
     * @param request
     * @param dataMap
     */
    public void parser(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "qrcodeParser");
        try {
            logInfo("解析二维码", "GatherQrCodeParser", request);
            String url = request.getUrl();
            if (StringUtils.isBlank(url)) {
                return;
            }
            log.info("二维码解析 {}", url);
            // 转换二维码，将收钱吧二维码转换为久久折二维码
            url = transQqbToJJzUrl(url);
            // 二维码解析
            parseQrCode(url,request.getTerminalTagValue(), dataMap);
            // 根据码中的信息，处理相关数据
            processQrCodeData(request, dataMap);

        } catch (Exception e) {
            logWarn("二维码解析出错：" + e.getMessage(), "GatherQrCodeParser", request, e);
            setError(dataMap, ReturnCode.QRCODE_PARSE_ERROR);
        }
        durationEnd(dataMap, "qrcodeParser");
    }

    /**
     * 解析二维码
     *
     * @param url
     */
    private void parseQrCode(String url,String terminalTagValue, Map<String, Object> dataMap) {
        Map extraMap = MapUtil.getMap(dataMap, "extra");
        url = URLDecoder.decode(url);
        String[] arr = url.split("\\?");
        String newUrl = arr[0];
        // 获取二维码中的数据，包含码类型以及码中的参数
        Map qrParams = getQrCodeParams(newUrl);
        if (qrParams.size() == 0  && StringUtils.isBlank(terminalTagValue)) {
            // 有些特殊的码并没有printCode，而是直接附带参数
            log.warn("无须获取terminal信息：{}", newUrl);
            return;
        }
        // 将二维码地址返回
        extraMap.put("qrCode", newUrl);
        // 码中的场景值
        String scene = MapUtil.getString(qrParams, "scene");
        extraMap.put("scene", scene);
        // 二维码的printCode
        String encryptedData = MapUtil.getString(qrParams, "encryptedData");
        // 不同的场景，有不同的处理
        // 解析二维码
        if (CodeScene.P.getScene().equals(scene)) {
            // 海报二维码
            // 海报需要根据参数查询MCC的页面信息
            if (StringUtils.isNotBlank(encryptedData)) {
                UidContentRequest uidContentRequest = new UidContentRequest();
                uidContentRequest.setUid(encryptedData);
//                String content = uidRemoteService.content(uidContentRequest);
                UidConfigDTO uidConfig = uidRemoteService.findByUid(uidContentRequest);
                if (null == uidConfig) {
                    return;
                }
                // 获取content中的参数信息
                Map params = CommonUtil.getUrlParams(uidConfig.getContent());
                extraMap.putAll(params);
                if (StringUtils.isNotBlank(uidConfig.getTerminalSn())) {
                    Map<String, Object> info = new HashMap<>();
                    info.put("terminal_sn", uidConfig.getTerminalSn());
                    info.put("store_id", uidConfig.getStoreId());
                    dataMap.put("terminal", info);
                }
                return;
            }
        }
        if (CodeScene.G.getScene().equals(scene)) {
            // 聚合码，直接返回聚合码ID
            if (StringUtils.isNotBlank(encryptedData)) {
                // 聚合码groupID，对应merchant服务中的apollo配置
                extraMap.put("group", encryptedData);
                return;
            }
        }
        if (StringUtils.isNotBlank(encryptedData) || StringUtils.isNotBlank(terminalTagValue)) {
            // 调用接口解析二维码的terminal信息
            QrcodeInfoRequest qrcodeInfoRequest = new QrcodeInfoRequest();
            qrcodeInfoRequest.setUrl(url);
            qrcodeInfoRequest.setTagValue(terminalTagValue);
            // 获取二维码跳转页面
            Map info = qrCodeRpcService.getInfoByUrl(qrcodeInfoRequest);
            completeTableName(info);
            dataMap.put("terminal", info);
            if (StringUtils.isNotBlank(terminalTagValue)){
                extraMap.put("from","payQrCode");
            }
        }
    }

    private void completeTableName(Map terminal) {
        String name = MapUtil.getString(terminal, "name");
        String tableName = MapUtil.getString(terminal, "tableName");
        if (StringUtils.isBlank(name) && StringUtils.isNotBlank(tableName)) {
            terminal.put("name", tableName);
        }
        if (StringUtils.isBlank(tableName) && StringUtils.isNotBlank(name)) {
            terminal.put("tableName", name);
        }
    }

    /**
     * 根据二维码解析出来的信息，来处理相关数据
     *
     * @return
     */
    private void processQrCodeData(GatherRequest request, Map<String, Object> dataMap) {
        Map<String, Object> terminalMap = (Map<String, Object>) MapUtils.getMap(dataMap, "terminal");
        Map<String, Object> extraMap = (Map<String, Object>) MapUtils.getMap(dataMap, "extra");
        if (MapUtils.isNotEmpty(terminalMap)) {
            extraMap.put("storeId", MapUtil.getString(terminalMap, "store_id"));
            extraMap.put("merchantId", MapUtil.getString(terminalMap, "merchant_id"));
        }
        String page = MapUtils.getString(extraMap, "page");
        String scene = MapUtils.getString(extraMap, "scene");
        if (MapUtils.isNotEmpty(terminalMap)) {
            // 轻餐围餐判断
            String mealType = MapUtil.getString(terminalMap, Constants.MEAL_TYPE_CONFIG_KEY, MealTypeValueEnum.single.getCode());
            boolean isPayFirstTableOrder = MapUtils.getBoolean(terminalMap, Constants.PAY_FIRST_TABLE_ORDER_CONFIG_KEY, false);
            if (MealTypeValueEnum.single.getCode().equalsIgnoreCase(mealType)) {
                // 轻餐：返回点单首页
                page = MpPageSceneEnums.HOME.getPage();
            }
            if (MealTypeValueEnum.round_meal.getCode().equalsIgnoreCase(mealType)) {

                // 围餐
                String tableStatus = MapUtil.getString(terminalMap, Constants.TABLE_STATUS);

                if (TableStatusEnum.FREE.name().equalsIgnoreCase(tableStatus) || TableStatusEnum.WAIT_CLEAN.name().equalsIgnoreCase(tableStatus)) {
                    // 返回选择人数页面(最新版选人页面在首页)
                    page = MpPageSceneEnums.HOME.getPage();
                    //这里要额外判断下是否小程序端已经设置了人数，如果已经设置了，那么就要返回PRE_OPEN状态
                }
                if (TableStatusEnum.WAIT_PAY.name().equalsIgnoreCase(tableStatus) || TableStatusEnum.PART_PAY.name().equalsIgnoreCase(tableStatus) || TableStatusEnum.HAVE_ALL_PAY.name().equalsIgnoreCase(tableStatus)) {
                    // 返回提交订单页面
                    if (isPayFirstTableOrder) {
                        page = MpPageSceneEnums.PAY_FIRST_TABLE_ORDER_SUBMIT_PAGE.getPage();
                    } else {
                        page = MpPageSceneEnums.SUBMIT_ORDER.getPage();
                    }
                }
                if (TableStatusEnum.WAIT_PLACE_ORDER.name().equalsIgnoreCase(tableStatus)) {
                    // 返回点单首页
                    page = MpPageSceneEnums.HOME.getPage();
                }
            }
        }
        if (CodeScene.getCodeScene(scene) == CodeScene.G) {
            // 聚合页面
            page = MpPageSceneEnums.AGGREGATION_LIST.getPage();
        }
        // 是否是聚合点单码
        boolean isGatherQrCode = false;
        // 聚合码ID  优先从extra中获取，这里的值是URL中带过来的
        int storeSetId = MapUtils.getIntValue(extraMap, "storeSetId", 0);
        if (storeSetId == 0 && MapUtils.isNotEmpty(terminalMap)) {
            // 从extra中获取不到，从terminal中获取，聚合点单码中也包含此id
            storeSetId = MapUtils.getIntValue(terminalMap, "storeSetId", 0);
            isGatherQrCode = true;
        }
        if (storeSetId > 0) {
            // 聚合点单码
            // 是聚合点单码，且入参了门店ID以及storeSetId，此时默认跳转到门店首页
            if (isGatherQrCode && StringUtils.isNotBlank(request.getStoreId()) && request.getStoreSetId() > 0) {
                page = MpPageSceneEnums.HOME.getPage();
            } else {
                page = MpPageSceneEnums.AGGREGATION.getPage();
            }
            extraMap.put("storeSetId", storeSetId);
            // 聚合页码关联校园页的功能下线
            // 查询是否关联了校园页
//            try {
//                StoreSetDTO storeSetDTO = storeSetService.getStoreSetById(storeSetId);
//                if (Objects.nonNull(storeSetDTO) && Objects.nonNull(storeSetDTO.getCampusId())) {
//                    extraMap.put("campusId", storeSetDTO.getCampusId());
//                    //extraMap.remove("storeSetId");
//                } else {
//                    extraMap.put("storeSetId", storeSetId);
//                }
//            } catch (Exception e) {
//                logWarn("查询聚合页基本信息出错", "getStoreSetById", request, e);
//            }


        }
        if (MapUtils.getIntValue(extraMap, "campusId", 0) > 0) {
            // 校园外卖
            page = MpPageSceneEnums.CAMPUS.getPage();
        }
        if (StringUtils.isEmpty(page)) {
            page = MpPageSceneEnums.HOME.getPage();
        }
        extraMap.put("page", page);
    }

    private String transQqbToJJzUrl(String url) {
        // 默认的码类型均为桌台码
        String scene = CodeScene.T.getScene();
        if (isSqbQrCodeUrl(url)) {
            Map params = CommonUtil.getUrlParams(url);
            String printCode = "";
            if (params.containsKey("s")) {
                // 新收钱吧码中附带的场景值
                // 自定义参数
                scene = MapUtil.getString(params, "s");
                if (CodeScene.P.getScene().equals(scene)) {
                    // 海报ID
                    printCode = MapUtil.getString(params, "p");
                } else {
                    printCode = "?" + CommonUtil.getUrlParamsByMap(params);
                }
            }
            String code = MapUtil.getString(params, "c");
            if (StringUtils.isNotBlank(code)) {
                printCode = getPrintCodeByQRCode(code);
                if (StringUtils.isBlank(printCode)) {
                    throw new MerchantServerException(QRCODE_NOT_FOUND);
                }
                printCode = QRCodeUtils.compress(printCode);
            }
            return jjzQrCodeDomainPrefix.concat("/").concat(scene).concat("/").concat(printCode);
        }
        return url;
    }

    /**
     * 1.获取二维码地址中的场景值，例如t,p,s
     * 2.获取二维码中的参数
     *
     * @param url
     * @return
     */
    private Map getQrCodeParams(String url) {
        Map data = new HashMap();
        Pattern pattern = Pattern.compile(jjzRegex);
        Matcher matcher = pattern.matcher(url);
        if (matcher.matches()) {
            data.put("scene", matcher.group("sceneCode"));
            data.put("encryptedData", matcher.group("encryptedData"));
        }
        return data;
    }

    /**
     * 根据收钱吧二维码中的QRCode获取PrintCode
     *
     * @param code
     * @return
     */
    private String getPrintCodeByQRCode(String code) {
        Map data = qrCodeService.getQrcodeByCode(code);
        if (data == null) {
            throw new MerchantServerException(QRCODE_NOT_FOUND);
        }
        return BeanUtil.getPropString(data, "qr_print_code");
    }


    // 正则判断是否为收钱吧码地址
    private boolean isSqbQrCodeUrl(String url) {
        return Pattern.matches(sqbRegex, url);
    }


}
