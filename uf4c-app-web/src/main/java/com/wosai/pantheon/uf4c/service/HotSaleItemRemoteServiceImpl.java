package com.wosai.pantheon.uf4c.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.uf4c.api.HotSaleItemRemoteService;
import com.wosai.pantheon.uf4c.model.HotSaleProductConfig;
import com.wosai.pantheon.uf4c.model.dto.HotSaleItemQueryRequest;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.web.api.ListResult;
import com.wosai.web.api.Pagination;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/11/17
 */
@Service
@AutoJsonRpcServiceImpl
public class HotSaleItemRemoteServiceImpl implements HotSaleItemRemoteService {

    @Autowired
    private HotSaleItemHelper hotSaleItemHelper;

    @Autowired
    private ItemHelper itemHelper;

    @Autowired
    private ItemService itemRpcService;

    @Override
    public ListResult<ItemDetailVO> listByStore(HotSaleItemQueryRequest request) {

        String storeId = request.getStoreId();
        int serviceType = Optional.ofNullable(request.getServiceType()).orElse(2);
        long days = Optional.ofNullable(request.getDays()).orElse(7L);
        int countLimit = Optional.ofNullable(request.getCountLimit()).orElse(5);
        List<ItemDetailVO> items = new ArrayList<>(countLimit);

        // 查询热销配置的商品
        HotSaleProductConfig config = hotSaleItemHelper.getConfig(storeId);
        if (Objects.nonNull(config)) {
            config.setMaxShowNum(countLimit);
            ListResult<ItemDetailVO> itemFromConfig = hotSaleItemHelper.listByStore(storeId, serviceType, days, config);
            if (Objects.nonNull(itemFromConfig) && CollectionUtils.isNotEmpty(itemFromConfig.getRecords())) {
                items.addAll(itemFromConfig.getRecords());
            }
        }

        // 若热销商品数量 < 预期数量，需要补全
        if (items.size() < countLimit) {
            Pagination pagination = new Pagination();
            pagination.setPage(0);
            pagination.setPageSize(countLimit);
            ListResult<ItemDto> result = itemRpcService.listForMiniIndex(storeId, serviceType, pagination);
            if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(result.getRecords())) {
                ListResult<ItemDetailVO> itemVos = itemHelper.processItemDetailList(result, serviceType, ThreadLocalHelper.getUserId());
                List<ItemDetailVO> extraItems = itemVos.getRecords().stream()
                        .filter(p -> {
                            boolean exist = items.stream().anyMatch(e -> StringUtils.equals(e.getItem().getId(), p.getItem().getId()));
                            return !exist;
                        })
                        .limit(countLimit - items.size())
                        .collect(Collectors.toList());
                items.addAll(extraItems);
            }
        }
        return new ListResult<>(items);
    }
}
