package com.wosai.pantheon.uf4c.service;

import com.shouqianba.campus.center.application.CampusStoreService;
import com.shouqianba.mk.campustakeout.delivery.application.CampusRpcService;
import com.shouqianba.mk.campustakeout.delivery.application.open.OpenTeamCampusRpcService;
import com.shouqianba.mk.campustakeout.delivery.enums.SupplierTypeEnum;
import com.shouqianba.mk.campustakeout.delivery.model.GoodsModel;
import com.shouqianba.mk.campustakeout.delivery.request.QueryCampusStationStatusCommand;
import com.shouqianba.mk.campustakeout.delivery.request.campus.SupplierAggregateDeliveryInfoRequest;
import com.shouqianba.mk.campustakeout.delivery.request.order.ofct.OrderFulfillmentCycleTimeRequest;
import com.shouqianba.mk.campustakeout.delivery.response.BaseResponse;
import com.shouqianba.mk.campustakeout.delivery.response.dto.CampusStatusInfoDTO;
import com.shouqianba.mk.campustakeout.delivery.response.dto.campus.CampusAggregateDeliveryInfoDTO;
import com.shouqianba.mk.campustakeout.delivery.response.order.ofct.OrderFulfillmentCycleTimeDTO;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.data.util.StringUtil;
import com.wosai.market.merchant.api.*;
import com.wosai.market.merchant.dto.campus.CampusBaseInfo;
import com.wosai.market.merchant.dto.campus.CampusFreeRateInfo;
import com.wosai.market.merchant.dto.campus.CampusStoreBaseInfo;
import com.wosai.market.merchant.dto.campus.request.CampusStoreRequest;
import com.wosai.market.merchant.dto.campus.request.CampusTakeoutRequest;
import com.wosai.market.merchant.dto.campus.request.StoreCampusListRequest;
import com.wosai.market.merchant.dto.campus.response.CampusTakeoutResponse;
import com.wosai.market.merchant.dto.customer.request.StoreDetailRequest;
import com.wosai.market.merchant.dto.request.ServiceChargeOpenedRequest;
import com.wosai.market.merchant.dto.response.ServiceChargeOpenedResponse;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.DeliverInfo;
import com.wosai.pantheon.uf4c.model.StoreCampusInfo;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.util.WeakReferenceCaller;
import com.wosai.smartbiz.base.utils.DateUtil;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.InvalidParameterException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CampusHelper {

    @Autowired
    private CampusRemoteService campusRemoteService;
    @Autowired
    private UfoodRemoteService ufoodRemoteService;
    @Autowired
    private CustomerStoreRemoteService customerStoreRemoteService;
    @Autowired
    private CustomerCampusRemoteService customerCampusRemoteService;
    @Autowired
    private CampusRpcService campusRpcService;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private CampusFreeRateRemoteService campusFreeRateRemoteService;

    @Autowired
    private OpenTeamCampusRpcService openTeamCampusRpcService;

    @Autowired
    private CampusStoreService campusStoreService;

    @Autowired
    private CartService cartService;

    /**
     * 站点状态，OPEN：可用 CLOSE：不可用
     */
    public static final String CAMPUS_STATION_STATUS_OPEN = "OPEN";
    public static final String CAMPUS_STATION_STATUS_CLOSE = "CLOSE";
    /**
     * 校内配送方式，SQB_STATION：收钱吧配送 MERCHANT_STATION：商家自配送
     */
    public static final String CAMPUS_STATION_TYPE_SQB = "SQB_STATION";
    public static final String CAMPUS_STATION_TYPE_MERCHANT = "MERCHANT_STATION";
    /**
     * 校内外卖下单失败信息
     */
    public static final String STORE_NONE_SHARING = "门店不支持分账";
    public static final String STORE_NONE_STATION = "暂无可用配送站点";
    public static final String STORE_NONE_CAMPUS = "门店不支持校园外卖";
    public static final String CAMPUS_ID_ERROR = "无效的校园ID";


    public CampusStoreBaseInfo getCampusStoreInfo(Integer campusId, String storeId) {
        if (Objects.isNull(campusId) || StringUtil.empty(storeId)) {
            throw new InvalidParameterException("参数不能为空");
        }
        CampusStoreRequest campusStoreRequest = new CampusStoreRequest();
        campusStoreRequest.setCampusId(campusId);
        campusStoreRequest.setStoreId(storeId);
        return campusRemoteService.campusStore(campusStoreRequest);
    }

    public boolean isSharingOpened(String storeId) {
        if (StringUtil.empty(storeId)) {
            throw new InvalidParameterException("storeId不能为空");
        }
        StoreDetailRequest storeDetailRequest = new StoreDetailRequest();
        storeDetailRequest.setStoreId(storeId);
        com.wosai.market.merchant.dto.customer.response.StoreDetailResponse storeDetailResponse = customerStoreRemoteService.storeDetailInfo(storeDetailRequest);
        ServiceChargeOpenedRequest serviceChargeOpenedRequest = new ServiceChargeOpenedRequest();
        serviceChargeOpenedRequest.setMerchant_id(storeDetailResponse.getMerchantId());
        List<String> chargeFlags = Collections.singletonList("smart_campusdistribution");
        serviceChargeOpenedRequest.setChargeFlags(chargeFlags);
        ServiceChargeOpenedResponse response = ufoodRemoteService.isServiceChargeOpened(serviceChargeOpenedRequest);
        if (Objects.nonNull(response)) {
            Map<String, Boolean> map = response.getChargeFlags();
            if (MapUtils.isNotEmpty(map)) {
                return response.isSharingFlag() && MapUtils.getBooleanValue(map, "smart_campusdistribution", false);
            }
        }
        return false;
    }


    /**
     * 查询门店在校园是否可配送
     *
     * @param campusId
     * @param storeId
     * @return
     */
    public CampusStatusInfoDTO queryCampusStationInfo(Integer campusId, String storeId, String addressCode, Long presetTime) {
        QueryCampusStationStatusCommand queryCampusStationStatusCommand = new QueryCampusStationStatusCommand();
        queryCampusStationStatusCommand.setCampusId(campusId);
        queryCampusStationStatusCommand.setStoreId(storeId);
        queryCampusStationStatusCommand.setAddressCode(addressCode);
        if (Objects.nonNull(presetTime) && presetTime > 0) {
            queryCampusStationStatusCommand.setExpectedDeliveryTime(WosaiDateTimeUtils.format(presetTime, DateUtil.data_format_yyyy_MM_dd_hh_mm_ss));
        }
        BaseResponse<CampusStatusInfoDTO> response = campusRpcService.queryCampusStationStatusInfo(queryCampusStationStatusCommand);
        if (Objects.nonNull(response) && response.getCode() == 200) {
            return response.getData();
        }
        return null;
    }


    public CampusAggregateDeliveryInfoDTO queryCampusInfoV2(Integer campusId, String storeId, String addressCode, Long presetTime, String floor) {
        SupplierAggregateDeliveryInfoRequest request = new SupplierAggregateDeliveryInfoRequest();
        if (Objects.equals(0, campusId)) {
            campusId = null;
        }
        request.setCampusId(campusId);
        if (Objects.nonNull(presetTime) && presetTime > 0) {
            String presetTimeStr = WosaiDateTimeUtils.format(presetTime, DateUtil.data_format_yyyy_MM_dd_hh_mm_ss);
            request.setExpectedDeliveryEndTime(presetTimeStr);
            request.setExpectedDeliveryStartTime(presetTimeStr);
        }
        // 为空表示没有上楼选项，为0表示配送到楼下
        if (StringUtils.isNotBlank(floor) && !Objects.equals("0", floor)) {
            request.setFloor(Integer.valueOf(floor));
        }
        request.setAddressCode(addressCode);
        request.setSupplierType(SupplierTypeEnum.STORE);
        request.setSupplierId(storeId);
        request.setGoodsList(buildGoodsList(storeId));
        //todo 校区code
        com.shouqianba.saas.utils.vo.BaseResponse<CampusAggregateDeliveryInfoDTO> response = openTeamCampusRpcService.querySupplierAggregateDeliveryInfo(request);
        if (Objects.isNull(response) || !response.isSuccess()) {
            return null;
        }
        return response.getData();
    }


    private List<GoodsModel> buildGoodsList(String storeId) {
        try {
            Cart cart = cartService.getCart(storeId, Constants.ServiceType.TAKEOUT, null, OrderMealTypeEnum.SINGLE);
            if (Objects.nonNull(cart) && CollectionUtils.isNotEmpty(cart.getRecords())) {
                return cart.getRecords().stream().map(record -> {
                    GoodsModel goodsModel = new GoodsModel();
                    goodsModel.setId(record.getItemId());
                    goodsModel.setName(record.getName());
                    goodsModel.setQuantity(record.getNum());
                    goodsModel.setPrice(record.getPrice() / record.getNum());
                    return goodsModel;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            LogUtils.logWarn("校园商品组装失败", "campus.buildGoodsList", storeId, e);

        }
        return new ArrayList<>();
    }

    /**
     * 根据围栏补充校园外卖信息
     *
     * @param storeId
     * @param deliverInfo
     * @return
     */
    public DeliverInfo fillCampusInfo(String storeId, DeliverInfo deliverInfo) {
        if (StringUtils.isNotBlank(deliverInfo.getAddressCode()) || StringUtils.isNotBlank(deliverInfo.getAddressName())) {
            Map<Object, Object> addressExtra = new HashMap<>();
            addressExtra.put("addressCode", deliverInfo.getAddressCode());
            addressExtra.put("addressName", deliverInfo.getAddressName());
            deliverInfo.setExtra(addressExtra);
        }
        String address = deliverInfo.getAddress();
        if (StringUtils.isNotBlank(deliverInfo.getAddressName())) {
            address = address + deliverInfo.getAddressName();
        }
        deliverInfo.setAddress(address);
        if (apolloConfigHelper.getBooleanConfigValueByKey("fillCampusInfo", true) && Objects.isNull(deliverInfo.getAppVersion())) {
            CampusTakeoutRequest campusTakeoutRequest = new CampusTakeoutRequest();
            campusTakeoutRequest.setStoreId(storeId);
            campusTakeoutRequest.setLon(deliverInfo.getLongitude());
            campusTakeoutRequest.setLat(deliverInfo.getLatitude());
            try {
                List<CampusTakeoutResponse> campusList = customerCampusRemoteService.campusTakeout(campusTakeoutRequest);
                if (CollectionUtils.isNotEmpty(campusList)) {
                    deliverInfo.setType(1);
                    deliverInfo.setCampusId(campusList.get(0).getCampusId());
                }
            } catch (Exception e) {
                log.warn("查询围栏出错：{}", e.getMessage(), e);
            }
        }
        return deliverInfo;
    }


    /**
     * 门店是否有校园外卖分账
     *
     * @param storeId
     * @return
     */
    public boolean hasProfitSharing(String storeId) {
        StoreCampusListRequest storeCampusListRequest = new StoreCampusListRequest();
        storeCampusListRequest.setStoreId(storeId);
        try {
            List<CampusBaseInfo> list = campusRemoteService.storeCampusList(storeCampusListRequest);
            if (CollectionUtils.isNotEmpty(list)) {
                return list.stream().anyMatch(it -> it.getStatus() == 0 && (it.getDeliveryType() == 1 || it.getOutsideDeliveryType() == 1));
            }
        } catch (Exception e) {
            log.warn("查询门店在校园的配送方式出错：{}", e.getMessage(), e);
        }

        return false;
    }


    /**
     * 查询门店在校园中的信息
     *
     * @param storeId 门店id
     * @return 是否入住校园，是否使用收钱吧配送，是否有流量服务费
     */
    public StoreCampusInfo getStoreInCampusInfo(String storeId) {
        try {
            StoreCampusInfo campusInfo = new StoreCampusInfo();
            campusInfo.setStoreId(storeId);
            StoreCampusListRequest storeCampusListRequest = new StoreCampusListRequest();
            storeCampusListRequest.setStoreId(storeId);

            // 返回的数据中，包含已下线的校园，需要过滤
            List<CampusBaseInfo> list = campusRemoteService.storeCampusList(storeCampusListRequest);
            if (CollectionUtils.isNotEmpty(list)) {
                List<CampusBaseInfo> onlineCampus = list.stream().filter(it -> Objects.equals(it.getStatus(), 0)).collect(Collectors.toList());
                // 是否入住校园
                campusInfo.setInCampus(CollectionUtils.isNotEmpty(onlineCampus));

                if (campusInfo.isInCampus()) {
                    // 是否使用了收钱吧配送
                    campusInfo.setHasCampusDelivery(list.stream().anyMatch(it -> Objects.equals(it.getDeliveryType(), 1) || Objects.equals(it.getOutsideDeliveryType(), 1)));
                    Set<String> onlineCampusIdSet = onlineCampus.stream().map(it -> String.valueOf(it.getId())).collect(Collectors.toSet());
                    // 查询是否有校园服务费
                    List<CampusFreeRateInfo> freeRateInfos = campusFreeRateRemoteService.findValidCampusRateListInfoByStoreId(storeId);
                    campusInfo.setHasCampusFee(CollectionUtils.isNotEmpty(freeRateInfos) && freeRateInfos.stream().anyMatch(it -> onlineCampusIdSet.contains(it.getCampusId())));
                }

            }
            LogUtils.logInfo("查询门店校园属性信息", "getStoreInCampusInfo", campusInfo);
            return campusInfo;
        } catch (Exception e) {
            LogUtils.logWarn("查询门店校园属性信息出错", "getStoreInCampusInfo", storeId, e);
        }
        return null;
    }

    /**
     * @param storeId
     * @return
     */
    public boolean isStoreInAnyCampus(String storeId) {
        Boolean isCampusStore = WeakReferenceCaller.call(() -> campusRemoteService.storeInAnyCampusByStoreId(storeId));
        return BooleanUtils.toBoolean(isCampusStore);
    }


    /**
     * 查询校园eta
     *
     * @param storeId     门店id
     * @param deliverInfo 配送信息
     * @return
     */
    public OrderFulfillmentCycleTimeDTO queryCampusETA(String storeId, DeliverInfo deliverInfo) {
        try {
            //即时单，请求配送方计算
            OrderFulfillmentCycleTimeRequest cycleTimeRequest = new OrderFulfillmentCycleTimeRequest();
            cycleTimeRequest.setCampusId(deliverInfo.getCampusId());
            cycleTimeRequest.setStoreId(storeId);
            cycleTimeRequest.setCampusAddressCode(deliverInfo.getAddressCode());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(deliverInfo.getFloor()) && !Objects.equals("0", deliverInfo.getFloor())) {
                cycleTimeRequest.setFloor(Integer.parseInt(deliverInfo.getFloor()));
            }
            cycleTimeRequest.setDeliveryAddressLatitude(deliverInfo.getLatitude());
            cycleTimeRequest.setDeliveryAddressLongitude(deliverInfo.getLongitude());
            cycleTimeRequest.setCustomerId(ThreadLocalHelper.getUserId());
            return WeakReferenceCaller.call(() -> openTeamCampusRpcService.queryOrderFulfillmentCycleTime(cycleTimeRequest).getData());

        } catch (Exception e) {
            LogUtils.logWarn("查询校园ETA出错", "queryCampusETA", deliverInfo, e);
            return null;
        }

    }


}
