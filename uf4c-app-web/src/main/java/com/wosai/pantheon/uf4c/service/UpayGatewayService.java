package com.wosai.pantheon.uf4c.service;

import com.wosai.market.trade.exception.MarketTradeException;
import com.wosai.market.trade.modal.upayGateway.OrderQueryResposne;
import com.wosai.market.trade.modal.upayGateway.PrePayResult;
import com.wosai.pantheon.uf4c.model.OrderQueryRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

@Service
public class UpayGatewayService {
    private final Logger log = LoggerFactory.getLogger(getClass());

    @Value("${service.upay-gateway.url}")
    private String upayUrl;

    @Autowired
    private RestTemplate restTemplate;

    public OrderQueryResposne queryOrder(OrderQueryRequest orderQueryRequest) {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity httpEntity = new HttpEntity<>(orderQueryRequest, headers);
        return this.sendPost(upayUrl + "/upay/v2/query", httpEntity, OrderQueryResposne.class);
    }


    private <t> t sendPost(String url, HttpEntity<?> httpEntity, Class<t> responseClass) {
        try {
            log.info("upay-gateway request : {}", httpEntity.toString());
            t response = restTemplate.postForObject(url, httpEntity, responseClass);
            log.info("upay-gateway response : {}", response.toString());
            return response;
        } catch (Exception e) {
            log.warn("upay-gateway服务获取错误", e);
            return null;
        }
    }

    public void checkResult(PrePayResult result, String resultCode) {
        if (result == null) {
            throw new MarketTradeException("交易异常，请稍候再试！");
        } else if (!Objects.equals(result.getResult_code(), "200")) {
            throw new MarketTradeException(result.getError_message() == null ? "交易异常，请稍候再试！" : result.getError_message());
        } else if (!result.getBiz_response().getResult_code().equals("PRECREATE_SUCCESS")
                || result.getBiz_response().getData().getWap_pay_request() == null) {
            if (result.getBiz_response().getError_message() != null) {
                throw new MarketTradeException(result.getBiz_response().getError_message());
            } else {
                throw new MarketTradeException("交易异常，请稍候再试！");
            }
        }
    }
}
