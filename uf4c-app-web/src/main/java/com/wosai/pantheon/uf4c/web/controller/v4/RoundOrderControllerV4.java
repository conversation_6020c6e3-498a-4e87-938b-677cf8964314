package com.wosai.pantheon.uf4c.web.controller.v4;

import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.InitRequest;
import com.wosai.pantheon.uf4c.service.apisix.OrderRoundService;
import com.wosai.pantheon.uf4c.web.exception.BaseException;
import com.wosai.smartbiz.oms.api.enums.ErrorTipWayEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.pojo.OrderMainWrapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @since 2022/5/20
 */
@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@RestController
@RequestMapping(path = "/api/v4/orders/round", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class RoundOrderControllerV4 {


    @Autowired
    private OrderRoundService orderRoundService;

    @PostMapping("/init")
    @ResponseBody
    @SneakyThrows
    public OrderMainWrapper init(@RequestBody InitRequest request) {
        OrderMainWrapper orderMainWrapper = new OrderMainWrapper();
        try {
            //设置是否需要检查购物车商品
            request.setCheckCartItem(true);
            orderMainWrapper = orderRoundService.init(new ApiRequest<>(request));
        } catch (BaseException ex) {
            log.warn("RoundOrderControllerV4.init BaseException, storeId{},tableId:{},tableNo:{}", request.getStoreId(), request.getTableId(), request.getTableNo(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(ErrorTipWayEnum.POPUP)
                    .errorCode(ex.getCode())
                    .errorMsg(ex.getMessage())
                    .build();

            orderMainWrapper.setCartCheckResult(cartCheckResult);
        } catch (Exception ex) {
            log.warn("RoundOrderControllerV4.init Exception, storeId{},tableId:{},tableNo:{}", request.getStoreId(), request.getTableId(), request.getTableNo(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(ErrorTipWayEnum.POPUP)
                    .errorCode(null)
                    .errorMsg("系统错误，请联系收钱吧客服处理" + TraceContext.traceId())
                    .build();

            orderMainWrapper.setCartCheckResult(cartCheckResult);
        }
        return orderMainWrapper;
    }


    @PostMapping("/addGoods")
    @ResponseBody
    @SneakyThrows
    public OrderMainWrapper addGoods(@RequestBody InitRequest request) {
        OrderMainWrapper orderMainWrapper = new OrderMainWrapper();
        try {
            //设置是否需要检查购物车商品
            request.setCheckCartItem(true);
            orderMainWrapper = orderRoundService.addGoods(new ApiRequest<>(request));
        } catch (BaseException ex) {
            log.warn("RoundOrderControllerV4.addGoods BaseException, storeId{},tableId:{},tableNo:{}", request.getStoreId(), request.getTableId(), request.getTableNo(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(ErrorTipWayEnum.POPUP)
                    .errorCode(ex.getCode())
                    .errorMsg(ex.getMessage())
                    .build();

            orderMainWrapper.setCartCheckResult(cartCheckResult);
        } catch (Exception ex) {
            log.warn("RoundOrderControllerV4.addGoods Exception, storeId{},tableId:{},tableNo:{}", request.getStoreId(), request.getTableId(), request.getTableNo(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(ErrorTipWayEnum.POPUP)
                    .errorCode(null)
                    .errorMsg("系统错误，请联系收钱吧客服处理" + TraceContext.traceId())
                    .build();

            orderMainWrapper.setCartCheckResult(cartCheckResult);
        }
        return orderMainWrapper;
    }
}
