package com.wosai.pantheon.uf4c.util;

import com.google.common.collect.Lists;
import com.wosai.market.dto.*;
import com.wosai.market.dto.product.ProductDetail;
import com.wosai.market.enums.*;
import com.wosai.market.util.ItemUtils;
import com.wosai.pantheon.core.uitem.model.AttributeDto;
import com.wosai.pantheon.core.uitem.model.AttributeOptionDto;
import com.wosai.pantheon.core.uitem.model.ItemTag;
import com.wosai.pantheon.uf4c.model.vo.*;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/1/10
 */
public class ItemConverter {

    public static ItemDetailVO toItemDetailVo(ProductDetail detail) {
        return toItemDetailVo(detail, null);
    }

    public static ItemDetailVO toItemDetailVo(ProductDetail detail, String targetSkuId) {
        if (Objects.isNull(detail) || Objects.isNull(detail.getSpu())) {
            return null;
        }
        ItemDetailVO detailVO = new ItemDetailVO();
        detailVO.setItem(toItemVo(detail, targetSkuId));
        detailVO.setSpecs(toSpecVo(detail, targetSkuId));
        detailVO.setAttributes(toAttrVos(detail, ArrayList::new));
        detailVO.setMaterials(toMaterialVos(detail, () -> null));
        detailVO.setMaterialGroups(toMaterialGroupVos(detail, ArrayList::new));
        detailVO.setItemTags(toItemTags(detail, () -> null));
        detailVO.setIngredientNames(toIngredientNames(detail, () -> null));
        detailVO.setSaleTime(toSaleTimeVo(detail));
        // 套餐特有的信息
        if (ProductTypeEnum.PACKAGE == detail.getSpu().getType()) {
            detailVO.setPackageMustOrderProducts(toMustOrderItemVos(detail, () -> null));
            detailVO.setPackageOptionalGroups(toOptionalOrderItemGroups(detail, () -> null));
            detailVO.getItem().setOutOfStock(ItemUtils.isPackageOutOfStock(detail));
        }
        return detailVO;
    }

    public static ItemVO toItemVo(ProductDetail detail, String targetSkuId) {
        ProductSpu spu = detail.getSpu();
        if (Objects.isNull(spu)) {
            return null;
        }
        // 基础信息
        ItemVO itemVo = new ItemVO();
        itemVo.setId(spu.getSpuId());
        itemVo.setName(spu.getSpuTitle());
        itemVo.setPhotoUrl(transPhotoUrl(detail.getMediaList()));
        itemVo.setImages(transImageVo(detail.getMediaList()));
        itemVo.setDescription(transDesc(spu, detail.getMustOrderProducts(), detail.getOptionalOrderProductGroups()));
        itemVo.setDisplayOrder(spu.getSort());
        itemVo.setMerchantId(detail.getMerchantId());
        itemVo.setStoreId(detail.getStoreId());
        itemVo.setForSale(SaleStatusEnum.ON_SALE == spu.getSaleStatus());
        itemVo.setUnitType(transUnitType(spu.getUnitType()));
        itemVo.setUnit(spu.getSaleUnit());
        itemVo.setBarcode(spu.getSpuBarcode());
        itemVo.setMinSaleNum(spu.getMinSaleNum());
        itemVo.setSaleTimes(transSaleTimePts(spu.getSaleTimes()));
        itemVo.setSaleTerminals(transSaleTerminal(spu.getSaleTerminalTypes()));
        // 冗余分类信息
        if (Objects.nonNull(detail.getCategory()) && Objects.nonNull(detail.getCategory().getCategory())) {
            CategoryDTO cat = detail.getCategory().getCategory();
            itemVo.setCategoryId(cat.getCategoryId());
            itemVo.setCategoryName(cat.getCategoryName());
            itemVo.setCategorySort(cat.getSort().toString());
        }
        // 特殊标识信息
        itemVo.setSpuType(OrderHelper.getNotNullSpuType(spu.getType().name()).name());
        itemVo.setIsMultiple(Optional.ofNullable(detail.getSkuList()).map(List::size).orElse(0));
        itemVo.setItemTag(0L);
        // 价格信息
        itemVo.setKeepPriceEqual(spu.getKeepPriceEqual());
        List<ProductSku> skus = detail.getSkuList();
        if (CollectionUtils.isNotEmpty(skus) && StringUtils.isNotBlank(targetSkuId)) {
            skus = skus.stream()
                    .filter(sku -> StringUtils.equals(sku.getSkuId(), targetSkuId))
                    .collect(Collectors.toList());
        }
        itemVo.setPrice(getMinPrice(skus, ProductSku::getSalePrice, null));
        itemVo.setArrivalPrice(getMinPrice(skus, ProductSku::getArrivalPrice, null));
        itemVo.setTakeoutPrice(getMinPrice(skus, ProductSku::getTakeoutPrice, null));
        // 库存信息
        Stock stock = detail.getStock();
        if (Objects.nonNull(stock)) {
            if (Objects.nonNull(stock.getQuantity())) {
                itemVo.setSku(stock.getQuantity().intValue());
                itemVo.setOutOfStock(stock.getQuantity().intValue() <= 0);
            } else {
                itemVo.setSku(null);
                itemVo.setOutOfStock(false);
            }
            itemVo.setResetSku(YesNoEnum.Y == stock.getAutoReset());
            itemVo.setSkuDefault(Optional.ofNullable(stock.getDefaultQuantity()).map(BigDecimal::intValue).orElse(null));
        } else {
            itemVo.setSku(null);
            itemVo.setOutOfStock(false);
            itemVo.setSkuDefault(null);
            itemVo.setResetSku(false);
        }
        return itemVo;
    }

    public static ItemSpecVO toSpecVo(ProductDetail detail, String targetSkuId) {
        List<ProductSku> skus = detail.getSkuList();
        if (CollectionUtils.isNotEmpty(skus) && StringUtils.isNotBlank(targetSkuId)) {
            skus = skus.stream()
                    .filter(sku -> StringUtils.equals(sku.getSkuId(), targetSkuId))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(skus)) {
            return null;
        }
        boolean noSku = skus.stream().anyMatch(sku -> StringUtils.isBlank(sku.getSkuTitle()) && StringUtils.isBlank(sku.getSpecName()));
        if (noSku) {
            return null;
        }
        ItemSpecVO specVO = new ItemSpecVO();
        specVO.setTitle(skus.get(0).getSkuTitle());
        List<SpecOptionVO> options = skus.stream()
                .map(sku -> {
                    SpecOptionVO option = new SpecOptionVO();
                    option.setId(sku.getSkuId());
                    option.setName(sku.getSpecName());
                    option.setSeq(sku.getSort());
                    option.setPrice(transMoney(sku.getSalePrice(), null));
                    option.setArrivalPrice(transMoney(sku.getArrivalPrice(), null));
                    option.setTakeoutPrice(transMoney(sku.getTakeoutPrice(), null));
                    return option;
                })
                .sorted(Comparator.comparing(SpecOptionVO::getSeq, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
        specVO.setOptions(options);
        return specVO;
    }

    public static List<AttributeDto> toAttrVos(ProductDetail detail, Supplier<List<AttributeDto>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(detail.getAttributeList())) {
            return defaultIfEmpty.get();
        }
        return detail.getAttributeList().stream()
                .map(attr -> {
                    AttributeDto dto = new AttributeDto();
                    dto.setId(attr.getAttributeId());
                    dto.setTitle(attr.getName());
                    dto.setSeq(attr.getSort());
                    dto.setMultiple(YesNoEnum.Y == attr.getIsMulti() ? 1 : 0);
                    dto.setMust(YesNoEnum.Y == attr.getIsMust() ? 1 : 0);
                    List<AttributeOptionDto> options = new ArrayList<>(16);
                    if (CollectionUtils.isNotEmpty(attr.getSubAttrList())) {
                        attr.getSubAttrList().forEach(sa -> {
                            AttributeOptionDto option = new AttributeOptionDto();
                            option.setId(sa.getAttributeId());
                            option.setName(sa.getName());
                            option.setSeq(sa.getSort());
                            options.add(option);
                        });
                    }
                    options.sort(Comparator.comparing(AttributeOptionDto::getSeq, Comparator.nullsLast(Integer::compareTo)));
                    dto.setOptions(options);
                    return dto;
                })
                .sorted(Comparator.comparing(AttributeDto::getSeq, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    public static List<MaterialVO> toMaterialVos(ProductDetail detail, Supplier<List<MaterialVO>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(detail.getMaterialGroups())) {
            return defaultIfEmpty.get();
        }
        List<MaterialVO> mvs = new ArrayList<>(16);
        detail.getMaterialGroups().forEach(mg -> {
            if (CollectionUtils.isNotEmpty(mg.getMaterials())) {
                List<MaterialVO> tmpMvs = toMaterialVos(mg.getMaterials(), ArrayList::new);
                mvs.addAll(tmpMvs);
            }
        });
        return mvs;
    }

    public static List<MaterialVO> toMaterialVos(List<Material> ms, Supplier<List<MaterialVO>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(ms)) {
            return defaultIfEmpty.get();
        }
        return ms.stream()
                .map(m -> {
                    MaterialVO mv = new MaterialVO();
                    mv.setId(m.getMaterialId());
                    mv.setName(m.getName());
                    mv.setPrice(transMoney(m.getPrice(), 0));
                    mv.setSeq(m.getSort());
                    mv.setStatus(m.getStatus().getCode());
                    return mv;
                })
                .sorted(Comparator.comparing(MaterialVO::getSeq, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    public static List<MaterialGroupVO> toMaterialGroupVos(ProductDetail detail, Supplier<List<MaterialGroupVO>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(detail.getMaterialGroups())) {
            return defaultIfEmpty.get();
        }
        return detail.getMaterialGroups().stream()
                .map(mg -> {
                    MaterialGroupVO mgv = new MaterialGroupVO();
                    mgv.setGroupId(mg.getGroupId());
                    mgv.setName(mg.getName());
                    mgv.setSeq(mg.getSort());
                    mgv.setMaterials(toMaterialVos(mg.getMaterials(), ArrayList::new));
                    return mgv;
                })
                .sorted(Comparator.comparing(MaterialGroupVO::getSeq, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    public static List<ItemTag> toItemTags(ProductDetail detail, Supplier<List<ItemTag>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(detail.getProductTagList())) {
            return defaultIfEmpty.get();
        }
        return detail.getProductTagList().stream()
                .map(t -> {
                    ItemTag tv = new ItemTag();
                    tv.setTagId(t.getTagId());
                    tv.setTagName(t.getTagName());
                    ProductTagColorEnum colorEnum = ProductTagColorEnum.valueOf(t.getTagColor());
                    if (Objects.nonNull(colorEnum)) {
                        tv.setTagColorCode(colorEnum.getTagColor());
                        tv.setFontColorCode(colorEnum.getFontColor());
                    }
                    return tv;
                }).collect(Collectors.toList());
    }

    public static List<String> toIngredientNames(ProductDetail detail, Supplier<List<String>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(detail.getIngredients())) {
            return defaultIfEmpty.get();
        }
        return detail.getIngredients().stream().map(Ingredient::getName).collect(Collectors.toList());
    }

    public static ItemSaleTimeVO toSaleTimeVo(ProductDetail detail) {
        ItemSaleTimeVO sv = new ItemSaleTimeVO().setType(SaleTimeTypeEnum.ALL_TIME.getCode());
        if (Objects.isNull(detail.getSaleTime())) {
            return sv;
        }
        SaleTime st = detail.getSaleTime();
        sv.setType(st.getType().getCode());
        sv.setStartDate(st.getStartDate());
        sv.setEndDate(st.getEndDate());
        sv.setCycle(Optional.ofNullable(st.getCycle()).orElseGet(ArrayList::new).stream().map(DayOfWeek::getValue).collect(Collectors.toList()));
        sv.setTimes(transTimePts(st.getTimes(), () -> null));
        return sv;
    }

    public static List<ItemDetailVO> toMustOrderItemVos(ProductDetail detail, Supplier<List<ItemDetailVO>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(detail.getMustOrderProducts())) {
            return defaultIfEmpty.get();
        }
        return detail.getMustOrderProducts().stream()
                .map(ItemConverter::toMustOrderItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static ItemDetailVO toMustOrderItem(MustOrderProduct mp) {
        ItemDetailVO tp = toItemDetailVo(mp.getProductDetail(), mp.getSkuId());
        if (Objects.isNull(tp)) {
            return null;
        }
        tp.getItem().setAddCount(mp.getSaleNum());
        tp.setMaterials(null);
        tp.setMaterialGroups(null);
        return tp;
    }

    public static List<PackageOptionalGroup> toOptionalOrderItemGroups(ProductDetail detail, Supplier<List<PackageOptionalGroup>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(detail.getOptionalOrderProductGroups())) {
            return defaultIfEmpty.get();
        }
        return detail.getOptionalOrderProductGroups().stream()
                .map(og -> {
                    if (CollectionUtils.isEmpty(og.getProducts())) {
                        return null;
                    }
                    PackageOptionalGroup ogv = new PackageOptionalGroup();
                    ogv.setGroupId(og.getGroupId());
                    ogv.setGroupName(og.getGroupName());
                    ogv.setMustOrderNum(og.getMustOrderNum());
                    ogv.setSupportDuplicate(YesNoEnum.Y == og.getSupportDuplicate());
                    List<ItemDetailVO> pvs = og.getProducts().stream()
                            .map(ItemConverter::toOptionalOrderItem)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    ogv.setProducts(pvs);
                    return ogv;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static ItemDetailVO toOptionalOrderItem(OptionalOrderProduct p) {
        ItemDetailVO pv = toItemDetailVo(p.getProductDetail(), p.getSkuId());
        if (Objects.isNull(pv)) {
            return null;
        }
        int addPrice = transMoney(p.getAddPrice(), 0);
        // 将加价设置为实际售价
        if (Objects.nonNull(pv.getSpecs()) && CollectionUtils.isNotEmpty(pv.getSpecs().getOptions())) {
            pv.getSpecs().getOptions().forEach(op -> {
                if (StringUtils.equals(op.getId(), p.getSkuId())) {
                    op.setPrice(addPrice);
                } else {
                    op.setPrice(0);
                }
            });
        } else {
            pv.getItem().setPrice(addPrice);
        }
        pv.setMaterials(null);
        pv.setMaterialGroups(null);
        return pv;
    }



    public static List<ItemSaleTimePartitionVO> transTimePts(List<TimeSection> origins, Supplier<List<ItemSaleTimePartitionVO>> defaultIfEmpty) {
        if (CollectionUtils.isEmpty(origins)) {
            return defaultIfEmpty.get();
        }
        return origins.stream()
                .map(t -> {
                    ItemSaleTimePartitionVO ts = new ItemSaleTimePartitionVO();
                    ts.setStartTime(t.getStartTime());
                    ts.setEndTime(t.getEndTime());
                    return ts;
                }).collect(Collectors.toList());
    }

    public static Integer transMoney(Long origin, Integer defaultIfNull) {
        return Optional.ofNullable(origin).map(Long::intValue).orElse(defaultIfNull);
    }

    public static List<Integer> transSaleTerminal(List<SaleTerminalTypeEnum> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Arrays.asList(SaleTerminalTypeEnum.MINI_APP_ORDER.getCode(), SaleTerminalTypeEnum.CASHIER_ORDER.getCode());
        }
        return origins.stream()
                .map(SaleTerminalTypeEnum::getCode)
                .collect(Collectors.toList());
    }

    public static List<com.wosai.pantheon.core.uitem.model.TimeSection> transSaleTimePts(List<TimeSection> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return null;
        }
        return origins.stream()
                .map(p -> {
                    com.wosai.pantheon.core.uitem.model.TimeSection timeSection = new com.wosai.pantheon.core.uitem.model.TimeSection();
                    timeSection.setStartTime(p.getStartTime());
                    timeSection.setEndTime(p.getEndTime());
                    return timeSection;
                }).collect(Collectors.toList());
    }

    public static Integer transUnitType(UnitTypeEnum unitTypeEnum) {
        if (Objects.isNull(unitTypeEnum)) {
            return 0;
        }
        return UnitTypeEnum.WEIGHT == unitTypeEnum ? 1 : 0;
    }

    public static Integer getMinPrice(List<ProductSku> skus, Function<ProductSku, Long> mapper, Integer defaultIfNull) {
        return Optional.ofNullable(skus).orElseGet(Lists::newArrayList)
                .stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .min(Long::compareTo)
                .map(Long::intValue)
                .orElse(defaultIfNull);
    }

    public static String transDesc(ProductSpu spu, List<MustOrderProduct> mustOrderProducts, List<OptionalOrderProductGroup> optionalGroups) {
        if (ProductTypeEnum.PACKAGE != spu.getType()) {
            return spu.getDescription();
        }
        if (StringUtils.isNotBlank(spu.getDescription())) {
            return spu.getDescription();
        }
        List<String> texts = new ArrayList<>(16);
        if (CollectionUtils.isNotEmpty(mustOrderProducts)) {
            mustOrderProducts.forEach(p -> {
                String singleText = Optional.ofNullable(p.getProductDetail()).map(ProductDetail::getSpu).map(ProductSpu::getSpuTitle).orElse("商品")
                        + Optional.ofNullable(p.getSaleNum()).orElse(1)
                        + Optional.ofNullable(p.getProductDetail()).map(ProductDetail::getSpu).map(ProductSpu::getSaleUnit).orElse("份");
                texts.add(singleText);
            });
        }
        if (CollectionUtils.isNotEmpty(optionalGroups)) {
            optionalGroups.forEach(p -> {
                String singleText = p.getGroupName()
                        + Optional.ofNullable(p.getMustOrderNum()).orElse(1)
                        + "份(可选)";
                texts.add(singleText);
            });
        }
        return StringUtils.join(texts, "+");
    }

    public static String transPhotoUrl(List<ProductMedia> medias) {
        if (CollectionUtils.isEmpty(medias)) {
            return null;
        }
        List<String> photoUrls = medias.stream()
                .filter(m -> MediaTypeEnum.IMAGE == m.getType())
                .sorted(Comparator.comparing(ProductMedia::getSort, Comparator.nullsLast(Integer::compareTo)))
                .map(ProductMedia::getUrl)
                .collect(Collectors.toList());
        return StringUtils.join(photoUrls, ",");
    }

    public static List<ItemImageVO> transImageVo(List<ProductMedia> medias) {
        if (CollectionUtils.isEmpty(medias)) {
            return Collections.emptyList();
        }
        return medias.stream()
                .filter(media -> MediaTypeEnum.IMAGE.equals(media.getType()))
                .sorted(Comparator.comparing(ProductMedia::getSort, Comparator.nullsLast(Integer::compareTo)))
                .map(media -> {
                    ItemImageVO imageVo = new ItemImageVO();
                    imageVo.setDisplayUrl(media.getUrl());
                    List<ItemImageVO.Spec> specVos = Optional.ofNullable(media.getSpecs())
                            .orElseGet(ArrayList::new)
                            .stream()
                            .map(spec -> {
                                ItemImageVO.Spec specVo = new ItemImageVO.Spec();
                                specVo.setType(spec.getType());
                                specVo.setUrl(spec.getUrl());
                                return specVo;
                            }).collect(Collectors.toList());
                    imageVo.setSpecs(specVos);
                    return imageVo;
                }).collect(Collectors.toList());
    }


    public static List<SaleSceneEnum> transSaleScenes(Integer serviceType) {
        return SaleSceneEnum.getByServiceType(serviceType, SaleSceneEnum.ALL).forQuery();
    }

}
