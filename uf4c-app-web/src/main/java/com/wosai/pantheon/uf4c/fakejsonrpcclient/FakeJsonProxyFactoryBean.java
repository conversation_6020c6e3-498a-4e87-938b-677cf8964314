package com.wosai.pantheon.uf4c.fakejsonrpcclient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import org.aopalliance.intercept.MethodInvocation;

import static com.wosai.pantheon.uf4c.util.TraceContextUtils.fake;

public class FakeJsonProxyFactoryBean extends JsonProxyFactoryBean {
    private String mockUrl;

    private JsonProxyFactoryBean fakeJsonProxyFactoryBean = new JsonProxyFactoryBean();

    public String getMockUrl() {
        return mockUrl;
    }

    public void setMockUrl(String mockUrl) {
        this.mockUrl = mockUrl;
        fakeJsonProxyFactoryBean.setServiceUrl(mockUrl);
    }

    public JsonProxyFactoryBean getFakeJsonProxyFactoryBean() {
        return fakeJsonProxyFactoryBean;
    }

    public void setFakeJsonProxyFactoryBean(JsonProxyFactoryBean fakeJsonProxyFactoryBean) {
        this.fakeJsonProxyFactoryBean = fakeJsonProxyFactoryBean;
    }

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        if (fake()) {
            return fakeJsonProxyFactoryBean.invoke(invocation);
        } else {
            return super.invoke(invocation);
        }
    }

    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();
        fakeJsonProxyFactoryBean.afterPropertiesSet();
    }

    public void setServiceInterface(Class<?> serviceInterface) {
        super.setServiceInterface(serviceInterface);
        fakeJsonProxyFactoryBean.setServiceInterface(serviceInterface);
    }

    public void setObjectMapper(ObjectMapper objectMapper) {
        super.setObjectMapper(objectMapper);
        fakeJsonProxyFactoryBean.setObjectMapper(objectMapper);
    }

    public void setConnectionTimeoutMillis(int connectionTimeoutMillis) {
        super.setConnectionTimeoutMillis(connectionTimeoutMillis);
        fakeJsonProxyFactoryBean.setConnectionTimeoutMillis(connectionTimeoutMillis);
    }

    public void setReadTimeoutMillis(int readTimeoutMillis) {
        super.setReadTimeoutMillis(readTimeoutMillis);
        fakeJsonProxyFactoryBean.setReadTimeoutMillis(readTimeoutMillis);
    }
}
