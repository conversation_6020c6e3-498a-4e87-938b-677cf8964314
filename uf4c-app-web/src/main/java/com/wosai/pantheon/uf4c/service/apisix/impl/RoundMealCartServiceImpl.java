package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.mcc.api.dto.request.BooleanConfigQueryRequest;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartAndRedeem;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.dto.CartsRequest;
import com.wosai.pantheon.uf4c.service.RedeemService;
import com.wosai.pantheon.uf4c.service.apisix.RoundMealCartService;
import com.wosai.pantheon.uf4c.util.CartHelper;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.apisix.uf4capp.Uf4cAppApiRequest;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.enums.DealTypeEnum;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.pojo.CartInfoDTO;
import com.wosai.smartbiz.oms.api.query.CartSyncQuery;
import com.wosai.smartbiz.oms.api.services.CartService;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import com.wosai.smartbiz.oms.api.services.apisix.ApisixCartServiceV2;
import com.wosai.smartbiz.oms.api.vos.CartVersionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class RoundMealCartServiceImpl implements RoundMealCartService {
    @Autowired
    private CartService roundMealCartService;

    @Autowired
    private ConfigRemoteService configRemoteService;

    @Autowired
    private RedeemService redeemService;

    @Autowired
    private com.wosai.pantheon.uf4c.service.CartService cartService;

    @Autowired
    private ApisixCartServiceV2 apisixCartServiceV2;

    @Autowired
    private TableRpcServiceV2 tableRpcServiceV2;

    @Override
    public Cart addCart(ApiRequest<CartItemCreate> request) {
        return cartService.addCart(request.getBody(), OrderMealTypeEnum.ROUND_MEAL, DealTypeEnum.ADD);
    }

    @Override
    public Cart setCartItem(ApiRequest<CartItemCreate> request) {
        return cartService.addCart(request.getBody(),OrderMealTypeEnum.ROUND_MEAL,DealTypeEnum.SET_ITEM_NUM);
    }

    @Override
    public Cart reduceCart(ApiRequest<CartItemCreate> request) {
        return cartService.reduceRoundMealCart(request.getBody());
    }

    @Override
    public Cart cleanCart(ApiRequest apiRequest) {

        Map queryParam = apiRequest.getQuery();


        String tableId = MapUtils.getString(queryParam, "table_id");
        String storeId = MapUtils.getString(queryParam, "store_id");

        String userName = MapUtils.getString(queryParam, "user_name");
        String userIcon = MapUtils.getString(queryParam, "user_icon");

        if (StringUtils.isBlank(tableId)){
            throw new ParamException("桌台信息不能为空");
        }

        if (StringUtils.isBlank(storeId)){
            throw new ParamException("门店信息不能为空");
        }

        boolean cleanMustOrder = false;

        BooleanConfigQueryRequest muserOrderEnableConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.MUST_ORDER_ENABLE_CONFIG_KEY, false);

        boolean muserOrderEnable = configRemoteService.getBooleanConfig(muserOrderEnableConfigQueryRequest);

        if(muserOrderEnable){
            BooleanConfigQueryRequest muserOrderEditableConfigQueryRequest = MccUtils.findBooleanConfigByNameRequest(OwnerType.STORE_ID, storeId, Constants.MUST_ORDER_EDITABLE_CONFIG_KEY, false);

            //可编辑的时候，需要清空购物车
            boolean muserOrderEditable = configRemoteService.getBooleanConfig(muserOrderEditableConfigQueryRequest);

            cleanMustOrder = muserOrderEditable;
        }else{
            cleanMustOrder = true;
        }

        CartSyncQuery cartSyncQuery = new CartSyncQuery();
        cartSyncQuery.setTableId(tableId);
        cartSyncQuery.setDealType(DealTypeEnum.CLEAN);
        cartSyncQuery.setUserId(ThreadLocalHelper.getUserId());
        cartSyncQuery.setCleanMustOrder(cleanMustOrder);
        cartSyncQuery.setUserName(userName);
        cartSyncQuery.setUserIcon(userIcon);
        Result<CartInfoDTO> result = roundMealCartService.cleanCart(cartSyncQuery);
        if (!result.isSuccess()){
            throw new BusinessException(ReturnCode.BUSINESS_ERROR,result.getErrorMsg());
        }
        return CartHelper.convertCart(result.getData());
    }

    @Override
    public CartCheckResultDTO checkItemStatus(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");
        String tableId = MapUtils.getString(queryParam, "table_id");
        Integer serviceType = MapUtils.getInteger(queryParam, "service_type", 0);

        if (StringUtils.isBlank(tableId)){
            throw new ParamException("桌台信息不能为空");
        }

        if (StringUtils.isBlank(storeId)){
            throw new ParamException("门店信息不能为空");
        }

        return cartService.checkItemStatus(tableId,storeId,serviceType, OrderMealTypeEnum.ROUND_MEAL);
    }

    @Override
    public Cart list(ApiRequest apiRequest) {
        Map queryParam = apiRequest.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");
        String tableId = MapUtils.getString(queryParam, "table_id");
        Boolean fillRecommendMaterials = MapUtils.getBoolean(queryParam, "fill_recommend_materials",false);

        return cartService.getCart(storeId,null, tableId, OrderMealTypeEnum.ROUND_MEAL, fillRecommendMaterials);
    }

    @Override
    public boolean setCartMustOrderEditable(ApiRequest<CartsRequest> apiRequest) {
        CartsRequest cartsRequest = apiRequest.getBody();

        CartSyncQuery cartQuery = new CartSyncQuery();
        cartQuery.setTableId(cartsRequest.getTableId());
        roundMealCartService.setOpenTableMustOrderEditable(cartQuery);
        return true;
    }

    @Override
    public CartAndRedeem listAndRedeem(ApiRequest<CartsRequest> apiRequest) {

        CartsRequest cartsRequest = apiRequest.getBody();


        Cart cart = cartService.getCart(cartsRequest.getStoreId(),null, cartsRequest.getTableId(), OrderMealTypeEnum.ROUND_MEAL, cartsRequest.getFillRecommendMaterials());
        //sn不为空说明是继续加菜，不查询优惠
        if(cart != null && StringUtils.isNotBlank(cart.getSn())){
            return new CartAndRedeem(cart, null);
        }
        RedeemResult redeemResult = redeemService.getRedeemResult(cartsRequest, cart, cartsRequest.isFromCart(), apiRequest.getUser());
        return new CartAndRedeem(cart, redeemResult);
    }

    @Override
    public Result<CartVersionVO> getCartVersion(ApiRequest<CartSyncQuery> apiRequest) {
        CartSyncQuery cartSyncQuery = apiRequest.getBody();

        String lastTableOpenId = cartSyncQuery.getTableOpenId();

        Uf4cAppApiRequest<CartSyncQuery> request = new Uf4cAppApiRequest<>(cartSyncQuery);
        Result<CartVersionVO> cartVersionRes = apisixCartServiceV2.getCartVersion(request);

        if(!cartVersionRes.isSuccess()){
            return cartVersionRes;
        }

        CartVersionVO cartVersionVO = cartVersionRes.getData();
        if(StringUtils.isNotBlank(lastTableOpenId) && !Objects.equals(lastTableOpenId, cartVersionVO.getTableOpenId())){
            return Result.error(cartVersionVO, ReturnCode.TABLE_CLEANED_ADD_ITEM.getCode(), ReturnCode.TABLE_CLEANED_ADD_ITEM.getMessage());
        }

        return cartVersionRes;
    }
}
