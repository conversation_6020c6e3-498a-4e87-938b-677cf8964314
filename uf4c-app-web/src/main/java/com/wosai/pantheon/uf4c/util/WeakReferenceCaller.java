package com.wosai.pantheon.uf4c.util;

import com.wosai.pantheon.uf4c.annotation.VoidSupplier;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
public class WeakReferenceCaller {

    public static <R> R call(Supplier<R> supplier) {

        try {
            return supplier.get();
        } catch (Throwable t) {
            log.warn("weak reference call error", keyValue("method", "weakReferenceCall"), t);
        }
        return null;
    }

    public static void callReturnVoid(VoidSupplier supplier) {
        try {
            supplier.get();
        } catch (Throwable t) {
            log.warn("weak reference call error", keyValue("method", "weakReferenceCall"), t);
        }
    }

}
