package com.wosai.pantheon.uf4c.util;

import com.alibaba.fastjson.JSON;
import com.wosai.market.tethys.api.enums.DiscountsEnum;
import com.wosai.pantheon.order.constant.Constants.OrderExtraKey;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.*;
import com.wosai.pantheon.order.model.dto.request.Attribute;
import com.wosai.pantheon.order.model.dto.request.Material;
import com.wosai.pantheon.order.model.dto.request.Spec;
import com.wosai.pantheon.order.model.dto.v2.OrderGoodsDTO;
import com.wosai.pantheon.order.model.dto.v2.OrderPaysDTO;
import com.wosai.pantheon.order.pojo.OrderExtraMemberInfo;
import com.wosai.pantheon.order.utils.OrderUtil;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.DeliverInfo;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.PayChannelInfo;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.utils.DateUtil;
import com.wosai.smartbiz.base.utils.MoneyUtil;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.oms.api.domain.MembershipPayInfoVO;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartGoodsDTO;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/30
 */

@Slf4j
public class EntityConvert {

    private static final String CART_NUM_UNIT = "x";

    private static final String EXTRA_KEY_SALE_WEIGHT = "sale_weight";

    private static final String EXTRA_KEY_UNIT_TYPE = "unit_type";

    public static final String EXTRA_KEY_CELLPHONE = "cellPhone";

    public static final String EXTRA_KEY_GROUP_BUYING_ID = "group_buying_id";


    public static Order convertOrderDTO4Print(OrderDTO orderDTO) {

        // 基本字段
        Order order = Order.builder()
                .id(orderDTO.getId())
                .sn(orderDTO.getSn())
                .type(orderDTO.getOrderType().getMsg())
                .transSn(orderDTO.getOrderSeq())
                .diningCode(MapUtils.getString(orderDTO.getExtra(), Constants.DINING_CODE))
                .status(orderDTO.getStatus().getCode())
                .subject(orderDTO.getSubject())
                .originalAmount(orderDTO.getOriginalAmount())
                .effectiveAmount(orderDTO.getEffectiveAmount())
                .buyerPayAmount(orderDTO.getBuyerPayAmount())
                .profitSharingAmount(orderDTO.getProfitSharingAmount())
                .buyerPayAmount(orderDTO.getBuyerPayAmount())
                .refundAmount(orderDTO.getRefundAmount())
                .totalDiscount(0L)
                .merchantDiscount(0L)
                .packAmount(orderDTO.getPackAmount())
                .buyerUid(orderDTO.getUserId())
                .buyerLogin(orderDTO.getUserName())
                .merchantId(orderDTO.getMerchantId())
                .storeId(orderDTO.getStoreId())
                .storeName(orderDTO.getStoreName())
                .terminalId(orderDTO.getTerminalId())
                .qrCode(orderDTO.getTerminalId())
                .qrCodeName(StringUtils.isEmpty(orderDTO.getTerminalName()) ? orderDTO.getTableNo() : orderDTO.getTerminalName())
                .qrCodeType(orderDTO.getTerminalType())
                .payway(orderDTO.getPayway())
                .subPayway(orderDTO.getSubPayway())
                .clientSn(orderDTO.getClientSn())
                .tradeNo(orderDTO.getTransSn())
                .packed(1 == Optional.ofNullable(orderDTO.getPacked()).map(PackType::getCode).orElse(0L))
                .remark(orderDTO.getRemark())
                .ctime(orderDTO.getCtime())
                .mtime(orderDTO.getMtime())
                .payTime(orderDTO.getPayTime())
                .orderTag(orderDTO.getOrderTag())
                .deleted(false)
                .version(orderDTO.getVersion())
                .tableNo(orderDTO.getTableNo())
                .tableId(orderDTO.getTableId())
                .campusOrder(Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false))
                .bookOrder(orderDTO.isBookOrder())
                .bookTime(Optional.ofNullable(orderDTO.getBookOrderInfoDTO()).map(BookOrderInfoDTO::getBookTime).orElse(-1L))
                .orderCampusDelivery(orderDTO.getOrderCampusDelivery())
                .orderCampusStation(orderDTO.getOrderCampusStation())
                .campusException(orderDTO.getCampusException())
                .build();


        List<OrderItemDTO> orderItemDTOS = orderDTO.getItems();

        List<CartItemCreate> items = Optional.ofNullable(orderItemDTOS)
                .map(it -> it.stream().map(info -> convertOrderItemDto2CartItemCreate(info, null, false)).collect(Collectors.toList()))
                .orElse(null);
        order.setItems(items);

        // 优惠
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderDTO.getRedeems())) {
            //相同类型的优惠信息合并
            List<OrderRedeemDTO> printRedeemDTOList = EntityConvert.mergeRedeems(orderDTO.getRedeems());
            List<RedeemResult.RedeemDetail> redeemDetails = new ArrayList<>();
            Long totalDiscount = 0L;
            long effectReductionAmount = 0;
            for (OrderRedeemDTO redeem : printRedeemDTOList) {
                totalDiscount += redeem.getDiscountAmount();
                if (3 == redeem.getType()) {
                    // 不更改原有逻辑
                    // 临时将配送费减免优惠加入小票打印优惠明细中
                    // 待配送费减免此项优惠进行系统性的梳理改造后再优化此处代码
                    if (redeem.getDiscountType() != 16) {
                        effectReductionAmount += redeem.getDiscountAmount();
                        order.setMerchantActivitySn(redeem.getActivitySn());
                        order.setMerchantDiscountName(redeem.getName());
                        if (!(orderDTO.getOrderSource() == OrderSource.CASHIER || orderDTO.getOrderSource() == OrderSource.APP || orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER)) {
                            //围餐的不要这样处理
                            order.setRefundAmount(Optional.ofNullable(order.getRefundAmount()).orElse(0L) - order.getMerchantDiscount());
                        }
                    }
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                } else if (1 == redeem.getType()) {
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                }
                order.setRedeemDetails(redeemDetails);
            }
            order.setMerchantDiscount(effectReductionAmount);
            order.setEffectiveAmount(order.getOriginalAmount() - effectReductionAmount);
            order.setTotalDiscount(totalDiscount);
        }


        // Extra字段
        Order.OrderExtra orderExtra = new Order.OrderExtra();
        if (orderDTO.getOrderAddress() != null) {
            DeliverInfo deliverInfo = new DeliverInfo();
            BeanUtils.copyProperties(orderDTO.getOrderAddress(), deliverInfo);
            deliverInfo.setDeliveryFee(Optional.ofNullable(orderDTO.getDeliverAmount()).map(Long::intValue).orElse(null));
            deliverInfo.setDeliveryType(Optional.ofNullable(orderDTO.getDeliverType()).map(String::valueOf).orElse(null));
            if (!CollectionUtils.isEmpty(orderDTO.getRedeems())) {
                orderDTO.getRedeems().forEach(r -> {
                    if (Objects.equals(r.getDiscountType(), DiscountsEnum.TAKEOUT_DELIVERY_FEE_REDUCTION_ACTIVITY.getCode())) {
                        deliverInfo.setReductionAmount(r.getDiscountAmount().intValue());
                    }
                });
            }
            orderExtra.setDeliveryInfo(deliverInfo);
            //校园单信息
            if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
                orderExtra.setCampusDelivery(orderDTO.getOrderCampusDelivery());
                orderExtra.setCampusStation(orderDTO.getOrderCampusStation());
            }
        }
        orderExtra.setStoreName(orderDTO.getStoreName());
        if (null != orderDTO.getExtra() && orderDTO.getExtra().containsKey(EXTRA_KEY_CELLPHONE)) {
            orderExtra.setCellphone((String) orderDTO.getExtra().get(EXTRA_KEY_CELLPHONE));
        }
        orderExtra.setDmInfo(orderDTO.getDeliveryInfo());

        if (Objects.nonNull(orderDTO.getExtra()) && orderDTO.getExtra().containsKey(Constants.MERCHANT_INDUSTRY_TYPE)) {
            orderExtra.setMerchantIndustryType(MapUtils.getString(orderDTO.getExtra(), Constants.MERCHANT_INDUSTRY_TYPE));
        }
        if (Objects.nonNull(orderDTO.getExtra()) && orderDTO.getExtra().containsKey(OrderExtraKey.EXTRA_KEY_MEMBERSHIP_INFO)) {
            Object membershipInfoObj = MapUtils.getObject(orderDTO.getExtra(), OrderExtraKey.EXTRA_KEY_MEMBERSHIP_INFO);
            if (null != membershipInfoObj) {
                orderExtra.setMembershipInfo(JSON.parseObject(JSON.toJSONString(membershipInfoObj), OrderExtraMemberInfo.class));
            }
        }

        order.setExtra(orderExtra);
        if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
            if (Objects.nonNull(orderDTO.getOrderCampusDelivery()) && orderDTO.getOrderCampusDelivery().getType() == 1) {
                if (Objects.nonNull(orderDTO.getOrderCampusStation())) {
                    order.setStationAddress(orderDTO.getOrderCampusStation().getAddress());
                }
            }
        }

        //支付信息处理 获取orderPaysDTO信息封装打印数据
        List<PayChannelInfo> payChannelList = getPayChannelListPrintData(orderDTO.getOrderPays());
        order.setPayChannelList(payChannelList);
        //支付会员信息处理，获取orderPaysDTO信息存储的会员信息
        List<MembershipPayInfoVO> membershipList = getMembershipListPrintData(orderDTO.getOrderPays(), orderDTO);
        order.setMembershipList(membershipList);

        return order;
    }

    public static Order convertOrderDTO(OrderDTO orderDTO) {


        if (orderDTO == null) {
            return null;
        }

        // 基本字段
        Order order = Order.builder()
                .id(orderDTO.getId())
                .sn(orderDTO.getSn())
                .type(orderDTO.getOrderType().getMsg())
                .transSn(orderDTO.getOrderSeq())
                .diningCode(MapUtils.getString(orderDTO.getExtra(), Constants.DINING_CODE))
                .status(orderDTO.getStatus().getCode())
                .subject(orderDTO.getSubject())
                .originalAmount(orderDTO.getOriginalAmount())
                .effectiveAmount(orderDTO.getEffectiveAmount())
                .receiveAmount(orderDTO.getReceiveAmount())
                .buyerPayAmount(orderDTO.getBuyerPayAmount())
                .refundAmount(orderDTO.getRefundAmount())
                .totalDiscount(0L)
                .merchantDiscount(0L)
                .packAmount(orderDTO.getPackAmount())
                .buyerUid(orderDTO.getUserId())
                .buyerLogin(orderDTO.getUserName())
                .merchantId(orderDTO.getMerchantId())
                .storeId(orderDTO.getStoreId())
                .storeName(orderDTO.getStoreName())
                .terminalId(orderDTO.getTerminalId())
                .qrCode(orderDTO.getTerminalId())
                .qrCodeName(StringUtils.isEmpty(orderDTO.getTerminalName()) ? orderDTO.getTableNo() : orderDTO.getTerminalName())
                .qrCodeType(orderDTO.getTerminalType())
                .payway(orderDTO.getPayway())
                .subPayway(orderDTO.getSubPayway())
                .clientSn(orderDTO.getClientSn())
                .tradeNo(orderDTO.getTransSn())
                .packed(1 == Optional.ofNullable(orderDTO.getPacked()).map(PackType::getCode).orElse(0L))
                .remark(orderDTO.getRemark())
                .ctime(orderDTO.getCtime())
                .mtime(orderDTO.getMtime())
                .payTime(orderDTO.getPayTime())
                .deleted(false)
                .version(orderDTO.getVersion())
                .processStatus(getProcessStatusForShow(orderDTO.getProcessStatus(), orderDTO.getStatus()))
                .tableNo(orderDTO.getTableNo())
                .tableId(orderDTO.getTableId())
                .extraInfo(orderDTO.getExtra())
                .orderTag(orderDTO.getOrderTag())
                .campusOrder(Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false))
                .bookOrder(orderDTO.isBookOrder())
                .bookTime(Optional.ofNullable(orderDTO.getBookOrderInfoDTO()).map(BookOrderInfoDTO::getBookTime).orElse(null))
                .orderCampusDelivery(orderDTO.getOrderCampusDelivery())
                .orderCampusStation(orderDTO.getOrderCampusStation())
                .orderFlags(orderDTO.getOrderFlags())
                .build();


        if (orderDTO.getOrderSource() == OrderSource.CASHIER || orderDTO.getOrderSource() == OrderSource.APP
                || orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER
                || orderDTO.getOrderType() == OrderType.PAY_FIRST_TABLE_ORDER) {
            //围餐的effectiveAmount是已经减过优惠金额的， 所以这里需要兼容下， 返回总金额
            order.setEffectiveAmount(order.getOriginalAmount());
        }


//
        List<CartItemCreate> items = Optional.ofNullable(orderDTO.getItems())
                .map(it -> it.stream().map(info -> convertOrderItemDto2CartItemCreate(info, GoodsRefPayType.PAY, false)).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(null);


        order.setItems(items);


        // 优惠
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderDTO.getRedeems())) {

            List<RedeemResult.RedeemDetail> redeemDetails = new ArrayList<>();
            Long totalDiscount = 0L;
            for (OrderRedeemDTO redeem : orderDTO.getRedeems()) {
                totalDiscount += redeem.getDiscountAmount();
                if (3 == redeem.getType() && redeem.getDiscountType() != 16) {
                    order.setMerchantActivitySn(redeem.getActivitySn());
                    order.setMerchantDiscount(redeem.getDiscountAmount());
                    order.setMerchantDiscountName(redeem.getName());
                    if (Objects.equals(redeem.getDiscountType(), DiscountsEnum.SECOND_ACTIVITY.getCode()) || Objects.equals(redeem.getDiscountType(), DiscountsEnum.SINGLE_ACTIVITY.getCode())) {
                        order.setMerchantActivityType(Constants.GOODS_DISCOUNT_GROUP);
                    } else {
                        order.setMerchantActivityType(Constants.COMMON_DISCOUNT_GROUP);
                    }
                    order.setEffectiveAmount(order.getEffectiveAmount() - order.getMerchantDiscount());
//                    if (!(orderDTO.getOrderSource() == OrderSource.CASHIER || orderDTO.getOrderSource() == OrderSource.APP || orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER)) {
//                        //围餐的不要这样处理
//                        order.setRefundAmount(Optional.ofNullable(order.getRefundAmount()).orElse(0L) - order.getMerchantDiscount());
//                    }
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                } else if (1 == redeem.getType()) {
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                }
                order.setRedeemDetails(redeemDetails);
            }
            order.setTotalDiscount(totalDiscount);
        }

        if (orderDTO.getOrderType() == OrderType.PAY_FIRST_TABLE_ORDER) {
            if (!org.apache.commons.lang3.StringUtils.equals(order.getProcessStatus(), OrderProcessStatusEnum.COMPLETED.getCode())
                    && !org.apache.commons.lang3.StringUtils.equals(order.getProcessStatus(), OrderProcessStatusEnum.CLOSED.getCode())
                    && !org.apache.commons.lang3.StringUtils.equals(order.getProcessStatus(), OrderProcessStatusEnum.REVOKED.getCode())) {
                if (!CollectionUtils.isEmpty(items)) {
                    boolean allowPay = false;
                    for (CartItemCreate item : items) {
                        if (item.getProcessStatus() == GoodsProcessStatus.NOT_ACCEPTED
                                || (TagUtil.hasTag(item.getItem().getItemTag(), OrderGoodsTagEnum.PAY_FIRST_TABLE_ORDER_NOT_PAY_GOODS.getValue())
                                && !TagUtil.hasTag(item.getItem().getItemTag(), OrderGoodsTagEnum.GIFT_FOOD.getValue()))) {
                            //有已经接单或其他状态的商品
                            allowPay = true;
                            break;
                        }
                    }

                    if (allowPay) {
                        order.setPayControll(Order.PayControll.builder().allowPay(true).build());
                    }
                }
            }

        }

        // Extra字段
        Order.OrderExtra orderExtra = new Order.OrderExtra();
        if (orderDTO.getOrderAddress() != null) {
            DeliverInfo deliverInfo = new DeliverInfo();
            BeanUtils.copyProperties(orderDTO.getOrderAddress(), deliverInfo);
            deliverInfo.setDeliveryFee(Optional.ofNullable(orderDTO.getDeliverAmount()).map(Long::intValue).orElse(null));
            deliverInfo.setDeliveryType(Optional.ofNullable(orderDTO.getDeliverType()).map(String::valueOf).orElse(null));
            if (!CollectionUtils.isEmpty(orderDTO.getRedeems())) {
                orderDTO.getRedeems().forEach(r -> {
                    if (Objects.equals(r.getDiscountType(), DiscountsEnum.TAKEOUT_DELIVERY_FEE_REDUCTION_ACTIVITY.getCode())) {
                        deliverInfo.setReductionAmount(r.getDiscountAmount().intValue());
                    }
                });
            }
            orderExtra.setDeliveryInfo(deliverInfo);
            //校园单信息
            if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
                orderExtra.setCampusDelivery(orderDTO.getOrderCampusDelivery());
                orderExtra.setCampusStation(orderDTO.getOrderCampusStation());
            }
        }
        orderExtra.setDmInfo(orderDTO.getDeliveryInfo());
        orderExtra.setStoreName(orderDTO.getStoreName());
        if (null != orderDTO.getExtra() && orderDTO.getExtra().containsKey(EXTRA_KEY_CELLPHONE)) {
            orderExtra.setCellphone((String) orderDTO.getExtra().get(EXTRA_KEY_CELLPHONE));
        }
        if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
            if (Objects.nonNull(orderDTO.getOrderCampusDelivery()) && orderDTO.getOrderCampusDelivery().getStatusCode() > 0) {
                OrderDeliveryDTO orderDeliveryDTO = new OrderDeliveryDTO();
                BeanUtils.copyProperties(orderDTO.getOrderCampusDelivery(), orderDeliveryDTO);
                orderExtra.setDmInfo(orderDeliveryDTO);
            }
        }

        orderExtra.setGroupBuyingId(MapUtils.getString(orderDTO.getExtra(), EXTRA_KEY_GROUP_BUYING_ID, ""));
        order.setExtra(orderExtra);


        return order;
    }


    public static CartItemCreate convertOrderItemDto2CartItemCreate(OrderItemDTO info, GoodsRefPayType filterRefPayType, Boolean isPackageGoods) {
        if (filterRefPayType != null) {
            if (info.getRefPayType() != filterRefPayType) {
                return null;
            }
        }
        CartItemCreate cartItemCreate = new CartItemCreate();

        if (info.getGoodsDiscountType() == GoodsDiscountType.MANUAL) {
            cartItemCreate.setManualChangePrice(true);
        }
        cartItemCreate.setProcessStatus(info.getProcessStatus());
        cartItemCreate.setOrderTime(info.getOrderTime());
        cartItemCreate.setExtraInfo(info.getExtraInfo());
        cartItemCreate.setItemUid(info.getId());

        if (OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.OPENTABLE_MUST_ORDER.getValue())) {
            cartItemCreate.setOpenTableMustOrder(true);
        } else {
            cartItemCreate.setOpenTableMustOrder(false);
        }
        if (MapUtils.getBoolean(info.getExtraInfo(), com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.EXTRA_KEY_MUST_ORDER_EDITABLE, false)) {
            cartItemCreate.setOpenTableItemEditable(true);
        } else {
            cartItemCreate.setOpenTableItemEditable(false);
        }

        Spec spec = info.getSpecInfo();
        if (spec != null) {
            CartItemCreate.Spec itemSpec = new CartItemCreate.Spec();
            itemSpec.setId(spec.getId());
            itemSpec.setName(spec.getName());
            itemSpec.setPrice(Optional.ofNullable(spec.getPrice()).map(price -> price.intValue()).orElse(0));
            cartItemCreate.setSpec(itemSpec);
        }

        cartItemCreate.setAttributes(
                Optional.ofNullable(info.getAttributeInfos())
                        .map(attributes -> attributes.stream()
                                .map(attribute -> {
                                    CartItemCreate.Attribute itemAttribute = new CartItemCreate.Attribute();
                                    itemAttribute.setId(attribute.getId());
                                    itemAttribute.setTitle(attribute.getTitle());
                                    itemAttribute.setName(attribute.getName());
                                    itemAttribute.setSeq(attribute.getSeq());
                                    return itemAttribute;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        cartItemCreate.setMaterials(
                Optional.ofNullable(info.getMaterials())
                        .map(materials -> materials.stream()
                                .map(dbMaterial -> {
                                    CartItemCreate.Material material = new CartItemCreate.Material();
                                    material.setId(dbMaterial.getId());
                                    material.setName(dbMaterial.getName());
                                    material.setPrice(dbMaterial.getPrice());
                                    material.setNumber(dbMaterial.getNumber());
                                    material.setSource(dbMaterial.getSource());
                                    return material;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );

        CartItemCreate.Item item = new CartItemCreate.Item();
        item.setId(info.getItemId());
        item.setName(info.getName());
        item.setNumber(Math.toIntExact(info.getCount().intValue()));
        item.setNumberDecimal(new BigDecimal(info.getCount()));
        item.setReturnNumber(info.getRefundCount());
        item.setUrl(info.getUrl());
        item.setPhotoUrl(info.getUrl());
        item.setPrice(info.getOriginalAmountPer().intValue());
        item.setTotalAmount(item.getNumberDecimal().multiply(new BigDecimal(info.getOriginalAmountPer())).setScale(0, RoundingMode.HALF_UP).longValue());
        item.setSpuType(Optional.ofNullable(info.getSpuType()).orElse(SpuType.PRODUCT).name());
        item.setCategoryId(info.getCategoryId());
        item.setDiscountNumber(info.getDiscountCount() != null ? info.getDiscountCount().intValue() : 0);
        item.setDiscountPrice(info.getDiscountAmount() != null ? info.getDiscountAmount().intValue() : 0);
        item.setItemTag(info.getGoodsTag());

        if (!isPackageGoods) {
            if (info.getSpuType() == SpuType.PACKAGE && CollectionUtil.isNotEmpty(info.getPackageGoods())) {
                cartItemCreate.setPackageItems(info.getPackageGoods().stream()
                        .map(itemDto -> convertOrderItemDto2CartItemCreate(itemDto, null, true))
                        .collect(Collectors.toList()));
            }
        }


        item.setUnit(info.getSaleUnit());
        if (info.getExtraInfo() != null && Objects.equals(GoodsUnitTypeEnum.WEIGHT.name(), info.getExtraInfo().get(EXTRA_KEY_UNIT_TYPE))) {
            try {
                Object saleWight = info.getExtraInfo().get(EXTRA_KEY_SALE_WEIGHT);
                if (saleWight != null) {
                    double weight = Double.parseDouble(String.valueOf(saleWight));
                    item.setWeight(BigDecimal.valueOf(weight).stripTrailingZeros());
                }
            } catch (Exception e) {
                log.warn("称重商品重量值计算错误", e);
            }
            item.setUnitType(1);
            item.setNumber(1);
        } else {
            item.setUnitType(0);
            item.setNumber(info.getCount().intValue());
        }

        cartItemCreate.setItem(item);

        item.setAttachedInfo(generateAttachInfo(cartItemCreate));

        return cartItemCreate;
    }

    public static CartItemCreate convertOrderItemDto2CartItemCreate(OrderGoodsDTO info, GoodsRefPayType filterRefPayType, Boolean isPackageGoods) {
        if (filterRefPayType != null) {
            if (info.getRefPayType() != filterRefPayType) {
                return null;
            }
        }
        CartItemCreate cartItemCreate = new CartItemCreate();

        if (info.getGoodsDiscountType() == GoodsDiscountType.MANUAL) {
            cartItemCreate.setManualChangePrice(true);
        }
        cartItemCreate.setProcessStatus(info.getProcessStatus());
        cartItemCreate.setOrderTime(Optional.ofNullable(info.getBatchNo()).map(batchNo -> Long.parseLong(batchNo)).orElse(null));
        cartItemCreate.setExtraInfo(info.getExtraInfo());
        cartItemCreate.setItemUid(info.getId());

        if (OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.OPENTABLE_MUST_ORDER.getValue())) {
            cartItemCreate.setOpenTableMustOrder(true);
        } else {
            cartItemCreate.setOpenTableMustOrder(false);
        }

        if (MapUtils.getBoolean(info.getExtraInfo(), com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.EXTRA_KEY_MUST_ORDER_EDITABLE, false)) {
            cartItemCreate.setOpenTableItemEditable(true);
        } else {
            cartItemCreate.setOpenTableItemEditable(false);
        }


        if (!StringUtils.isEmpty(info.getSkuId()) && !StringUtils.isEmpty(info.getSkuTitle())) {
            CartItemCreate.Spec itemSpec = new CartItemCreate.Spec();
            itemSpec.setId(info.getSkuId());
            itemSpec.setName(info.getSkuTitle());
            itemSpec.setPrice(info.getOriginSalePrice().intValue());
            cartItemCreate.setSpec(itemSpec);
        }

        cartItemCreate.setAttributes(
                Optional.ofNullable(info.getAttributeInfos())
                        .map(attributes -> attributes.stream()
                                .map(attribute -> {
                                    CartItemCreate.Attribute itemAttribute = new CartItemCreate.Attribute();
                                    itemAttribute.setId(attribute.getId());
                                    itemAttribute.setName(attribute.getName());
                                    return itemAttribute;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        cartItemCreate.setMaterials(
                Optional.ofNullable(info.getMaterials())
                        .map(materials -> materials.stream()
                                .map(dbMaterial -> {
                                    CartItemCreate.Material material = new CartItemCreate.Material();
                                    material.setId(dbMaterial.getId());
                                    material.setName(dbMaterial.getName());
                                    material.setPrice(dbMaterial.getPrice());
                                    material.setNumber(dbMaterial.getNumber());
                                    material.setSource(dbMaterial.getSource());
                                    return material;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );

        CartItemCreate.Item item = new CartItemCreate.Item();
        item.setId(info.getSpuId());
        item.setName(info.getSpuTitle());
        item.setNumber(Math.toIntExact(info.getCount().intValue()));
        item.setNumberDecimal(info.getCount());
        item.setReturnNumber(Optional.ofNullable(info.getRefundCount()).map(BigDecimal::intValue).orElse(null));
        item.setUrl(info.getMainImageUrl());
        item.setPhotoUrl(info.getMainImageUrl());
        item.setPrice(info.getOriginSalePrice().intValue());
        item.setTotalAmount(item.getNumberDecimal().subtract(Optional.ofNullable(info.getRefundCount()).orElse(BigDecimal.ZERO)).multiply(new BigDecimal(info.getOriginSalePrice())).setScale(0, RoundingMode.HALF_UP).longValue());
        item.setSpuType(Optional.ofNullable(info.getSpuType()).orElse(SpuType.PRODUCT).name());
        item.setCategoryId(info.getCategoryId());
        item.setDiscountNumber(info.getDiscountCount() != null ? info.getDiscountCount().intValue() : 0);
        item.setDiscountPrice(info.getDiscountAmount() != null ? info.getDiscountAmount().intValue() : 0);
        item.setItemTag(info.getGoodsTag());

        if (!isPackageGoods) {
            if (info.getSpuType() == SpuType.PACKAGE && CollectionUtil.isNotEmpty(info.getPackageGoods())) {
                cartItemCreate.setPackageItems(info.getPackageGoods().stream()
                        .map(itemDto -> convertOrderItemDto2CartItemCreate(itemDto, null, true))
                        .collect(Collectors.toList()));
            }
        }


        item.setUnit(info.getSaleUnit());
        if (info.getExtraInfo() != null && Objects.equals(GoodsUnitTypeEnum.WEIGHT.name(), info.getExtraInfo().get(EXTRA_KEY_UNIT_TYPE))) {
            try {
                Object saleWight = info.getExtraInfo().get(EXTRA_KEY_SALE_WEIGHT);
                if (saleWight != null) {
                    double weight = Double.parseDouble(String.valueOf(saleWight));
                    item.setWeight(BigDecimal.valueOf(weight).stripTrailingZeros());
                }
            } catch (Exception e) {
                log.warn("称重商品重量值计算错误", e);
            }
            item.setUnitType(1);
            item.setNumber(1);
        } else {
            item.setUnitType(0);
            item.setNumber(info.getCount().intValue());
        }


        if (OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.SINGLE_DISCOUNT.getValue())
                || OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.SECOND_DISCOUNT.getValue())
                || MoneyUtil.getNotNullAmount(info.getDiscountAmount()) > 0) {
            // 如果是前置验券加购的商品，不展示折
            if (!OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.MEITUAN_GROUP_COUPON.getValue())
                    && !OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.DOUYIN_GROUP_COUPON.getValue())
                    && !OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.JINGDONG_GROUP_COUPON.getValue())
                    && !OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.PIAOFUTONG_GROUP_COUPON.getValue())) {
                cartItemCreate.setTagName("折");
            }
        }

        if (OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.VIP_PRICE_GOOOS.getValue())) {
            cartItemCreate.setTagName("会");
        }

        if (OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.GOODS_COUPON.getValue())) {
            cartItemCreate.setTagName("抵");
        }

        if (OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.GIFT_FOOD.getValue())) {
            cartItemCreate.setGiftFood(true);
            cartItemCreate.setTagName("赠");
        }

        if (info.getProcessStatus() == GoodsProcessStatus.REFUNDED
                || info.getProcessStatus() == GoodsProcessStatus.RETURNED) {
            cartItemCreate.setTagName("退");
        }

        cartItemCreate.setActivityName(MapUtils.getString(info.getExtraInfo(), com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.EXTRA_INFO_ITEM_ACTIVITY_NAME, null));


        cartItemCreate.setItem(item);

        item.setAttachedInfo(generateAttachInfo(cartItemCreate));

        return cartItemCreate;
    }


    private static String getProcessStatusForShow(OrderProcessStatusEnum processStatus, OrderStatus orderStatus) {
        if (processStatus == null) {
            return null;
        }
        if (processStatus != OrderProcessStatusEnum.COMPLETED) {
            return processStatus.getCode();
        }

        if (orderStatus == OrderStatus.REFUNDED || orderStatus == OrderStatus.PARTIAL_REFUNDED) {
            //结账状态下，如果是退款，那么显示退款
            return "REFUNDED";
        }

        return processStatus.getCode();
    }

    public static String generateAttachInfo(CartItemCreate create) {
        return generateAttachInfo(create, false);
    }

    public static String generateAttachInfo(ShoppingCartGoodsDTO goods, boolean isPackageSubGoods) {
        if (!isPackageSubGoods && Objects.equals(goods.getSpuType(), SpuType.PACKAGE)) {

            List<ShoppingCartGoodsDTO> packageGoods = goods.getPackageGoods();
            if (CollectionUtils.isEmpty(packageGoods)) {
                return "";
            }

            packageGoods = CartHelper.mergeSameGoodsCartDTO(packageGoods);

            List<String> packageAttachInfoList = packageGoods.stream()
                    .map(i -> {
                        String packageGoodsAttachInfo = generateAttachInfo(i, true);

                        if (StringUtils.isEmpty(packageGoodsAttachInfo)) {
                            return i.getSpuTitle() + CART_NUM_UNIT + i.getSaleCount();
                        } else {
                            return i.getSpuTitle() + "(" + packageGoodsAttachInfo + ")" + CART_NUM_UNIT + i.getSaleCount();
                        }


                    })
                    .collect(Collectors.toList());

            String attachInfo = String.join("+", packageAttachInfoList);
            goods.setAttachedInfo(attachInfo);
            return attachInfo;
        }

        List<String> attachInfoList = new ArrayList<>();
        String skuTitle = goods.getSkuTitle();
        List<Attribute> attributes = goods.getRecipes();
        List<Material> materials = goods.getMaterials();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(skuTitle)) {
            attachInfoList.add(skuTitle);
        }

        if (!CollectionUtils.isEmpty(attributes)) {
            attributes.forEach(attribute -> {
                attachInfoList.add(attribute.getName());
            });
        }

        if (!CollectionUtils.isEmpty(materials)) {
            materials.forEach(material -> {
                String materialInfo = material.getName();
                if (material.getNumber() > 1) {
                    materialInfo = materialInfo + CART_NUM_UNIT + material.getNumber();
                }
                attachInfoList.add(materialInfo);
            });
        }

        String attachInfo = String.join(",", attachInfoList);
        goods.setAttachedInfo(attachInfo);
        return attachInfo;
    }

    /**
     * @param create            商品信息
     * @param isPackageSubGoods 是否是套餐内的商品
     * @return
     */
    public static String generateAttachInfo(CartItemCreate create, boolean isPackageSubGoods) {
        List<String> attachInfoList = new ArrayList<>();
        CartItemCreate.Spec spec = create.getSpec();
        List<CartItemCreate.Attribute> attributes = create.getAttributes();
        List<CartItemCreate.Material> materials = create.getMaterials();


        if (!isPackageSubGoods && Objects.equals(create.getItem().getSpuType(), SpuType.PACKAGE.name())) {

            List<CartItemCreate> cartItemCreateList = CartHelper.mergeSameGoodsCartItem(create.getPackageItems());

            List<String> packageAttachInfoList = cartItemCreateList.stream()
                    .map(i -> {

                        String packageGoodsAttachInfo = generateAttachInfo(i, true);

                        if (StringUtils.isEmpty(packageGoodsAttachInfo)) {
                            return i.getItem().getName() + CART_NUM_UNIT + i.getItem().getNumber();
                        } else {
                            return i.getItem().getName() + "(" + packageGoodsAttachInfo + ")" + CART_NUM_UNIT + i.getItem().getNumber();
                        }


                    })
                    .collect(Collectors.toList());

            String attachInfo = String.join("+", packageAttachInfoList);
            create.getItem().setAttachedInfoWithoutMaterials(attachInfo);
            create.getItem().setAttachedInfo(attachInfo);
            create.setPackageItems(cartItemCreateList);
            return attachInfo;
        }

        if (spec != null && !StringUtils.isEmpty(spec.getName())) {
            attachInfoList.add(spec.getName());
        }

        if (!CollectionUtils.isEmpty(attributes)) {
            attributes.forEach(attribute -> {
                attachInfoList.add(attribute.getName());
            });
        }

        String attachInfoWithoutMaterials = String.join(",", attachInfoList);

        if (!CollectionUtils.isEmpty(materials)) {
            materials.forEach(material -> {
                String materialInfo = material.getName();

                if (material.getNumber() != null && material.getNumber() > 1) {
                    materialInfo = materialInfo + CART_NUM_UNIT + material.getNumber();
                }
                attachInfoList.add(materialInfo);
            });
        }

        String attachInfo = String.join(",", attachInfoList);
        create.getItem().setAttachedInfo(attachInfo);
        create.getItem().setAttachedInfoWithoutMaterials(attachInfoWithoutMaterials);
        return attachInfo;
    }

    /**
     * 获取支付信息
     *
     * @param orderPaysDTOS
     * @return
     */
    private static List<PayChannelInfo> getPayChannelListPrintData(List<OrderPaysDTO> orderPaysDTOS) {
        List<PayChannelInfo> payChannelList = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderPaysDTOS)) {
            return payChannelList;
        }
        List<OrderPaysDTO> payTradeOrderList = orderPaysDTOS.stream()
                .filter(orderPaysDTO -> orderPaysDTO.getPayType() == PaysTypeEnum.PAYMENT || orderPaysDTO.getPayType() == PaysTypeEnum.RECHARGE_PAYMENT)
                .filter(orderPaysDTO -> orderPaysDTO.getOrderPayStatus() == OrderPayStatusEnum.PAID || orderPaysDTO.getOrderPayStatus() == OrderPayStatusEnum.REFUNDED || orderPaysDTO.getOrderPayStatus() == OrderPayStatusEnum.PARTIAL_REFUNDED)
                .collect(Collectors.toList());
        payTradeOrderList.forEach(orderPaysDTO -> {
            PayChannelInfo payChannelInfo = new PayChannelInfo();
            // TODO 未来orderPaysDto不再返回payway，返回paywayint值，需要手动将paywayInt转成payway对应的文案
            // TODO 废弃payway枚举后，需要手动将paywayInt转成payway对应的支付方式文案，本期暂时采用转换payway来获取文案的方式
            if (Objects.isNull(orderPaysDTO.getPayway())) {
                if (Objects.nonNull(orderPaysDTO.getPaywayInt())) {
                    PayWay payWay = PayWay.getByCode(orderPaysDTO.getPaywayInt());
                    if (Objects.nonNull(payWay)) {
                        orderPaysDTO.setPayway(payWay);
                    }
                }
            }
            if (Objects.nonNull(orderPaysDTO.getPayway())) {
                payChannelInfo.setPayChannelName(orderPaysDTO.getPayway().getDesc());
            } else {
                payChannelInfo.setPayChannelName("在线支付");
            }
            payChannelInfo.setReceiveAmount(orderPaysDTO.getReceiveAmount());
            payChannelInfo.setRefundAmount(orderPaysDTO.getRefundAmount());
            payChannelList.add(payChannelInfo);
        });
        return payChannelList;
    }

    /**
     * 获取支付信息中的会员相关信息
     *
     * @param orderPaysDTOS
     * @param orderDTO
     * @return
     */
    private static List<MembershipPayInfoVO> getMembershipListPrintData(List<OrderPaysDTO> orderPaysDTOS, OrderDTO orderDTO) {
        List<MembershipPayInfoVO> membershipList = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderPaysDTOS)) {
            return membershipList;
        }

        List<OrderPaysDTO> cardPayList = orderPaysDTOS.stream()
                .filter(orderPaysDTO -> orderPaysDTO.getPayType() == PaysTypeEnum.PAYMENT || orderPaysDTO.getPayType() == PaysTypeEnum.RECHARGE_PAYMENT)
                .filter(orderPaysDTO -> orderPaysDTO.getOrderPayStatus() == OrderPayStatusEnum.PAID || orderPaysDTO.getOrderPayStatus() == OrderPayStatusEnum.REFUNDED || orderPaysDTO.getOrderPayStatus() == OrderPayStatusEnum.PARTIAL_REFUNDED)
                .filter(orderPaysDTO -> orderPaysDTO.getPayway() == PayWay.CARD || orderPaysDTO.getPayway() == PayWay.GIFT_CARD)
                .collect(Collectors.toList());

        for (OrderPaysDTO orderPaysDTO : cardPayList) {
            Map<String, String> extraMap = orderPaysDTO.getExtraMap();
            if (null == extraMap) {
                continue;
            }
            String membershipBalance = extraMap.get(Constants.MEMBERSHIP_BALANCE);
            String membershipCellphone = extraMap.get(Constants.MEMBERSHIP_CELLPHONE);
            if (org.apache.commons.lang3.StringUtils.isBlank(membershipCellphone)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(membershipBalance)) {
                membershipBalance = "0";
            }
            MembershipPayInfoVO membershipPayInfo = new MembershipPayInfoVO();
            membershipPayInfo.setMembershipPayTime(DateUtil.getTimeMillis(orderPaysDTO.getPayTime()));
            membershipPayInfo.setMembershipPayAmount(MoneyUtil.getNotNullAmount(orderPaysDTO.getBuyerPayAmount()));
            membershipPayInfo.setMembershipCellphone(membershipCellphone);
            membershipPayInfo.setMembershipBalance(Long.parseLong(membershipBalance));
            membershipPayInfo.setOperatorName(orderDTO.getCashierName());
            membershipList.add(membershipPayInfo);
        }
        return membershipList;
    }

    /**
     * 订单相同类型的优惠信息合并
     *
     * @param originRedeems
     * @return
     */
    public static List<OrderRedeemDTO> mergeRedeems(List<OrderRedeemDTO> originRedeems) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(originRedeems)) {
            return originRedeems;
        }
        //按照产品需求， 这里需要进行一次合并
        Map<Integer, List<OrderRedeemDTO>> redeemMap = originRedeems.stream().collect(Collectors.groupingBy(OrderRedeemDTO::getDiscountType));

        return redeemMap.entrySet().stream().map(entry -> {
            List<OrderRedeemDTO> redeems = entry.getValue();
            if (redeems.size() == 1) {
                return redeems.get(0);
            }
            OrderRedeemDTO redeem = redeems.get(0);
            if (Objects.equals(redeem.getDiscountType(), OrderRedeemDiscountType.SECOND_ACTIVITY.getType())) {
                //第二份优惠, 需要去掉原来的"第二份XX折"， 改为"第二份优惠"
                redeem.setName("第二份优惠");
            }

            Long totalDiscountAmount = 0L;

            for (OrderRedeemDTO item : redeems) {
                totalDiscountAmount += MoneyUtil.getNotNullAmount(item.getDiscountAmount());
            }
            redeem.setDiscountAmount(totalDiscountAmount);

            return redeem;
        }).collect(Collectors.toList());
    }
}
