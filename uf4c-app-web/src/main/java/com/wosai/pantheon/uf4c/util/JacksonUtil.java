package com.wosai.pantheon.uf4c.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.wosai.data.jackson.RowDeserializerInstantiator;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Map;

/**
 * created by shij on 2019/4/8
 */
@Slf4j
public class JacksonUtil {

    private static final ObjectMapper mapper = createMapper();

    public static String toJsonString(final Object data) {
        Writer strWriter = new StringWriter();
        try {
            mapper.writeValue(strWriter, data);
            strWriter.flush();
            strWriter.close();
        } catch (IOException e) {
            log.error("", e);
        }

        return strWriter.toString();
    }

    public static <T> T toBean(final String jsonString, Class<T> clazz) {
        try {
            return mapper.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("", e);
            return null;
        }
    }

    public static <T> T toBean(final String jsonString, TypeReference<T> typeReference) {
        try {
            return mapper.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("", e);
            return null;
        }
    }

    public static <T> T convert(Object params, TypeReference<T> typeReference) {
        try {
            return mapper.convertValue(params, typeReference);
        } catch (IllegalArgumentException e) {
            log.error("", e);
            return null;
        }
    }

    public static <T> T convert(Object params, Class<T> clazz) {
        try {
            return mapper.convertValue(params, clazz);
        } catch (IllegalArgumentException e) {
            log.error("", e);
            return null;
        }
    }

    // 将对象转成字符串
    public static String objectToString(Object obj, ObjectMapper objectMapper) throws Exception {
        if (objectMapper == null) {
            objectMapper = mapper;
        }
        return objectMapper.writeValueAsString(obj);
    }


    // 将Bean转成Map
    public static Map beanToMap(Object obj, ObjectMapper objectMapper) throws Exception {
        if (objectMapper == null) {
            objectMapper = mapper;
        }
        return objectMapper.readValue(objectToString(obj, objectMapper), Map.class);
    }

    public static ObjectMapper createMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.setHandlerInstantiator(new RowDeserializerInstantiator());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        return objectMapper;
    }

}
