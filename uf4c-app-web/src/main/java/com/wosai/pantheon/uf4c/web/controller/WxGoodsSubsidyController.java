package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.pantheon.uf4c.model.ItemFind;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.util.Map;

@RestController
@Slf4j
@RequestMapping(path = "/api/v1/wxgoods", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class WxGoodsSubsidyController {


//    @Autowired
//    private WxGoodsHelper wxGoodsHelper;

    /**
     * 查询门店的微信加价购活动信息
     * @return 微信侧门店ID,微信侧商户ID，活动IDs
     */
    @PostMapping(value = "/wxacts")
    public Map findWxGoodsSubsidy(@RequestBody ItemFind find){
//        String storeId = find.getStoreId();
//        if (StringUtils.isEmpty(storeId)){
//            return null;
//        }
//        return wxGoodsHelper.getStoreActs(storeId);
        return null;
    }


}
