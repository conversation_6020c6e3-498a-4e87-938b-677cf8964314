package com.wosai.pantheon.uf4c.web.controller.v3;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.market.tethys.api.dto.request.storedCard.AchieveRedeemCodeRequest;
import com.wosai.market.tethys.api.dto.response.storedCardResponse.RedeemCode;
import com.wosai.market.tethys.api.service.StoredCardRemoteService;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.market.trade.modal.constant.MarketTradeConstant;
import com.wosai.market.trade.service.PayService;
import com.wosai.pantheon.order.enums.OrderSource;
import com.wosai.pantheon.order.enums.OrderStatus;
import com.wosai.pantheon.order.enums.OrderTagEnum;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.order.model.dto.OrderDTO;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.fallbackconfig.server.BlockHandleServer;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.*;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV2;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV3;
import com.wosai.pantheon.uf4c.util.*;
import com.wosai.pantheon.uf4c.web.exception.BaseException;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.oms.api.enums.ErrorTipWayEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.pojo.OrderMainWrapper;
import com.wosai.smartbiz.oms.api.services.OrderRpcService;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import com.wosai.smartbiz.payment.api.trade.defs.SubPayWay;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.core.model.Terminal;
import com.wosai.web.api.PaginatedResult;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.wosai.pantheon.uf4c.constant.OrderConstant.SN;
import static com.wosai.pantheon.uf4c.constant.OrderConstant.TRANS_SN;
import static com.wosai.upay.activity.model.ActivityOrder.SPECIFIC_GOODS_CARD_LIST;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * <AUTHOR>
 * @since 2020/5/6
 */
@RestController
@RequestMapping(path = "/api/v3/orders", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
@RequiredArgsConstructor
public class OrderControllerV3 {

    public static final String SUCCESS = "success";

    public static final String FAIL = "fail";

    @Autowired
    private OrderServiceV3 orderServiceV3;

    @Autowired
    private OrderService orderService;

    @Autowired
    private PrePayUtil prePayUtil;

    @Autowired
    private OrderRpcService orderRpcService;

    @Autowired
    private StoredCardRemoteService storedCardRemoteService;

    @Autowired
    private OrderServiceV2 orderServiceV2;

    @Autowired
    private PayService payService;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @PostMapping("/pay")
    @ResponseBody
    @SneakyThrows
    public PayResult pay(@RequestBody PayRequest request) {
        return payTemporary(request);
    }

    /**
     * 2024-03-06
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/OrderControllerV3/pay", blockHandlerClass = BlockHandleServer.class, blockHandler = "handlePay")
    private PayResult payTemporary(PayRequest request) {
        PayResult payResult = new PayResult();
        try {
            //设置需要预扣库存
            request.setCheckCartItem(true);
            payResult = orderServiceV2.pay(new ApiRequest<>(request));
        } catch (BaseException ex) {
            log.warn("OrderControllerV3.pay BaseException, storeId{}", request.getStoreId(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(ErrorTipWayEnum.POPUP)
                    .errorCode(ex.getCode())
                    .errorMsg(ex.getMessage())
                    .build();

            payResult.setCartCheckResult(cartCheckResult);
        } catch (Exception ex) {
            log.warn("OrderControllerV3.pay Exception, storeId{}", request.getStoreId(), ex);
            CartCheckResultDTO cartCheckResult = CartCheckResultDTO.builder()
                    .success(false)
                    .errorTipWay(ErrorTipWayEnum.POPUP)
                    .errorCode(ReturnCode.SYSTEM_EXCEPTION.getCode())
                    .errorMsg(ex.getMessage())
                    .build();

            payResult.setCartCheckResult(cartCheckResult);
        }
        return payResult;
    }

    @PostMapping("/find")
    @ResponseBody
    public PaginatedResult<Order> findOrders(@RequestBody OrderFind request) {
        return orderServiceV3.findOrders(new ApiRequest<>(request));
    }

    @PostMapping(value = "/recharge/notify", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String balancePayAfterRecharge(@RequestBody Map map) throws IOException {
        log.info("receive recharge notify，arguments:{}", JSON.toJSONString(map), keyValue("uri", "/api/v3/orders/recharge/notify"));

        //这里要清理下可能干扰的用户信息
        ThreadLocalHelper.reset();

        String sn = MapUtil.getString(map, "client_sn");
        OrderDTO orderDTO = orderService.getOrderByClientSn(sn);
        if (orderDTO == null) {
            log.info("receive recharge notify, order not exist", keyValue("sn", sn));
            return SUCCESS;
        }
        if (!TagUtil.hasTag(orderDTO.getOrderTag(), OrderTagEnum.RECHARGE_AND_PAY.getValue())) {
            //不是充值并支付订单
            log.info("receive recharge notify, 不是充值订单", keyValue("sn", sn));
            return SUCCESS;
        }
        // if (!(OrderStatus.CREATED == orderDTO.getStatus() || OrderStatus.PAY_CANCELED == orderDTO.getStatus()
        //         || OrderStatus.CANCELED == orderDTO.getStatus() || OrderStatus.ACCEPTED == orderDTO.getStatus())) {
        //     //订单状态不对
        //     log.info("receive recharge notify, 订单状态不对, 当前状态：{}", orderDTO.getStatus(), keyValue("sn", sn));
        //     return SUCCESS;
        // }

        // 在储值并支付的时候,会将营销的优惠透传参数 "mkCustomInfo" 放到支付记录的 extraInfo 中,这时候我们解析出来还传过去
        Map<Object, Object> mkCustomInfo = Optional.ofNullable(orderDTO.getOrderPays())
                .orElse(Collections.emptyList())
                .stream()
                .filter(orderPay -> sn.equals(orderPay.getClientSn()))
                .findFirst()
                .map(orderPay -> {
                    try {
                        Map<Object, Object> tempMkCustomInfo = JSON.parseObject(MapUtils.getString(orderPay.getExtraMap(), "mkCustomInfo"),
                                new TypeReference<Map<Object, Object>>() {
                                });
                        /*
                         * 储值并支付时,会发放一个临时的抵用券,营销那边使用 mock 开头来标注的,在储值之后变成真正发放的,mock 需要被替换为 ----,我们后续使用这个才能获得正确的优惠
                         */
                        Object o = MapUtils.getObject(tempMkCustomInfo, SPECIFIC_GOODS_CARD_LIST);
                        if (o instanceof JSONArray) {
                            JSONArray replacedCardIds = new JSONArray();
                            JSONArray originalCardIds = (JSONArray) o;
                            for (Object object : originalCardIds) {
                                if (object instanceof String) {
                                    String originalCardId = (String) object;
                                    if (StringUtils.isNotBlank(originalCardId) && originalCardId.startsWith("mock")) {
                                        originalCardId = originalCardId.replace("mock", "----");
                                    }
                                    replacedCardIds.add(originalCardId);
                                }
                            }
                            tempMkCustomInfo.put(SPECIFIC_GOODS_CARD_LIST, replacedCardIds);
                        }
                        return tempMkCustomInfo;
                    } catch (Exception e) {
                        LogUtils.logWarn("balancePayAfterRecharge error", "balancePayAfterRecharge", orderDTO.getSn(), e);
                        return null;
                    }
                })
                .orElse(null);

        //储值并支付，存储下支付场景值
        String sqbPaySource =  Optional.ofNullable(orderDTO.getOrderPays())
                .orElse(Collections.emptyList())
                .stream()
                .filter(orderPay -> sn.equals(orderPay.getClientSn()))
                .findFirst()
                .map(orderPay -> MapUtils.getString(orderPay.getExtraMap(), Constants.SQB_PAY_SOURCE))
                .orElse(null);

        Map<String, String> payHeaders = new HashMap<>();
        apolloConfigHelper.getPayHeaderConfigMap().forEach((key, value)->{
                if(StringUtils.isNotEmpty(value)){

                        String headerValue =  Optional.ofNullable(orderDTO.getOrderPays())
                            .orElse(Collections.emptyList())
                            .stream()
                            .filter(orderPay -> sn.equals(orderPay.getClientSn()))
                            .findFirst()
                            .map(orderPay -> MapUtils.getString(orderPay.getExtraMap(), value))
                            .orElse(null);

                        if(StringUtils.isNotEmpty(headerValue)){
                            payHeaders.put(value, headerValue);
                        }
                    }
                }
        );

        if (orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER || orderDTO.getOrderType() == OrderType.PAY_FIRST_TABLE_ORDER) {
            //围餐
            com.wosai.smartbiz.oms.api.query.PayRequest payRequest = new com.wosai.smartbiz.oms.api.query.PayRequest();
            payRequest.setOrderNo(orderDTO.getSn());
            payRequest.setUserId(orderDTO.getUserId());
            payRequest.setOrderSource(OrderSource.MINI);
            payRequest.setIsRoundMeal(true);

//            Map tradeTerminal = prePayUtil.getTerminal(null, orderDTO.getStoreId());
            Map tradeTerminal = payService.getTerminal(orderDTO.getStoreId(), null);
            payRequest.setTerminalSn(BeanUtil.getPropString(tradeTerminal, Terminal.SN));
            payRequest.setTerminalId(BeanUtil.getPropString(tradeTerminal, DaoConstants.ID));
            payRequest.setTerminalKey(BeanUtil.getPropString(tradeTerminal, Terminal.CURRENT_SECRET));

            Map extraMap = new HashMap();

//            extraMap.put("terminalDiscountType", MapUtils.getString(tradeTerminal,"terminalDiscountType",null));
            extraMap.put(Constants.SQB_PAY_SOURCE, sqbPaySource);
            extraMap.put(Constants.PAY_HEADERS, payHeaders);
            payRequest.setExtraInfo(extraMap);
            payRequest.setMkCustomInfo(mkCustomInfo);

            try {
                AchieveRedeemCodeRequest achieveRedeemCodeRequest = new AchieveRedeemCodeRequest();
                achieveRedeemCodeRequest.setChannelUserId(MapUtil.getString(orderDTO.getExtra(), "channelUserId"));
                achieveRedeemCodeRequest.setStoreId(orderDTO.getStoreId());
                achieveRedeemCodeRequest.setUserId(orderDTO.getUserId());
                RedeemCode redeemCode = storedCardRemoteService.achieveRedeemCodeOnlyForCardPayway(achieveRedeemCodeRequest);

                if (redeemCode == null || org.springframework.util.StringUtils.isEmpty(redeemCode.getAuth_code())) {
                    throw new BusinessException(ReturnCode.INVALID_PARAM_EXCEPTION, "无法找到对应的储值卡信息，无法使用储值卡进行支付");
                }
                payRequest.setPayWay(PayWay.CARD);
                payRequest.setBarcode(redeemCode.getAuth_code());
            } catch (Exception ex) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, "无法获取到对应的储值卡信息，无法使用储值卡进行支付");
            }

            Result<OrderMainWrapper> result = orderRpcService.pay(payRequest);

            if (!result.isSuccess()) {
                log.warn("balancePayAfterRecharge error,{}", result.getErrorMsg(), keyValue("sn", orderDTO.getSn()));
                return FAIL;
            }

        } else {
            Order order = EntityConvert.convertOrderDTO(orderDTO);
            order.setPayway(MarketTradeConstant.payWay.CARD.getCode());
            order.setSubPayway(SubPayWay.MINI.getCode());

            PayParamWrapper payParamWrapper = new PayParamWrapper();
            RedeemParamWrapper redeemParamWrapper = new RedeemParamWrapper();
            redeemParamWrapper.setMkCustomInfo(mkCustomInfo);
            payParamWrapper.setSqbPaySource(sqbPaySource);
            payParamWrapper.setPayHeaders(payHeaders);
            prePayUtil.prePay(order, payParamWrapper, redeemParamWrapper);
        }


        return SUCCESS;


    }

    @PostMapping("/latest")
    @ResponseBody
    public Order findLatestOne(@RequestBody OrderFind request) {
        return orderServiceV3.findLatestOne(new ApiRequest<>(request));

    }


    @PostMapping("/getBySn")
    @ResponseBody
    public Order getBySn(@RequestBody @PropNotEmpty.List({
            @PropNotEmpty(value = SN, message = "{value}不能为空")
    }) Map request) {
        return orderServiceV3.getBySn(new ApiRequest<>(request));
    }

    @PostMapping("/getByTransSn")
    @ResponseBody
    public Order getByTransSn(@RequestBody @PropNotEmpty.List({
            @PropNotEmpty(value = TRANS_SN, message = "{value}不能为空")
    }) Map request) {
        return orderServiceV3.getByTransSn(new ApiRequest<>(request));
    }

    @PostMapping("/again")
    @ResponseBody
    public CartCheckResultDTO orderAgain(@RequestParam Map map) {
        return orderServiceV3.orderAgain(ApiRequest.buildGetRequest(map));
    }


    /**
     * 获取订单取餐二维码
     *
     * @param request
     * @return
     */
    @PostMapping("/getPickUpMealsQrCodeBySn")
    @ResponseBody
    public Qrcode getPickUpMealsQrCodeBySn(@RequestBody @PropNotEmpty.List({
            @PropNotEmpty(value = SN, message = "{value}不能为空")
    }) Map request) {
        return orderServiceV3.getPickUpMealsQrCodeBySn(new ApiRequest<>(request));
    }


}
