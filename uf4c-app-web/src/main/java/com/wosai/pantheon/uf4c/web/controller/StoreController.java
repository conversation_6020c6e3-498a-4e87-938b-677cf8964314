package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.pantheon.core.ufood.model.StoreConfig;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.MultiConfigRequest;
import com.wosai.pantheon.uf4c.service.apisix.StoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * created by shij on 2019/4/28
 */
@Controller
@RequestMapping(path = "/api/v1/stores")
public class StoreController {

    @Autowired
    private StoreService storeService;


    @GetMapping("/configs")
    @ResponseBody
    public StoreConfig getStoreConfig(@RequestParam Map queryParam) {

        return storeService.getStoreConfig(ApiRequest.buildGetRequest(queryParam));
    }

    @PostMapping("/multiConfigs")
    @ResponseBody
    public List<ConfigResponse> getStoreConfig(@RequestBody MultiConfigRequest request) {
        return storeService.getMultiStoreConfig(new ApiRequest<>(request));
    }


    @GetMapping("/shareInfo")
    @ResponseBody
    public Map<String, Object> getStoreShareInfo(@RequestParam Map queryParam) {
        return storeService.getStoreShareInfo(ApiRequest.buildGetRequest(queryParam));
    }
}
