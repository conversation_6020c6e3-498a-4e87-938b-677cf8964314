package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.DeliverFeeRequest;
import com.wosai.pantheon.uf4c.model.dto.OrderPrintRequest;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV1;
import com.wosai.pantheon.uf4c.util.TimeSplit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> zhen.pan, xuyuanxiang
 * @since 2019/4/9
 */
@RestController
@RequestMapping(path = "/api/v1/orders", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class OrderController {

    @Autowired
    private OrderServiceV1 orderServiceV1;


    @GetMapping("/packAmount")
    public Long getPackAmount(@RequestParam Map map) {

        return orderServiceV1.getPackAmount(ApiRequest.buildGetRequest(map));
    }

    @PostMapping("/deliveryFee")
    public Map getDeliveryFee(@RequestBody DeliverFeeRequest request) {
        return orderServiceV1.getDeliveryFee(new ApiRequest<>(request));
    }

    @GetMapping("/times")
    public List<TimeSplit.TimeResult> getTimeSplit(@RequestParam Map map) {
        return orderServiceV1.getTimeSplit(ApiRequest.buildGetRequest(map));
    }

    @GetMapping("/bookTime")
    public List<TimeSplit.TimeResult> getBookTimeSplit(@RequestParam Map map) {
        return orderServiceV1.getBookTimeSplit(ApiRequest.buildGetRequest(map));
    }

    @GetMapping("/cashierParams")
    public Map getCashierParams(@RequestParam Map map) {
        return orderServiceV1.getCashierParams(ApiRequest.buildGetRequest(map));
    }


    @PostMapping(value = "/notify/{orderSn}")
    @ResponseBody
    public String notify(@PathVariable String orderSn, @RequestBody Map request) {
        request.put("order_sn", orderSn);

        return orderServiceV1.notify(new ApiRequest<>(request));
    }

    @PostMapping(value = "/printByTemplate")
    @ResponseBody
    public String printByTemplate(@RequestBody OrderPrintRequest request) {
        return orderServiceV1.printByTemplate(new ApiRequest<>(request));
    }

    @Deprecated
    @PostMapping(value = "/print")
    @ResponseBody
    public String print(@RequestBody OrderPrintRequest request) {
        return orderServiceV1.print(new ApiRequest<>(request));
    }

}
