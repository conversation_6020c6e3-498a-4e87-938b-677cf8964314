package com.wosai.pantheon.uf4c.service;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.request.V2.CommonRequest;
import com.wosai.market.response.BaseItemSummary;
import com.wosai.market.response.ItemSummary;
import com.wosai.market.service.StatisticsService;
import com.wosai.pantheon.core.uitem.model.Material;
import com.wosai.pantheon.core.uitem.model.MaterialTuple;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.vo.MaterialVO;
import com.wosai.pantheon.uf4c.model.vo.RecommendMaterial;
import com.wosai.pantheon.uf4c.model.vo.RecommendMaterialTuple;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.util.WeakReferenceCaller;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * <AUTHOR>
 * @create 2021/11/15
 */
@Component
@Slf4j
public class MaterialHelper {

    @Autowired
    private ItemService itemService;

    @Autowired
    private ConfigRemoteService configRemoteService;

//    @Autowired
//    private DataReportService dataReportService;

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private HashOperations<String, String, RecommendMaterial> hashOperations;

    private static final String RECOMMEND_MATERIAL_KEY_PREFIX = "com.uf4c.recommend.material";

    private static final String EMPTY_RECOMMEND_ID = "empty_recommend_id";

    private static final String ERROR_RECOMMEND_ID = "error_recommend_id";

    @PostConstruct
    public void postConstruct() {
        hashOperations = redisTemplate.opsForHash();
    }

    Map<String, List<RecommendMaterial>> recommendMap(String storeId, List<String> itemIds) {
        List<RecommendMaterialTuple> rms = this.recommend(storeId, itemIds);
        return rms.stream().collect(Collectors.toMap(RecommendMaterialTuple::getItemId, RecommendMaterialTuple::getRecommends, (k1, k2) -> k1));
    }


    /**
     * 推荐加料
     *
     * @param storeId
     * @param itemIds
     * @return
     */
    public List<RecommendMaterialTuple> recommend(String storeId, List<String> itemIds) {

        if (apolloConfigHelper.getServiceDegradeConfig().isRecommendMaterialDegrade()) {
            List<RecommendMaterialTuple> list = itemIds.stream()
                    .map(itemId -> new RecommendMaterialTuple(itemId, Lists.newArrayList())).collect(Collectors.toList());
            return list;
        }

        List<RecommendMaterialTuple> resultTuples = ListUtils.defaultIfNull(itemIds, Lists.newArrayList()).stream()
                .map(itemId -> new RecommendMaterialTuple(itemId, Lists.newArrayList()))
                .collect(Collectors.toList());

        // 开关关闭，直接返回
        List<FindConfigByNameRequest> configRequests = Lists.newArrayList(
                MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, MccUtils.MATERIAL_RECOMMEND),
                MccUtils.findConfigByNameRequest(OwnerType.STORE_ID, storeId, MccUtils.MATERIAL_RECOMMEND_METHOD)
        );
        List<ConfigResponse> configResponses = Optional.ofNullable(configRemoteService.batchFindByName(configRequests)).orElseGet(Lists::newArrayList);
        boolean enable = configResponses.stream()
                .filter(p -> StringUtils.equalsIgnoreCase(p.getName(), MccUtils.MATERIAL_RECOMMEND))
                .findFirst()
                .map(m -> StringUtils.equalsIgnoreCase(m.getValue(), MccUtils.SWITCH_ON))
                .orElse(false);
        if (!enable) {
            return resultTuples;
        }

        // 没有加料直接返回
        List<MaterialTuple> tuples = Optional.ofNullable(itemService.listMaterialByItemIds(storeId, itemIds))
                .map(ListResult::getRecords)
                .orElseGet(Lists::newArrayList);
        if (CollectionUtils.isEmpty(tuples)) {
            return resultTuples;
        }
        Map<String, List<Material>> itemIdToMsMap = new HashMap<>(16);
        for (MaterialTuple tuple : tuples) {
            itemIdToMsMap.put(tuple.getItemId(), tuple.getMaterials());
        }

        Integer method = configResponses.stream()
                .filter(p -> StringUtils.equalsIgnoreCase(p.getName(), MccUtils.MATERIAL_RECOMMEND_METHOD))
                .findFirst()
                .map(m -> Integer.valueOf(m.getValue()))
                .orElse(1);

        Map<String, RecommendMaterialTuple> tupleMap = Maps.newHashMap();
        switch (method) {
            // 智能推荐
            case 1:
                tupleMap = this.autoRecommend(storeId, itemIdToMsMap);
                break;
            // 手动推荐
            case 2:
                tupleMap = this.manualRecommend(storeId, itemIdToMsMap);
                break;
            default:
                break;
        }
        for (RecommendMaterialTuple resultTuple : resultTuples) {
            RecommendMaterialTuple tmpTuple = MapUtils.getObject(tupleMap, resultTuple.getItemId(), null);
            if (Objects.nonNull(tmpTuple)) {
                resultTuple.setRecommends(tmpTuple.getRecommends());
            }
        }

        return resultTuples;
    }

    /**
     * 手动推荐
     *
     * @param storeId
     * @param itemIdToMsMap
     * @return
     */
    public Map<String, RecommendMaterialTuple> manualRecommend(String storeId, Map<String, List<Material>> itemIdToMsMap) {
        if (MapUtils.isEmpty(itemIdToMsMap)) {
            return Maps.newHashMap();
        }
        Map<String, RecommendMaterialTuple> tupleMap = Maps.newHashMap();

        for (Map.Entry<String, List<Material>> entry : itemIdToMsMap.entrySet()) {
            List<RecommendMaterial> tmpRms = ListUtils.defaultIfNull(entry.getValue(), Lists.newArrayList()).stream()
                    .filter(m -> BooleanUtils.isTrue(m.getManualRecommend()) && Objects.equals(m.getStatus(), 1))
                    .map(p -> {
                        RecommendMaterial tmpRm = new RecommendMaterial();
                        tmpRm.setId(p.getId());
                        tmpRm.setName(p.getName());
                        tmpRm.setPrice(p.getPrice());
                        tmpRm.setSeq(p.getSeq());
                        tmpRm.setStatus(p.getStatus());
                        return tmpRm;
                    })
                    .sorted(Comparator.comparing(RecommendMaterial::getSeq, Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());
            tupleMap.put(entry.getKey(), new RecommendMaterialTuple(entry.getKey(), tmpRms));
        }

        return tupleMap;
    }

    /**
     * 自动推荐
     *
     * @param storeId
     * @param itemIdToMsMap
     * @return
     */
    public Map<String, RecommendMaterialTuple> autoRecommend(String storeId, Map<String, List<Material>> itemIdToMsMap) {
        if (MapUtils.isEmpty(itemIdToMsMap)) {
            return Maps.newHashMap();
        }
        Map<String, RecommendMaterialTuple> tupleMap = Maps.newHashMap();

        // 检查缓存
        String key = this.genCacheKey(storeId);
        List<RecommendMaterial> rms = Lists.newArrayList();
        boolean emptyCache = hashOperations.hasKey(key, EMPTY_RECOMMEND_ID);

        // 非空缓存，取缓存里的数据
        if (!emptyCache) {
            List<RecommendMaterial> cache = Optional.ofNullable(hashOperations.values(key)).orElseGet(Lists::newArrayList);
            if (hashOperations.hasKey(key, ERROR_RECOMMEND_ID)) {
                cache.clear();
            }
            if (CollectionUtils.isEmpty(cache)) {
                rms = this.aggFromDataReport(storeId);
                this.putCache(storeId, rms);
            } else {
                rms = cache;
            }
        }
        Map<String, RecommendMaterial> mIdToRmMap = rms.stream()
                .collect(Collectors.toMap(MaterialVO::getId, Function.identity(), (k1, k2) -> k1));

        // 返回结果
        for (Map.Entry<String, List<Material>> entry : itemIdToMsMap.entrySet()) {
            List<RecommendMaterial> tmpRms = ListUtils.defaultIfNull(entry.getValue(), Lists.newArrayList()).stream()
                    .map(p -> {
                        if (!Objects.equals(p.getStatus(), 1)) {
                            return null;
                        }
                        RecommendMaterial tmpRm = MapUtils.getObject(mIdToRmMap, p.getId(), null);
                        if (Objects.isNull(tmpRm)) {
                            tmpRm = new RecommendMaterial();
                            tmpRm.setId(p.getId());
                            tmpRm.setSaleCount(BigDecimal.ZERO);
                        }
                        tmpRm.setName(p.getName());
                        tmpRm.setPrice(p.getPrice());
                        tmpRm.setSeq(p.getSeq());
                        tmpRm.setStatus(p.getStatus());
                        return tmpRm;
                    })
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(RecommendMaterial::getSaleCount, Comparator.nullsFirst(BigDecimal::compareTo))
                            .reversed()
                            .thenComparing(RecommendMaterial::getSeq, Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());
            tupleMap.put(entry.getKey(), new RecommendMaterialTuple(entry.getKey(), tmpRms));
        }

        return tupleMap;
    }

    public String genCacheKey(String storeId) {
        return RECOMMEND_MATERIAL_KEY_PREFIX + ":" + storeId;
    }

    public void putCache(String storeId, List<RecommendMaterial> rms) {
        String key = this.genCacheKey(storeId);
        // 返回结果为空，说明没有数据，放置空缓存
        if (CollectionUtils.isEmpty(rms)) {
            RecommendMaterial special = new RecommendMaterial();
            special.setId(EMPTY_RECOMMEND_ID);
            rms.add(special);
        }
        Map<String, RecommendMaterial> rmMap = rms.stream()
                .collect(Collectors.toMap(MaterialVO::getId, Function.identity(), (k1, k2) -> k1));
        hashOperations.putAll(key, rmMap);

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        redisTemplate.expireAt(key, calendar.getTime());
    }

    public List<RecommendMaterial> aggFromDataReport(String storeId) {
        try {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(new Date());
//            String endDate = sdf.format(calendar.getTime());
//            calendar.add(Calendar.DATE, -7);
//            String startDate = sdf.format(calendar.getTime());
//
//            ReportRequest reportRequest = new ReportRequest();
//            reportRequest.setStartDate(startDate);
//            reportRequest.setEndDate(endDate);
//            reportRequest.setStoreIds(Collections.singletonList(storeId));
//            reportRequest.setReportLoadTypes("ITEM_SUMMARY,TIME_CUSTOM_SUMMARY");

//            ReportResponse reportResponse = dataReportService.dataReport(reportRequest);
            CommonRequest commonRequest = CommonRequest.builder().begin(LocalDateTime.now().plusDays(-7)).end(LocalDateTime.now()).storeSet(Sets.newHashSet(storeId)).build();
            ItemSummary itemSummary = WeakReferenceCaller.call(() -> statisticsService.materialStatistics(commonRequest));
            List<BaseItemSummary> materialSummaries = Optional.ofNullable(itemSummary)
                    .map(ItemSummary::getMaterialSummary)
                    .orElseGet(Lists::newArrayList);

            return materialSummaries.stream()
                    .map(p -> {
                        RecommendMaterial tmp = new RecommendMaterial();
                        tmp.setId(p.getItemId());
                        tmp.setSaleCount(BigDecimal.valueOf(Optional.ofNullable(p.getCount()).orElse(0L)));
                        return tmp;
                    })
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            log.warn("item summary report rpc service error", keyValue("storeId", storeId), ex);
            RecommendMaterial errorRecord = new RecommendMaterial();
            errorRecord.setId(ERROR_RECOMMEND_ID);
            return Lists.newArrayList(errorRecord);
        }
    }

}
