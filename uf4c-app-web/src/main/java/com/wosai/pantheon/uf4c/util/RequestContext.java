package com.wosai.pantheon.uf4c.util;

import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Accessors(chain = true)
@Data
public class RequestContext {
    private UserContextDTO userContextDTO;
    private MiniProgramType miniProgramType;
    private String scene;
    private String realIp;
    private String externalSource;
    private String sqbPaySource;
    private Map payHeaders;

    private String appid;

    public RequestContext() {
    }
}
