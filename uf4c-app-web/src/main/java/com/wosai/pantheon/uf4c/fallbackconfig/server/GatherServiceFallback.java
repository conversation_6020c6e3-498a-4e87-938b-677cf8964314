package com.wosai.pantheon.uf4c.fallbackconfig.server;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCMethodFallbackHandler;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.pantheon.uf4c.api.DegradeTestService;
import com.wosai.pantheon.uf4c.service.apisix.GatherService;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;

import java.lang.reflect.Method;
import java.util.HashMap;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;
import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.named;
import static com.wosai.pantheon.uf4c.web.exception.ReturnCode.BLOCK_FALLBACK_ERROR;

public class GatherServiceFallback extends JsonRPCFallbackDefine {
    private static ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public JsonRPCMethodFallbackHandler[] getJsonRPCMethodFallbackHandlers() {
        return new JsonRPCMethodFallbackHandler[]{
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("orderExtra");
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        return OBJECT_MAPPER.valueToTree(new HashMap<>());
                    }
                },
                new JsonRPCMethodFallbackHandler() {
                    @Override
                    public ElementMatcher<NamedElement.MethodElement> getMethodsMatcher() {
                        return named("orderMain");
                    }

                    @Override
                    public Object handleMethodBlockException(BlockException exception, Method method, Object[] args) {
                        throw new BusinessException(BLOCK_FALLBACK_ERROR);
                    }
                },
                ElementMatchers::any
        };
    }

    public static void main(String[] args) {
        JsonNode jsonNode = OBJECT_MAPPER.valueToTree(new HashMap<>());
        System.out.println(jsonNode);
    }

    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(GatherService.class);
    }

    @Override
    public Provider getProvider() {
        return Provider.SERVER;
    }
}
