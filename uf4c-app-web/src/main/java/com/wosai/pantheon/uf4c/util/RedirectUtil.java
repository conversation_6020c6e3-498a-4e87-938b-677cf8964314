package com.wosai.pantheon.uf4c.util;

import com.wosai.pantheon.uf4c.constant.Properties;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class RedirectUtil {

    public static String buildAlipayAuthUrl(final String appId,
                                            final String redirectUrl,
                                            final String state) {
        return String.format(Properties.ALIPAY_AUTH_URL, appId, encodeURIComponent(redirectUrl), state);
    }

    public static String buildWeixinPlatformAuthUrl(
        final String platform,
        final String appId,
        final String redirectUri,
        final String qrCodeId) {
        return UriComponentsBuilder.fromUriString(platform)
                                   .queryParam("appid", appId)
                                   .queryParam("redirect_user_uri", redirectUri)
                                   .queryParam("state", qrCodeId)
                                   .queryParam("scope", "snsapi_userinfo")
                                   .toUriString();
    }

    public static String buildWeixinAuthUrl(final String appId, final String redirectUrl, final String state) {
        return String.format(Properties.WEIXIN_AUTH_URL, appId, encodeURIComponent(redirectUrl), state);
    }

    public static String encodeURIComponent(String component) {
        try {
            return URLEncoder.encode(component, "UTF-8")
                             .replaceAll("\\%28", "(")
                             .replaceAll("\\%29", ")")
                             .replaceAll("\\+", "%20")
                             .replaceAll("\\%27", "'")
                             .replaceAll("\\%21", "!")
                             .replaceAll("\\%7E", "~");
        } catch (UnsupportedEncodingException e) {
            return component;
        }
    }
}
