package com.wosai.pantheon.uf4c.web.exception;

public class BaseException extends RuntimeException{
    private String code = ReturnCode.BUSINESS_ERROR.getCode();
    public BaseException() {
        super();
    }

    public BaseException(String message) {
        super(message);
    }

    public BaseException(String code, String message) {
        super(message);
        this.code = code;
    }

    public BaseException(ReturnCode returnCode) {
        super(returnCode.getMessage());
        this.code = returnCode.getCode();
    }

    public BaseException(ReturnCode returnCode, String message) {
        super(message);
        this.code = returnCode.getCode();
    }

    public BaseException(ReturnCode returnCode, Throwable cause) {
        super(cause);
        this.code = returnCode.getCode();
    }

    public String getCode() {
        return code;
    }
}
