package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.market.trade.modal.upayDelivery.RiderLocation;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.service.apisix.DeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/24
 */
@RestController
@RequestMapping(path = "/api/v1/delivery", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class DeliveryController {

    @Autowired
    private DeliveryService deliveryService;

    @GetMapping("/rider/location")
    @ResponseBody
    public RiderLocation getRiderLocation(@RequestParam Map map) {
        return deliveryService.getRiderLocation(ApiRequest.buildGetRequest(map));
    }
}
