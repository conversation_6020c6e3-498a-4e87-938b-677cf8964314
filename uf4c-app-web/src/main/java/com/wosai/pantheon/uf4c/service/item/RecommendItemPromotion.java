package com.wosai.pantheon.uf4c.service.item;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/6
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecommendItemPromotion implements Serializable {

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 商品列表
     */
    private List<ItemDetailVO> items;

}
