package com.wosai.pantheon.uf4c.model.item;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/6/12
 */
@Data
public class RetailItemCursorQueryReq implements Serializable {

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 服务类型
     */
    private Integer serviceType;

    /**
     * 当前页数
     */
    private Integer page;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 加载当前页的游标
     */
    private String cursor;

    /**
     * 分类ID
     */
    private List<String> categoryIds;

    /**
     * 搜索关键词
     */
    private String searchWord;

}
