package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.mcc.api.dto.request.UidContentRequest;
import com.wosai.market.mcc.api.service.UidRemoteService;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.order.model.dto.OrderDTO;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.model.dto.LoveQrcodeRequest;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import com.wosai.pantheon.uf4c.service.CartService;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.service.RedeemService;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV2;
import com.wosai.pantheon.uf4c.util.*;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.smartbiz.oms.api.pojo.CartCheckResultDTO;
import com.wosai.smartbiz.oms.api.pojo.PreReduceGoodsDTO;
import com.wosai.smartbiz.oms.api.query.PreReduceStockRequest;
import com.wosai.smartbiz.oms.api.services.OrderGoodsStockRpcService;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import com.wosai.smartbiz.payment.api.trade.defs.PayWay;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhen.pan, xuyuanxiang
 * @since 2019/4/9
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class OrderServiceV2Impl implements OrderServiceV2 {
    @Autowired
    private OrderService orderService;
    @Autowired
    private CartService cartService;
    @Autowired
    private PrePayUtil prePayUtil;
    @Autowired
    private OrderHelper orderHelper;
    @Autowired
    private RedeemService redeemService;
    @Autowired
    private OrderGoodsStockRpcService orderGoodsStockService;
    @Autowired
    private UidRemoteService uidRemoteService;

    @Autowired
    private TableRpcServiceV2 tableRpcServiceV2;

    @Override
    public RedeemResult getRedeemResult(ApiRequest<RedeemRequest> apiRequest) {
        RedeemRequest redeemRequest = apiRequest.getBody();

        return redeemService.getRedeemResult(redeemRequest, ThreadLocalHelper.getUser());
    }

    @SneakyThrows
    @Override
    public PayResult pay(ApiRequest<PayRequest> apiRequest) {
        PayRequest request = apiRequest.getBody();

        PayWay payWay = getByCode(request.getPayWay());

        if (payWay == null) {
            request.setPayWay(Integer.valueOf(ThreadLocalHelper.getMiniProgramType().getCode()));
        }else if (payWay == PayWay.CASH || payWay == PayWay.ACCOUNTING){
            throw new ParamException("支付通道不能为现金/记账");
        }

        if(request.isRechargeAndPay() &&
                payWay != PayWay.GIFT_CARD && payWay != PayWay.CARD){
            // SMART-17836 兼容储值并支付使用预送券抵扣到0的情况, 等本期小程序灰度完成后，这个校验可以去掉
            throw new ParamException("0元订单暂不支持下单");
        }

        request.setSqbPaySource(ThreadLocalHelper.getSqbPaySource());
        request.setPayHeaders(ThreadLocalHelper.getPayHeaders());


        Integer serviceType = (null != request.getServiceType())?request.getServiceType():0;
        //判断你是否需要验证购物车商品,如果不需要，说明是旧版接口，旧版接口有单独check方法，这里就不用再次check了
        if(request.isCheckCartItem()){
            if (StringUtils.isBlank(request.getStoreId())){
                throw new ParamException("门店信息不能为空");
            }
            //检查购物车商品状态
            CartCheckResultDTO cartCheckResult = cartService.checkItemStatus(null, request.getStoreId(), serviceType, OrderMealTypeEnum.SINGLE);
            if(!cartCheckResult.isSuccess()){
                PayResult payResult = new PayResult();
                payResult.setCartCheckResult(cartCheckResult);
                return payResult;
            }
        }

        Order order;
        // 1.查询订单是否已经存在
        if (request.getSn() != null) {
            OrderDTO orderDTO = orderService.getOrderBySn(request.getSn());

            if (orderDTO == null) {
                throw new BusinessException(ReturnCode.ORDER_CAN_NOT_PAY);
            }

            order = EntityConvert.convertOrderDTO(orderDTO);
            if (request.getPayWay() != null) {
                order.setPayway(request.getPayWay());
            }
            order.setSubPayway(4);
            if (org.apache.commons.lang.StringUtils.isNotBlank(request.getClientSn())) {
                order.setClientSn(request.getClientSn());
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(request.getTerminalSn())) {
                order.setTerminalSn(request.getTerminalSn());
            }

            request.setAcquiring(orderDTO.getAcquiring());

        } else {
            // 2.转换订单
            order = orderHelper.convertPayRequest(request);

            // 3.预扣库存

            PreReduceStockRequest preReduceStockRequest = new PreReduceStockRequest();
            preReduceStockRequest.setMerchantId(order.getMerchantId());
            preReduceStockRequest.setStoreId(order.getStoreId());
            preReduceStockRequest.setProcessTime(System.currentTimeMillis());
            List<PreReduceGoodsDTO> preReduceGoodsList = order.getItems().stream()
                    .map(item -> CartHelper.convert2PreReduceGoods(item, false)).collect(Collectors.toList());
            preReduceStockRequest.setGoods(preReduceGoodsList);
            CartCheckResultDTO cartCheckResultDTO = orderGoodsStockService.preReduceProductStock(preReduceStockRequest);
            if(!cartCheckResultDTO.isSuccess()){
                //清空购物车商品
                if(CollectionUtils.isNotEmpty(cartCheckResultDTO.getCheckFailList())){
                    cartCheckResultDTO.getCheckFailList().stream().forEach(item -> {
                        cartService.removeItem(order.getStoreId(), order.getTableId(), item.getItemUid(), serviceType, OrderMealTypeEnum.SINGLE);
                    });
                }
                //这里预扣库存后需要兼容老版本接口返回，老版本接口只需要给错误提示就行，新版本需要返回PayResult对象
                if(request.isCheckCartItem()){
                    PayResult payResult = new PayResult();
                    payResult.setCartCheckResult(cartCheckResultDTO);
                    return payResult;
                }else {
                    throw new BusinessException(ReturnCode.BUSINESS_ERROR, "商品库存不足，请重新选购");
                }
            }
            Order.OrderExtra extra = order.getExtra();
            if(null == extra){
                extra = new Order.OrderExtra();
            }
            // 获得预扣库存ID，传递到下单接口中
            extra.setPreReduceNo(cartCheckResultDTO.getPreReduceNo());
            /**
             * 微信门店快送业务的场景参数传送
             */
            extra.setMpScene(request.getMpScene());
            extra.setWxTraceId(request.getWxTraceId());
            order.setExtra(extra);
            order.setClientTrackingData(request.getClientTrackingData());

            // 4.清空购物车
            cartService.resetCart(request.getStoreId(), CommonUtil.getServiceType(orderHelper.getOrderType(request.getType())), true);
        }
        // 5.预下单
        return prePayUtil.prePay(order, orderHelper.makePayParamWrapper(request), orderHelper.buildRedeemParamWrapper(request));
    }

    @Override
    public PayResult lovePay(ApiRequest<PayRequest> apiRequest) {
        return prePayUtil.lovePay(apiRequest.getBody().getStoreId());
    }

    @Override
    public Map loveQrCode(ApiRequest<LoveQrcodeRequest> apiRequest) {
        String url = apiRequest.getBody().getUrl();
        if (StringUtils.isEmpty(url)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "错误的二维码，请确认");
        }
        Map<String, Object> qrParams = CommonUtil.getUrlParams(url);
        String s = MapUtils.getString(qrParams, "s");
        String uid = MapUtils.getString(qrParams, "p");
        if (!"p".equalsIgnoreCase(s) || StringUtils.isBlank(uid)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "错误的二维码，请确认");
        }
        UidContentRequest uidContentRequest = new UidContentRequest();
        uidContentRequest.setUid(uid);
        try {
            String content = uidRemoteService.content(uidContentRequest);
            return CommonUtil.getUrlParams(content);
        } catch (Exception e) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "错误的二维码，请确认");
        }
    }

    PayWay getByCode(Integer payway) {
        for (PayWay payWay : PayWay.values()) {
            if (Objects.equals(payWay.getCode(), payway)) {
                return payWay;
            }
        }
        return null;
    }

}
