package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CartItemCreateBatch {

    List<CartItemCreate> items;

    /**
     * 营销的透传参数, 透传给优惠平台
     */
    private Map<Object,Object> mkCustomInfo;

    /**
     * 开台标识（使用开台时间）
     */
    private String tableOpenId;
}
