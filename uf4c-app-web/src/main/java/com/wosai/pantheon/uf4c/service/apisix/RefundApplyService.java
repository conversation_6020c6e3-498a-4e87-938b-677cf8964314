package com.wosai.pantheon.uf4c.service.apisix;


import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.RefundApplyRequest;

import java.util.Map;

@JsonRpcService(value = "/rpc/refund/apply")
public interface RefundApplyService {

    boolean cancel(ApiRequest<Map> map);


    boolean revokeApply(ApiRequest<Map> map);


    boolean refundApply(ApiRequest<RefundApplyRequest> request);
}
