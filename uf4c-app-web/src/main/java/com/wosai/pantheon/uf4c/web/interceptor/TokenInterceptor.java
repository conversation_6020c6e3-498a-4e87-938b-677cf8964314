package com.wosai.pantheon.uf4c.web.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.googlecode.jsonrpc4j.JsonUtil;
import com.googlecode.jsonrpc4j.spring.JsonServiceExporter;
import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.market.user.service.TokenService;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.util.IpUtils;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.UnauthorizedException;
import com.wosai.pantheon.uf4c.web.exception.UnknownMiniProgramTypeException;
import com.wosai.pantheon.util.StringUtil;
import kafka.utils.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static net.logstash.logback.argument.StructuredArguments.e;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
@Component
public class TokenInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    TokenService tokenService;
    public static final Config apolloConfig = ConfigService.getAppConfig();

    @Autowired
    public ApolloConfigHelper apolloConfigHelper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {

        if (handler instanceof JsonServiceExporter) {
            //说明这是jsonrpc的请求，这个拦截器不做处理，交给：JsonRpcTokenInterceptor 来处理


            return true;
        }

        // 检查小程序类型
        String miniProgramType = request.getHeader("Wosai-MiniProgramType");
        if (StringUtil.isEmpty(miniProgramType)
                || !EnumUtils.isValidEnum(MiniProgramType.class, miniProgramType)) {
            throw new UnknownMiniProgramTypeException();
        }

        // 小程序类型
        ThreadLocalHelper.getRequestContextThreadLocal().get()
                .setMiniProgramType(MiniProgramType.valueOf(miniProgramType));

        // 来源场景
        String scene = request.getHeader("Wosai-Scene");
        ThreadLocalHelper.getRequestContextThreadLocal().get()
                .setScene(scene);

        //小程序场景值
        String sqbPaySource = request.getHeader("X-Smart-Mp-Scene");
        ThreadLocalHelper.getRequestContextThreadLocal().get()
                .setSqbPaySource(sqbPaySource);
        log.info("Sqb-Pay-Source value", keyValue("method", "sqbPaySource"), keyValue("value", sqbPaySource));

        // 小程序appid
        String appid = request.getHeader("x-sqb-mp-platform-appid");
        ThreadLocalHelper.getRequestContextThreadLocal().get().setAppid(appid);
        log.info("appid", keyValue("method", "sqbAppid"), keyValue("value", appid));

        Map<String, String> payHeaders = new HashMap<String, String>();
        apolloConfigHelper.getPayHeaderConfigMap().forEach((key, value) -> {
            String headerValue = request.getHeader(key);
            if(StringUtils.isNotEmpty(headerValue) && StringUtils.isNotEmpty(value)){
                payHeaders.put(value, headerValue);
                log.info("Wosai-Pay-Headers:{}", keyValue("payHeader", value), keyValue("value", headerValue));
            }
         });
         ThreadLocalHelper.getRequestContextThreadLocal().get()
                    .setPayHeaders(payHeaders);




        // 检查授权令牌
        String token = request.getHeader("Wosai-Token");
        if (StringUtil.isEmpty(token)) {
            throw new UnauthorizedException();
        }
        log.info("Wosai-Token:{}", token);
        // 检查用户
        UserContextDTO userContextDTO = tokenService.validate(token);
        if (userContextDTO == null) {
            throw new UnauthorizedException();
        }

        // 黑名单用户校验
        String userBlacklist = apolloConfig.getProperty("user.blacklist", "");
        if (StringUtil.isNotBlank(userBlacklist)
                && StringUtil.isNotBlank(userContextDTO.getThirdpartyUserId())
                && userBlacklist.contains(userContextDTO.getThirdpartyUserId())) {
            LogUtils.logWarn("命中用户黑名单", "UserBlacklist", userContextDTO);
            throw new UnauthorizedException();
        }

        // 请求ip
        ThreadLocalHelper.getRequestContextThreadLocal().get()
                .setRealIp(IpUtils.getIp(request));

        ThreadLocalHelper.getRequestContextThreadLocal().get()
                .setUserContextDTO(userContextDTO);
        return true;
    }

}
