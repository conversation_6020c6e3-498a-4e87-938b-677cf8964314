package com.wosai.pantheon.uf4c.gather;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Sets;
import com.wosai.market.mcc.api.dto.request.WhiteListV2Request;
import com.wosai.market.mcc.api.service.WhiteListV2RemoteService;
import com.wosai.market.outer.adapter.api.service.AlipayNOrderRemoteService;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.CodeScene;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.constant.MpPageSceneEnums;
import com.wosai.pantheon.uf4c.gather.cache.GatherCacheHelper;
import com.wosai.pantheon.uf4c.model.ErrorInfo;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ServiceTypeData;
import com.wosai.pantheon.uf4c.model.vo.CategoryVo;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.model.vo.ItemNewVo;
import com.wosai.pantheon.uf4c.service.*;
import com.wosai.pantheon.uf4c.util.CommonUtil;
import com.wosai.pantheon.uf4c.util.GatherTraceRunnable;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;

import static com.wosai.pantheon.uf4c.constant.Constants.Closing.N_ORDER_NOT_SUPPORT;

@Service
@Slf4j
public class GatherService extends GatherBase {
    @Autowired
    private GatherQrCodeService gatherQrCodeService;
    @Autowired
    private GatherStoreService gatherStoreService;
    @Autowired
    private ServiceTypeUtil serviceTypeUtil;
    @Autowired
    private GatherGoodsService gatherGoodsService;
    @Autowired
    private GatherUserService gatherUserService;
    @Autowired
    private GatherHelper gatherHelper;
    @Autowired
    private GatherCacheHelper gatherCacheHelper;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private ItemsNewHelper itemsNewHelper;
    @Autowired
    private BrandActivityHelper brandActivityHelper;
    @Autowired
    private CartService cartService;
    @Autowired
    private TranslationConvertService translationConvertService;
    @Autowired
    private StoreHelper storeHelper;
    @Autowired
    private WhiteListV2RemoteService whiteListV2RemoteService;
    @Autowired
    private AlipayNOrderRemoteService alipayNOrderRemoteService;

    private static final String N_ORDER_MP_SCENE = "1301";

    public Map<String, Object> pageIndex(GatherRequest request) {
        Map<String, Object> dataMap = new ConcurrentHashMap<>();
        Map extraMap = new HashMap();
        extraMap.put("page", MpPageSceneEnums.INDEX.getPage());
        extraMap.put("timestamp", System.currentTimeMillis());
        dataMap.put("extra", extraMap);
        return dataMap;
    }

    /**
     * 查询默认的数据
     *
     * @param request
     * @return
     */
    public Map<String, Object> defaultRequest(GatherRequest request) {
        Map<String, Object> dataMap = new ConcurrentHashMap<>();
        // 阿波罗配置
        gatherStoreService.queryApolloConfig(request, dataMap);
        return dataMap;
    }

    /**
     * 点单首页聚合数据
     *
     * @param request
     * @return
     */
    public Map<String, Object> orderIndex(GatherRequest request) {
        log.info("点单首页聚合接口查询：{}", JSON.toJSONString(request));
        if (request.needCategory() && (StringUtils.isNotBlank(request.getCode())
                || StringUtils.isNotBlank(request.getToken())
                || StringUtils.isNotBlank(request.getUserId()))) {
            // 需要查询点过的菜
            request.setRecentItemCountDownLatch(new CountDownLatch(2));
        }
        ConcurrentHashMap<String, Object> dataMap = new ConcurrentHashMap<>();
        durationStart(dataMap, "total");
        // 登录
        CompletableFuture<Void> userFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> gatherUserService.login(request, dataMap)), userExecutor);
        // 其他与用户无关的请求
        CompletableFuture<Void> otherFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadDataWithoutUser(request, dataMap)));
        // 查询点过的菜
        CompletableFuture<Void> recentItemsFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> gatherGoodsService.loadRecentItems(request, dataMap)), userExecutor);
        // 并行执行
        CompletableFuture.allOf(userFuture, otherFuture, recentItemsFuture).join();
        fillTempData(request, dataMap);
        removeTempData(dataMap);
        durationEnd(dataMap, "total");
        if (((gatherCacheHelper.isCacheEnabled(request.getStoreId())
                && dataMap.containsKey("goods")
                && dataMap.containsKey("category")
                && !gatherCacheHelper.hasGoodsCache(request.getStoreId(), request.getServiceType()))
                || request.isCacheUpdate()) && !hasError(dataMap)) {
            // 保存缓存数据
            if (StringUtils.isNotBlank(request.getStoreId()) && Objects.nonNull(request.getServiceType())) {
                gatherCacheHelper.saveDataToCache(request, dataMap);
            }
        }

        // 转换商品为新结构
        itemsNewHelper.convertGatherItems(request, dataMap);
//            fillGiftCardDiscountPrice(request, dataMap);
        // 处理mcc的默认值
        processMccDefault(dataMap);
        // 翻译替换文案
        translationConvertService.convertGatherIndexData(dataMap, request);
        storeHelper.processMultiTemplates(request.getStoreId(), dataMap);
        if (nOrderAndStoreNotInNOrderWhiteList(request, dataMap)) {
            setClosing(dataMap, N_ORDER_NOT_SUPPORT);
        }
        return dataMap;
    }

    private void addItemNewVoToMap(Map<String, ItemNewVo> map, ItemNewVo item) {
        if (map == null) {
            map = new HashMap<>();
        }
        if (map.get(item.getId()) == null) {
            map.put(item.getId(), item);
            return;
        }

        return;
    }


    /**
     * 删除一些临时存放的数据
     *
     * @param dataMap
     */
    private void removeTempData(Map<String, Object> dataMap) {
        dataMap.remove("recentItems");
        dataMap.remove("recommend");
        dataMap.remove("hotSale");
        dataMap.remove("discount");
        dataMap.remove("single");
        dataMap.remove("second");
        dataMap.remove("categoryActivity");
        dataMap.remove("index");
        dataMap.remove("tag");
        dataMap.remove("takeoutProfitSharing");
        dataMap.remove("tempCursor");
    }

    private void fillTempData(GatherRequest request, Map<String, Object> dataMap) {
        if (dataMap.containsKey("recentItems") && Objects.nonNull(dataMap.get("recentItems"))) {
            List<ItemDetailVO> recentItems = (List<ItemDetailVO>) MapUtils.getObject(dataMap, "recentItems");
            if (dataMap.containsKey("category") && Objects.nonNull(dataMap.get("category"))) {
                List<CategoryVo> categoryVoList = (List<CategoryVo>) MapUtils.getObject(dataMap, "category");
                List<CategoryVo> newList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(recentItems)) {
                    newList.add(new CategoryVo() {{
                        setId("recent");
                        setStoreId(request.getStoreId());
                        setName("点过的菜");
                        setItemCount(recentItems.size());
                        setDisplayOrder(-1000);
                        setItems(recentItems);
                    }});
                }
                newList.addAll(categoryVoList);
                dataMap.put("category", newList);
            }
        }
        if (MapUtils.isNotEmpty(MapUtils.getMap(dataMap, "mcc")) && Objects.nonNull(dataMap.get("takeoutProfitSharing"))) {
            Map mccMap = MapUtils.getMap(dataMap, "mcc");
            mccMap.put("takeoutProfitSharing", MapUtils.getBooleanValue(dataMap, "takeoutProfitSharing"));
        }
        if (!dataMap.containsKey("supportCardPay")) {
            dataMap.put("supportCardPay", "");
        }
        if (dataMap.containsKey("tempCursor") && Objects.nonNull(dataMap.get("tempCursor"))) {
            if (dataMap.containsKey("goods") && Objects.nonNull(dataMap.get("goods"))) {
                Map<String, Object> goods = (Map<String, Object>) dataMap.get("goods");
                goods.put("cursor", dataMap.get("tempCursor"));
            }
        }

    }


    private boolean hasError(Map<String, Object> dataMap) {
        Set<String> ignoreErrorCodes = Sets.newHashSet(
                ReturnCode.APOLLO_DATA_ERROR.getCode(),
                ReturnCode.LOGIN_ERROR.getCode(),
                ReturnCode.GOODS_SUBSIDY_ERROR.getCode(),
                ReturnCode.DELIVERY_AREA_DATA_ERROR.getCode(),
                ReturnCode.STORE_THEMES_DATA_ERROR.getCode(),
                ReturnCode.HOT_SALE_DATA_ERROR.getCode(),
                ReturnCode.RECENT_ITEMS_DATA_ERROR.getCode());
        if (dataMap.containsKey("errors")) {
            List<ErrorInfo> errorInfos = (List<ErrorInfo>) MapUtils.getObject(dataMap, "errors");
            if (CollectionUtils.isNotEmpty(errorInfos)) {
                return errorInfos.stream().anyMatch(it -> !ignoreErrorCodes.contains(it.getCode()));
            }
        }
        return false;
    }

    private void loadDataWithoutUser(GatherRequest request, Map<String, Object> dataMap) {
        Map extraMap = new HashMap();
        extraMap.put("scene", Constants.From.MANUAL);
        extraMap.put("page", MpPageSceneEnums.HOME.getPage());
        extraMap.put("timestamp", System.currentTimeMillis());
        dataMap.put("extra", extraMap);
        try {
            if (StringUtils.isNotBlank(request.getUrl())) {
                if (CommonUtil.isValidURL(request.getUrl())) {
                    // 获取链接中的参数
                    extraMap.putAll(CommonUtil.getUrlParams(request.getUrl()));
                    // 解析二维码
                    gatherQrCodeService.parser(request, dataMap);
                    // 判断二维码是否有效
                    if (!isValidQrCode(dataMap)) {
                        return;
                    }
                    log.info("二维码信息：{}", JSON.toJSONString(MapUtils.getMap(dataMap, "terminal")));
                } else {
                    extraMap.putAll(CommonUtil.getUrlParams(request.getUrl()));
                    int storeSetId = MapUtils.getIntValue(extraMap, "storeSetId", 0);
                    if (storeSetId > 0) {
                        extraMap.put("storeSetId", storeSetId);
                        // 查询是否关联了校园页，下线
//                        try {
//                            StoreSetDTO storeSetDTO = storeSetService.getStoreSetById(storeSetId);
//                            if (Objects.nonNull(storeSetDTO) && Objects.nonNull(storeSetDTO.getCampusId())) {
//                                extraMap.put("campusId", storeSetDTO.getCampusId());
//                                extraMap.put("page", MpPageSceneEnums.CAMPUS.getPage());
//                                //extraMap.remove("storeSetId");
//                            } else {
//                                extraMap.put("storeSetId", storeSetId);
//                            }
//                        } catch (Exception e) {
//                            log.warn("查询聚合页基本信息出错", "getStoreSetById", request, e);
//                        }
                    }
                }
            }
            if (StringUtils.isBlank(request.getStoreId())) {
                // 没有传storeId，赋值为从extra中取出的门店Id
                // extra中门店id有两种来源：码的terminal信息、链接地址中的带的storeId
                request.setStoreId(getStoreId(extraMap));
                request.setMerchantId(getMerchantId(extraMap));
            }

            // 判断数据查询流程是否需要继续
            if (!needProceed(request, dataMap)) {
                return;
            }
            if (request.needStore() || request.needMcc()) {
                // 查询门店信息
                gatherStoreService.loadStoreData(request, dataMap);
            }

            if (request.needStore()) {
                if (!dataMap.containsKey("store") || Objects.isNull(dataMap.get("store"))) {
                    setError(dataMap, ReturnCode.STORE_DATA_ERROR);
                    return;
                }
            }
            // A码与S码特殊处理逻辑，判断是否继续往下走
            if (isSpecialCode(dataMap)) {
                return;
            }
            // 计算ServiceType
            if (Objects.isNull(request.getServiceType())) {
                if (!request.needMcc()) {
                    // 不传serviceType,且不查询mcc，则直接返回
                    return;
                }
                // 计算返回前端的ServiceType数据
                ServiceTypeData serviceTypeData = serviceTypeUtil.compute(request, dataMap);
                // 判断计算出的服务类型数据是否正确
                if (checkServiceTypeData(request, serviceTypeData, dataMap)) {
                    return;
                }
                request.setServiceType(serviceTypeData.getServiceType());
            }
            // 检查是否触发风控，如果触发，则流程终止
            if (checkRisk(request, dataMap)) {
                // 此时将值置为null，是为了避免无异议的点过的菜查询
                request.setServiceType(null);
                return;
            }
        } finally {
            request.recentCountDown();
        }
        if (request.needGoods() || request.needCategory()) {
            // 查询商品信息
            gatherGoodsService.loadGoodsAndActivity(request, dataMap);
        }


        extraMap.put("fullPath", gatherHelper.getFullPath(extraMap));
    }

    // 判断如果是碰一碰点单，门店是否在白名单内。（如果聚合接口没有查询到门店信息或者不用查询门店信息，也不用判断是否N点单白名单）
    private boolean nOrderAndStoreNotInNOrderWhiteList(GatherRequest request, Map<String, Object> dataMap) {
        try {
            if (!dataMap.containsKey("store") || Objects.isNull(dataMap.get("store"))) {
                return false;
            }

            Boolean gatherIndexNOrderWhiteListEnable = apolloConfigHelper.getBooleanConfigValueByKey("gather_index_n_order_white_list_enable", false);
            if (!gatherIndexNOrderWhiteListEnable) {
                return false;
            }
            if (!Objects.equals(request.getMiniProgramType(), MiniProgramType.ALIPAY.name())) {
                return false;
            }
            if (!Objects.equals(request.getMpScene(), N_ORDER_MP_SCENE)) {
                return false;
            }
            if (Constants.From.CAMPUS.equalsIgnoreCase(request.getFrom())) {
                return false;
            }
            Map<String, Object> store = (Map<String, Object>) MapUtils.getMap(dataMap, "store", null);
            String storeSn = MapUtils.getString(store, "storeSn", null);
            String gatherIndexNOrderWhiteListId = apolloConfigHelper.getStringConfigValueByKey("n_order_white_list_id", null);
            if (StringUtils.isAnyBlank(gatherIndexNOrderWhiteListId, storeSn)) {
                return false;
            }
            WhiteListV2Request whiteListV2Request = new WhiteListV2Request();
            whiteListV2Request.setBusinessType(gatherIndexNOrderWhiteListId);
            whiteListV2Request.setBusinessId(storeSn);
            boolean inWhiteList = whiteListV2RemoteService.inWhiteList(whiteListV2Request);
            if (!inWhiteList) {
                // 异步发消息
                storeExecutor.execute(() -> alipayNOrderRemoteService.whenNOrderRejectedSendNotice(request.getStoreId()));
            }
            return !inWhiteList;
        } catch (Exception e) {
            LogUtils.logWarnWithError("nOrderAndStoreNotInNOrderWhiteList error", "nOrderAndStoreNotInNOrderWhiteList", request, e);
            return false;
        }
    }


    /**
     * 根据extra数据判断是否需要继续流程<br>
     * 此需求是根据前端逻辑而来
     */
    private boolean needProceed(GatherRequest request, Map<String, Object> dataMap) {
        Map<String, Object> extraMap = (Map<String, Object>) MapUtils.getMap(dataMap, "extra");
        String scene = MapUtils.getString(extraMap, "scene", "");
        Map<String, Object> terminalMap = (Map<String, Object>) MapUtils.getMap(dataMap, "terminal");
        String page = MapUtils.getString(extraMap, "page");
        // 有的请求中，page可能传入的是页面路径，例如/packages/merchant/store/show,此时，需要将页面path替换为页面page
        String newPage = gatherHelper.getPage(page);
        if (StringUtils.isNotBlank(newPage)) {
            page = newPage;
            extraMap.put("page", page);
        }
        // 根据前端逻辑，如果页面path是"packages"开头，则不需要进行下面的数据查询操作，简单的返回页面映射就可以
        if (gatherHelper.isPackagePage(page)) {
            // 如果是插件页面，则不需要走后面逻辑
            return false;
        }
        String pageMapping = gatherHelper.getPageMapping(page);
        if (StringUtils.isNotBlank(pageMapping)) {
            page = pageMapping;
            extraMap.put("page", page);
        }
        // 如果码中没有门店信息，或者入参没传门店，则一下与门店相关数据不请求
        if (StringUtils.isBlank(request.getStoreId())) {
            // 聚合页
            if (MapUtils.getIntValue(extraMap, "group", 0) > 0) {
                return false;
            }
            // 校园外卖
            if (MapUtils.getIntValue(extraMap, "campusId", 0) > 0) {
                return false;
            }
            // 聚合码
            if (MapUtils.getIntValue(extraMap, "storeSetId", 0) > 0) {
                return false;
            }
            // 特殊海报码，不带门店id
            if (CodeScene.P == CodeScene.getCodeScene(scene)) {
                return false;
            }
            setError(dataMap, ReturnCode.STORE_ID_ERROR);
            return false;
        }
        return true;
    }

    /**
     * 检查是否触发风控
     *
     * @param request
     * @param dataMap
     * @return
     */
    private boolean checkRisk(GatherRequest request, Map<String, Object> dataMap) {

        Map<String, String> mccMap = (Map<String, String>) MapUtils.getMap(dataMap, "mcc");
        Map<String, String> mchMccMap = (Map<String, String>) MapUtils.getMap(dataMap, "mch_mcc");
        // 外卖风控
        if (request.getServiceType() == 1) {
            boolean isMchRisk = MccUtils.getBooleanValue(mchMccMap, Constants.TAKEOUT_RISK, false);
            boolean isRisk = MccUtils.getBooleanValue(mccMap, Constants.TAKEOUT_RISK, false);
            if (isMchRisk || isRisk) {
                setClosing(dataMap, Constants.Closing.TAKEOUT_EXCEPTION);
                return true;
            }
        }
        // 堂食风控
        if (request.getServiceType() == 2) {
            boolean isMchRisk = MccUtils.getBooleanValue(mchMccMap, Constants.SCAN_RISK, false);
            boolean isRisk = MccUtils.getBooleanValue(mccMap, Constants.SCAN_RISK, false);
            if (isMchRisk || isRisk) {
                setClosing(dataMap, Constants.Closing.QRCODE_EXCEPTION);
                return true;
            }
        }
        return false;
    }


    /**
     * 处理特殊的码，例如A码和S码
     *
     * @param dataMap
     */
    private boolean isSpecialCode(Map<String, Object> dataMap) {
        if (codeSceneEqual(dataMap, CodeScene.A) || codeSceneEqual(dataMap, CodeScene.S)) {
            Map<String, Object> extraMap = (Map<String, Object>) MapUtils.getMap(dataMap, "extra");
            Map<String, Object> storeMap = (Map<String, Object>) MapUtils.getMap(dataMap, "store");
            if (MapUtils.isNotEmpty(MapUtils.getMap(dataMap, "terminal"))) {
                extraMap.put("page", MapUtils.getIntValue(storeMap, "payWay", 1) != 2 ? "pay" : "store");
                return true;
            }
        }
        return false;
    }

    /**
     * 判断二维是否有效
     *
     * @param dataMap
     * @return
     */
    private boolean isValidQrCode(Map<String, Object> dataMap) {
        Map<String, String> terminalMap = (Map<String, String>) MapUtils.getMap(dataMap, "terminal");
        Map<String, String> extraMap = (Map<String, String>) MapUtils.getMap(dataMap, "extra");
        String mealType = MapUtils.getString(terminalMap, Constants.MEAL_TYPE_CONFIG_KEY);
        // 有终端信息，会进行码的有效性判断，A码不判断二维码的有效性
        if (MapUtils.isNotEmpty(terminalMap) && !codeSceneEqual(dataMap, CodeScene.A) && !codeSceneEqual(dataMap, CodeScene.S) && !codeSceneEqual(dataMap, CodeScene.P)) {
            if (MapUtils.getIntValue(extraMap, "storeSetId") > 0) {
                return true;
            }
            if (StringUtils.isBlank(MapUtils.getString(terminalMap, "store_id"))) {
                setClosing(dataMap, Constants.Closing.QRCODE_UNBIND);
                return false;
            }
            // 桌台名称
            String name = MapUtils.getString(terminalMap, Constants.NAME);
            // 桌台名称
            String tableName = MapUtils.getString(terminalMap, Constants.TABLE_NAME);
            if (StringUtils.isBlank(tableName)) {
                tableName = name;
            }
            // 业务类型：1-桌台 2-门店点单码
            // 没有业务类型，也及是错误的码
            int jjzBusinessType = MapUtils.getIntValue(terminalMap, Constants.JJZ_BUSINESS_TYPE, 1);
//            if (jjzBusinessType == 0) {
//                setClosing(dataMap, Constants.Closing.QRCODE_ERROR);
//                return false;
//            }
            // 是点单码，但是没有桌号
            if (jjzBusinessType == 1 && StringUtils.isEmpty(tableName)) {
                setClosing(dataMap, Constants.Closing.QRCODE_NO_TABLE_NAME);
                return false;
            }
            // 是门店点单码、并且开围餐，且没桌号
            if (jjzBusinessType == 2 && Constants.MEAL_ROUND.equalsIgnoreCase(mealType) && StringUtils.isEmpty(tableName)) {
                setClosing(dataMap, Constants.Closing.QRCODE_NO_TABLE_NAME_FOR_ROUND_MEAL);
                return false;
            }
        }
        return true;
    }


    /**
     * 判断服务类型数据
     *
     * @param serviceTypeData
     * @return
     */
    private boolean checkServiceTypeData(GatherRequest request, ServiceTypeData serviceTypeData, Map<String, Object> dataMap) {
        Map<String, String> extraMap = (Map<String, String>) MapUtils.getMap(dataMap, "extra");
        String scene = MapUtils.getString(extraMap, "scene", "");
        String mealShareType = MapUtils.getString(extraMap, "mealShareType");

        if (Objects.isNull(serviceTypeData) || CollectionUtils.isEmpty(serviceTypeData.getServiceTypeList())) {
            setClosing(dataMap, Constants.Closing.NOT_ON_BUSINESS);
            return true;
        }
        // 没有外卖的服务类型，但是从再来一单或者外卖海报进入
        boolean hasNoTakeout = serviceTypeData.getServiceTypeList().stream().noneMatch(it -> it.getServiceType() == 1);
        if (hasNoTakeout && ((CodeScene.P == CodeScene.getCodeScene(scene) && "poster".equalsIgnoreCase(mealShareType))
                || OrderType.TAKE_OUT_ORDER.getMsg().equalsIgnoreCase(request.getBuyAgainOrderType())
                || OrderType.PRE_ORDER.getMsg().equalsIgnoreCase(request.getBuyAgainOrderType()))) {
            // 没有外卖
            setClosing(dataMap, Constants.Closing.TAKEOUT_OFFLINE);
            return true;
        }
        return false;
    }


    private String getStoreId(Map extraMap) {
        String storeId;
        storeId = MapUtil.getString(extraMap, "storeId");
        storeId = StringUtils.isBlank(storeId) ? MapUtil.getString(extraMap, "store_id") : storeId;
        return storeId;
    }

    private String getMerchantId(Map extraMap) {
        String merchantId;
        merchantId = MapUtil.getString(extraMap, "merchantId");
        merchantId = StringUtils.isBlank(merchantId) ? MapUtil.getString(extraMap, "merchant_id") : merchantId;
        return merchantId;
    }

    /**
     * 判断码是否为指定类型
     *
     * @param dataMap
     * @param codeScene
     * @return
     */
    private boolean codeSceneEqual(Map<String, Object> dataMap, CodeScene codeScene) {
        Map<String, String> extraMap = (Map<String, String>) MapUtils.getMap(dataMap, "extra");
        String scene = MapUtils.getString(extraMap, "scene", "");
        return CodeScene.getCodeScene(scene) == codeScene;
    }


    private void processMccDefault(Map<String, Object> dataMap) {
        String mccDefault = apolloConfigHelper.getStringConfigValueByKey("mcc.default", "");
        if (StringUtils.isBlank(mccDefault)) {
            return;
        }
        Map<String, Object> defaultMccMap = JSON.parseObject(mccDefault, new TypeReference<Map<String, Object>>() {
        });
        if (MapUtils.isEmpty(defaultMccMap)) {
            return;
        }
        Map<String, Object> mccMap = (Map<String, Object>) MapUtils.getMap(dataMap, "mcc");
        if (MapUtils.isNotEmpty(mccMap)) {
            mccMap.putAll(defaultMccMap);
            dataMap.put("mcc", mccMap);
        }
    }

}
