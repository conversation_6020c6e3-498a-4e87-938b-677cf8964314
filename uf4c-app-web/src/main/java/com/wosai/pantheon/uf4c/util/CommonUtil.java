package com.wosai.pantheon.uf4c.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import jodd.net.URLDecoder;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 公用工具类 (不包含具体的业务逻辑)
 */
public class CommonUtil {
    // 收钱吧小程序appid
    private static final String SQB_APPID = "wxccbcac9a3ece5112";

    public static String getAllExceptionMessage(Throwable t) {
        if (t == null) {
            return "";
        }
        String message = t.getMessage();
        StringBuilder sb = new StringBuilder(t.getClass() + ":");
        sb.append(message == null ? "" : message);
        Throwable cause = t.getCause();
        while (cause != null) {
            String causeMessage = cause.getMessage();
            sb.append(";").append(causeMessage);
            cause = cause.getCause();
        }
        return sb.toString();
    }

    /**
     * take("wosai", 3) => "wos"
     *
     * @param s
     * @param n
     * @return
     */
    public static String take(String s, int n) {
        return s.substring(0, n);
    }

    /**
     * drop("wosai", 3) => sai
     *
     * @param s
     * @param n
     * @return
     */
    public static String drop(String s, int n) {
        return s.substring(n, s.length());
    }

    /**
     * takeRight("wosai", 3) => "sai"
     *
     * @param s
     * @param n
     * @return
     */
    public static String takeRight(String s, int n) {
        return s.substring(Math.max(n, 0), s.length());
    }

    /**
     * dropRight("wosai", 3) => "wos"
     *
     * @param s
     * @param n
     * @return
     */
    public static String dropRight(String s, int n) {
        return s.substring(0, Math.max(n, 0));
    }


    public static void sleepIgnoreException(long mills) {
        if (mills <= 0) return;

        try {
            Thread.sleep(mills);
        } catch (InterruptedException e) {

        }
    }


    /**
     * default partition fn for hashcode-strategy
     *
     * @param s
     * @param partitionNum
     * @return
     */
    public static int getPartition(String s, int partitionNum) {
        if (partitionNum == 0) return 0;

//        byte[] bytes = DigestUtils.sha1(s);
//        return (bytes[19] | bytes[18] << 8 | bytes[17] << 16 | bytes[16] << 24) & 0x7fffffff % partitionNum;

        return Math.abs(s.hashCode() % partitionNum);
    }

    /**
     * 注意，此方法只会深拷贝map类型的value, 其他对象还是浅拷贝
     *
     * @param original
     * @return
     */
    public static Map<String, Object> clone(Map<String, Object> original) {
        if (original == null) {
            return null;
        }
        Map<String, Object> target = new HashMap<>();
        synchronized (original) {
            for (String key : original.keySet()) {
                Object value = original.get(key);
                if (value instanceof Map) {
                    target.put(key, clone((Map<String, Object>) value));
                } else {
                    target.put(key, value);
                }
            }
        }
        return target;
    }


    public static int getServiceType(OrderType orderType) {
        int serviceType = 0;
        if (orderType == OrderType.TAKE_OUT_ORDER || orderType == OrderType.PRE_ORDER) {
            serviceType = 1;
        }
        if (orderType == OrderType.SUBSCRIBE_ORDER || orderType == OrderType.EAT_FIRST_ORDER) {
            serviceType = 2;
        }
        return serviceType;
    }

    // 判断一个字符是否是中文
    public static boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;// 根据字节码判断
    }

    // 判断一个字符串是否含有中文
    public static boolean hasChinese(String str) {
        if (str == null) return false;
        for (char c : str.toCharArray()) {
            if (isChinese(c)) return true;// 有一个中文字符就返回
        }
        return false;
    }

    public static boolean isInDate(String startTime, String endTime) {
        if ("24:00".equals(endTime)) {
            endTime = "23:59";
        }
        LocalTime now = LocalDateTime.now(ZoneId.of("+8")).toLocalTime().truncatedTo(ChronoUnit.MINUTES);
        LocalTime start = LocalTime.parse(startTime, DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime end = LocalTime.parse(endTime, DateTimeFormatter.ofPattern("HH:mm"));
        if (start.isAfter(end)) {
            return now.compareTo(start) >= 0 || now.compareTo(end) <= 0;

        } else return now.compareTo(start) >= 0 && now.compareTo(end) <= 0;
    }

    public static String getUrlParamsByMap(Map<String, Object> map) {
        if (map == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue());
            sb.append("&");
        }
        String s = sb.toString();
        if (s.endsWith("&")) {
            s = StringUtils.substringBeforeLast(s, "&");
        }
        return s;
    }

    /**
     * 将url参数转为map
     *
     * @param url
     * @return
     */
    public static Map getUrlParams(String url) {
        Map map = new HashMap();
        if (StringUtils.isNotBlank(url)) {
            String param = url;
            if (url.contains("?")) {
                param = url.substring(url.indexOf("?") + 1);
                String head = url.substring(0, url.indexOf("?"));
                if (head.contains("=")) {
                    param = head + "&" + param;
                }
            }
            String[] params = param.split("&");
            for (String s : params) {
                String[] p = s.split("=");
                if (p.length == 2) {
                    map.put(p[0], p[1]);
                }
            }
        }
        return map;
    }


    public static boolean isValidURL(String urlString) {
        try {
            URL url = new URL(urlString);
            url.toURI();
            return true;
        } catch (Exception exception) {
            return false;
        }
    }


    /**
     * 获取微信中query附带的url信息，例如：q=https://99zhe.com/t/eabde
     *
     * @param query
     * @return
     */
    public static String getGatherQueryParams(String query) {
        Map params = getUrlParams(query);
        if (StringUtils.isNotBlank(MapUtils.getString(params, "q"))) {
            String url = MapUtils.getString(params, "q");
            if (CommonUtil.isValidURL(url)) {
                params.remove("q");
                return url + "?" + getUrlParamsByMap(params);
            }
        }
        return null;
    }

    /**
     * 处理请求过来的参数
     *
     * @param request
     */
    public static void parseGatherRequestParams(GatherRequest request) {
        // 如果是收钱吧小程序，则去掉authCode
        if (SQB_APPID.equalsIgnoreCase(request.getAppid())) {
            request.setCode(null);
        }
        if (StringUtils.isNotBlank(request.getOpenid())) {
            request.setThirdPartUserId(request.getOpenid());
        }
        if (StringUtils.isNotBlank(request.getQuery())) {
            // 微信预拉取接口中的二维码数据放在query字段中，并且经过URL ENCODE
            request.setQuery(URLDecoder.decode(request.getQuery()));
            Map params = getUrlParams(request.getQuery());
            if (params.containsKey("q")) {
                // 微信扫码后，码的地址放在“q”字段中
                String url = MapUtils.getString(params, "q");
                params.remove("q");
                if (StringUtils.isNotBlank(request.getScancode_time())) {
                    params.put("scancode_time", request.getScancode_time());
                }
                if (params.size() > 0) {
                    url += "?" + getUrlParamsByMap(params);
                }
                request.setUrl(url);
            } else {

                request.setUrl(request.getQuery());
            }

        }

        if (StringUtils.isNotBlank(request.getUrl())) {
            request.setUrl(URLDecoder.decode(request.getUrl()));
            Map params = getUrlParams(request.getUrl());
            if (params.containsKey("storeId")) {
                // 分享链接中，大部分会带上storeId
                request.setStoreId(MapUtils.getString(params, "storeId"));
            }
            // 设置请求地址是否为测试环境
            request.setBetaEnv("beta".equalsIgnoreCase(MapUtils.getString(params, "env")));
        }

    }


    public static <T> T transferObject(Object source, TypeReference<T> typeReference) {
        String data = JSON.toJSONString(source);
        return JSON.parseObject(data, typeReference);
    }

    public static <T> T transferObject(byte[] source, TypeReference<T> typeReference) {
        String data = JSON.toJSONString(source);
        return JSON.parseObject(data, typeReference);
    }

    /**
     * 判断当前时间是否在时间区间内（格式：HH:mm）
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean inTimes(String startTime, String endTime) {
        try {
            int start = Integer.parseInt(startTime.replace(":", ""));
            int end = Integer.parseInt(endTime.replace(":", ""));
            SimpleDateFormat sdf = new SimpleDateFormat("HHmm");
            int curr = Integer.parseInt(sdf.format(new Date()));
            return curr <= end && curr >= start;
        } catch (Exception e) {
            return false;
        }
    }
}

