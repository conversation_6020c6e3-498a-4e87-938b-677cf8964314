package com.wosai.pantheon.uf4c.gather;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.wosai.market.tethys.api.dto.CategoryInfo;
import com.wosai.market.tethys.api.dto.DiscountInfo;
import com.wosai.market.tethys.api.dto.Discounts;
import com.wosai.market.tethys.api.dto.SkuInfo;
import com.wosai.market.tethys.api.dto.request.CategoryActivityDetailRequest;
import com.wosai.market.tethys.api.enums.EffectiveType;
import com.wosai.market.tethys.api.service.DiscountsRemoteService;
import com.wosai.pantheon.core.uitem.model.CategoryExt;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.OrderGoodsTagEnum;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.cache.GatherCacheHelper;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.item.CategoryTreeDTO;
import com.wosai.pantheon.uf4c.model.item.RetailCategoryQueryReq;
import com.wosai.pantheon.uf4c.model.item.RetailItemCursorQueryReq;
import com.wosai.pantheon.uf4c.model.item.RetailItemCursorQueryRes;
import com.wosai.pantheon.uf4c.model.vo.CategoryVo;
import com.wosai.pantheon.uf4c.model.vo.HotSaleItem;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.service.CategoryHelper;
import com.wosai.pantheon.uf4c.service.ItemHelper;
import com.wosai.pantheon.uf4c.service.item.retail.IRetailItemAdapterService;
import com.wosai.pantheon.uf4c.util.GatherTraceRunnable;
import com.wosai.pantheon.uf4c.util.JacksonUtil;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.web.api.ListResult;
import com.wosai.web.api.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 查询商品信息与门店优惠，包括：商品分类、热销商品、推荐商品、首页商品、门店优惠
 */
@Slf4j
@Service
public class GatherGoodsService extends GatherBase {
    @Autowired
    private ItemService itemService;
    @Autowired
    private ItemHelper itemHelper;
    @Autowired
    private com.wosai.pantheon.uf4c.service.apisix.ItemService apiSixItemService;
    @Autowired
    private DiscountsRemoteService discountsRemoteService;
    @Autowired
    private CategoryHelper categoryHelper;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private GatherStoreService gatherStoreService;
    @Autowired
    private GatherCacheHelper gatherCacheHelper;
    @Autowired
    private IRetailItemAdapterService retailItemAdapterService;
    // 异步线程超时时间
    @Value("${service.async_timeout}")
    private int ASYNC_TIMEOUT;

    /**
     * 查询商品信息和门店优惠
     *
     * @param request
     * @param dataMap
     */
    public void loadGoodsAndActivity(GatherRequest request, Map<String, Object> dataMap) {
        // 零售门店暂时不使用商品缓存
        if (!request.isRetailStore() && gatherCacheHelper.isCacheEnabled(request.getStoreId()) && !request.isCacheUpdate() && gatherCacheHelper.hasGoodsCache(request.getStoreId(), request.getServiceType())) {
            gatherCacheHelper.getGoodsCache(request, dataMap);
            return;
        }
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        CompletableFuture<Void> categoryFuture = null;
        if (request.needCategory()) {
            // 查询分类信息
            categoryFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadCategory(request, dataMap)), goodsExecutor);
            futureList.add(categoryFuture);
            if (!request.isRetailStore()) {
                // 查询热销数据
                // 注意，设置了超时时间，若超时，则返回空数据
                // 超时后，查询线程会依然执行中，直到线程中的逻辑处理完毕
                futureList.add(within(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadHotSale(request, dataMap)), activityExecutor), ASYNC_TIMEOUT, TimeUnit.MILLISECONDS)
                        .exceptionally(throwable -> {
                            log.warn("查询热销商品超时");
                            setError(dataMap, ReturnCode.HOT_SALE_DATA_ERROR);
                            durationEnd(dataMap, "loadHotSale");
                            return null;
                        }));
                // 查询推荐商品
                futureList.add(within(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadRecommend(request, dataMap)), activityExecutor), ASYNC_TIMEOUT, TimeUnit.MILLISECONDS)
                        .exceptionally(throwable -> {
                            log.warn("查询推荐商品超时");
                            setError(dataMap, ReturnCode.RECOMMEND_DATA_ERROR);
                            durationEnd(dataMap, "loadRecommend");
                            return null;
                        }));
                // 查询独立显示标签商品
                futureList.add(within(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadProductTag(request, dataMap)), activityExecutor), ASYNC_TIMEOUT, TimeUnit.MILLISECONDS)
                        .exceptionally(throwable -> {
                            log.warn("查询商品标签超时");
                            setError(dataMap, ReturnCode.PRODUCT_TAG_DATA_ERROR);
                            durationEnd(dataMap, "loadProductTag");
                            return null;
                        }));

                // 查询优惠商品
                futureList.add(within(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadDiscountCategory(request, dataMap)), activityExecutor), ASYNC_TIMEOUT, TimeUnit.MILLISECONDS)
                        .exceptionally(throwable -> {
                            log.warn("查询优惠商品超时");
                            setError(dataMap, ReturnCode.DISCOUNT_CATEGORY_DATA_ERROR);
                            durationEnd(dataMap, "loadDiscountCategory");
                            return null;
                        }));
            }
        }
        if (request.needGoods()) {
            // 查询单品优惠信息，之后与商品信息进行合并
            futureList.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadSingleActivity(request, dataMap)), executorNew));
            // 查询第二份半价信息，之后与商品信息合并
            futureList.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadSecondActivity(request, dataMap)), executorNew));
            // 查询首页商品，目前只查询第一页商品
            CompletableFuture<Void> goodFuture;
            if (categoryFuture != null && request.isRetailStore()) {
                // 零售门店，需要在分类信息加载完成后再根据第一个分类id查询商品
                goodFuture = categoryFuture.thenRunAsync(GatherTraceRunnable.of(() -> loadIndexItems(request, dataMap)), goodsExecutor);
            } else {
                goodFuture = CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadIndexItems(request, dataMap)), goodsExecutor);
            }
            futureList.add(goodFuture);
            // 查询商品最优活动
//            goodFuture.thenRunAsync(GatherTraceRunnable.of(() -> loadGoodsAndCategoryBestActivity(request, dataMap)));
            futureList.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadCategoryBestActivity(request, dataMap))));
        }
        if (request.needActivity()) {
            // 查询门店优惠信息
            futureList.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> loadStoreActivity(request, dataMap)), executorNew));
        }
        if (request.needMcc()) {
            // 查询是否有外卖分账
            // 放在这里的原因是查询商品等信息耗时比较久，可以覆盖分账查询的耗时
            futureList.add(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> gatherStoreService.hasTakeoutProfitSharing(request.getStoreId(), dataMap)), goodsExecutor));

            // 查询储值卡可支付场景
            futureList.add(within(CompletableFuture.runAsync(GatherTraceRunnable.of(() -> gatherStoreService.supportCardPay(request.getStoreId(), dataMap)), goodsExecutor), ASYNC_TIMEOUT, TimeUnit.MILLISECONDS)
                    .exceptionally(throwable -> {
                        log.warn("查询储值卡可支付场景超时");
                        setError(dataMap, ReturnCode.SUPPORT_CARD_PAY_ERROR);
                        durationEnd(dataMap, "supportCardPay");
                        return null;
                    }));
        }

        if (!futureList.isEmpty()) {
            CompletableFuture<Void> future = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
            future.join();
            /**
             * 保存商品信息到缓存
             */
            // gatherCacheHelper.saveGoodsToCache(request.getStoreId(), request.getServiceType(), new HashMap<>(dataMap));
            if (request.needCategory()) {
                // 合并分类，将推荐、热销合并到分类数据中
                mergeCategory(request, dataMap);
            }
            if (request.needGoods()) {
                // 合并优惠信息到商品中
                mergeItemActivity(request, dataMap);
            }
        }


    }


    /**
     * 查询商品分类
     *
     * @param request
     * @param dataMap
     */
    private void loadCategory(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadCategory");
        try {
            logInfo("查询商品分类", "GatherQueryCategory", request);
            List<? extends CategoryExt> effective = null;
            if (request.isRetailStore()) {
                ListResult<CategoryTreeDTO> categoryTreeDTOListResult = retailItemAdapterService.queryCategories(new RetailCategoryQueryReq(request.getStoreId(), request.getServiceType()));
                effective = categoryTreeDTOListResult.getRecords();
            } else {
                effective = categoryHelper.findEffectiveCategories(request.getStoreId(), request.getServiceType());
            }
            if (CollectionUtils.isEmpty(effective)) {
                setClosing(dataMap, Constants.Closing.NO_GOODS);
                return;
            }
            dataMap.put("category", effective);
        } catch (Exception e) {
            logWarn("查询商品分类出错：" + e.getMessage(), "GatherQueryCategory", request, e);
            setError(dataMap, ReturnCode.CATEGORY_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "loadCategory");
        }

    }

    /**
     * 查询首页商品
     *
     * @param request
     * @param dataMap
     */
    private void loadIndexItems(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadIndexItems");
        try {
            logInfo("查询首页商品", "GatherQueryItems", request);
            ListResult<ItemDto> result = indexItems(request, dataMap);
            if (!request.isRetailStore() && (Objects.isNull(result) || result.getTotal() == 0 || CollectionUtils.isEmpty(result.getRecords()))) {
                // 没有商品则清空缓存
                gatherCacheHelper.clean(request.getStoreId());
                LogUtils.logInfo("无商品清空缓存", "cleanCacheWithoutGoods", request.getStoreId());
            }
            if (Objects.nonNull(result)) {
                dataMap.put("index", result);
            }
        } catch (Exception e) {
            logWarn("查询首页商品出错：" + e.getMessage(), "GatherQueryItems", request, e);
            setError(dataMap, ReturnCode.GOODS_DATA_ERROR);
        } finally {
            durationEnd(dataMap, "loadIndexItems");
        }
    }

    private ListResult<ItemDto> indexItems(GatherRequest request, Map<String, Object> dataMap) {
        ListResult<ItemDto> result;
        if (request.isRetailStore()) {
            Map<String, Object> configMap = (Map<String, Object>) MapUtils.getMap(dataMap, "config");
            int pageSize = MapUtils.getIntValue(configMap, "PLUGIN_GOODS_PAGE_SIZE", 30);
            if (pageSize > 30) {
                pageSize = 30;
            }
            RetailItemCursorQueryReq req = new RetailItemCursorQueryReq();
            req.setStoreId(request.getStoreId());
            req.setServiceType(request.getServiceType());
            req.setPage(1);
            req.setPageSize(pageSize);
            req.setCursor(null);
            // 零售门店查询商品需要第一个分类id
            List<? extends CategoryExt> category = (List<? extends CategoryExt>) MapUtils.getObject(dataMap, "category");
            if (!CollectionUtils.isEmpty(category)) {
                req.setCategoryIds(Collections.singletonList(category.get(0).getId()));
            }
            RetailItemCursorQueryRes res = retailItemAdapterService.cursorQueryItem(req);
            if (StringUtils.isNotBlank(res.getCursor())) {
                dataMap.put("tempCursor", res.getCursor());
            }
            result = new ListResult<>();
            result.setRecords(res.getRecords().stream().map(o -> o.exportAsCatering(request.getServiceType(), request.getStoreId())).collect(Collectors.toList()));
            result.setTotal(res.getTotal());
        } else {
            Map<String, Object> configMap = (Map<String, Object>) MapUtils.getMap(dataMap, "config");
            int pageSize = MapUtils.getIntValue(configMap, "PLUGIN_GOODS_PAGE_SIZE", 30);
            ItemFind find = new ItemFind();
            find.setPage(1);
            find.setPageSize(pageSize);
            find.setStoreId(request.getStoreId());
            find.setServiceType(request.getServiceType());
            // 查询首页商品
            Pagination pagination = new Pagination();
            pagination.setPage(find.getPage() > 0 ? find.getPage() - 1 : find.getPage());
            pagination.setPageSize(find.getPageSize());
            find.setPagination(pagination);
            find.remove(ItemFind.PAGE);
            find.remove(ItemFind.PAGE_SIZE);
            result = itemService.listForMiniIndex(find.getStoreId(), find.getServiceType(), find.getPagination());
        }
        return result;

    }

    /**
     * 查询单品优惠
     *
     * @param request
     * @param dataMap
     */
    private void loadSingleActivity(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadSingleActivity");
        try {
            logInfo("查询单品优惠", "GatherSingleActivity", request);
            List<SkuInfo> list = itemHelper.getSingActivitySkuInfos(request.getStoreId(), request.getServiceType());
            dataMap.put("single", list);
        } catch (Exception e) {
            setError(dataMap, ReturnCode.SINGLE_ACTIVITY_DATA_ERROR);
            logWarn("查询单品优惠出错：" + e.getMessage(), "GatherSingleActivity", request, e);
        } finally {
            durationEnd(dataMap, "loadSingleActivity");
        }
    }

    /**
     * 查询第二份半价优惠
     *
     * @param request
     * @param dataMap
     */
    private void loadSecondActivity(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadSecondActivity");
        try {
            logInfo("查询第二份半价优惠", "GatherSecondActivity", request);
            List<SkuInfo> list = itemHelper.getSecondActivitySkuInfos(request.getStoreId(), request.getServiceType());
            dataMap.put("second", list);
        } catch (Exception e) {
            setError(dataMap, ReturnCode.SECOND_ACTIVITY_DATA_ERROR);
            logWarn("查询第二份半价优惠出错：" + e.getMessage(), "GatherSecondActivity", request, e);
        } finally {
            durationEnd(dataMap, "loadSecondActivity");
        }
    }

    /**
     * 查询商品分类的折扣
     *
     * @param request
     * @param dataMap
     */
    private void loadCategoryBestActivity(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadCategoryActivity");
        try {
            logInfo("查询商品分类的折扣", "GatherGoodsAndCategoryBestActivity", request);
            Map<String, List<CategoryInfo>> categoryDiscountMap = itemHelper.categoryDiscountDetails(request.getStoreId(), request.getServiceType(), EffectiveType.YES);
            dataMap.put("categoryActivity", categoryDiscountMap);
        } catch (Exception e) {
            setError(dataMap, ReturnCode.CATEGORY_ACTIVITY_DATA_ERROR);
            logWarn("查询商品分类的折扣：" + e.getMessage(), "GatherCategoryBestActivity", request, e);
        } finally {
            durationEnd(dataMap, "loadCategoryActivity");
        }
    }

    /**
     * 加载热销商品
     *
     * @param request
     * @param dataMap
     */
    private void loadHotSale(GatherRequest request, Map<String, Object> dataMap) {
        try {
            durationStart(dataMap, "loadHotSale");
            logInfo("查询热销商品", "GatherHotSale", request);
            String hosSaleConfigValue = getStoreConfigValue(dataMap, MccUtils.HOT_SALE_PRODUCT_CONFIG);
            if (StringUtils.isBlank(hosSaleConfigValue)) {
                return;
            }
            Map<String, Object> hostSaleReqMap = new HashMap<>();
            hostSaleReqMap.put("store_id", request.getStoreId());
            hostSaleReqMap.put("service_type", request.getServiceType());
            hostSaleReqMap.put(MccUtils.HOT_SALE_PRODUCT_CONFIG, hosSaleConfigValue);

            List<ItemDetailVO> list = apiSixItemService.hotsale(new ApiRequest<>(hostSaleReqMap));
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            dataMap.put("hotSale", list);
        } catch (Exception e) {
            setError(dataMap, ReturnCode.HOT_SALE_DATA_ERROR);
            logWarn("查询热销商品出错：" + e.getMessage(), "GatherHotSale", request, e);
        } finally {
            durationEnd(dataMap, "loadHotSale");
        }
    }

    /**
     * 加载推荐商品
     *
     * @param request
     * @param dataMap
     */
    private void loadRecommend(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadRecommend");
        logInfo("查询推荐商品", "GatherRecommend", request);
        ItemFind find = new ItemFind();
        find.setStoreId(request.getStoreId());
        find.setServiceType(request.getServiceType());
        try {
            ListResult<ItemDetailVO> result = apiSixItemService.recommend(new ApiRequest<>(find));
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getRecords())) {
                return;
            }
            dataMap.put("recommend", result.getRecords());
        } catch (Exception e) {
            setError(dataMap, ReturnCode.RECOMMEND_DATA_ERROR);
            logWarn("查询推荐商品出错：" + e.getMessage(), "GatherRecommend", request, e);
        } finally {
            durationEnd(dataMap, "loadRecommend");
        }
    }

    /**
     * 加载商品标签
     *
     * @param request
     * @param dataMap
     */
    private void loadProductTag(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadProductTag");
        logInfo("查询商品标签", "GatherProductTag", request);
        ItemFind find = new ItemFind();
        find.setStoreId(request.getStoreId());
        find.setServiceType(request.getServiceType());
        try {
            ListResult<CategoryVo> result = apiSixItemService.productTag(find);
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getRecords())) {
                return;
            }
            dataMap.put("tag", result.getRecords());
        } catch (Exception e) {
            setError(dataMap, ReturnCode.RECOMMEND_DATA_ERROR);
            logWarn("查询商品标签出错：" + e.getMessage(), "GatherProductTag", request, e);
        } finally {
            durationEnd(dataMap, "loadProductTag");
        }
    }

    /**
     * 加载推荐分类下的商品
     *
     * @param request
     * @param dataMap
     */
    private void loadDiscountCategory(GatherRequest request, Map<String, Object> dataMap) {
        try {
            durationStart(dataMap, "loadDiscountCategory");
            logInfo("查询优惠分类", "GatherDiscountCategory", request);
            if (!MccUtils.getBooleanValue(MapUtils.getMap(dataMap, "mcc"), MccUtils.SHOW_DISCOUNT_CATEGORY, false)) {
                return;
            }
            ItemFind find = new ItemFind();
            find.setStoreId(request.getStoreId());
            find.setServiceType(request.getServiceType());

            ListResult<ItemDetailVO> result = categoryHelper.getDiscountItems(find, false);
            if (CollectionUtils.isEmpty(result.getRecords())) {
                return;
            }
            dataMap.put("discount", result.getRecords());
        } catch (Exception ex) {
            setError(dataMap, ReturnCode.DISCOUNT_CATEGORY_DATA_ERROR);
            logWarn("查询优惠分类商品出错：" + ex.getMessage(), "GatherDiscountCategory", request, ex);
        } finally {
            durationEnd(dataMap, "loadDiscountCategory");
        }
    }

    /**
     * 加载点过的菜
     *
     * @param request
     * @param dataMap
     */
    public void loadRecentItems(GatherRequest request, Map<String, Object> dataMap) {
        try {
            if (apolloConfigHelper.getServiceDegradeConfig().isRecentItemsDegrade()) {
                return;
            }
            if (Objects.nonNull(request.getRecentItemCountDownLatch())) {
                // 等待用户信息和门店以及ServiceType结果出来再进行下面的处理
                // request.getRecentItemCountDownLatch().await();
                boolean allFinished = request.getRecentItemCountDownLatch().await(400, TimeUnit.MILLISECONDS);
                if (!allFinished) {
                    return;
                }
            }
            if (StringUtils.isBlank(request.getUserId()) || StringUtils.isBlank(request.getStoreId()) || Objects.isNull(request.getServiceType())) {
                return;
            }
            if (request.isRetailStore()) {
                return;
            }
            durationStart(dataMap, "loadRecentItems");
            logInfo("查询点过的菜", "GatherRecentItems", request);
            if (!MccUtils.getBooleanValue(MapUtils.getMap(dataMap, "mcc"), MccUtils.SHOW_ORDERED_DISH, false)) {
                return;
            }
            ItemFind find = new ItemFind();
            find.setStoreId(request.getStoreId());
            find.setServiceType(request.getServiceType());
            ListResult<ItemDetailVO> result = categoryHelper.getBoughtItems(find, request.getUserId(), false);
            if (CollectionUtils.isEmpty(result.getRecords())) {
                dataMap.put("recentItems", new ArrayList<>());
                return;
            }
            dataMap.put("recentItems", result.getRecords());
        } catch (Exception e) {
            setError(dataMap, ReturnCode.DISCOUNT_CATEGORY_DATA_ERROR);
            logWarn("查询点过的菜出错：" + e.getMessage(), "GatherRecentItems", request, e);
        } finally {
            durationEnd(dataMap, "loadRecentItems");
        }
    }

    /**
     * 合并商品分类
     *
     * @param dataMap
     */
    public void mergeCategory(GatherRequest request, Map<String, Object> dataMap) {
        List<ItemDetailVO> recommendList = (List<ItemDetailVO>) MapUtils.getObject(dataMap, "recommend");
        List<ItemDetailVO> hotSaleList = (List<ItemDetailVO>) MapUtils.getObject(dataMap, "hotSale");
        List<? extends CategoryExt> categoryList = (List<? extends CategoryExt>) MapUtils.getObject(dataMap, "category");
        List<ItemDetailVO> discountItems = (List<ItemDetailVO>) MapUtils.getObject(dataMap, "discount");
        List<CategoryVo> productTagList = (List<CategoryVo>) MapUtils.getObject(dataMap, "tag");
        List<CategoryVo> newList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recommendList)) {
            this.addHotSaleTag(hotSaleList, recommendList);
            newList.add(new CategoryVo() {{
                setId("recommend");
                setStoreId(request.getStoreId());
                setName("推荐");
                setItemCount(recommendList.size());
                setDisplayOrder(-999);
                setItems(recommendList);
            }});
        }
        if (!CollectionUtils.isEmpty(hotSaleList)) {
            newList.add(new CategoryVo() {{
                setId("hotSale");
                setStoreId(request.getStoreId());
                setName("热销");
                setItemCount(hotSaleList.size());
                setDisplayOrder(-998);
                setItems(hotSaleList);

            }});
        }
        if (!CollectionUtils.isEmpty(discountItems)) {
            this.addHotSaleTag(hotSaleList, discountItems);
            newList.add(new CategoryVo() {{
                setId("discount");
                setStoreId(request.getStoreId());
                setName("优惠");
                setItemCount(discountItems.size());
                setDisplayOrder(-997);
                setItems(discountItems);
            }});
        }

        if (!CollectionUtils.isEmpty(productTagList)) {
            newList.addAll(productTagList);
        }
        if (!CollectionUtils.isEmpty(categoryList)) {
            newList.addAll(categoryList.stream().map(it -> convertCategoryVo(it)).collect(Collectors.toList()));
        }
        dataMap.put("category", newList);
    }

    private CategoryVo convertCategoryVo(CategoryExt category) {
        CategoryVo vo = new CategoryVo();
        vo.setId(category.getId());
        vo.setStoreId(category.getStoreId());
        vo.setMerchantId(category.getMerchantId());
        vo.setName(category.getName());
        vo.setItemCount(category.getItemCount());
        vo.setDisplayOrder(category.getDisplayOrder());
        vo.setCategoryType(category.getCategoryType());
        vo.setType(category.getType());
        if (category instanceof CategoryTreeDTO) {
            CategoryTreeDTO tree = (CategoryTreeDTO) category;
            if (!CollectionUtils.isEmpty(tree.getSubCategories())) {
                vo.setSubCategories(tree.getSubCategories().stream().map(this::convertCategoryVo).collect(Collectors.toList()));
            }
        }
        return vo;
    }

    /**
     * 合并商品优惠信息
     *
     * @param request
     * @param dataMap
     */
    public void mergeItemActivity(GatherRequest request, Map<String, Object> dataMap) {
        ListResult<ItemDto> result = (ListResult<ItemDto>) MapUtils.getObject(dataMap, "index");
        Map<String, HotSaleItem> hotSaleItemMap = null;
        try {
            hotSaleItemMap = itemHelper.hotSaleItemMapFromCache(request.getStoreId(), request.getServiceType());
        } catch (Exception e) {
            setError(dataMap, ReturnCode.HOT_SALE_DATA_ERROR);
            logWarn("查询热销商品出错：" + e.getMessage(), "GatherHotSale", request, e);
        }
        if (Objects.isNull(result) || result.getTotal() == 0 || CollectionUtils.isEmpty(result.getRecords())) {
            return;
        }
        List<ItemDetailVO> itemDetailVOList = new ArrayList<>();
        List<SkuInfo> singleActivitySkuInfoList = (List<SkuInfo>) MapUtils.getObject(dataMap, "single");
        List<SkuInfo> secondActivitySkuInfoList = (List<SkuInfo>) MapUtils.getObject(dataMap, "second");
        Map<String, List<CategoryInfo>> categoryActivity = (Map<String, List<CategoryInfo>>) MapUtils.getObject(dataMap, "categoryActivity");
        for (ItemDto record : result.getRecords()) {
            ItemDetailVO itemDetailVO = itemHelper.processItemDetail(record, singleActivitySkuInfoList, secondActivitySkuInfoList, categoryActivity, hotSaleItemMap, request.getServiceType());
            if (Objects.nonNull(itemDetailVO)) {
                itemDetailVOList.add(itemDetailVO);
            }
        }
        Map<String, Object> data = new HashMap<>();
        Map<String, List<ItemDetailVO>> categoryMap =
                itemDetailVOList.stream().collect(Collectors.groupingBy(it -> it.getItem().getCategoryId(),
                        LinkedHashMap::new, Collectors.toList()));
        List<Map<String, Object>> goodsMap = new ArrayList<>();
        categoryMap.forEach((k, v) -> {
            Map<String, Object> goodsItem = new HashMap<>();
            goodsItem.put("category_id", k);
            goodsItem.put("category_name", v.get(0).getItem().getCategoryName());
            goodsItem.put("items", v);
            goodsMap.add(goodsItem);
        });
        List<List<Map<String, Object>>> pages = new ArrayList<>();
        pages.add(goodsMap);
        data.put("total", result.getTotal());
        data.put("pages", pages);
        dataMap.put("goods", data);
    }

    /**
     * 查询门店优惠
     *
     * @param request
     * @param dataMap
     */
    private void loadStoreActivity(GatherRequest request, Map<String, Object> dataMap) {
        durationStart(dataMap, "loadStoreActivity");
        logInfo("查询门店优惠", "GatherStoreActivity", request);
        DiscountInfo discountInfo = new DiscountInfo();
        discountInfo.setStoreId(request.getStoreId());
        discountInfo.setMerchant_id(request.getMerchantId());
        discountInfo.setId("uf4c-app");
        if (request.getServiceType() == 1) {
            discountInfo.setEffectiveOrderTypes(Lists.newArrayList(OrderType.TAKE_OUT_ORDER.getMsg(), OrderType.PRE_ORDER.getMsg()));
        } else {
            discountInfo.setEffectiveOrderTypes(Lists.newArrayList(OrderType.SUBSCRIBE_ORDER.getMsg()));
        }
        try {
            Discounts discounts = discountsRemoteService.findDiscounts(discountInfo);
            dataMap.put("activity", JacksonUtil.beanToMap(discounts, new ObjectMapper()));
        } catch (Exception e) {
            setError(dataMap, ReturnCode.ACTIVITY_DATA_ERROR);
            logWarn("查询门店优惠出错：" + e.getMessage(), "GatherStoreActivity", request, e);
        } finally {
            durationEnd(dataMap, "loadStoreActivity");
        }
    }

    private String getStoreConfigValue(Map<String, Object> dataMap, String key) {
        Map mcc = MapUtils.getMap(dataMap, "mcc");
        return MapUtils.getString(mcc, key);
    }

    private void addHotSaleTag(List<ItemDetailVO> hotSaleItems, List<ItemDetailVO> otherItems) {
        if (CollectionUtils.isEmpty(hotSaleItems) || CollectionUtils.isEmpty(otherItems)) {
            return;
        }
        Map<String, ItemDetailVO> itemIdToHotSaleItemMap = hotSaleItems.stream()
                .collect(Collectors.toMap(p -> p.getItem().getId(), Function.identity(), (k1, k2) -> k1));
        for (ItemDetailVO otherItem : otherItems) {
            ItemDetailVO tmpHotSaleItem = itemIdToHotSaleItemMap.get(otherItem.getItem().getId());
            if (Objects.nonNull(tmpHotSaleItem)) {
                otherItem.getItem().setHotsaleProduct(true);
                otherItem.getItem().setHotsaleSeq(tmpHotSaleItem.getItem().getHotsaleSeq());
                otherItem.getItem().setLast30DaysSaleCount(tmpHotSaleItem.getItem().getLast30DaysSaleCount());
                otherItem.getItem().setItemTag(TagUtil.addTag(otherItem.getItem().getItemTag(), OrderGoodsTagEnum.HOT_SALE.getValue()));
            }
        }
    }

}
