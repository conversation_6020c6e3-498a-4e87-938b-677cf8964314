package com.wosai.pantheon.uf4c.util;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;

public class GatherTraceRunnable {
    private static final Config apolloConfig = ConfigService.getAppConfig();

    public static Runnable of(Runnable runnable) {
        if (apolloConfig.getBooleanProperty("gatherTrace", true)) {
            return RunnableWrapper.of(runnable);
        }
        return runnable;
    }

}
