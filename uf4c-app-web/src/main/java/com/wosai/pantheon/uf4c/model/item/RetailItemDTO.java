package com.wosai.pantheon.uf4c.model.item;

import com.wosai.market.enums.ProductTypeEnum;
import com.wosai.market.enums.SaleTerminalTypeEnum;
import com.wosai.market.enums.SaleTimeTypeEnum;
import com.wosai.pantheon.core.uitem.model.Item;
import com.wosai.pantheon.core.uitem.model.ItemImageDTO;
import com.wosai.pantheon.core.uitem.model.ItemSaleTime;
import com.wosai.pantheon.core.uitem.model.SaleTerminalList;
import com.wosai.pantheon.uf4c.model.dto.RetailExtendItemDTO;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.RetailItemConverter;
import com.wosai.smart.goods.common.constant.ProductConstant;
import com.wosai.smart.goods.common.constant.SaleChannelFlag;
import com.wosai.smart.goods.common.utils.SellWeightUtils;
import com.wosai.smart.goods.enums.SaleTypeEnum;
import com.wosai.smart.goods.enums.SpuSaleStatusEnum;
import com.wosai.smart.goods.product.dto.*;
import com.wosai.smart.goods.stock.dto.StockDTO;
import com.wosai.smart.goods.stock.dto.StockWithSpuIdDTO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 内部DTO，聚合商品、库存信息
 * 后续也可在这里扩充营销、标签信息
 */
public class RetailItemDTO implements Serializable {

    /**
     * 商品信息
     */
    private ProductDTO productDTO;

    /**
     * 库存信息
     * key=skuId,value=库存值
     */
    private Map<Long, StockDTO> skuStockMap;

    /**
     * 分类ID路径，按一二级已排序
     */
    @Getter
    @Setter
    private List<Long> categoryIdPath;

    public RetailItemDTO(ProductDTO productDTO) {
        Objects.requireNonNull(productDTO);
        this.productDTO = productDTO;
        this.skuStockMap = new HashMap<>();
    }

    public void appendStockInfo(List<StockWithSpuIdDTO> stocks) {
        if (CollectionUtils.isEmpty(stocks)) {
            return;
        }
        stocks.forEach(s -> this.skuStockMap.put(s.getSkuId(), s));
    }

    /**
     * 此处按照餐饮商品格式做转换
     */
    public RetailExtendItemDTO exportAsCatering(int serviceType, String storeId) {
        Objects.requireNonNull(this.productDTO);
        // 零售外卖称重商品库存转换关系
        Map<String, Object> skuConvert = new HashMap<>();
        // 基础信息
        Item item = new Item();
        item.setStatus(1);
        item.setItemType(ProductTypeEnum.NORMAL.name());
        item.setOutOfStock(false);
        item.setStoreId(storeId);
        SpuDTO spuDTO = this.productDTO.getSpu();
        item.setId(String.valueOf(spuDTO.getSpuId()));
        item.setName(spuDTO.getTitle());
        item.setForSale(Objects.equals(spuDTO.getSaleStatus(), SpuSaleStatusEnum.LISTING.getCode()));
        item.setDescription(spuDTO.getDescription());
        item.setDisplayOrder(spuDTO.getSeq().intValue());
        item.setCategoryId(String.valueOf(spuDTO.getCategoryId()));
        item.setBarcode(spuDTO.getBarcode());
        item.setUnit(spuDTO.getUnit());
        item.setUnitType(RetailItemConverter.saleType2UnitType(spuDTO.getSaleType()));
        item.setServiceType(RetailItemConverter.saleChannelFlag2ServiceType(spuDTO.getSaleChannelFlag()));
        SaleTerminalList saleTerminalList = new SaleTerminalList();
        saleTerminalList.add(SaleTerminalTypeEnum.MINI_APP_ORDER.getCode());
        item.setSaleTerminals(saleTerminalList);
        // 媒体信息
        String photoUrl = Optional.ofNullable(spuDTO.getMedias())
                .orElseGet(ArrayList::new)
                .stream()
                .map(MediaDTO::getUrl)
                .collect(Collectors.joining(","));
        item.setPhotoUrl(photoUrl);

        // 规格信息
        List<SkuDTO> skuDTOList = this.productDTO.getSkus();
        if (CollectionUtils.isNotEmpty(skuDTOList)) {
            item.setSpecNum(skuDTOList.size());
            for (SkuDTO skuDTO : skuDTOList) {
                // 售卖价
                Long salePrice = RetailItemConverter.getPriceByServiceType(skuDTO.getPriceInfo(), serviceType);
                if (Objects.nonNull(salePrice)) {
                    item.setPrice(salePrice.intValue());
                }
                // 打包费
                if (Objects.nonNull(skuDTO.getTakeoutPackFee())) {
                    item.setPackFee(skuDTO.getTakeoutPackFee().intValue());
                    item.setTakeoutPackFee(skuDTO.getTakeoutPackFee().intValue());
                }
                // 若为称重商品，须根据配置的转化关系改变单位、库存
                if (Objects.equals(spuDTO.getSaleType(), SaleTypeEnum.WEIGHT.getCode())) {
                    String newName = RetailItemConverter.generateNameOfWeightProduct(item.getName(), skuDTO);
                    item.setName(newName);
                    item.setUnit("份");
                    StockDTO stockDTO = this.skuStockMap.get(skuDTO.getSkuId());
                    //一份外卖总重量(单位：g)
                    BigDecimal perTakeoutTotalWeight = SellWeightUtils.convert(BigDecimal.valueOf(skuDTO.getPerTakeoutWeight()), skuDTO.getTakeoutWeightUnit(), "g");
                    //一份商品总重量(单位: g)
                    Integer perItemTotalWeight = SellWeightUtils.convert(spuDTO.getUnit(), skuDTO.getConversionRatio(), skuDTO.getConversionRatioUnit());
                    if (Objects.nonNull(stockDTO) && Objects.nonNull(stockDTO.getQuantity())) {
                        if(perTakeoutTotalWeight.equals(BigDecimal.ZERO)){
                            item.setSku(0);
                        }else{
                            BigDecimal quantityAfterConversion = stockDTO.getQuantity().multiply(BigDecimal.valueOf(perItemTotalWeight)
                                    .divide(perTakeoutTotalWeight, 3, RoundingMode.HALF_DOWN));
                            item.setSku(quantityAfterConversion.intValue());
                        }
                        item.setOutOfStock(item.getSku() <= 0);
                    }
                    try {
                        // 保存库存转换关系
                        skuConvert.put("unit", spuDTO.getUnit());
                        skuConvert.put("takeout_unit", skuDTO.getTakeoutWeightUnit());
                        skuConvert.put("per_takeout_weight", skuDTO.getPerTakeoutWeight());
                        if(perItemTotalWeight == 0){
                            skuConvert.put("per_weight", 0);
                        }else{
                            //一份外卖可以抵扣商品的份数
                            double perWeight = perTakeoutTotalWeight.divide(BigDecimal.valueOf(perItemTotalWeight), 3, RoundingMode.HALF_DOWN).doubleValue();
                            skuConvert.put("per_weight",perWeight);
                        }
                    } catch (Exception ignored) {
                        LogUtils.logWarn("库存转换出错", "RetailItemDTO.exportAsCatering", skuDTO);
                    }

                } else {
                    // 起售份数
                    if (Objects.nonNull(skuDTO.getMinSaleNum())) {
                        item.setMinSaleNum(skuDTO.getMinSaleNum().intValue());
                    }
                    StockDTO stockDTO = this.skuStockMap.get(skuDTO.getSkuId());
                    if (Objects.nonNull(stockDTO) && Objects.nonNull(stockDTO.getQuantity())) {
                        item.setSku(stockDTO.getQuantity().intValue());
                        item.setOutOfStock(item.getSku() <= 0);
                    }
                }
            }
        }

        RetailExtendItemDTO itemDto = new RetailExtendItemDTO();
        itemDto.setItem(item);
        // 以下信息均无，处理成空数组
        itemDto.setSpecs(Collections.emptyList());
        itemDto.setAttributes(Collections.emptyList());
        itemDto.setMaterialIds(Collections.emptyList());
        itemDto.setMaterials(Collections.emptyList());
        itemDto.setMaterialGroups(Collections.emptyList());
        itemDto.setGallery(Collections.emptyList());
        itemDto.setPackageMustOrderProducts(Collections.emptyList());
        itemDto.setPackageOptionalGroups(Collections.emptyList());
        itemDto.setPackageOptionalGroupIds(Collections.emptyList());
        itemDto.setItemTags(Collections.emptyList());
        itemDto.setIngredientIds(Collections.emptyList());
        itemDto.setIngredients(Collections.emptyList());
        // 售卖时间默认无限制
        ItemSaleTime saleTime = new ItemSaleTime();
        saleTime.setType(SaleTimeTypeEnum.ALL_TIME.getCode());
        itemDto.setSaleTime(saleTime);
        // 多级分类。在商品列表查询时会返回，组装到商品vo对象中。商品详情不需要返回
        if (this.categoryIdPath != null) {
            itemDto.setCategoryPath(this.categoryIdPath.stream().map(String::valueOf).collect(Collectors.toList()));
        }
        //快照信息
        itemDto.setSnapshot(getSnapshot());
        if (MapUtils.isNotEmpty(skuConvert)) {
            itemDto.setSkuConvert(skuConvert);
        }
        return itemDto;
    }

    /**
     * 获取商品快照信息。(数据分析使用)
     */
    public Map<String, Object> getSnapshot() {
        Map<String, Object> snapshot = new HashMap<>();
        snapshot.put("spuId", this.productDTO.getSpu().getSpuId().toString());
        snapshot.put("title", this.productDTO.getSpu().getTitle());
        if (CollectionUtils.isNotEmpty(this.productDTO.getSkus())) {
            SkuDTO skuDTO = productDTO.getSkus().get(0);
            for (PriceDTO priceDTO : skuDTO.getPriceInfo()) {
                if (ProductConstant.PriceType.SALE_PRICE.getCode().equals(priceDTO.getType())
                        || ProductConstant.PriceType.PURCHASE_PRICE.getCode().equals(priceDTO.getType())
                        || ProductConstant.PriceType.COST_PRICE.getCode().equals(priceDTO.getType())
                ) {
                    snapshot.put(priceDTO.getType(), priceDTO.getPrice());
                }
            }
        }
        snapshot.put("barcode", this.productDTO.getSpu().getBarcode());
        return snapshot;
    }

    /**
     * 获取商品配置的服务渠道
     */
    public Integer getServiceType() {
        return RetailItemConverter.saleChannelFlag2ServiceType(Optional.ofNullable(this.productDTO)
                .map(ProductDTO::getSpu)
                .map(SpuDTO::getSaleChannelFlag)
                .orElse(new SaleChannelFlag(2L)));
    }

    /**
     * 获取指定服务渠道下的售卖价格
     */
    public Long getSalePriceByServiceType(int serviceType) {
        return RetailItemConverter.getPriceByServiceType(Optional.ofNullable(this.productDTO)
                .map(ProductDTO::getSkus)
                .orElseGet(ArrayList::new)
                .stream()
                .findFirst()
                .map(SkuDTO::getPriceInfo)
                .orElse(null), serviceType);
    }

    /**
     * 获取指定服务渠道下的打包费<p>
     * PS：零售目前不支持到店场景，故不需要考虑打包费
     */
    public Long getPackFeeByServiceType(int serviceType) {
        return Optional.ofNullable(this.productDTO)
                .map(ProductDTO::getSkus)
                .orElseGet(ArrayList::new)
                .stream()
                .findFirst()
                .map(sku -> {
                    if (Objects.equals(serviceType, RetailItemConverter.SERVICE_TYPE_TAKEOUT)) {
                        return sku.getTakeoutPackFee();
                    }
                    return null;
                })
                .orElse(null);
    }

    /**
     * 获取商品真实的售卖单位
     */
    public String getOriginalUnit() {
        return Optional.ofNullable(this.productDTO).map(ProductDTO::getSpu).map(SpuDTO::getUnit).orElse(null);
    }

    /**
     * 获取商品真实的库存
     * 目前零售商品仅支持单规格，所以直接取第一个sku的库存
     */
    public BigDecimal getOriginalQuantity() {
        return this.skuStockMap.values().stream()
                .findFirst()
                .map(StockDTO::getQuantity)
                .orElse(null);
    }

    /**
     * 获取售卖的库存<p>
     * 计件商品：直接返回<p>
     * 称重商品：按配置的重量按份转化
     */
    public BigDecimal getSaleQuantity() {
        if (Objects.isNull(this.productDTO) || Objects.isNull(this.productDTO.getSpu())) {
            return null;
        }
        SkuDTO sku = Optional.ofNullable(this.productDTO.getSkus()).orElseGet(ArrayList::new).stream().findFirst().orElse(null);
        if (Objects.isNull(sku)) {
            return null;
        }
        StockDTO stockDTO = this.skuStockMap.get(sku.getSkuId());
        if (Objects.isNull(stockDTO)) {
            return null;
        }
        // 非称重
        if (!Objects.equals(this.productDTO.getSpu().getSaleType(), SaleTypeEnum.WEIGHT.getCode())) {
            return stockDTO.getQuantity();
        }
        if (Objects.isNull(sku.getTakeoutWeightUnit())) {
            return stockDTO.getQuantity();
        }
        // 称重转化
        BigDecimal quantityAfterConversion = SellWeightUtils.convert(stockDTO.getQuantity(), this.productDTO.getSpu().getUnit(), sku.getTakeoutWeightUnit());
        return quantityAfterConversion.divide(BigDecimal.valueOf(sku.getPerTakeoutWeight()), 3, RoundingMode.HALF_DOWN);
    }

    /**
     * 获取商品的重量转化值
     */
    public BigDecimal getPerTakeoutWeight() {
        return Optional.ofNullable(this.productDTO)
                .map(ProductDTO::getSkus)
                .orElseGet(ArrayList::new)
                .stream()
                .findFirst()
                .map(sku -> Objects.isNull(sku.getPerTakeoutWeight()) ? null : BigDecimal.valueOf(sku.getPerTakeoutWeight()))
                .orElse(null);
    }

    /**
     * 获取商品的重量转化单位
     */
    public String getTakeoutWeightUnit() {
        return Optional.ofNullable(this.productDTO)
                .map(ProductDTO::getSkus)
                .orElseGet(ArrayList::new)
                .stream()
                .findFirst()
                .map(SkuDTO::getTakeoutWeightUnit)
                .orElse(null);
    }

}
