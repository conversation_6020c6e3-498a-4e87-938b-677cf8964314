package com.wosai.pantheon.uf4c.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.uf4c.api.GatherRemoteService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.service.apisix.GatherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@AutoJsonRpcServiceImpl
public class GatherRemoteServiceImpl implements GatherRemoteService {
    @Autowired
    private GatherService gatherService;

    @Override
    public Map<String, Object> index(GatherRequest request) {
        return gatherService.index(new ApiRequest<>(request));
    }

    @Override
    public void clean(String storeId) {
        gatherService.cleanCache(storeId);
    }
}
