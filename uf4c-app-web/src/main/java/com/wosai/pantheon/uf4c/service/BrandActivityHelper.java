package com.wosai.pantheon.uf4c.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wosai.market.merchant.api.BrandActivityRemoteService;
import com.wosai.market.merchant.dto.BrandActStoreUseDTO;
import com.wosai.market.merchant.dto.request.QueryStoreBrandActivityRequest;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.model.ItemSaleTime;
import com.wosai.pantheon.core.uitem.model.SingleItemDetailRequest;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.GatherOrderRequest;
import com.wosai.pantheon.uf4c.model.item.ItemVoBuildWrapper;
import com.wosai.pantheon.uf4c.model.vo.*;
import com.wosai.pantheon.uf4c.service.apisix.impl.ItemServiceImpl;
import com.wosai.pantheon.uf4c.util.LogUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.smart.goods.common.constant.ProductConstant;
import com.wosai.web.api.ListResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 品牌购相关
 */
@Component
public class BrandActivityHelper {
    @Autowired
    private BrandActivityRemoteService brandActivityRemoteService;
    @Autowired
    private ItemServiceImpl itemService;
    @Autowired
    private ItemsNewHelper itemsNewHelper;
    @Autowired
    private ApolloConfigHelper apolloConfigHelper;
    @Autowired
    private ItemService uitemCoreitemService;

    public static final List<OrderType> supportOrderTypes = Arrays.asList(OrderType.SUBSCRIBE_ORDER, OrderType.TAKE_OUT_ORDER, OrderType.PRE_ORDER);

    /**
     * 活动是否启用
     *
     * @param
     * @return
     */
    public boolean isEnabled(String appid) {
        if (!apolloConfigHelper.getBooleanConfigValueByKey("brand.activity.enabled", false)) {
            return false;
        }
//        if (OrderType.SUBSCRIBE_ORDER != request.getType()
//                || !apolloConfigHelper.getBooleanConfigValueByKey("brand.activity.enabled", false)
//                || MiniProgramType.WECHAT != request.getMiniProgramType()) {
//            return false;
//        }
        String appids = apolloConfigHelper.getStringConfigValueByKey("brand.activity.support.appid", "");
        return !StringUtils.isAnyBlank(appids, appid) && appids.contains(appid);
    }


    public void processBrandProductCartNumber(Map<String, Object> dataMap) {
        Cart cart = (Cart) MapUtils.getObject(dataMap, "cart");
        if (Objects.equals(dataMap.get("brandActivity"), "")) {
            return;
        }
        BrandActivityVO vo = (BrandActivityVO) MapUtils.getObject(dataMap, "brandActivity");
        if (Objects.isNull(cart) || Objects.isNull(vo) || CollectionUtils.isEmpty(vo.getItems())) {
            return;
        }
        // 非门店商品不进行此处理
//        if (!Constants.BRAND_PRODUCT_SOURCE_STORE.equalsIgnoreCase(vo.getProductSource())) {
//            return;
//        }
        Map<Long, Integer> buyMap = cart.getRecords().stream().filter(Cart.Record::isBrandAct)
                .collect(Collectors.toMap(Cart.Record::getBrandAcdProductId, Cart.Record::getNum));
        vo.getItems().forEach(it -> {
            if (buyMap.containsKey(it.getProductId())) {
                it.setActivityNumber(buyMap.get(it.getProductId()));
            }
        });
    }

    /**
     * 如果是活动商品，则将商品本身的名称与图片替换为活动商品名称与图片
     * 并且将购物车item对象标记为加价购活动
     *
     * @param cartItemCreate
     */
    public void processBrandCartItem(CartItemCreate cartItemCreate) {
        cartItemCreate.setBrandAct(false);
        BrandActivityProduct product = cartItemCreate.getBrandActProduct();
        // 判断活动商品信息
        if (Objects.isNull(product)) {
            return;
        }
        if (StringUtils.isBlank(product.getActivityId()) || Objects.isNull(product.getProductId()) || product.getProductId() <= 0) {
            return;
        }
        // 查询当前可用活动
        QueryStoreBrandActivityRequest queryStoreBrandActivityRequest = new QueryStoreBrandActivityRequest();
        queryStoreBrandActivityRequest.setStoreId(cartItemCreate.getStoreId());
        queryStoreBrandActivityRequest.setUserId(ThreadLocalHelper.getUserId());
        queryStoreBrandActivityRequest.setMiniProgramType(ThreadLocalHelper.getMiniProgramType().name());
        queryStoreBrandActivityRequest.setDisplayPosition(Constants.BRAND_DISPLAY_POSITION_BRAND_CART);
        if (Objects.equals(cartItemCreate.getServiceType(), Constants.ServiceType.SCANNING)) {
            queryStoreBrandActivityRequest.setSaleScene(Constants.BRAND_SALE_SCENE_SCAN);
        }
        if (Objects.equals(cartItemCreate.getServiceType(), Constants.ServiceType.TAKEOUT)) {
            queryStoreBrandActivityRequest.setSaleScene(Constants.BRAND_SALE_SCENE_TAKEOUT);
        }
        // 查询活动商品是否有效
        BrandActStoreUseDTO dto = brandActivityRemoteService.relateEffective(queryStoreBrandActivityRequest);
        if (Objects.isNull(dto)) {
            return;
        }
        if (CollectionUtils.isEmpty(dto.getItems())) {
            return;
        }
        BrandActStoreUseDTO.ReferItem productDTO = dto.getItems().stream().filter(it -> Objects.equals(it.getActProId(), product.getProductId())).findFirst().orElse(null);
        if (Objects.isNull(productDTO)) {
            return;
        }
        // 查到生效的活动商品
        cartItemCreate.setBrandAct(true);
        cartItemCreate.setBrandProductSource(dto.getProductSource());
        // 设置商品名称与图片
        cartItemCreate.getBrandActProduct().setName(productDTO.getName());
        cartItemCreate.getBrandActProduct().setUrl(productDTO.getUrl());
        cartItemCreate.getItem().setSku(productDTO.getMaxBuyLimit());
        cartItemCreate.getItem().setStockId(productDTO.getStockId());
        cartItemCreate.getItem().setUnit(productDTO.getUnit());
        cartItemCreate.getItem().setUrl(productDTO.getUrl());
        // 如果是门店商品，则需要补充原始的名称与图片
        if (Constants.BRAND_PRODUCT_SOURCE_STORE.equalsIgnoreCase(dto.getProductSource())) {
            // 补充原始的名称与图片
            fillOriNameAndUrl(cartItemCreate);
        }
        if (Constants.BRAND_PRODUCT_SOURCE_BRAND.equalsIgnoreCase(dto.getProductSource())) {
            // 补充分类id
            cartItemCreate.getItem().setCategoryId(productDTO.getSpuId());
        }
    }


    /**
     * 填充原始名称和图片
     *
     * @param cartItemCreate
     */
    private void fillOriNameAndUrl(CartItemCreate cartItemCreate) {
        SingleItemDetailRequest request = new SingleItemDetailRequest();
        request.setStoreId(cartItemCreate.getStoreId());
        request.setItemId(cartItemCreate.getItem().getId());
        request.setServiceType(cartItemCreate.getServiceType());
        request.setFillPrintTemplate(false);
        try {
            ItemDto itemDto = uitemCoreitemService.getItemDetailV3(request);
            if (Objects.nonNull(itemDto)) {
                cartItemCreate.getBrandActProduct().setOriName(itemDto.getItem().getName());
                cartItemCreate.getBrandActProduct().setOriUrl(itemDto.getItem().getPhotoUrl());
            }
        } catch (Exception e) {
            LogUtils.logWarn("补充详情出错", "fillOriNameAndUrl", request, e);
        }

    }

    /**
     * 查询门店活动信息
     *
     * @return
     */
    public BrandActivityVO queryBrandActivity(GatherOrderRequest request, String displayPosition) {
        return queryBrandActivity(request.getStoreId(), request.getServiceType(), request.getUserId(), request.getMiniProgramType().name(), displayPosition);
    }

    private BrandActivityVO queryBrandActivity(String storeId, Integer serviceType, String userId, String miniProgramType, String position) {
        // 查询当前可用活动
        QueryStoreBrandActivityRequest queryStoreBrandActivityRequest = new QueryStoreBrandActivityRequest();
        queryStoreBrandActivityRequest.setStoreId(storeId);
        queryStoreBrandActivityRequest.setUserId(userId);
        queryStoreBrandActivityRequest.setMiniProgramType(miniProgramType);
        queryStoreBrandActivityRequest.setDisplayPosition(position);
        if (Objects.equals(serviceType, Constants.ServiceType.SCANNING)) {
            queryStoreBrandActivityRequest.setSaleScene(Constants.BRAND_SALE_SCENE_SCAN);
        }
        if (Objects.equals(serviceType, Constants.ServiceType.TAKEOUT)) {
            queryStoreBrandActivityRequest.setSaleScene(Constants.BRAND_SALE_SCENE_TAKEOUT);
        }
        BrandActStoreUseDTO brandActStoreUseDTO = brandActivityRemoteService.relateEffective(queryStoreBrandActivityRequest);
        if (Objects.isNull(brandActStoreUseDTO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(brandActStoreUseDTO.getItems())) {
            return null;
        }
        // 组装返回的商品信息
        BrandActivityVO vo = new BrandActivityVO();
        vo.setActivityId(brandActStoreUseDTO.getActId());
        vo.setActivityName(brandActStoreUseDTO.getActName());
        vo.setActivityImage(brandActStoreUseDTO.getActImage());
        vo.setProductSource(brandActStoreUseDTO.getProductSource());

        if (Objects.equals(brandActStoreUseDTO.getProductSource(), Constants.BRAND_PRODUCT_SOURCE_STORE)) {
            // 查询所有的
            List<String> spuIds = brandActStoreUseDTO.getItems().stream().map(BrandActStoreUseDTO.ReferItem::getSpuId).distinct().collect(Collectors.toList());
            List<ItemNewVo> items = queryItemsInfo(storeId, spuIds);
            if (CollectionUtils.isEmpty(items)) {
                return null;
            }
            vo.setItems(buildActivityItems(brandActStoreUseDTO.getActId(), items, brandActStoreUseDTO.getItems()));
        }
        if (Objects.equals(brandActStoreUseDTO.getProductSource(), Constants.BRAND_PRODUCT_SOURCE_BRAND)) {
            vo.setItems(buildItemsFromBrandProduct(serviceType, brandActStoreUseDTO));
        }

        return vo;
    }


    private List<BrandActivityItemVO> buildItemsFromBrandProduct(Integer serviceType, BrandActStoreUseDTO brandActStoreUseDTO) {
        List<BrandActivityItemVO> resultItems = new ArrayList<>();
        List<BrandActStoreUseDTO.ReferItem> items = brandActStoreUseDTO.getItems();
        items.forEach(item -> {
            BrandActivityItemVO vo = new BrandActivityItemVO();
            vo.setUuid(item.getSpuId() + serviceType);
            vo.setId(item.getSpuId());
            vo.setCategoryId(item.getSpuId());
            vo.setName(item.getName());
            vo.setPrice(item.getPrice());
            vo.setPhotoUrl(item.getUrl());
            vo.setUnit(item.getUnit());
            vo.setSku(item.getMaxBuyLimit());
            vo.setProductId(item.getActProId());
            vo.setActivityId(brandActStoreUseDTO.getActId());
            vo.setActivitySpec(item.getSpec());
            vo.setStockId(item.getStockId());
            resultItems.add(vo);

        });
        return resultItems;
    }


    /**
     * 将活动商品的数量合并到原始商品中，
     * 并且将活动商品信息放在原始CartItemCreate对象中
     *
     * @param itemCreates
     */
    public void mergeBrandActivityProduct(List<CartItemCreate> itemCreates) {
        Map<String, List<CartItemCreate>> itemMap = itemCreates.stream().collect(Collectors.groupingBy(it -> it.getItem().getId()));
        itemMap.forEach((k, v) -> {
            if (v.size() > 1) {
                //itemId相同，数量超过1个。肯定包含一个[自有]加价购商品
                List<CartItemCreate> brandList = v.stream().filter(CartItemCreate::isBrandAct).collect(Collectors.toList());
                List<BrandActivityProduct> products = brandList.stream().map(it -> {
                    it.getBrandActProduct().setNumber(it.getItem().getNumber());
                    return it.getBrandActProduct();
                }).collect(Collectors.toList());
                int brandTotal = brandList.stream().mapToInt(it -> it.getItem().getNumber()).sum();
                v.removeIf(CartItemCreate::isBrandAct);
                itemCreates.removeAll(brandList);
                v.get(0).getItem().setNumber(v.get(0).getItem().getNumber() + brandTotal);
                v.get(0).setNumber(v.get(0).getNumber() + brandTotal);
                v.get(0).setBrandActivityProducts(products);
                v.get(0).setBrandAct(true);
                if (brandList.get(0) != null) {
                    //购物车中的[自有]加价购商品信息
                    v.get(0).setBrandActProduct(brandList.get(0).getBrandActProduct());
                    v.get(0).setBrandProductSource(brandList.get(0).getBrandProductSource());
                }
            } else {
                //数量只有1个。可能是加价购商品，也可能是普通商品
                if (v.get(0).isBrandAct() && v.get(0).getBrandProductSource().equals(Constants.BRAND_PRODUCT_SOURCE_STORE)) {
                    v.get(0).getItem().setName(v.get(0).getBrandActProduct().getOriName());
                    v.get(0).getItem().setPhotoUrl(v.get(0).getBrandActProduct().getOriUrl());
                    v.get(0).getBrandActProduct().setNumber(v.get(0).getItem().getNumber());
                    v.get(0).setBrandActivityProducts(Lists.newArrayList(v.get(0).getBrandActProduct()));
                }
            }
        });
    }

    private List<ItemNewVo> queryItemsInfo(String storeId, List<String> spuIds) {
        ListResult<ItemDetailVO> result = itemService.findItemListBySpuIdsAndFilterByServiceType(storeId, spuIds, Constants.ServiceType.SCANNING);
        if (result.getTotal() == 0 || CollectionUtils.isEmpty(result.getRecords())) {
            return null;
        }
        List<ItemNewVo> list = new ArrayList<>();
        for (ItemDetailVO record : result.getRecords()) {
            //过滤无库存商品
            if (Objects.nonNull(record.getItem().getSku()) && record.getItem().getSku() <= 0) {
                continue;
            }
            //过滤售卖时间段
            ItemDto itemDto = new ItemDto();
            itemDto.setSaleTime(JSON.parseObject(JSON.toJSONString(record.getSaleTime()), ItemSaleTime.class));
            if (!ItemHelper.curTimeForSale(itemDto)) {
                continue;
            }

            // 默认展示1:1图片
            ItemVoBuildWrapper buildWrapper = new ItemVoBuildWrapper(record, Constants.ServiceType.SCANNING);
            buildWrapper.setImageSizeForShow(ProductConstant.MediaSpecTypeEnum.RATIO_1_1.getCode());
            list.add(itemsNewHelper.convert(buildWrapper));
        }
        return list;
    }

    private List<BrandActivityItemVO> buildActivityItems(String activityId, List<ItemNewVo> itemNewVos, List<BrandActStoreUseDTO.ReferItem> referItems) {
        List<BrandActivityItemVO> items = new ArrayList<>();
        Map<String, BrandActStoreUseDTO.ReferItem> refItemMap =
                referItems.stream().collect(Collectors.toMap(BrandActStoreUseDTO.ReferItem::getSpuId, Function.identity(), (k1, k2) -> k1));
        itemNewVos.forEach(itemNewVo -> {
            BrandActivityItemVO vo = JSON.parseObject(JSON.toJSONString(itemNewVo), BrandActivityItemVO.class);
            vo.setProductId(refItemMap.get(vo.getId()).getActProId());
            vo.setName(refItemMap.get(vo.getId()).getName());
            vo.setActivitySpec(refItemMap.get(vo.getId()).getSpec());
            vo.setPhotoUrl(refItemMap.get(vo.getId()).getUrl());
            vo.setActivityId(activityId);
            items.add(vo);
        });
        return items;
    }


    /**
     * 处理自动加购逻辑
     *
     * @param storeId
     * @param serviceType
     */
    public List<CartItemCreate> processAutoCart(String storeId, Integer serviceType, String userId, String miniProgramType, String appid) {
        if (!isEnabled(appid)) {
            return null;
        }
        BrandActivityVO vo = queryBrandActivity(storeId, serviceType, userId, miniProgramType, Constants.BRAND_DISPLAY_POSITION_AUTO_CART);
        if (Objects.isNull(vo)) {
            return null;
        }
        if (CollectionUtils.isEmpty(vo.getItems())) {
            return null;
        }

        return vo.getItems().stream().map(it -> {
            CartItemCreate create = new CartItemCreate();
            create.setServiceType(serviceType);
            create.setStoreId(storeId);
            create.setCtime(System.currentTimeMillis());
            create.setCover(true);
            create.setClientVersion(System.currentTimeMillis());
            create.setFromBrandAuto(true);
            create.setBrandAct(true);
            create.setBrandProductSource(vo.getProductSource());

            BrandActivityProduct brandActivityProduct = new BrandActivityProduct();
            brandActivityProduct.setActivityId(vo.getActivityId());
            brandActivityProduct.setProductId(it.getProductId());
            brandActivityProduct.setName(it.getName());
            brandActivityProduct.setUrl(it.getPhotoUrl());

            brandActivityProduct.setNumber(1);

            create.setBrandActProduct(brandActivityProduct);

            CartItemCreate.Item item = new CartItemCreate.Item();
            item.setId(it.getId());
            item.setName(it.getName());
            item.setCategoryId(it.getCategoryId());
            item.setNumber(1); // 默认加购一份
            item.setPhotoUrl(it.getPhotoUrl());
            item.setUrl(it.getPhotoUrl());
            item.setCategorySort(1);
            item.setDisplayOrder(1);
            item.setPrice(it.getPrice());
            item.setSku(it.getSku());
            item.setStockId(it.getStockId());
            item.setUnit(it.getUnit());

            create.setItem(item);
            fillOriNameAndUrl(create);
            return create;
        }).collect(Collectors.toList());

    }
}
