package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.jielong.*;
import com.wosai.pantheon.uf4c.service.JielongHelper;
import com.wosai.pantheon.uf4c.service.apisix.JielongService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class JielongServiceImpl implements JielongService {
    @Autowired
    JielongHelper jielongHelper;

    @Override
    public Map<String, Object> query(ApiRequest apiRequest) {
        Map map = apiRequest.getQuery();
        jielongHelper.setDataFromUrl(map);
        Integer jielongId = MapUtils.getInteger(map, "jielong_id");
        if (jielongId == null || jielongId <= 0) {
            return null;
        }
        return jielongHelper.queryJielongPage(jielongId);

    }

    @Override
    public JielongCart addCart(ApiRequest<JielongCartItem> apiRequest) {
        return jielongHelper.addorReduceCart(apiRequest.getBody());
    }

    @Override
    public PayCheckResponse check(ApiRequest<JielongCheckRequest> apiRequest) {
        return jielongHelper.getCartAndCalcPrice(apiRequest.getBody());
    }

    @Override
    public PayResult pay(ApiRequest<JielongPayRequest> apiRequest) {
        return jielongHelper.jielongPay(apiRequest.getBody());
    }

    @Override
    public List<JielongPage.OrderInfo> orderList(ApiRequest<JielongOrderQuery> apiRequest) {
        return jielongHelper.orderList(apiRequest.getBody());
    }

    @Override
    public JielongPage.OrderInfo getOrderBySn(ApiRequest apiRequest) {
        return jielongHelper.getOrderBySn(MapUtils.getString(apiRequest.getQuery(), "order_sn"));
    }

}
