package com.wosai.pantheon.uf4c.util;

import com.wosai.smart.goods.common.constant.ProductConstant;
import com.wosai.smart.goods.common.constant.ProductSearchConstant;
import com.wosai.smart.goods.common.constant.SaleChannelFilters;
import com.wosai.smart.goods.common.constant.SaleChannelFlag;
import com.wosai.smart.goods.enums.SaleTypeEnum;
import com.wosai.smart.goods.product.dto.PriceDTO;
import com.wosai.smart.goods.product.dto.SkuDTO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2024/6/13
 */
public class RetailItemConverter {

    public final static int SERVICE_TYPE_TAKEOUT = 1;

    public final static int SERVICE_TYPE_ARRIVAL = 2;

    public final static int SERVICE_TYPE_ARRIVAL_TAKEOUT = 0;

    public static int saleType2UnitType(Integer saleType) {
        return Objects.equals(saleType, SaleTypeEnum.WEIGHT.getCode()) ? 1 : 0;
    }

    public static Integer saleChannelFlag2ServiceType(SaleChannelFlag flag) {
        Objects.requireNonNull(flag);
        int serviceType = SERVICE_TYPE_ARRIVAL_TAKEOUT;
        if (flag.isSelfTakeout() && !flag.isInStore()) {
            serviceType = SERVICE_TYPE_TAKEOUT;
        }
        if (!flag.isSelfTakeout() && flag.isInStore()) {
            serviceType = SERVICE_TYPE_ARRIVAL;
        }
        return serviceType;
    }

    public static SaleChannelFilters serviceType2SaleChannelFilters(Integer serviceType) {
        // 指定渠道（默认查询外卖）
        SaleChannelFilters saleChannelFilters = new SaleChannelFilters();
        saleChannelFilters.setOperator(ProductSearchConstant.LogicalOperator.OR.getCode());
        SaleChannelFlag flag = new SaleChannelFlag(0L);
        if (Objects.equals(serviceType, SERVICE_TYPE_ARRIVAL)) {
            flag.setInStore(true);
        } else {
            flag.setSelfTakeout(true);
        }
        saleChannelFilters.setSaleChannels(flag.getBitsAvailable());
        return saleChannelFilters;
    }

    public static Long getPriceByServiceType(List<PriceDTO> priceList, Integer serviceType) {
        ProductConstant.PriceType priceType = ProductConstant.PriceType.TAKEOUT_PRICE;
        if (Objects.equals(serviceType, SERVICE_TYPE_ARRIVAL)) {
            priceType = ProductConstant.PriceType.SALE_PRICE;
        }
        ProductConstant.PriceType finalPriceType = priceType;
        return Optional.ofNullable(priceList)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(p -> StringUtils.equals(p.getType(), finalPriceType.getCode()))
                .findFirst()
                .map(PriceDTO::getPrice)
                .orElse(null);
    }

    public static String generateNameOfWeightProduct(String name, SkuDTO skuDTO) {
        String weight = Optional.ofNullable(skuDTO.getPerTakeoutWeight()).map(p -> BigDecimal.valueOf(p).stripTrailingZeros().toPlainString()).orElse("");
        return String.format("%s 约%s%s", name, weight, skuDTO.getTakeoutWeightUnit());
    }

}
