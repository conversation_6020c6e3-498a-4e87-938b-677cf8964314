package com.wosai.pantheon.uf4c.fallbackconfig.client;

import com.wosai.aop.gateway.service.ContentCenterService;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;

public class ContentCenterServiceFallback extends JsonRPCFallbackDefine {
    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(ContentCenterService.class);
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}
