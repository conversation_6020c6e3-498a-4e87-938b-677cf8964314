package com.wosai.pantheon.uf4c.web.controller.v2;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.fallbackconfig.server.BlockHandleServer;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV2;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR> zhen.pan, xuyuanxiang
 * @since 2019/4/9
 */
@RestController
@RequestMapping(path = "/api/v2/orders", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class OrderControllerV2 {

    @Autowired
    private OrderServiceV2 orderServiceV2;


    @PostMapping("/pay/discount")
    @ResponseBody
    @SneakyThrows
    public RedeemResult getRedeemResult(@RequestBody RedeemRequest redeemRequest) {
        return getRedeemResultTemporary(redeemRequest);
    }

    /**
     * 2024-03-06
     * 限流私有接口。现在框架组给controller限流有问题，会导致hera上不展示数据了。
     * 临时解决方案，将限流的接口放到私有接口中。
     */
    @SentinelResource(value = "smart/uf4c-app/OrderControllerV2/getRedeemResult",blockHandlerClass = BlockHandleServer.class,blockHandler = "handleGetRedeemResult")
    private RedeemResult getRedeemResultTemporary(RedeemRequest redeemRequest) {
        return orderServiceV2.getRedeemResult(new ApiRequest<>(redeemRequest));
    }

    @PostMapping("/pay")
    @ResponseBody
    @SneakyThrows
    public PayResult pay(@RequestBody PayRequest request) {
        return orderServiceV2.pay(new ApiRequest<>(request));
    }


    @PostMapping("/love/pay")
    @ResponseBody
    public PayResult lovePay(@RequestBody PayRequest request) {
        return orderServiceV2.lovePay(new ApiRequest<>(request));
    }
}
