package com.wosai.pantheon.uf4c.advice;

import com.wosai.market.trade.exception.MarketTradeException;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.pantheon.uf4c.util.RequestHelper;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.BaseException;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.pantheon.uf4c.web.response.FailResponse;
import com.wosai.pantheon.uf4c.web.response.Response;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.web.api.exception.UnknownException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

/**
 * created by shij on 2019/3/27
 */
@RestControllerAdvice("com.wosai.pantheon.uf4c.web.controller")
@RequiredArgsConstructor
@Slf4j
public class ResponseExceptionHandler {

    private final RequestHelper requestHelper;

    // TODO 后面可能需要再增加一个code作为接口原生msg给前端debug用, 目前都cover掉，要么系统异常，要么业务内部错误
    @org.springframework.web.bind.annotation.ExceptionHandler(Exception.class)
    public Response handleError(Exception e, HttpServletRequest request) {



        // exception => response
        if (e instanceof BaseException) {
            return new FailResponse(((BaseException) e).getCode(), e.getMessage());
        }else if (e instanceof org.springframework.web.bind.MissingServletRequestParameterException){
            logOnlyMessage(request,e.getMessage());
            return new FailResponse(ReturnCode.INVALID_PARAM_EXCEPTION.getCode(), "参数错误，缺少必要参数");
        } else if (e instanceof MethodArgumentNotValidException) {
            FieldError fieldError = ((MethodArgumentNotValidException) e).getBindingResult().getFieldError();
            if (fieldError == null) {
                return new FailResponse(ReturnCode.INVALID_PARAM_EXCEPTION.getCode(), ((MethodArgumentNotValidException) e).getBindingResult().getGlobalError().getDefaultMessage());
            }
            return new FailResponse(ReturnCode.INVALID_PARAM_EXCEPTION.getCode(), fieldError.getField() + fieldError.getDefaultMessage());
        } else if (e instanceof MarketTradeException) {
            return new FailResponse(ReturnCode.ORDER_CAN_NOT_PAY.getCode(), e.getMessage());
        } else if (e instanceof ParamException) {
            return new FailResponse(ReturnCode.INVALID_PARAM_EXCEPTION.getCode(), e.getMessage());
        }else if (e instanceof BusinessException || e instanceof com.wosai.smartbiz.base.exceptions.BusinessException){
            return new FailResponse(ReturnCode.BUSINESS_ERROR.getCode(), e.getMessage());
        }else if (e instanceof UnknownException) {
            return new FailResponse(ReturnCode.BUSINESS_ERROR.getCode(), e.getMessage());
        }else {
            logErrorFullStack(request,e);
            return new FailResponse(ReturnCode.SYSTEM_EXCEPTION, "系统错误，请联系收钱吧客服处理" + TraceContext.traceId());
        }
    }


    private void logErrorFullStack(HttpServletRequest request, Exception e){

        String userId = Optional.ofNullable(ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO()).map( requestContext -> requestContext.getUserId()).orElse("");
        log.error(String.format("Exception raised, %s, %s",
                keyValue("queryParams",request.getQueryString()),
                keyValue("params",getRequestParams(request))),
                keyValue("user_id",userId),
                keyValue("uri",request.getRequestURI()),e);
    }

    private void logOnlyMessage(HttpServletRequest request, String errorMsg){

        String userId = Optional.ofNullable(ThreadLocalHelper.getRequestContextThreadLocal().get().getUserContextDTO()).map( requestContext -> requestContext.getUserId()).orElse("");
        log.warn(String.format("Exception raised, %s, %s",
                keyValue("queryParams",request.getQueryString()),
                keyValue("params",getRequestParams(request))),
                keyValue("user_id",userId),
                keyValue("uri",request.getRequestURI()),
                keyValue("error_msg",errorMsg));
    }

    private String getRequestParams(HttpServletRequest request) {
        return requestHelper.getParameters(request);
    }

}
