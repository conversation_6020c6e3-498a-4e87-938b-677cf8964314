package com.wosai.pantheon.uf4c.fallbackconfig.client;

import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;
import com.wosai.pantheon.core.uitem.service.ItemService;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;

public class ItemServiceFallback extends JsonRPCFallbackDefine {
    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(ItemService.class);
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}
