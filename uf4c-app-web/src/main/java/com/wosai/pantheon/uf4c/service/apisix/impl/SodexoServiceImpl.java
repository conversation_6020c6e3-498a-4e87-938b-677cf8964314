package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.order.model.dto.OrderDTO;
import com.wosai.pantheon.order.model.dto.v2.OrderPaysDTO;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.apisix.UserContext;
import com.wosai.pantheon.uf4c.model.sodexo.SodexoBalanceRequest;
import com.wosai.pantheon.uf4c.model.sodexo.SodexoPayRequest;
import com.wosai.pantheon.uf4c.model.sodexo.SodexoResponse;
import com.wosai.pantheon.uf4c.service.UpaySodexoService;
import com.wosai.pantheon.uf4c.service.apisix.SodexoService;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.upay.util.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@AutoJsonRpcServiceImpl
public class SodexoServiceImpl implements SodexoService {
    @Autowired
    private OrderService orderService;
    @Autowired
    private UpaySodexoService upaySodexoService;

    @Override
    public boolean pay(ApiRequest<Map> apiRequest) {
        UserContext userContext = apiRequest.getUser();
        String sn = MapUtils.getString(apiRequest.getBody(), "sn");
        String encryptData = MapUtils.getString(apiRequest.getBody(), "encrypt_data");
        if (StringUtils.isBlank(sn) || StringUtils.isBlank(encryptData)) {
            throw new BusinessException(ReturnCode.SODEXO_ERROR, "订单号或支付参数错误");
        }
        OrderDTO orderDTO = orderService.getOrderBySn(sn);
        if (Objects.isNull(orderDTO)) {
            throw new BusinessException(ReturnCode.SODEXO_ERROR, "订单不存在");
        }

        List<OrderPaysDTO> paysDTOList = orderDTO.getOrderPays();
        if (CollectionUtils.isEmpty(paysDTOList)) {
            throw new BusinessException(ReturnCode.SODEXO_ERROR, "订单支付信息错误");
        }
        OrderPaysDTO paysDTO = paysDTOList.get(0);
        SodexoPayRequest payRequest = new SodexoPayRequest();
        payRequest.setClient_sn(paysDTO.getClientSn());
        payRequest.setOrder_sn(paysDTO.getTransSn());
        payRequest.setMerchant_id(orderDTO.getMerchantId());
        payRequest.setPayer_uid(Base64.encode(userContext.getCellphone().getBytes(StandardCharsets.UTF_8)));
        payRequest.setUser_id(userContext.getUserId());
        SodexoPayRequest.WapPayRequest wapPayRequest = new SodexoPayRequest.WapPayRequest();
        wapPayRequest.setEncrypt_data(encryptData);
        payRequest.setWap_pay_request(wapPayRequest);
        SodexoResponse sodexoResponse = upaySodexoService.pay(payRequest);
        checkResult(sodexoResponse);
        return true;
    }

    @Override
    public String balance(ApiRequest<Map> apiRequest) {
        String store_sn = MapUtils.getString(apiRequest.getBody(), "store_sn");
        if (StringUtils.isBlank(store_sn)) {
            throw new BusinessException(ReturnCode.SODEXO_ERROR, "门店SN未传");
        }
        UserContext userContext = apiRequest.getUser();
        SodexoBalanceRequest request = new SodexoBalanceRequest();
        request.setPayer_uid(Base64.encode(userContext.getCellphone().getBytes(StandardCharsets.UTF_8)));
        request.setUser_id(userContext.getUserId());
        request.setStore_sn(store_sn);
        SodexoResponse sodexoResponse = upaySodexoService.balance(request);
        checkResult(sodexoResponse);
        return MapUtils.getString(sodexoResponse.getBiz_response().getData(), "total_balance", "0");
    }


    private void checkResult(SodexoResponse result) {
        if (Objects.isNull(result)) {
            throw new BusinessException(ReturnCode.SODEXO_ERROR, "交易异常，请稍候再试！");
        } else if (!Objects.equals(result.getResult_code(), "200")) {
            throw new BusinessException(ReturnCode.SODEXO_ERROR, result.getError_message() == null ? "交易异常，请稍候再试！" : result.getError_message());
        } else if (!result.getBiz_response().getResult_code().equals("SUCCESS")) {
            if (result.getBiz_response().getError_message() != null) {
                throw new BusinessException(ReturnCode.SODEXO_ERROR, result.getBiz_response().getError_message());
            } else {
                throw new BusinessException(ReturnCode.SODEXO_ERROR);
            }
        }
    }
}
