package com.wosai.pantheon.uf4c.model.item;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/6/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RetailCategoryQueryReq implements Serializable {

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 服务类型
     */
    private Integer serviceType;

}
