package com.wosai.pantheon.uf4c.model.payfirsttableorder;

import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ItemsAndRedeem implements Serializable {

    List<CartItemCreate> items ;

    RedeemResult redeem;

    List<Order.GoodsBatchInfo> goodsBatchInfos;

    /**
     * 应付金额
     */
    Long needPayAmount;

    /**
     * 原价
     */
    Long originalAmount;

    Cart cart;

    /*
     * 告知前端禁用整块儿优惠,如果是 true 的话就禁用,否则的话就不禁用
     */
    boolean disableRedeem;

    private String tradeApp;

    private Map amountComposition;

}
