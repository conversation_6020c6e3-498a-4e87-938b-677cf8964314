package com.wosai.pantheon.uf4c.service.apisix.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.order.enums.OrderSource;
import com.wosai.pantheon.order.service.OrderService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.uf4c.service.CartService;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.service.apisix.TableService;
import com.wosai.pantheon.uf4c.util.CartHelper;
import com.wosai.pantheon.uf4c.util.EntityConvert;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.exception.BusinessException;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.enums.DealTypeEnum;
import com.wosai.smartbiz.oms.api.query.CartSyncQuery;
import com.wosai.smartbiz.oms.api.query.UpdatePeopleNumQuery;
import com.wosai.smartbiz.oms.api.query.table.TableQueryRequest;
import com.wosai.smartbiz.oms.api.services.TableRpcServiceV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.KeyValue;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static net.logstash.logback.argument.StructuredArguments.keyValue;

@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class TableServiceImpl implements TableService {
    @Autowired
    private TableRpcServiceV2 tableRpcServiceV2;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderHelper orderHelper;

    @Autowired
    private com.wosai.smartbiz.oms.api.services.CartService roundMealCartService;


    @Override
    public Boolean updatePeopleNum(ApiRequest request) {

        Map queryParam = request.getQuery();

        String storeId = MapUtils.getString(queryParam, "store_id");
        String tableId = MapUtils.getString(queryParam, "table_id");
        String userName = MapUtils.getString(queryParam, "user_name");
        String userIcon = MapUtils.getString(queryParam, "user_icon");
        Integer peopleNum = MapUtils.getInteger(queryParam, "people_num", 0);

        if (StringUtils.isBlank(tableId)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "桌台信息不能为空");
        }

        if (peopleNum == null || peopleNum <= 0) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "桌台人数必须大于零");
        }



        UpdatePeopleNumQuery query = new UpdatePeopleNumQuery();
        query.setOrderSource(OrderSource.MINI);
        query.setTableId(tableId);
        query.setStoreId(storeId);
        query.setPeopleNum(peopleNum);
        query.setMiniSetPeopleNum(true);
        Result<Boolean> result = tableRpcServiceV2.updatePeopleNum(query);

        if (!result.isSuccess()) {
            if (StringUtils.equals(result.getErrorCode(), "CAN_NOT_CHANGE_PEOPLE")) {
                //临时处理，遇到这个错误码，先
                return true;
            }
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
        }

        addMustOrderItem(tableId,storeId,userName,userIcon,peopleNum);

        return result.getData();
    }

    @Override
    public Boolean addMustOrderItem(String tableId, String storeId, String userName, String userIcon, Integer peopleNum) {

        List<CartItemCreate> cartItemCreates = orderHelper.getOpenTableMustOrderItems(storeId, tableId, userName, userIcon, peopleNum);

        if (CollectionUtils.isEmpty(cartItemCreates)) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(cartItemCreates)) {

            //开台成功，添加、更新开台必点商品
            CartSyncQuery cartSyncQuery = new CartSyncQuery();
            cartSyncQuery.setTableId(tableId);
            cartSyncQuery.setDealType(DealTypeEnum.ADD);

            cartSyncQuery.setUserId(Optional.ofNullable(ThreadLocalHelper.getUserNotThrowExcetpion()).map(user -> user.getUserId()).orElse(null));
            cartSyncQuery.setUserIcon(userIcon);
            cartSyncQuery.setUserName(userName);
            cartSyncQuery.setOpenTableMustOrderGoods(cartItemCreates.stream().map(itemCreate -> CartHelper.convert2RoundGoods(itemCreate)).collect(Collectors.toList()));

            roundMealCartService.addOpenTableMustOrder(cartSyncQuery);
        }

        return true;
    }


    @Override
    public Order getTableOrder(ApiRequest request) {

        Map queryParam = request.getQuery();

        String sn = MapUtils.getString(queryParam, "sn");
        String tableId = MapUtils.getString(queryParam, "table_id");

        if (StringUtils.isBlank(tableId)) {
            throw new ParamException("桌台信息不能为空");
        }

        if (StringUtils.isBlank(sn)) {
            TableQueryRequest tableQueryRequest = new TableQueryRequest();
            tableQueryRequest.setTableId(tableId);
            Result<String> result = tableRpcServiceV2.getTableOrderNo(tableQueryRequest);

            if (!result.isSuccess()) {
                throw new BusinessException(ReturnCode.BUSINESS_ERROR, result.getErrorMsg());
            }

            if (StringUtils.isBlank(result.getData())) {
                return null;
            }

            sn = result.getData();
        }


        return EntityConvert.convertOrderDTO(orderService.getOrderBySn(sn));
    }

    @Override
    public Result<Boolean> updatePeopleNumV2(ApiRequest request) {
        Map queryParam = request.getQuery();
        String tableId = MapUtils.getString(queryParam, "table_id");

        if (StringUtils.isBlank(tableId)) {
            throw new BusinessException(ReturnCode.BUSINESS_ERROR, "桌台信息不能为空");
        }
        //更新人数，非开台
        Boolean updatePeople = MapUtils.getBoolean(queryParam, "update_people", false);

        if(updatePeople && !canContinueOrder(tableId)){
            return Result.error(ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getCode(), ReturnCode.TABLE_CLEANED_CHOOSE_PEOPLE.getMessage());
        }
        Boolean res = updatePeopleNum(request);

        return Result.success(res);
    }

    @Override
    public Boolean canContinueOrder(String tableId) {
        if(StringUtils.isBlank(tableId)){
            return false;
        }
        try {
            Result<Boolean> continueOrderRes = tableRpcServiceV2.continueOrder(tableId);
            if(!continueOrderRes.isSuccess()){
                return false;
            }
            return continueOrderRes.getData();
        } catch(Exception e){
            log.error("canContinueOrder exception",  keyValue("method", "canContinueOrder"), keyValue("tableId", tableId), e);
        }
        return true;
    }


}
