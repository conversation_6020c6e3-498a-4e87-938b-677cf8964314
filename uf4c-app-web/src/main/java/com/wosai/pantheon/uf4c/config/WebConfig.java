package com.wosai.pantheon.uf4c.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.wosai.data.jackson.RowDeserializerInstantiator;
import com.wosai.pantheon.uf4c.constant.Properties;
import com.wosai.pantheon.uf4c.web.filter.RequestWrapperFilter;
import com.wosai.pantheon.uf4c.web.interceptor.TokenInterceptor;
import com.wosai.web.util.JsonUtil;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.Arrays;
import java.util.List;

/**
 * created by shij on 2019/3/27
 */
@Configuration
@DependsOn("rpcServiceConfig")
public class WebConfig extends WebMvcConfigurerAdapter {

    @Autowired
    TokenInterceptor tokenInterceptor;

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = JsonUtil.defaultRpcObjectMapper();
        objectMapper.setHandlerInstantiator(new RowDeserializerInstantiator());
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    @Bean
    public MappingJackson2HttpMessageConverter jackson2HttpMessageConverter() {
        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter =
                new MappingJackson2HttpMessageConverter();
        jackson2HttpMessageConverter.setObjectMapper(objectMapper());
        return jackson2HttpMessageConverter;
    }

    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        return new StringHttpMessageConverter();
    }

    @Bean
    public RestTemplate restTemplate(final Properties properties, final RestTemplateBuilder builder) {
        PoolingHttpClientConnectionManager connectionManager =
                new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(properties.getConcurrency() * properties.getCpuNum());
        connectionManager.setDefaultMaxPerRoute(properties.getConcurrency());
        return builder
                .requestFactory(new HttpComponentsClientHttpRequestFactory(HttpClientBuilder
                        .create()
                        .setConnectionManager(connectionManager)
                        .setUserAgent("uf4c-app")
                        .setDefaultRequestConfig(RequestConfig
                                .custom()
                                .setConnectionRequestTimeout(properties.getRequestTimeout())
                                .setConnectTimeout(properties.getRequestTimeout())
                                .setSocketTimeout(properties.getRequestTimeout())
                                .build())
                        .build()))
                .messageConverters(Arrays.asList(stringHttpMessageConverter(), jackson2HttpMessageConverter()))
                .build();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 访问令牌拦截器
        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/api/v1/gather/**")
                .excludePathPatterns("/api/v1/table/add/must/order")
                .excludePathPatterns("/api/v3/orders/recharge/notify")
                .excludePathPatterns("/metrics","/error","/check","/apollo/test","/api/v1/stores/multiConfigs", "/api/v1/orders/notify/*", "/api/v1/orders/printByTemplate", "/api/v1/orders/print")
                .excludePathPatterns("/test/**");
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(0, jackson2HttpMessageConverter());
    }

    @Bean
    public FilterRegistrationBean filterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RequestWrapperFilter());
        registration.addUrlPatterns("/*");
        return registration;
    }

}
