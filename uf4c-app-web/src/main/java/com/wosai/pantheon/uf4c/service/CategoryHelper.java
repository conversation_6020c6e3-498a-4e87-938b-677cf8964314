package com.wosai.pantheon.uf4c.service;

import com.google.common.collect.Sets;
import com.wosai.market.dto.CategoryDTO;
import com.wosai.market.dto.category.CategoryDetail;
import com.wosai.market.enums.ProductTypeEnum;
import com.wosai.market.enums.SaleSceneEnum;
import com.wosai.market.mcc.api.dto.request.FindConfigByNameRequest;
import com.wosai.market.mcc.api.dto.response.ConfigResponse;
import com.wosai.market.mcc.api.enums.AppId;
import com.wosai.market.mcc.api.enums.OwnerType;
import com.wosai.market.mcc.api.service.ConfigRemoteService;
import com.wosai.market.request.V2.CommonRequest;
import com.wosai.market.response.ItemSummary;
import com.wosai.market.service.StatisticsService;
import com.wosai.market.service.store.StoreCategoryService;
import com.wosai.market.tethys.api.dto.CategoryInfo;
import com.wosai.market.tethys.api.dto.SkuInfo;
import com.wosai.market.tethys.api.dto.request.CategoryActivityDetailRequest;
import com.wosai.market.tethys.api.enums.EffectiveType;
import com.wosai.market.tethys.api.enums.OrderType;
import com.wosai.market.tethys.api.service.ActivityRemoteService;
import com.wosai.pantheon.core.uitem.model.CategoryExt;
import com.wosai.pantheon.core.uitem.model.ItemDto;
import com.wosai.pantheon.core.uitem.model.ItemQuery;
import com.wosai.pantheon.core.uitem.model.opentable.FindOpenTableMustOrderRequest;
import com.wosai.pantheon.core.uitem.model.opentable.OpenTableMustOrderItem;
import com.wosai.pantheon.core.uitem.service.ItemService;
import com.wosai.pantheon.core.uitem.service.OpenTableMustOrderService;
import com.wosai.pantheon.order.enums.OrderGoodsTagEnum;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import com.wosai.pantheon.uf4c.model.ItemFind;
import com.wosai.pantheon.uf4c.model.vo.BoughtItem;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.pantheon.uf4c.util.MccUtils;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.util.WeakReferenceCaller;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/9/29
 */
@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Service
@Slf4j
public class CategoryHelper {

    @Autowired
    private StoreCategoryService categoryRpcService;

    @Autowired
    private ItemHelper itemHelper;

    @Autowired
    private ConfigRemoteService configRemoteService;


    @Autowired
    private ApolloConfigHelper apolloConfigHelper;

    @Autowired
    private OpenTableMustOrderService openTableMustOrderService;

//    @Autowired
//    private DataReportService dataReportService;

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private ItemService itemService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ActivityRemoteService activityRemoteService;

    private HashOperations<String, String, BoughtItem> hashOperations;

    private static final int DISCOUNT_ITEM_MAX_SHOW_COUNT = 10;

    private static final int BOUGHT_ITEM_MAX_SHOW_COUNT = 5;

    private static final String BOUGHT_ITEM_CACHE_PREFIX = "com.uf4c.bought:";

    private static final String BOUGHT_ITEM_TIME = "bought.items.query.date";

    @PostConstruct
    public void postConstruct() {
        hashOperations = redisTemplate.opsForHash();
    }

    /**
     * 查询商家自定义分类列表
     * @param storeId
     * @param serviceType
     * @return
     */
    public List<CategoryExt> findEffectiveCategories(String storeId, Integer serviceType) {
        List<CategoryDetail> cats = categoryRpcService.listForMini(storeId, SaleSceneEnum.getByServiceType(serviceType));
        if (CollectionUtils.isEmpty(cats)) {
            return Lists.newArrayList();
        }
        return cats.stream()
                .filter(cd -> Objects.nonNull(cd.getCategory()) && cd.getCategory().getProductNum() > 0)
                .map(cd -> {
                    CategoryDTO cat = cd.getCategory();
                    CategoryExt ce = new CategoryExt();
                    ce.setId(cat.getCategoryId());
                    ce.setType(cat.getType().getCode());
                    ce.setName(cat.getCategoryName());
                    ce.setDisplayOrder(cat.getSort());
                    ce.setItemCount(cat.getProductNum());
                    ce.setMerchantId(cat.getMerchantId());
                    ce.setStoreId(cat.getStoreId());
                    ce.setCategoryType(cat.getType().name());
                    return ce;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取点过的菜
     *
     * @param find
     * @return
     */
    public ListResult<ItemDetailVO> getBoughtItems(ItemFind find, String userId, boolean queryMcc) {

        String storeId = find.getStoreId();
        if (StringUtils.isBlank(userId)) {
            userId = ThreadLocalHelper.getUserId();
        }
        if (StringUtils.isBlank(userId)) {
            return ListResult.emptyResult();
        }
        Integer serviceType = Optional.ofNullable(find.getServiceType()).orElse(0);

        if (queryMcc) {
            FindConfigByNameRequest findConfigRequest = new FindConfigByNameRequest(AppId.UFOOD, OwnerType.STORE_ID, storeId, MccUtils.SHOW_ORDERED_DISH);
            ConfigResponse configResponse = configRemoteService.findByName(findConfigRequest);
            boolean show = Optional.ofNullable(configResponse)
                    .map(p -> StringUtils.equals(MccUtils.SWITCH_ON, p.getValue()))
                    .orElse(false);
            if (!show) {
                return ListResult.emptyResult();
            }
        }

        // 先取缓存，缓存不存在则走数据统计
        List<BoughtItem> itemSummaries;
        String cacheKey = this.getBoughtItemCacheKey(storeId, userId, serviceType);
        Map<String, BoughtItem> cache = hashOperations.entries(cacheKey);
        if (MapUtils.isNotEmpty(cache)) {
            itemSummaries = new ArrayList<>(cache.values()).stream()
                    .sorted(Comparator.comparing(BoughtItem::getSeq, Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());
        } else {
            itemSummaries = this.getBoughtItemReport(storeId, userId);
        }
        if (CollectionUtils.isEmpty(itemSummaries)) {
            return ListResult.emptyResult();
        }

        // 筛选出可用的商品
        List<ItemDto> filterItems = this.filterBoughtItems(itemSummaries, find);
        if (CollectionUtils.isEmpty(filterItems)) {
            return ListResult.emptyResult();
        }

        // 组装
        ListResult<ItemDetailVO> itemVoResult = itemHelper.processItemDetailList(new ListResult<>(filterItems), serviceType,userId);
        List<ItemDetailVO> itemVos = Optional.ofNullable(itemVoResult)
                .map(ListResult::getRecords)
                .orElseGet(Lists::newArrayList);
        if (CollectionUtils.isEmpty(itemVos)) {
            return ListResult.emptyResult();
        }

        Map<String, BoughtItem> itemIdToBoughtItemMap = itemSummaries.stream()
                .collect(Collectors.toMap(BoughtItem::getItemId, Function.identity(), (k1, k2) -> k1));
        itemVos.forEach(p -> {
            BoughtItem boughtItem = itemIdToBoughtItemMap.get(p.getItem().getId());
            p.setBoughtTimes(Optional.ofNullable(boughtItem)
                    .map(BoughtItem::getBoughtTimes)
                    .orElse(BigDecimal.ZERO));
            p.setLatestBuyTime(Optional.ofNullable(boughtItem)
                    .map(BoughtItem::getLatestBuyTime)
                    .orElse(null));
            p.getItem().setItemTag(TagUtil.addTag(p.getItem().getItemTag(), OrderGoodsTagEnum.BOUGHT.getValue()));
        });

        if (MapUtils.isEmpty(cache)) {
            this.cacheBoughtItem(storeId, userId, serviceType, itemVos);
        }

        return new ListResult<>(itemVos);
    }

    /**
     * 获取有优惠的商品
     *
     * @param find
     * @param secondActivitySkus
     * @param singleActivitySkus
     * @return
     */
    public ListResult<ItemDetailVO> getDiscountItems(ItemFind find, List<SkuInfo> secondActivitySkus, List<SkuInfo> singleActivitySkus) {

        String storeId = find.getStoreId();
        Integer serviceType = Optional.ofNullable(find.getServiceType()).orElse(0);

        // 准备商品数据
        List<String> itemIds = Lists.newArrayList();
        Set<String> secondActivitySpuIds = ListUtils.defaultIfNull(secondActivitySkus, Lists.newArrayList()).stream()
                .map(SkuInfo::getSpuId)
                .collect(Collectors.toSet());
        // 过滤掉已经在第二份优惠中的商品
        Set<String> singleActivitySpuIds = ListUtils.defaultIfNull(singleActivitySkus, Lists.newArrayList()).stream()
                .map(SkuInfo::getSpuId)
                .filter(p -> !secondActivitySpuIds.contains(p))
                .collect(Collectors.toSet());

        itemIds.addAll(secondActivitySpuIds);
        itemIds.addAll(singleActivitySpuIds);
        if (CollectionUtils.isEmpty(itemIds)) {
            return ListResult.emptyResult();
        }

        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setServiceType(serviceType);
        itemQuery.setForSale(Boolean.TRUE);
        itemQuery.setSaleTerminal(Constants.SaleTerminal.MINI);
        ListResult<ItemDto> itemDtoResult = itemService.findItemDetailsByIdAndQuery(storeId, itemIds, itemQuery, false, null);
        List<ItemDto> allItems = Optional.ofNullable(itemDtoResult)
                .map(ListResult::getRecords)
                .orElseGet(Lists::newArrayList);

        // 过滤非售卖时间商品
        allItems = itemHelper.filterItemNotInSaleTime(storeId, allItems);

        if (CollectionUtils.isEmpty(allItems)) {
            return ListResult.emptyResult();
        }

        // 查询门店下挂靠在分类上的活动
        // 这个map的key只有两个, 分别是"singleCategory", "secondCategory", 分别对应单品和第二份活动
        Map<String, List<CategoryInfo>> categoryDiscountMap = itemHelper.categoryDiscountDetails(storeId, serviceType, EffectiveType.YES);

        // 分类维度参加活动的商品，或同时以分类及商品维度参加活动的商品都放在原分类下
        if (MapUtils.isNotEmpty(categoryDiscountMap)) {
            List<String> discountCategoryIds = categoryDiscountMap.values().stream()
                    .flatMap(Collection::stream)
                    .map(CategoryInfo::getCategoryId)
                    .collect(Collectors.toList());
            allItems = allItems.stream()
                   .filter(item -> !discountCategoryIds.contains(item.getItem().getCategoryId()))
                   .collect(Collectors.toList());

       }

        // 筛选 + 数据组装
        List<ItemDto> filterItems = Lists.newArrayList();

        // 第二份优惠展示在前面
        if (CollectionUtils.isNotEmpty(secondActivitySpuIds)) {
            List<ItemDto> filterSecondActivityItems = allItems.stream()
                    .filter(p -> secondActivitySpuIds.contains(p.getItem().getId()))
                    .collect(Collectors.toList());
            filterItems.addAll(filterSecondActivityItems);
        }

        // 数量不够，继续补充单品优惠中的商品
        if (DISCOUNT_ITEM_MAX_SHOW_COUNT > filterItems.size() && CollectionUtils.isNotEmpty(singleActivitySpuIds)) {
            List<ItemDto> filterSingleActivityItems = allItems.stream()
                    .filter(p -> singleActivitySpuIds.contains(p.getItem().getId()))
                    .collect(Collectors.toList());
            filterItems.addAll(filterSingleActivityItems);
        }

        if (CollectionUtils.isEmpty(filterItems)) {
            return ListResult.emptyResult();
        }

        List<ItemDetailVO> result = Optional.ofNullable(itemHelper.processItemDetailList(storeId, serviceType, new ListResult<>(filterItems), singleActivitySkus, secondActivitySkus))
                .map(ListResult::getRecords)
                .orElseGet(Lists::newArrayList)
                .stream()
                .limit(DISCOUNT_ITEM_MAX_SHOW_COUNT)
                .collect(Collectors.toList());
        result.forEach(p -> p.getItem().setItemTag(TagUtil.addTag(p.getItem().getItemTag(), OrderGoodsTagEnum.DISCOUNT.getValue())));
        return new ListResult<>(result);
    }

    /**
     * 获取有优惠的商品
     *
     * @param find
     * @return
     */
    public ListResult<ItemDetailVO> getDiscountItems(ItemFind find, boolean needQueryConfig) {

        if (apolloConfigHelper.getServiceDegradeConfig().isDiscountCategoryDegrade()) {
            return ListResult.emptyResult();
        }

        String storeId = find.getStoreId();
        Integer serviceType = Optional.ofNullable(find.getServiceType()).orElse(0);
        if (needQueryConfig) {
            FindConfigByNameRequest findConfigRequest = new FindConfigByNameRequest(AppId.UFOOD, OwnerType.STORE_ID, storeId, MccUtils.SHOW_DISCOUNT_CATEGORY);
            ConfigResponse configResponse = configRemoteService.findByName(findConfigRequest);
            boolean show = Optional.ofNullable(configResponse)
                    .map(p -> StringUtils.equals(MccUtils.SWITCH_ON, p.getValue()))
                    .orElse(false);
            if (!show) {
                return ListResult.emptyResult();
            }
        }
        List<SkuInfo> secondActivitySkus = itemHelper.getSecondActivitySkuInfos(storeId, serviceType);
        List<SkuInfo> singleActivitySkus = itemHelper.getSingActivitySkuInfos(storeId, serviceType);
        return this.getDiscountItems(find, secondActivitySkus, singleActivitySkus);
    }

    public String getBoughtItemCacheKey(String storeId, String userId, Integer serviceType) {
        String suffix = storeId + ":" + serviceType + ":" + userId;
        return BOUGHT_ITEM_CACHE_PREFIX + DigestUtils.md5DigestAsHex(suffix.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 缓存「点过的菜」
     *
     * @param storeId
     * @param userId
     * @param itemVos
     */
    public void cacheBoughtItem(String storeId, String userId, Integer serviceType, List<ItemDetailVO> itemVos) {
        if (CollectionUtils.isEmpty(itemVos)) {
            return;
        }
        Map<String, BoughtItem> cache = new HashMap<>(16);
        int seq = 1;
        for (ItemDetailVO itemVo : itemVos) {
            BoughtItem cacheValue = new BoughtItem()
                    .setItemId(itemVo.getItem().getId())
                    .setBoughtTimes(itemVo.getBoughtTimes())
                    .setLatestBuyTime(itemVo.getLatestBuyTime())
                    .setSeq(seq);
            cache.put(itemVo.getItem().getId(), cacheValue);
            seq++;
        }
        String cacheKey = this.getBoughtItemCacheKey(storeId, userId, serviceType);
        hashOperations.putAll(cacheKey, cache);
        redisTemplate.expire(cacheKey, 120, TimeUnit.SECONDS);
    }

    /**
     * 获取商品购买次数统计
     *
     * @param storeId
     * @param userId
     * @return
     */
    private List<BoughtItem> getBoughtItemReport(String storeId, String userId) {


        Long data = apolloConfigHelper.getLongConfigValueByKey(BOUGHT_ITEM_TIME, -7L);

        CommonRequest commonRequest = CommonRequest.builder()
                .begin(LocalDateTime.now().plusDays(data)).end(LocalDateTime.now())
                .storeSet(Sets.newHashSet(storeId))
                .userIdList(Collections.singletonList(userId)).size(100).build();

        ItemSummary itemSummary = WeakReferenceCaller.call(() -> statisticsService.buyGoodsStatistics(commonRequest));

//        ReportResponse reportResponse = dataReportService.dataReport(reportRequest);

        return Optional.ofNullable(itemSummary)
                .map(ItemSummary::getItemSummary)
                .orElseGet(Lists::newArrayList)
                .stream()
                .map(p -> new BoughtItem()
                        .setItemId(p.getItemId())
                        .setBoughtTimes(BigDecimal.valueOf(Optional.ofNullable(p.getSaleCount()).orElse(0D)))
                        .setLatestBuyTime(p.getCtime()))
                .collect(Collectors.toList());
    }

    /**
     * 筛选买过的商品
     *
     * @param itemSummaries
     * @param find
     * @return
     */
    private List<ItemDto> filterBoughtItems(List<BoughtItem> itemSummaries, ItemFind find) {

        if (CollectionUtils.isEmpty(itemSummaries)) {
            return Lists.newArrayList();
        }
        String storeId = find.getStoreId();
        Integer serviceType = Optional.ofNullable(find.getServiceType()).orElse(0);

        //2024-03-20 data-report保证返回的是最近的数据，不再需要排序
        // 先排序，后筛选，买过的菜由es迁移至redis，仅返回商品id，不再用销量与最近一次的时间进行排序
//        itemSummaries = itemSummaries.stream()
//                .filter(p -> Objects.nonNull(p.getBoughtTimes()))
//                .sorted(Comparator.comparing(BoughtItem::getLatestBuyTime, Comparator.nullsFirst(Long::compareTo))
//                        .reversed()
//                        .thenComparing(BoughtItem::getBoughtTimes, Comparator.nullsFirst(BigDecimal::compareTo)
//                                .reversed()))
//                .collect(Collectors.toList());

        List<String> itemIds = itemSummaries.stream()
                .map(BoughtItem::getItemId)
                .distinct()
                .collect(Collectors.toList());

        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setServiceType(serviceType);
        itemQuery.setSaleTerminal(Constants.SaleTerminal.MINI);
        ListResult<ItemDto> itemResult = itemService.findItemDetailsByIdAndQuery(storeId, itemIds, itemQuery, false, null);
        List<ItemDto> items = Optional.ofNullable(itemResult)
                .map(ListResult::getRecords)
                .orElseGet(Lists::newArrayList);

        // 过滤非售卖时间商品
        items = itemHelper.filterItemNotInSaleTime(storeId, items);

        if (CollectionUtils.isEmpty(items)) {
            return Lists.newArrayList();
        }

        Map<String, ItemDto> itemIdToItemMap = items.stream()
                .collect(Collectors.toMap(p -> p.getItem().getId(), Function.identity(), (k1, k2) -> k1));

        // 需要过滤在开台必点中的商品
        FindOpenTableMustOrderRequest mustOrderRequest = new FindOpenTableMustOrderRequest();
        mustOrderRequest.setBizStoreId(storeId);
        mustOrderRequest.setServiceType(serviceType);
        ListResult<OpenTableMustOrderItem> mustOrderItemResult = openTableMustOrderService.find(mustOrderRequest);
        Set<String> mustOrderItemIds = Optional.ofNullable(mustOrderItemResult)
                .map(ListResult::getRecords)
                .orElseGet(Lists::newArrayList)
                .stream()
                .map(OpenTableMustOrderItem::getItemId)
                .collect(Collectors.toSet());

        // 返回结果
        List<ItemDto> filterItems = Lists.newArrayList();
        for (BoughtItem boughtItem : itemSummaries) {
            ItemDto tmpItem = itemIdToItemMap.get(boughtItem.getItemId());
            boolean available = this.checkItemAvailableForBought(tmpItem, mustOrderItemIds);
            if (available) {
                filterItems.add(tmpItem);
            }
            if (filterItems.size() >= BOUGHT_ITEM_MAX_SHOW_COUNT) {
                break;
            }
        }

        return filterItems;
    }

    /**
     * 检查点过的菜合法性
     *
     * @param itemDto
     * @param mustOrderItemIds
     * @return
     */
    private boolean checkItemAvailableForBought(ItemDto itemDto, Set<String> mustOrderItemIds) {
        if (Objects.isNull(itemDto) || Objects.isNull(itemDto.getItem())) {
            return false;
        }
        // 称重
        if (Objects.equals(itemDto.getItem().getUnitType(), 1)) {
            return false;
        }
        // 不在可售时段
        if (!ItemHelper.curTimeForSale(itemDto)) {
            return false;
        }
        // 下架
        if (!itemDto.getItem().getForSale()) {
            return false;
        }
        // 售罄
        if (itemDto.getItem().getOutOfStock()) {
            return false;
        }
        // 在必点商品中
        if (mustOrderItemIds.contains(itemDto.getItem().getId())) {
            return false;
        }
        if (apolloConfigHelper.getBooleanConfigValueByKey("recentItemsFilterPackage", false)) {
            // 过滤套餐商品
            if (ProductTypeEnum.PACKAGE.name().equals(itemDto.getItem().getItemType())) {
                return false;
            }
        }

        // 库存少于起售份数
        Integer minSaleNum = itemDto.getItem().getMinSaleNum();
        Integer stock = itemDto.getItem().getSku();
        if (Objects.nonNull(stock) && Objects.nonNull(minSaleNum)) {
            return stock > minSaleNum;
        }
        return true;
    }

}
