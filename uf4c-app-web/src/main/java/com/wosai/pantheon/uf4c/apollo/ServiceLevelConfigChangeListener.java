package com.wosai.pantheon.uf4c.apollo;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.wosai.pantheon.uf4c.gather.ApolloConfigHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * apollo配置更新的回调listener
 */
@Configuration
public class ServiceLevelConfigChangeListener {

    @Autowired
    ApolloConfigHelper apolloConfigHelper;

    @Value("${service.degrade.config.uf4c-app}")
    String serviceDegradeConfig;


    @ApolloConfigChangeListener(value = { "marketing.common-config" }, interestedKeys = {"service.degrade.config.uf4c-app"})
    public void serviceDegradeConfigOnChange(ConfigChangeEvent configChangeEvent){

        ConfigChange configChange = configChangeEvent.getChange("service.degrade.config.uf4c-app");


        if (configChange == null){
            return;
        }

        String newConfigValue = configChange.getNewValue();
        initServiceDegradeConfig(newConfigValue);
    }

    @PostConstruct
    public void init(){
        initServiceDegradeConfig(serviceDegradeConfig);
    }


    private void initServiceDegradeConfig(String configValue){

        if (StringUtils.isBlank(configValue)){
            apolloConfigHelper.setServiceDegradeConfig(new ApolloConfigHelper.ServiceDegradeConfig());
            return;
        }

        ApolloConfigHelper.ServiceDegradeConfig serviceDegradeConfig = JSON.parseObject(configValue,ApolloConfigHelper.ServiceDegradeConfig.class);

        if (serviceDegradeConfig == null){
            serviceDegradeConfig = new ApolloConfigHelper.ServiceDegradeConfig();
        }

        apolloConfigHelper.setServiceDegradeConfig(serviceDegradeConfig);
    }
}
