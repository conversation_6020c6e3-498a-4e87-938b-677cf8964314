package com.wosai.pantheon.uf4c.service.apisix;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.DeliverFeeRequest;
import com.wosai.pantheon.uf4c.model.dto.OrderPrintRequest;
import com.wosai.pantheon.uf4c.util.TimeSplit;

import java.util.List;
import java.util.Map;

@JsonRpcService(value = "/rpc/order/v1")
public interface OrderServiceV1 {

    Long getPackAmount(ApiRequest apiRequest);

    Map getDeliveryFee(ApiRequest<DeliverFeeRequest> apiRequest);

    List<TimeSplit.TimeResult> getTimeSplit(ApiRequest apiRequest);

    List<TimeSplit.TimeResult> getBookTimeSplit(ApiRequest apiRequest);


    String notify(ApiRequest<Map> request);


    String printByTemplate(ApiRequest<OrderPrintRequest> apiRequest);

    String print(ApiRequest<OrderPrintRequest> apiRequest);

    Map getCashierParams(ApiRequest<Map> apiRequest);
}
