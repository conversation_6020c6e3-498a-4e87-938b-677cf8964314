package com.wosai.pantheon.uf4c.util;

import com.googlecode.jsonrpc4j.JsonRpcHttpClient;
import com.googlecode.jsonrpc4j.ProxyUtil;
import lombok.SneakyThrows;

import java.net.URL;
import java.util.Collections;

public class RpcProxyUtil {

    @SneakyThrows
    public static <T> T proxy(String serviceUrl, Class<T> instance, String serviceName, int readTimeout, int connectionTimeout) {
//        JsonProxyFactoryBeanTracerV146 proxy = new JsonProxyFactoryBeanTracerV146();
//        proxy.setServiceUrl(serviceUrl);
//        proxy.setServerName(serviceName);
//        proxy.setServiceInterface(instance);
//        proxy.setReadTimeoutMillis(readTimeout);
//        proxy.setConnectionTimeoutMillis(connectionTimeout);
//        return (T) proxy.getObject();
        JsonRpcHttpClient rpcClient = new JsonRpcHttpClient(
                new URL(serviceUrl),
                Collections.singletonMap("User-Agent", serviceName)
        );
        rpcClient.setReadTimeoutMillis(readTimeout);
        rpcClient.setConnectionTimeoutMillis(connectionTimeout);
        return ProxyUtil.createClientProxy(RpcProxyUtil.class.getClassLoader(), instance, rpcClient);
    }
}
