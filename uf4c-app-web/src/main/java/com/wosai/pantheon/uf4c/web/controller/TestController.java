package com.wosai.pantheon.uf4c.web.controller;

import com.wosai.market.merchant.api.CustomerStoreRemoteService;
import com.wosai.market.merchant.api.StoreRemoteService;
import com.wosai.market.merchant.dto.StoreSetDTO;
import com.wosai.market.merchant.dto.customer.request.StoreDetailRequest;
import com.wosai.market.merchant.dto.response.StoreDetailResponse;
import com.wosai.market.tethys.api.dto.DiscountInfo;
import com.wosai.market.tethys.api.dto.Discounts;
import com.wosai.market.tethys.api.dto.SkuInfo;
import com.wosai.market.tethys.api.dto.request.SingleActivityDetailRequest;
import com.wosai.market.tethys.api.service.DiscountsRemoteService;
import com.wosai.market.tethys.api.service.SingleActivityRemoteService;
import com.wosai.smartbiz.base.exceptions.ParamException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/test")
public class TestController {
    @Autowired
    private SingleActivityRemoteService singleActivityRemoteService;

    @Autowired
    private StoreRemoteService storeRemoteService;

    @Autowired
    private CustomerStoreRemoteService customerStoreRemoteService;

    @Autowired
    private DiscountsRemoteService discountsRemoteService;

    @PostMapping("/singleActivity")
    public List<SkuInfo> singleActivity(@RequestBody SingleActivityDetailRequest request) {
        return singleActivityRemoteService.detail(request);
    }

    /**
     * mock2
     * @param request
     * @return
     */
    @PostMapping("/store")
    public StoreDetailResponse store(@RequestBody Map request) {
        return storeRemoteService.storeDetailInfo(request);
    }

    /**
     * mock1
     * @param request
     * @return
     */
    @PostMapping("/customerStore")
    public com.wosai.market.merchant.dto.customer.response.StoreDetailResponse customerStore(@RequestBody StoreDetailRequest request) {
        return customerStoreRemoteService.storeDetailInfo(request);
    }

    /**
     * 原始的
     * @return
     */
    @GetMapping("/storeSet")
    public StoreSetDTO storeSet(@RequestParam Integer id) {
        throw new ParamException("liwenmao");
    }

    @PostMapping("/discounts")
    public Discounts discounts(@RequestBody DiscountInfo discountInfo) {
        return discountsRemoteService.findDiscounts(discountInfo);
    }
}
