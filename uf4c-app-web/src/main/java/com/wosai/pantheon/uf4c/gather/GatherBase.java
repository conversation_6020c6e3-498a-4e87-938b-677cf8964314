package com.wosai.pantheon.uf4c.gather;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wosai.pantheon.uf4c.constant.MpPageSceneEnums;
import com.wosai.pantheon.uf4c.model.ErrorInfo;
import com.wosai.pantheon.uf4c.model.GatherRequest;
import com.wosai.pantheon.uf4c.web.exception.ReturnCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;

import static com.wosai.pantheon.uf4c.gather.GatherBase.Delayer.delayer;
import static net.logstash.logback.argument.StructuredArguments.keyValue;

@Slf4j
public class GatherBase {
    public static final ThreadPoolExecutor executorNew = new ThreadPoolExecutor(25, 50, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(10000));
    public static final ThreadPoolExecutor userExecutor = new ThreadPoolExecutor(25, 50, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(10000));
    public static final ThreadPoolExecutor storeExecutor = new ThreadPoolExecutor(25, 50, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(10000));
    public static final ThreadPoolExecutor goodsExecutor = new ThreadPoolExecutor(25, 50, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(10000));
    public static final ThreadPoolExecutor activityExecutor = new ThreadPoolExecutor(25, 50, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(10000));
    // 下单聚合接线程池
    public static final ThreadPoolExecutor orderMainExecutor = new ThreadPoolExecutor(25, 50, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(10000));
    // 下单聚合接线程池
    public static final ThreadPoolExecutor orderExtraExecutor = new ThreadPoolExecutor(25, 50, 300, TimeUnit.SECONDS, new LinkedBlockingDeque<>(10000));

    public static CompletableFuture<Void> within(CompletableFuture<Void> future, long timeout, TimeUnit unit) {
        final CompletableFuture<Void> timeoutFuture = timeoutAfter(timeout, unit);
        return future.applyToEither(timeoutFuture, Function.identity());
    }


    public static CompletableFuture<Void> timeoutAfter(long timeout, TimeUnit unit) {
        CompletableFuture<Void> result = new CompletableFuture<>();
        // timeout 时间后 抛出TimeoutException 类似于sentinel / watcher
        delayer.schedule(() -> result.completeExceptionally(new TimeoutException()), timeout, unit);
        return result;
    }


    public static void setDuration(Map<String, Object> dataMap, String key, Long duration) {
        Map<String, Long> durationMap = (Map<String, Long>) MapUtils.getMap(dataMap, "duration");
        if (Objects.isNull(durationMap)) {
            durationMap = new HashMap<>();
            dataMap.put("duration", durationMap);
        }
        durationMap.put(key, duration);
    }

    public static void durationStart(Map<String, Object> dataMap, String key) {
        try {
            Map<String, Map> durationMap = (Map<String, Map>) MapUtils.getMap(dataMap, "duration");
            if (MapUtils.isEmpty(durationMap)) {
                durationMap = new HashMap<>();
            }
            Map<String, Long> duration = new HashMap<>();
            duration.put("start", System.currentTimeMillis());
            dataMap.put("duration", durationMap);
            durationMap.put(key, duration);
        } catch (Exception ignored) {
        }

    }

    public static void durationEnd(Map<String, Object> dataMap, String key) {
        try {
            Map<String, Map> durationMap = (Map<String, Map>) MapUtils.getMap(dataMap, "duration");
            if (MapUtils.isNotEmpty(durationMap)) {
                Map<String, Long> duration = (Map<String, Long>) MapUtils.getMap(durationMap, key);
                if (MapUtils.isNotEmpty(duration)) {
                    long end = System.currentTimeMillis();
                    duration.put("end", end);
                    duration.put("cost", end - MapUtils.getLongValue(duration, "start", end));
                }

            }
        } catch (Exception ignored) {
        }
    }

    public static void setError(Map<String, Object> dataMap, ReturnCode returnCode) {
        List<ErrorInfo> errors = (List<ErrorInfo>) MapUtils.getObject(dataMap, "errors");
        if (CollectionUtils.isEmpty(errors)) {
            errors = new ArrayList<>();
        }
        errors.add(new ErrorInfo(returnCode.getCode(), returnCode.getMessage()));
        dataMap.put("errors", errors);
        if (returnCode.getCode().startsWith("E")) {
            setClosing(dataMap, Lists.newArrayList(String.format("[%s]数据加载错误", returnCode.getCode())));
        }
    }

    /**
     * 处理空态页
     *
     * @param dataMap
     * @param closing
     */
    public static void setClosing(Map<String, Object> dataMap, List<String> closing) {
        Map<String, Object> extraMap = (Map<String, Object>) MapUtils.getMap(dataMap, "extra");
        if (Objects.isNull(extraMap)) {
            extraMap = new HashMap<>();
            dataMap.put("extra", extraMap);
        }
        extraMap.put("closing", closing);
        extraMap.put("page", MpPageSceneEnums.CLOSING.getPage());
    }

    public static boolean isClosing(Map<String, Object> dataMap) {
        Map<String, Object> extraMap = (Map<String, Object>) MapUtils.getMap(dataMap, "extra");
        return MapUtils.isNotEmpty(extraMap) && Objects.nonNull(MapUtils.getObject(extraMap, "closing"));
    }


    public static void logError(String message, String method, GatherRequest request, Exception e) {
        log.error(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : ""),
                keyValue("req_id", request.getReqId()),
                e);
    }

    public static void logError(String message, String method, Object request, Exception e) {
        log.warn(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : ""),
                e);
    }

    public static void logInfo(String message, String method, GatherRequest request) {
        log.info(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : ""),
                keyValue("req_id", request.getReqId()));
    }

    public static void logWarn(String message, String method, Object request, Exception e) {
        log.warn(message,
                keyValue("method", method),
                keyValue("arguments", Objects.nonNull(request) ? JSON.toJSONString(request) : ""),
                e);
    }

    static final class Delayer {
        static ScheduledFuture<?> delay(Runnable command, long delay,
                                        TimeUnit unit) {
            return delayer.schedule(command, delay, unit);
        }

        static final class DaemonThreadFactory implements ThreadFactory {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setDaemon(true);
                t.setName("CompletableFutureDelayScheduler");
                return t;
            }
        }

        static final ScheduledThreadPoolExecutor delayer;

        // 注意，这里使用一个线程就可以搞定 因为这个线程并不真的执行请求 而是仅仅抛出一个异常
        static {
            (delayer = new ScheduledThreadPoolExecutor(
                    1, new DaemonThreadFactory())).
                    setRemoveOnCancelPolicy(true);
        }
    }


}
