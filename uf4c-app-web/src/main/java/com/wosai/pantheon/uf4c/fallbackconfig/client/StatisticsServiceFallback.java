package com.wosai.pantheon.uf4c.fallbackconfig.client;

import com.wosai.market.service.StatisticsService;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.JsonRPCFallbackDefine;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatcher;
import com.wosai.middleware.hera.toolkit.jsonrpc4j.match.NamedElement;

import static com.wosai.middleware.hera.toolkit.jsonrpc4j.match.ElementMatchers.is;

public class StatisticsServiceFallback extends JsonRPCFallbackDefine {
    @Override
    public ElementMatcher<NamedElement.TypeElement> handleClass() {
        return is(StatisticsService.class);
    }

    @Override
    public Provider getProvider() {
        return Provider.CLIENT;
    }
}
