package com.wosai.pantheon.uf4c.web.controller.v3;

import com.wosai.market.trade.modal.PayResult;
import com.wosai.market.trade.modal.upayGateway.PrePayResult;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.model.dto.InitRequest;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.service.apisix.OrderRoundService;
import com.wosai.pantheon.uf4c.service.OrderHelper;
import com.wosai.pantheon.uf4c.util.PrePayUtil;
import com.wosai.smartbiz.oms.api.domain.HsPreCreateOrderVO;
import com.wosai.smartbiz.oms.api.pojo.OrderMainWrapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;


/**
 * <AUTHOR> zhen.pan, xuyuanxiang
 * @since 2019/4/9
 */
@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@RestController
@RequestMapping(path = "/api/v3/orders/round", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
@Slf4j
public class RoundOrderController {


    @Autowired
    private OrderRoundService orderRoundService;

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    PrePayUtil prePayUtil;



    @PostMapping("/init")
    @ResponseBody
    @SneakyThrows
    public OrderMainWrapper init(@RequestBody InitRequest request) {
        return orderRoundService.init(new ApiRequest<>(request));
    }


    @PostMapping("/addGoods")
    @ResponseBody
    @SneakyThrows
    public OrderMainWrapper addGoods(@RequestBody InitRequest request) {
        return orderRoundService.addGoods(new ApiRequest<>(request));
    }

    @PostMapping("/pay")
    @ResponseBody
    @SneakyThrows
    public PayResult roundPay(@RequestBody PayRequest request) {
        return orderRoundService.roundPay(new ApiRequest<>(request));
    }

    private String getStringValue(Long amount) {
        if (amount == null) {
            return null;
        }
        return String.valueOf(amount);
    }

    private PayResult convert2PayResult(HsPreCreateOrderVO preCreateOrderVO) {
        PayResult payResult = new PayResult();
        //兼容之前的前端程序，这里clientSn也返回sn
        payResult.setClientSn(preCreateOrderVO.getOrderSn());
        payResult.setSn(preCreateOrderVO.getOrderSn());
        payResult.setTotalAmount(getStringValue(preCreateOrderVO.getTotalAmount()));
        payResult.setNetAmount(getStringValue(preCreateOrderVO.getNetAmount()));
        payResult.setTotalDiscount(getStringValue(preCreateOrderVO.getDiscountAmount()));
        if (preCreateOrderVO.getWapPayRequest() != null) {
            PrePayResult.biz_response.data.WapPayRequest wapPayRequest = new PrePayResult.biz_response.data.WapPayRequest();
            wapPayRequest.setAppId(preCreateOrderVO.getWapPayRequest().getAppId());
            wapPayRequest.setNonceStr(preCreateOrderVO.getWapPayRequest().getNonceStr());
            wapPayRequest.setPackage(preCreateOrderVO.getWapPayRequest().getPackageStr());
            wapPayRequest.setPaySign(preCreateOrderVO.getWapPayRequest().getPaySign());
            wapPayRequest.setSignType(preCreateOrderVO.getWapPayRequest().getSignType());
            wapPayRequest.setTimeStamp(preCreateOrderVO.getWapPayRequest().getTimeStamp());
            wapPayRequest.setTradeNO(preCreateOrderVO.getWapPayRequest().getTradeNO());
            payResult.setWapPayRequest(wapPayRequest);
        }

        if (preCreateOrderVO.getRiskInfo() != null) {
            payResult.setHasRisk(preCreateOrderVO.getRiskInfo().isHashRisk());
            payResult.setAction(preCreateOrderVO.getRiskInfo().getAction());
            payResult.setRiskId(preCreateOrderVO.getRiskInfo().getRiskId());
        }
        return payResult;
    }


    @RequestMapping("/paylock/cancel")
    @ResponseBody
    @SneakyThrows
    public Boolean canclePayLock(@RequestParam Map map) {
        return orderRoundService.canclePayLock(ApiRequest.buildGetRequest(map));
    }

}
