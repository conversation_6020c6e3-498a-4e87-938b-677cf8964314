spring:
  application:
    name: uf4c-app

dingding:
  robot:
    url: https://oapi.dingtalk.com/robot/send?access_token=21d3472fffebdcd8db241e92b9c42873aa26ad1448d034841072abca44c5dc9b

# redis
redis:
  base:
    host: r-bp1tciwqheqxp3wq3s.redis.rds.aliyuncs.com
    port: 6379
    pass: J04zCkL&v32Tsplj
    db: 9
  gather:
    host: r-bp10c4612e22b8e4.redis.rds.aliyuncs.com
    port: 6379
    pass: T1@SZ)x5=&4cCYsm
    db: 168




# jedis
jedis:
  maxTotal: 200
  maxIdle: 50
  maxWait: 10000
  connect:
    timeout: 3000

#port
server:
  port: 8080
  session:
    timeout: 1800

service:
  rpc-connection-timeout-mills: 200
  rpc-read-timeout-mills: 400
  async_timeout: 400
  ufood-core:
    url: http://ufood-core
  notify:
    host: https://uf4c-app.shouqianba.com
  upay-gateway:
    url: http://upay-gateway.vpc.shouqianba.com
  wap-pro:
    url: https://qr.shouqianba.com/gateway
  open-wap:
    url: https://open-wap.shouqianba.com
  core-business:
    url: http://app-core-business
  weixin-service:
    url: http://weixin-service
  mcc:
    url: http://mcc
  uitem-core:
    url: http://uitem-core
  customer-user:
    url: http://customer-user
  market-trade:
    url: http://market-trade
  tethys:
    url: http://tethys
  merchant-service: http://merchant
  awesomeOrder: http://awesome-order
  uprint-core: http://uprint-core
  painter:
    url: http://painter
  aop:
    url: http://aop-gateway
  merchant-user:
    url: http://merchant-user-service
  round-order-center:
    url: http://order-center
  goods-center:
    url: http://goods-center
  data-reports:
    url: http://data-report
  awesome-goods:
    url: http://awesome-goods
  campus-delivery:
    url: http://campus-takeout-delivery
  upay-qrcode:
    url: http://upay-qrcode-internal.shouqianba.com
  jjz-dataworks:
    url: http://jjz-dataworks
  mini-apps-manage:
    url: http://mini-apps-manage
  voicebox-push:
    url: http://app-push-service
  data-events:
    url: http://data-events
  sodexo:
    pay: http://upay-gateway.vpc.shouqianba.com/upay/support/sodexo/expenseByToken
    balance: http://upay-gateway.vpc.shouqianba.com/upay/support/sodexo/balanceQuery
  mock:
    url: https://eugenie-mock.shouqianba.com
  campus-center:
    url: http://campus-center
  smart-goods:
    url: http://smart-goods
  smart-translation-manager:
    url: http://smart-translation-manager
  smart-template:
    url: http://smart-template
  outer-service-adapter:
    url: http://outer-service-adapter

wosai:
  cpu-num: ${WS_CPU_NUM:4}
  concurrency: ${WS_CONCURRENCY:32}
  h5: https://uf4c-app.shouqianba.com/pages/index/index
  h5-qrenter-prefix: https://uf4c-app.shouqianba.com/
  secure-cookie-enable: false
  remember-me-ttl: 31536000
  request-timeout: 2000
  rpc:
    read-timeout: 2000
    connection-timeout: 2000
  alipay:
    app-id: 2019042764324175
    sign-type: RSA2
    private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCVHQ1d8A1dt61WYqXPm/urp5fCk4Blkb96tsHf3F/Z6Q7fcgxDPwcMp47ePhE2suq1Qor6blVvXAiqp6V5E/u7coJZErjc8W+2D1ORV8jssy05OyswyJq41/d1y5LGQV/uAY+Y8zbxjRxgA04EwhZdDS+gF6YbLR+UJh3jZ0l3wbZRU8K1znIpX1QtE5NtoJ8dheXYFtAcqOIAyyEB/gl8BJxxhuJieRMoH3a1KirYZY6ccSxBB5oEDVlPhIPqdWw2fyfonYgVoyUozjjPz7HGTG6lE15lKMtMZ6KzGkkoOcYh3kOsqf9sQWtSEx4zat9UHUDXtPbGI48nNlPnKF7fAgMBAAECggEAEbXpqIaij8cJJ4GAADCeq7y0IcAzN90fyQt764LZP6CWy6NYIYSS6cXdCjiFAuItTmog563DJ/7eaKaQ2NiuxYzMPpgirNtgUVdwrYqnqrV9YZbNgnXRKrM8CRmszS9mnXZCAACjPf1sqVq7IYWL8nFmfrVQXVn2rjn4wtG5wCdrNAjLJ2S/je8Q3C+g2vyPWGvG26lemY/RJFGIwlUiWokMrESqMwjhQ+eu4axF8PepLvW+CinqBZfoNDstDKAeOpa8Rc3D4GAeE6/MUnsHYsVK/aCHL584mIUnkcnzgPO9AsL4Str/Y97kpZWhbqaduieH7c385GIIy3WaB26pQQKBgQDaip+MPV1m3EdjCjGIBxSm63R1dxh9rZVhJCmPKZRVYcI3/5gO7wdyIp0BrWcX0/YvJot1okumSRpjbiv54i6o7VGBHmVXWoRzw+3vY40q6d4L1EYzHEB7m2NBoowrj2bVG1IMx125hwtAEMJMWz7WC962R9q64t5fLlD+uzVTRQKBgQCuq//AEU7gEFym1YOwBH55epUDvEhxYjx1aZEXHKBGZi82OhxksbK8wM7twBgRyuTo2Ji4/bdXdnqIB6XZfweGKCCIpZS/ZPQe+7RZIlla/r5xdBK7uU4gFASjqihiK+yHkf5NEWQkQVq5LxYuDZSbiuZVPU5v54PmH1yZ8HcZ0wKBgG1P+4A9TZPeyQJcHexphkX77jO3T5PFmLuPSA5pAN44WNqDyX5mx3WqpJgrMS71AeYRxJ4hgO97D2z+tZZ3MVmj4ynPl2c7SpdRQJmGVR1K/5sxIa/9CLALSHmTIcPEJe6gjMkBi/r59oGkIFvrJCISq+LHTDxAsHQxiafw4jp1AoGAYCgTnLpqf3LCbp1v50AyF9HhorLN9o0r1z8MPnrlyJuMNf/7jXs9eCd0QvHSzkixh3VlbcFOYvm4LpZ3OKpzi+kfL9pnbo22zi+mcsM0CaHfWTma7njuhz4955NOs+yWQ3OdWPPP+x13QianwSZ8vrvSXO5o9bWvqMlvAteb2T0CgYBXan74AeMRxCZXlTC7DfGOGhlYzaWAv6W59Cv9a1uVj5/vo3/dskTvG3E2cB6e6Nnm00+LV8/m+GImQ7fN1RunanSWZjqYFE4UjuB+s9oNSDlN1Xzo3RJpJGjZC/7lcpNgkgjINFFWRZCSo6OaffvHg1PzxpBQcoCsbp1z/2k+kA==
    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6RrtsNlauS416OlRhCTGROc1G50fVS4nSL0CykEOZzwmHf5getuuDWr8W9RfdJwjtT1JAxUDzdtPXiJ0Vou3Bhg39T+ciVrw4pwXgcYzCrH5Yg3YM/v5oSjmocTCqcJbFxvw9kCqDmjYG2Epozvz6FSk32G5hxgHwXw7ysrVdWry+PRAbCzwY5O6HjMV1SLpxAGRcNumwI3Wrgnz7rBymhbEddzcEJRvqsbjO9L+tOfNqHa3gCuE6uf6zyqJPcPXzzdmZL2jjo/iQI+UntxXqbtBKUOceHCdJv1ydhztJC/JRlah2X2x7wBk99ypHC7dFLzMjRen0xLXkuflJxKOMwIDAQAB
  weixin:
    app-id: wx0fd179d3b11b7b34
    secret: cd68e802c15aeaf333456ffa6d035b48
  weixin-platform:
    uri: https://weixin.open.wosai.cn/weixinservice/oauth/auto.jsp
    app-id: wx0fd179d3b11b7b34
    white-list: wx0fd179d3b11b7b34,wx7fc420ba498b4b19,wx37135a21fb63f366,wx80505d7058490d2a
  push:
    template-id: TAKEOUTPUSH1
  notice:
    template-id: TAKEOUT00001
  aop:
    code: 58IXWQQA9ULX


callback:
  profitSharingCallback: http://market-trade.internal.shouqianba.com/hook/profitSharing

app:
  id: uf4c-app # 使用的 Apollo 的项目（应用）编号,等同于 classPath:/META_INF/app.properties
apollo:
  bootstrap:
    enabled: true
    # will inject 'application' and 'TEST1.apollo' namespaces in bootstrap phase
    namespaces: application,marketing.common-config

front:
  error404: https://uf4c-app.shouqianba.com/pages/fallback/index
  error500: https://uf4c-app.shouqianba.com/pages/error/index
  order-return-url: https://uf4c-app.shouqianba.com/pages/payment/result
  ua-incorrect-url: https://uf4c-app.shouqianba.com/pages/fallback/index

qrcode:
  jjzRegex: (http|https)://99zhe[a-zA-Z.]*/(?<sceneCode>[sdtabcpg])/(?<encryptedData>[0-9a-zA-Z]+)
  sqbRegex: .*m.sqbe.cn.*
  jjzQrCodeDomainPrefix: https://99zhe.com
  jjzDomain: https://99zhe.com



refund:
  apply:
    push:
      # app push对应的服务标识
      aop-code: 58IXWQQA9ULX
      # 服务通知对应的服务标识
      notice-aop-code: L9ZOYSQZXUXV
      cancel-takeout:
        template-id: 85CWMXXMZGJX
      cancel-pre:
        template-id: PRUSZYGFOCNR
      refund-takeout:
        template-id: 5ZL1SLUKJNYY
      refund-pre:
        template-id: LJRSHM6VJ8MG
      refund-revoke:
        template-id: TM4HZ9NTOZKU
    voice:
      cancel-takeout: cancelWaimai
      cancel-pre: cancelZiqu
      refund-takeout: refundWaimai
      refund-pre: refundZiqu

poster:
  jielong: https://m.sqbe.cn/62?s=p&p=%s


test-env: false