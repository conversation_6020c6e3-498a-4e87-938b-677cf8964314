spring:
  application:
    name: uf4c-app


dingding:
  robot:
    url: https://oapi.dingtalk.com/robot/send?access_token=7a4d95c336bb5fce70997ef9d908fdc88b209213befd9a85de7467fb20fe8166
# redis
redis:
  base:
    host: r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    pass: roFXzHwXPY3RnI%5
    db: 9
  gather:
    host: r-8vbm3pb0p4sw7cnuk5.redis.zhangbei.rds.aliyuncs.com
    port: 6379
    pass: sbnRrdGf@l^9HM7I
    db: 168
# jedis
jedis:
  maxTotal: 200
  maxIdle: 50
  maxWait: 10000
  connect:
    timeout: 3000

#port
server:
  port: 80
  session:
    timeout: 1800

service:
  rpc-connection-timeout-mills: 1000
  rpc-read-timeout-mills: 3000
  async_timeout: 3000
  ufood-core:
    url: http://ufood-core.beta.iwosai.com
  notify:
    host: https://uf4c-app.test.shouqianba.com
  upay-gateway:
    url: http://upay-gateway.beta.iwosai.com
  wap-pro:
    url: http://qr.test.shouqianba.com/gateway
  open-wap:
    url: http://wap.test.shouqianba.com
  core-business:
    url: http://core-business.beta.iwosai.com
  weixin-service:
    url: http://weixin-service.beta.iwosai.com
  mcc:
    url: http://mcc.beta.iwosai.com
  uitem-core:
    url: http://uitem-core.beta.iwosai.com
    #url: http://127.0.0.1:19999
  customer-user:
    url: http://customer-user.beta.iwosai.com
  market-trade:
    url: http://market-trade.beta.iwosai.com
  open-wap-merchant-whitelist:
    on-off: off
  open-wap-merchant-blacklist:
    on-off: off
  open-wap-mode: 02
  merchant-service: http://merchant.beta.iwosai.com
  awesomeOrder: http://awesome-order.beta.iwosai.com
  tethys:
    url: http://tethys.beta.iwosai.com
  uprint-core: http://uprint-core.beta.iwosai.com
  painter:
    url: http://painter.beta.iwosai.com
  aop:
    url: http://aop-gateway.beta.iwosai.com
  merchant-user:
    url: http://merchant-user-service.beta.iwosai.com
  round-order-center:
    url: http://order-center.beta.iwosai.com
  goods-center:
    url: http://goods-center.beta.iwosai.com
  data-reports:
    url: http://data-report.beta.iwosai.com
  awesome-goods:
    url: http://awesome-goods.beta.iwosai.com
  campus-delivery:
    url: http://campus-takeout-delivery-2410.iwosai.com
  upay-qrcode:
    url: http://upay-qrcode.beta.iwosai.com
  jjz-dataworks:
    url: http://jjz-dataworks
  mini-apps-manage:
    url: http://mini-apps-manage.beta.iwosai.com
  voicebox-push:
    url: http://app-push-service.iwosai.com
  data-events:
    url: http://data-events.beta.iwosai.com
  sodexo:
    pay: http://upay-gateway.beta.iwosai.com/upay/support/sodexo/expenseByToken
    balance: http://upay-gateway.beta.iwosai.com/upay/support/sodexo/balanceQuery
  mock:
    url: http://deepmock.beta.iwosai.com
  campus-center:
    url: http://campus-center.beta.iwosai.com
  smart-goods:
    url: http://smart-goods.beta.iwosai.com
  smart-translation-manager:
    url: http://smart-translation-manager.beta.iwosai.com
  smart-template:
    url: http://smart-template.beta.iwosai.com
  outer-service-adapter:
    url: http://outer-service-adapter.beta.iwosai.com


app:
  id: uf4c-app # 使用的 Apollo 的项目（应用）编号,等同于 classPath:/META_INF/app.properties
  name: uf4c-app
  group: smart
apollo:
  bootstrap:
    enabled: true
    # will inject 'application' and 'TEST1.apollo' namespaces in bootstrap phase
    namespaces: application,marketing.common-config

callback:
  profitSharingCallback: http://market-trade.beta.iwosai.com/hook/profitSharing

wosai:
  cpu-num: ${WS_CPU_NUM:4}
  concurrency: ${WS_CONCURRENCY:32}
  h5: https://uf4c-app.test.shouqianba.com/pages/index/index
  h5-qrenter-prefix: https://uf4c-app.test.shouqianba.com/
  secure-cookie-enable: false
  remember-me-ttl: 31536000
  request-timeout: 2000
  rpc:
    read-timeout: 2000
    connection-timeout: 2000
  #  alipay:
  #    app-id: 2017111609967071
  #    sign-type: RSA2
  #    private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCR9DVA+034RgTfa7qp9rk0gDq6Jxd9dIycFLJC/tf0zLCs09uHOGR3McfQcJ+c7T/2TEJS8XKAC08PW66vynLhQCkpLbAHTliLK+4CZ/a7yLlI/V/nuvvu5S9IIzAzROS3Zv3vtK0pUQQFFoOoEnLBM+J2x83TlLy33wLbrJFilVzmc/kPMUZ1cFZ+N28Hkm/9SUWIxhgJ+rNucdSVRemRkVlm0RS7lVB4WDAc05RFWB4EydW2iGfyLF9Pfo1LEeYli95AutyEkGr85ZmwDhNXcEQSEiGJJQMcyUBzU7RGLSgygPOgb6O4XqsKG+naX1MfegSPIiMGGMs8MVfFY4ujAgMBAAECggEAajLtRNkWhJ4+EWtLqTwSyra7BJRJLUnOvgr5InJYJJGDxF1rENrhjzEzmOE54m9m6QnYAI4aqDySTDSDyPlf2bJbwNCzkr1ZA/r09d870qtc1lU2oWCWy98LJkwkWEm6uhBAmVg33yZJ7IHTTkoOIbJlB9SeWWOSZZ/pbnoEV1wJigCVrvn0J0RuL7Db2kJcLcZIpjDW7Unopg6RQKAoEI9YhX7cGlUZ8CE9M0R8pWQYfhovbuHsDeH3ZNmNFFsVmJQg8arrgxbZyEzOPX9oZOExDP03ZdYqr90DACcsgQ1jgMcG4LzLqn8ShypCR4gBfqu0gaaKAy4ewHm9pLvVkQKBgQDHhUjNrI1DJOfGaihxwm9IUJFbBgtLbmRW33jj8vVwe4s/GI+qsdIp93VHY0jVvuPB2NmVFFJ+CqkjYXRswxWS+BBna+taH9efUrE69NpBm4h68B9u3CMI1sJwQ0BMu9xbblvQviDgZ7WaSoOXoZmQ5B+on3PHkEB8loa9lRR1xwKBgQC7RRdJUnkMbHT+C4ngKsQvSVMNInchGIR5ZeLxms07a46jXlT9X8/nhmCZT9l9OVlNvXv8K14FkEvxY8693AtCnMhSTjEnuGHB7lk6GGaP/YrWMA4Nspffa5497GZwrRqQlb+AqVFkLd5u3elrhN/HiwDs1ZA1uLwyyBuWCq3LRQKBgEeieRMvQsC6vWSltMOzVZUXKDkQIHoCto/iSFs/XhP2p3YN8XcNxTFAIaFx6jKkCM4Od4Bo/X03gqarVMu/9zz/R5R6UNPY3ufbHrrWMo6fsw7Aq+h3rTR9Nu5rluh2ApXpN5noyZEIa1FsM3b7lFFLNnqqVT1vc1fIC7SBlAj9AoGAGqPigyi7VZwcLIOhsoUC8aMhsG2lusyNE+P9m9ckuP49BVqHBqBZqBsCpOyqCNlereN1vtLAhlQXqZhEMdgatfgtux+qeHcPWRI6GTydYoBz/rJJXYN5fWwGz9JQZFkLvWPAIWJ01KZ9zRG+gfAeEXnhwM9Eu4+zHho/amHMhW0CgYEAneJxCC7RbBLgLHzkhRDiiWPdTqIY8I0xH7ifyef5qLrP+ad4/5mmczurzLhY3ueaLKLA533fiyYSwEXlpYghsRmCBYzscNWJMFQjMx9pamDyBqP6Gjj8TwKy91+rqKuyVvFGGOaXKRSrGbmuBVhkABgco+BhdcSgr0ppt41C/+E=
  #    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAy5OlvmyXl6pbQG8e7eNxJd1VB0b0cuZI1giLAjyNeP5Eu8AffsW+nbV+9gI7jppUfWNRbcgqM/Mx1G8NVE6OPr/1wUxTZm9ac/dEKwQSqi6KgfM101XVEo2Q1+PVks6GfdxB4ENTTwryokyn3LFz+cpqrdUXjQISDGRN4d7HEB2VxXP9bezPn6aHS2ADFYW1aIhiILJvdY7G9W8AoHgZ9Yhlfn+yVKi1AGA/c4+wINwkl731d+scC4WFsjdRjgcRxZ2uWTadwlhVRq799Slvd2k+ozHO56Gdd6itjDtFVdqIqWKM3V9PEHdBLbLjCcW0m+equhq7P6bRshjmitjzZwIDAQAB
  alipay:
    app-id: 2019042964324366
    sign-type: RSA2
    private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCfCujGrJksdxUPn3xSUOvYXCsh37ybHLS2s9ZxCUuO4z1BbWw2UwW5rNZzS2+l3XlNiwpb5u/8F+Ay37V02Tumm7kMk0Lv9YnLvjnDIwA5Uu+7y71xc7Yhb/1lzszYi6JueJvkswJ3nRsHhyVXc1TaU7oN0yjhBht21LlMsJaDIEgJPVY6XxTe5GYmQM+GjU09Z+f8lZXmzoW1e7rUuZ3PG0h09W4bbqyZUAJXnzfp4vs73Ew3D8+Ut4EFQgFcDe9FfJeVjufaVzG+q6XsCnJw3xEOdY53QZrkA9/+lrV5ylqsS03f1JhX0EWdimD2EGF8yT6f8jh3wtzI23KU+CsPAgMBAAECggEAHf9HwllBlsJrJYQ2Raxfn3Ja7Euv3i2+5ArtcmqHZs7zeciytVhZZxjzZdGfoFAyle6YqJbZQtoZ/l51cjGa76TFdVq9MFdGgzhA2s5aN3WuI+Scua5SduIRkEc+GcXUgDOda7hbycXHGFdkMqddJMTP/dWLC05EldgvjwhrixWaSn3a9+9YVeNHXnMNx7nVXwdn3EL+yt27G9awojlSVQIIp4ZVKrsw/bY+Z5lmSdC22qlnXamoteWGcOjhDf0H1idplp+TM/hy/NLgjRcPnYa5QmdywqDPIiBH074Rae1klpjyZe9ieKJ1uHZksyF5m/rE+BSvLDMlcsbhKx0toQKBgQDZta12HHjaeatiLtPKh9B8by1I58RN0yHa9RmvSNZ1VNDrv/0i2LItS/a0huO9XymMFHq9NcSHZrABXl8PKDMXjYQ9+XlV11Mo9YPOlX1eo7aDtIQrofzVz7pnrGSZERNCSkeZtrng0aJQW88N8b831f2sAQd3+XIOEfz1B4rIvwKBgQC7A8MfuPNqOzrvrREvb15AGwcDfeJDS69cH5enfywRS8nMslcHfsnsXVUTn2eKy4bd0mbBnqEzhScTKY/WH0RdgNrX2+OpgbU2N/AUzpNGnMBc8t+yMYAhaDeeYtlX6Op8zquaK+zrAnFXjKjDtiHNutW+GEQMqxWv1j0Iuc1hsQKBgQC+gMZnmuR5sIuyX21+RLe/mv1SVVQmEYEHev/drOvUQcKK+DXGPz4yes1HDN3NgK73X5hDZwAwxz9uAylFNxT2ICQ+osV0wvWG2sdaht7saG818kjRcW6vnZTsUSK5+gITOLJMpGMkgev5S0z0U7zSXBMcZs4lQoZ2H3Fs7AuaOwKBgEdoaFj0hf1KIHbj3mv+Wj6gE02CA6MugwbqCT4rkOgJyEQg1JpID1HxUUrs0txsWlLYw+OqbmL9tK3ccM+RoMkTKnzBuWw/2rGZKlLHjfSoZcsX8dJYlFQtNw+DLvoiNWbygxObMhuKEz0auFYCdWXYsUSkf1dW/DDn3OY4C/QBAoGAG2Ds99Li+2GERKsWSx+GJH/9dhiSE0TZoAfufdrddVPz1vxyvMLMD3aTbTzx59iuGji4kRZX/aT0e9KDWJ1pfhg+KfFCHBfy2w91WrNn8XeBHaQNOkXo9MgeNbY5oywCrFLlj919JDOHhjtubhtG23sgSr6IyJyJOixtE82z+ss=
    public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnwroxqyZLHcVD598UlDr2FwrId+8mxy0trPWcQlLjuM9QW1sNlMFuazWc0tvpd15TYsKW+bv/BfgMt+1dNk7ppu5DJNC7/WJy745wyMAOVLvu8u9cXO2IW/9Zc7M2Iuibnib5LMCd50bB4clV3NU2lO6DdMo4QYbdtS5TLCWgyBICT1WOl8U3uRmJkDPho1NPWfn/JWV5s6FtXu61LmdzxtIdPVuG26smVACV5836eL7O9xMNw/PlLeBBUIBXA3vRXyXlY7n2lcxvqul7ApycN8RDnWOd0Ga5APf/pa1ecparEtN39SYV9BFnYpg9hBhfMk+n/I4d8LcyNtylPgrDwIDAQAB

  weixin:
    app-id: wxd986b5cd3f7e0be0
    secret: cd68e802c15aeaf333456ffa6d035b48
  weixin-platform:
    uri: https://m.test.shouqianba.com/weixinservice/oauth/auto.jsp
    app-id: wxd986b5cd3f7e0be0
    white-list: wxd986b5cd3f7e0be0,wx7fc420ba498b4b19,wx37135a21fb63f366,wx80505d7058490d2a
  push:
    template-id: TAKEOUTPUSH1
  notice:
    template-id: TAKEOUT00001
  aop:
    code: 58IXWQQA9ULX

front:
  error404: https://uf4c-app.test.shouqianba.com/pages/fallback/index
  error500: https://uf4c-app.test.shouqianba.com/pages/error/index
  order-return-url: https://uf4c-app.test.shouqianba.com/pages/payment/result
  ua-incorrect-url: https://uf4c-app.test.shouqianba.com/pages/fallback/index


qrcode:
  jjzRegex: (http|https)://99zhe[a-zA-Z.]*.com/(?<sceneCode>[sdtabcpg])/(?<encryptedData>[0-9a-zA-Z]+)
  sqbRegex: .*mp.iwosai.com.*
  jjzQrCodeDomainPrefix: http://99zhe.test.shouqianba.com
  jjzDomain: https://99zhe.iwosai.com



refund:
  apply:
    push:
      aop-code: 58IXWQQA9ULX
      notice-aop-code: L9ZOYSQZXUXV
      cancel-takeout:
        template-id: 85CWMXXMZGJX
      cancel-pre:
        template-id: PRUSZYGFOCNR
      refund-takeout:
        template-id: 5ZL1SLUKJNYY
      refund-pre:
        template-id: LJRSHM6VJ8MG
      refund-revoke:
        template-id: TM4HZ9NTOZKU
    voice:
      cancel-takeout: cancelWaimai
      cancel-pre: cancelZiqu
      refund-takeout: refundWaimai
      refund-pre: refundZiqu

poster:
  jielong: https://mp.iwosai.com/62/?s=p&p=%s

test-env: true