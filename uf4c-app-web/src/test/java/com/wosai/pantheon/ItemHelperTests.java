package com.wosai.pantheon;

import com.wosai.pantheon.uf4c.Application;
import com.wosai.pantheon.uf4c.service.ItemHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2023/1/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ItemHelperTests {

    @Autowired
    private ItemHelper itemHelper;

    @Test
    public void testNeedFilterItemNotInSaleTime() {
        boolean result = itemHelper.needFilterItemNotInSaleTime("6c381908-3ff9-4eae-920e-97d8cb16c8f9");
        Assert.assertTrue("需要过滤", result);
    }

}
