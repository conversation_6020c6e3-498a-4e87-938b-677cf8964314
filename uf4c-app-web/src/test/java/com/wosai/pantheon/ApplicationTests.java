package com.wosai.pantheon;

import com.wosai.pantheon.uf4c.Application;
import com.wosai.pantheon.uf4c.util.TimeSplit;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ApplicationTests {

    @Test
    public void contextLoads() {
    }


    @Test
    public void testTime() {

        // 正常情况
        String startTime1 = "08:00";
        String endTime1 = "21:00";
        long presetTime1 = 1649210400000L; // 2022-04-06 10:00:00
        boolean result1 = TimeSplit.isInDate4PresetTime(startTime1, endTime1, presetTime1);
        System.out.println("result1: " + result1); // 预期结果：true

        String startTime2 = "22:00";
        String endTime2 = "03:00";
        long presetTime2 = 1649296800000L; // 2022-04-07 10:00:00
        boolean result2 = TimeSplit.isInDate4PresetTime(startTime2, endTime2, presetTime2);
        System.out.println("result2: " + result2); // 预期结果：false

        String startTime3 = "00:00";
        String endTime3 = "00:00";
        long presetTime3 = 1649226000000L; // 2022-04-06 14:20:00
        boolean result3 = TimeSplit.isInDate4PresetTime(startTime3, endTime3, presetTime3);
        System.out.println("result3: " + result3); // 预期结果：true

        String startTime4 = "09:00";
        String endTime4 = "18:00";
        long presetTime4 = 1649203200000L; // 2022-04-06 08:00:00
        boolean result4 = TimeSplit.isInDate4PresetTime(startTime4, endTime4, presetTime4);
        System.out.println("result4: " + result4); // 预期结果：false

        // 边界条件
        String startTime5 = "00:00";
        String endTime5 = "23:59";
        long presetTime5 = 1649258400000L; // 2022-04-06 23:20:00
        boolean result5 = TimeSplit.isInDate4PresetTime(startTime5, endTime5, presetTime5);
        System.out.println("result5: " + result5); // 预期结果：true

        String startTime6 = "00:00";
        String endTime6 = "23:59";
        long presetTime6 = 1649347171000L; // 2022-04-07 23:59:31
        boolean result6 = TimeSplit.isInDate4PresetTime(startTime6, endTime6, presetTime6);
        System.out.println("result6: " + result6); // 预期结果：false

        String startTime7 = "08:00";
        String endTime7 = "18:00";
        long presetTime7 = 1649203200000L; // 2022-04-06 08:00:00
        boolean result7 = TimeSplit.isInDate4PresetTime(startTime7, endTime7, presetTime7);
        System.out.println("result7: " + result7); // 预期结果：true

        String startTime8 = "08:00";
        String endTime8 = "18:00";
        long presetTime8 = 1649239200000L; // 2022-04-06 18:00:00
        boolean result8 = TimeSplit.isInDate4PresetTime(startTime8, endTime8, presetTime8);
        System.out.println("result8: " + result8); // 预期结果：true

    }
}
