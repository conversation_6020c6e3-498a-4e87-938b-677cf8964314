package com.wosai.pantheon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.wosai.market.trade.modal.PayResult;
import com.wosai.market.user.dto.UserContextDTO;
import com.wosai.market.user.service.TokenService;
import com.wosai.pantheon.uf4c.Application;
import com.wosai.pantheon.uf4c.apisix.ApiRequest;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.model.dto.PayRequest;
import com.wosai.pantheon.uf4c.service.apisix.OrderServiceV1;
import com.wosai.pantheon.uf4c.service.apisix.impl.RefundApplyServiceImpl;
import com.wosai.pantheon.uf4c.util.ThreadLocalHelper;
import com.wosai.pantheon.uf4c.web.controller.v3.OrderControllerV3;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/3/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class OrderTest {

    @Autowired
    OrderControllerV3 orderControllerV3;

    @Autowired
    TokenService tokenService;

    @Autowired
    OrderServiceV1 orderServiceV1;

    @Autowired
    RefundApplyServiceImpl refundApplyService;

    /**
     * 真实下单流程本地测试
     * 1.店铺为上海特产店001(store_id:"846707641abf-f4db-b6c4-ba47-8cb988c8")
     * 2.进入店铺加购外卖商品
     * 3.填写小程序中token
     * 4.该接口参数为基本的自营外卖请求，参数可以在json中自己调整
     * 5.调用接口进行下单全流程测试
     */
    @Test
    public void pay() {
        String json = "{\n" +
                "    \"store_id\": \"846707641abf-f4db-b6c4-ba47-8cb988c8\",\n" +
                "    \"terminal_id\": null,\n" +
                "    \"pay_way\": 3,\n" +
                "    \"type\": \"take_out_order\",\n" +
                "    \"sn\": null,\n" +
                "    \"packed\": true,\n" +
                "    \"packde_amount\": null,\n" +
                "    \"delivery_info\": {\n" +
                "        \"order_sn\": null,\n" +
                "        \"user_id\": \"1856a376-c25d-11ea-b417-00163e03be12\",\n" +
                "        \"user_name\": \"江岸测试\",\n" +
                "        \"gender\": 1,\n" +
                "        \"cellphone\": \"11111111111\",\n" +
                "        \"province\": \"上海市\",\n" +
                "        \"province_code\": \"310000\",\n" +
                "        \"city\": \"上海市\",\n" +
                "        \"city_code\": \"310100\",\n" +
                "        \"district\": \"普陀区\",\n" +
                "        \"district_code\": \"310107\",\n" +
                "        \"address\": \"上海天地软件园(普陀区中江路879号)\",\n" +
                "        \"latitude\": \"31.232588\",\n" +
                "        \"longitude\": \"121.39016\",\n" +
                "        \"bulid_number\": \"测试5号楼\",\n" +
                "        \"house_number\": \"测试3号房间\",\n" +
                "        \"preset_time\": -1,\n" +
                "        \"delivery_type\": null,\n" +
                "        \"delivery_fee\": null,\n" +
                "        \"delivery_no\": null,\n" +
                "        \"reduction_amount\": null,\n" +
                "        \"is_convert\": null,\n" +
                "        \"distance\": 3000,\n" +
                "    },\n" +
                "    \"remark\": null,\n" +
                "    \"hb_fq\": null,\n" +
                "    \"compatible\": true\n" +
                "}";
        PayRequest payRequest = JSON.parseObject(json, PayRequest.class);
        //提前设置小程序中的token
        UserContextDTO userContextDTO = tokenService.validate("3da8e2cd-9356-4477-bcd0-70f4fced8dcc");
        if (userContextDTO == null){
            return;
        }
        ThreadLocalHelper.getRequestContextThreadLocal().get().setUserContextDTO(userContextDTO);
        //来源设置：微信小程序
        ThreadLocalHelper.getRequestContextThreadLocal().get().setMiniProgramType(MiniProgramType.WECHAT);
        //场景设置
        ThreadLocalHelper.getRequestContextThreadLocal().get().setScene("manual");
        PayResult pay = orderControllerV3.pay(payRequest);
        System.out.println(pay);
    }

    @Test
    public void timeSplit() {
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setQuery(new HashMap(){{
            put("store_id", "da145271-139b-41bb-9411-b4e95c573ae8");
        }});
        orderServiceV1.getTimeSplit(apiRequest);
    }

    @Test
    public void getExpireTime() {
//        Map map1 = new HashMap();
//        map1.put("startTime", "10:00");
//        map1.put("endTime", "10:20");
//
//        Map map2 = new HashMap();
//        map2.put("startTime", "23:00");
//        map2.put("endTime", "24:00");
//
//        Map<String, List<Map>> timeMaps = new TreeMap<>();
//        timeMaps.put("1", Arrays.asList(map1,map2));
//        timeMaps.put("2", Arrays.asList(map2));
//        timeMaps.put("3", Arrays.asList(map1));
//        timeMaps.put("4", Arrays.asList(map1));
//        timeMaps.put("5", Arrays.asList(map1));
//        timeMaps.put("6", Arrays.asList(map1));
//        timeMaps.put("7", Arrays.asList());
//        String str = "{\"1\":[],\"2\":[{\"endTime\":\"11:00\",\"startTime\":\"09:00\"},{\"endTime\":\"16:00\",\"startTime\":\"15:00\"},{\"endTime\":\"18:00\",\"startTime\":\"17:00\"}],\"3\":[{\"endTime\":\"22:00\",\"startTime\":\"00:00\"}],\"4\":[{\"endTime\":\"18:00\",\"startTime\":\"17:00\"}],\"5\":[],\"6\":[{\"endTime\":\"24:00\",\"startTime\":\"23:00\"}],\"7\":[]}";
        String str = "{\"1\":[{\"endTime\":\"00:00\",\"startTime\":\"00:00\"}],\"2\":[{\"endTime\":\"00:00\",\"startTime\":\"00:00\"}],\"3\":[{\"endTime\":\"00:00\",\"startTime\":\"00:00\"}],\"4\":[{\"endTime\":\"00:00\",\"startTime\":\"00:00\"}],\"5\":[{\"endTime\":\"00:00\",\"startTime\":\"00:00\"}],\"6\":[{\"endTime\":\"00:00\",\"startTime\":\"00:00\"}],\"7\":[{\"endTime\":\"00:00\",\"startTime\":\"00:00\"}]}";
        Map<String, List<Map<String, String>>> timeMaps = JSON.parseObject(str, new TypeReference<Map<String, List<Map<String, String>>>>() {});
        System.out.println(new Date(refundApplyService.getExpireTime("e77eb07e-1c45-467e-9d83-044b13acd5af", timeMaps)));
    }

}
