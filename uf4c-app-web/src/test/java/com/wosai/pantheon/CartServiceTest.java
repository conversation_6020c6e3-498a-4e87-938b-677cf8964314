package com.wosai.pantheon;

import com.wosai.pantheon.uf4c.Application;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.web.controller.CartController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/12/5
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CartServiceTest {

    @Autowired
    private CartController cartController;

    @Test
    public void testAddCart() {
        CartItemCreate cartItemCreate = new CartItemCreate();
        cartItemCreate.setStoreId("efb9c1afba41-45f9-c884-5927-b591902b");
        CartItemCreate.Item item = new CartItemCreate.Item();
        item.setId("83cffd94-b1a9-45ba-a394-d6f68c86a88d");
        item.setName("小猪礼盒");
        cartController.addCart(cartItemCreate);
    }
}
