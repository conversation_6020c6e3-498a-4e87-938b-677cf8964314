package com.wosai.pantheon;

import com.alibaba.fastjson.JSON;
import com.wosai.pantheon.uf4c.Application;
import com.wosai.pantheon.uf4c.model.item.CategoryTreeDTO;
import com.wosai.pantheon.uf4c.model.item.RetailCategoryQueryReq;
import com.wosai.pantheon.uf4c.model.item.RetailItemCursorQueryReq;
import com.wosai.pantheon.uf4c.model.item.RetailItemCursorQueryRes;
import com.wosai.pantheon.uf4c.service.item.retail.IRetailItemAdapterService;
import com.wosai.web.api.ListResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2024/6/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class RetailItemAdapterServiceTests {

    @Autowired
    private IRetailItemAdapterService retailItemAdapterService;

    @Test
    public void testQueryCategories() {
        RetailCategoryQueryReq queryReq = new RetailCategoryQueryReq();
        queryReq.setStoreId("a4bcf1a9-00a9-4ebe-a323-ffa5e58ff89c");
        queryReq.setServiceType(1);
        ListResult<CategoryTreeDTO> result = retailItemAdapterService.queryCategories(queryReq);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testCursorQueryItem() {
        RetailItemCursorQueryReq queryReq = new RetailItemCursorQueryReq();
        queryReq.setStoreId("a4bcf1a9-00a9-4ebe-a323-ffa5e58ff89c");
        queryReq.setServiceType(1);
        queryReq.setPage(1);
        queryReq.setPageSize(30);
        queryReq.setCategoryIds(Arrays.asList("134781"));

        RetailItemCursorQueryRes res = retailItemAdapterService.cursorQueryItem(queryReq);
        System.out.println(JSON.toJSONString(res));
    }

}
