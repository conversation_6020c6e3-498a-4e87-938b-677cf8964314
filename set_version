#!/bin/bash
if [ $# -eq 0 ]; then
  echo "tips: sh set_version.sh command"
  echo "you can use 'sh set_version.sh help' to see supported commands"
  exit
fi

version=`git describe --tags \`git rev-list --tags --max-count=1\``

echo "latest version is: ${version}"
cur_branch=`git symbolic-ref --short HEAD`
echo "current branch is: ${cur_branch}"

version_arr=(${version//./ })



case $1 in
"show")
      exit
  ;;
"tag")
  if ! [ ${#version_arr[@]} -eq 3 ]; then
      echo "当前项目tag格式不满足：x.y.z格式， 不支持自动升级版本号"
      exit
  fi

  major_version=${version_arr[0]}
  minor_version=${version_arr[1]}
  build_num=${version_arr[2]}

  let minor_version=$minor_version+1
  let build_num=0

  if [ $minor_version -gt 99 ]; then
    let minor_version=0
    let major_version=$major_version+1
  fi
  new_version="${major_version}.${minor_version}.${build_num}"
  ;;

"hotfix")
  if ! [ ${#version_arr[@]} -eq 3 ]; then
      echo "当前项目tag格式不满足：x.y.z格式， 不支持自动升级版本号"
      exit
  fi

  major_version=${version_arr[0]}
  minor_version=${version_arr[1]}
  build_num=${version_arr[2]}
  let build_num=$build_num+1

  if [ $build_num -gt 99 ]; then
    let build_num=0
    let minor_version=minor_version+1
  fi
  if [ $minor_version -gt 99 ]; then
    let minor_version=0
    let major_version=$major_version+1
  fi
  new_version="${major_version}.${minor_version}.${build_num}"
  ;;

"snap")
  if ! [ ${#version_arr[@]} -eq 3 ]; then
      echo "当前项目tag格式不满足：x.y.z格式， 不支持自动升级版本号"
      exit
  fi

  major_version=${version_arr[0]}
  minor_version=${version_arr[1]}
  build_num=${version_arr[2]}
  branch_arr=(${cur_branch//// })
  if [ ${#branch_arr[@]} -eq 1 ]; then
    new_version="${version}.${cur_branch}-SNAPSHOT"
  fi
  if [ ${#branch_arr[@]} -eq 2 ]; then
    new_version="${version}.${branch_arr[1]}-SNAPSHOT"
  fi

  ;;

"help")
  echo "supported commands:"
  echo "help   : show this"
  echo "show   : show project latest version"
  echo "tag    : change project minor version, example: 1.2.0 ==> 1.3.0"
  echo "hotfix : change project build num, example: 1.2.0 ==> 1.2.1"
  echo "snap   : change project version to snapshot, rules: currentVersion.currentBranch-SNAPSHOT, example: 1.2.0 feature/SMART-123 ==> 1.2.0.SMART-123-SNAPSHOT"
  echo "newversion: change project version to your new version, example: set_version.sh 1.2.3.giftfoods-SNAPSHOT"
  exit
  ;;
*)
  new_version=$1
  ;;
esac

echo "will change project version from ${version} to ${new_version}"

## 升级项目版本
mvn versions:set -DnewVersion=${new_version}
#
## 确认更改
mvn versions:commit