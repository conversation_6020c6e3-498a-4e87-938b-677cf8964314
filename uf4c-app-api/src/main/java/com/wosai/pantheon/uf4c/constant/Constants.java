package com.wosai.pantheon.uf4c.constant;

import com.google.common.collect.Lists;

import java.util.List;

public class Constants {
    public static final String CHARSET_UTF_8 = "UTF-8";
    public static final String STORE_BULLETIN = "store_bulletin";
    public static final String STORE_TAKEOUT_BULLETIN = "store_takeout_bulletin";
    public static final String BUSINESS_STATUS = "business_status";
    public static final String BUSINESS_TIMES = "business_times";
    public static final String TAKEOUT_BUSINESS_STATUS = "takeout_business_status";
    public static final String DELIVERY_TIMES = "delivery_times_v2";
    public static final String DELIVERY_TYPE = "delivery_type";
    public static final String DELIVERY_RANGE_TYPE = "delivery_range_type";
    public static final String DELIVERY_CHARGE_TYPE = "delivery_charge_type";
    public static final String DELIVERY_FEE = "delivery_fee";
    public static final String DELIVERY_MIN_PRICE = "delivery_min_price";
    public static final String STORE_MUST_CATEGORY = "store_must_category";
    public static final String STORE_TAKEOUT_MUST_CATEGORY = "store_takeout_must_category";
    public static final String SWITCH_SELF_DELIVERY = "switch_self_delivery";
    public static final String DELIVERY_TIERED_PRICING = "delivery_tiered_pricing";
    public static final String PRESET_TYPE = "preset_type";
    public static final String PRESET_BUSINESS_STATUS = "preset_business_status";
    public static final String PRESET_DAYS = "preset_days";
    public static final String TAKEOUT_PRE_STATUS = "takeout_pre_status";
    public static final String TAKEOUT_PRE_DAYS = "takeout_pre_days";
    public static final String TAKEOUT_PRE_REMIND_TIME = "takeout_pre_remind_time";
    public static final String TAKEOUT_PRE_PRINT_TIME = "takeout_pre_print_time";
    public static final String PRESET_REMIND_TIME = "preset_remind_time";
    public static final String PRESET_PRINT_TIME = "preset_print_time";
    public static final String MERCHANT_TYPE = "MERCHANT_TYPE";
    public static final int DEFAULT_COOK_TIME = 15;
    public static final int DEFAULT_DELIVERY_TIME = 20;
    /**
     * 仅接受预定单配置，1-仅接受预定单 0-可接受即时单
     */
    public static final String TAKEOUT_ONLY_BOOK_ORDER = "takeout_only_book_order";
    public static final String PRE_ONLY_BOOK_ORDER = "pre_only_book_order";

    /**
     * 自定义的外卖配送时间点
     */
    public static final String CUSTOMIZED_TAKEOUT_TIME = "customized_takeout_time";
    /**
     * 自定义的到店自取时间点
     */
    public static final String CUSTOMIZED_PRESET_TIME = "customized_preset_time";
    /**
     * 自定义的出餐时间花费
     */
    public static final String CUSTOMIZED_COOK_TIME_COST = "customized_cook_time_cost";
    /**
     * 自定义的配送时间花费
     */
    public static final String CUSTOMIZED_DELIVERY_TIME_COST = "customized_delivery_time_cost";


    public static final List<String> BOOK_ORDER_CONFIGS = Lists.newArrayList(TAKEOUT_PRE_STATUS, DELIVERY_TYPE, DELIVERY_TIMES,
            TAKEOUT_PRE_DAYS, TAKEOUT_PRE_REMIND_TIME, TAKEOUT_PRE_PRINT_TIME, PRESET_DAYS, PRESET_REMIND_TIME, PRESET_PRINT_TIME, TAKEOUT_ONLY_BOOK_ORDER, PRE_ONLY_BOOK_ORDER,
            CUSTOMIZED_TAKEOUT_TIME, CUSTOMIZED_PRESET_TIME, CUSTOMIZED_COOK_TIME_COST, CUSTOMIZED_DELIVERY_TIME_COST);

    /**
     * 仅支持在店内扫码下单
     */
    public static final String ONLY_IN_STORE = "only_in_store";

    /**
     * 门店的就餐类型（轻餐SINGLE、围餐ROUND_MEAL）的配置key
     */
    public static final String MEAL_TYPE_CONFIG_KEY = "meal_type";

    /**
     * 是否是先付围餐的配置key
     */
    public static final String PAY_FIRST_TABLE_ORDER_CONFIG_KEY = "pay_first_table_order";

    /**
     * 门店是否是收银机模式的配置key
     */
    public static final String CASHIER_MODE_CONFIG_KEY = "cashier_mode";


    /**
     * 开台必点是否启用
     */
    public static final String MUST_ORDER_ENABLE_CONFIG_KEY = "must_order_enable";


    /**
     * 开台必点商品是否可以编辑
     */
    public static final String MUST_ORDER_EDITABLE_CONFIG_KEY = "must_order_editable";

    /**
     * 收银机、手机收银的优惠模式
     */
    public static final String CASHIER_DISCOUNT_STRATEGY = "cashier_order";

    public static final String GOODS_DISCOUNT_GROUP = "goods_discount_group";

    public static final String COMMON_DISCOUNT_GROUP = "common_discount_group";

    /**
     * 桌台状态
     */
    public static final String TABLE_STATUS = "tableStatus";

    /**
     * 会员手机号
     */
    public static final String MEMBERSHIP_CELLPHONE = "membershipCellphone";
    /**
     * 会员余额
     */
    public static final String MEMBERSHIP_BALANCE = "membershipBalance";

    /**
     * 商户行业类型：零售（RETAIL）、餐饮(CATERING)
     */
    public static final String MERCHANT_INDUSTRY_TYPE = "merchantIndustryType";

    // 餐牌号
    public static final String DINING_CODE = "dining_code";

    public static final String RETAIL = "RETAIL";
    public static final String CATERING = "CATERING";

    public static final String SQB_PAY_SOURCE = "sqb_pay_source";

    public static final String PAY_HEADERS = "pay_headers";


    public static final String DEFAULT_PRE_DAYS = "[0,1,2,3,4,5,6,7]";


    public static class ServiceType {
        public static final Integer TAKEOUT = 1;
        public static final Integer SCANNING = 2;
    }

    public static final String NAME = "name";
    public static final String TABLE_NAME = "tableName";
    public static final String TABLE_ID = "tableId";
    public static final String QRCODE_TYPE = "qrcode_type";
    public static final String JJZ_BUSINESS_TYPE = "jjz_business_type";
    public static final String TAKEOUT_RISK = "takeout_risk";
    public static final String SCAN_RISK = "scan_risk";
    public static final String MEAL_SINGLE = "single";
    public static final String MEAL_ROUND = "round_meal";

    public static final String CELL_PHONE_KEY = "cellPhone";

    //下单瞬间，门店是否在校园
    public static final String ORDER_IN_CAMPUS = "orderInCampus";

//    public static final String CACHE_DATA_ORDER_INDEX = "orderIndex";

    public static final String GROUP_BUYING_ID = "group_buying_id";


    /**
     * 品牌购商品来源：品牌
     */
    public static final String BRAND_PRODUCT_SOURCE_BRAND = "BRAND";
    /**
     * 品牌购商品来源：门店
     */
    public static final String BRAND_PRODUCT_SOURCE_STORE = "STORE";


    /**
     * 门店商品
     */
    public static final int GOODS_BIZ_TYPE_STORE = 1;
    /**
     * 品牌推广商品
     */
    public static final int GOODS_DATA_SOURCE_TYPE_BRAND = 2;

    /**
     * 品牌商品展示位：自动加入购物车
     */
    public static final String BRAND_DISPLAY_POSITION_AUTO_CART = "AUTO_CART";
    /**
     * 品牌商品展示位：品牌购
     */
    public static final String BRAND_DISPLAY_POSITION_BRAND_CART = "BRAND_CART";


    public static final String BRAND_SALE_SCENE_SCAN = "SCAN";
    public static final String BRAND_SALE_SCENE_TAKEOUT = "TAKEOUT";


    public static class From {
        public static final String MANUAL = "manual";
        public static final String CAMPUS = "campus";
    }

    public static class ServiceTypeAction {
        public static final String SCAN = "scan";
        public static final String CONFIRM = "confirm";
    }

    public static class Closing {
        // 对应前端1，2，3，4，5，6
        public static final List<String> NOT_ON_BUSINESS = Lists.newArrayList("休息中", "目前暂时无法下单哦 ~");
        public static final List<String> NO_GOODS = Lists.newArrayList("该店暂无可点商品", "请联系服务员");
        public static final List<String> TAKEOUT_OFFLINE = Lists.newArrayList("该商家外卖服务已下线");
        public static final List<String> TAKEOUT_EXCEPTION = Lists.newArrayList("该店外卖服务异常", "请联系服务员");
        public static final List<String> QRCODE_EXCEPTION = Lists.newArrayList("该店扫码点单服务异常", "请联系服务员");
        public static final List<String> QRCODE_ERROR = Lists.newArrayList("无效二维码，请扫码正确的点单码");
        public static final List<String> QRCODE_UNBIND = Lists.newArrayList("点单码未绑定", "请联系服务员");
        public static final List<String> QRCODE_NO_TABLE_NAME = Lists.newArrayList("点单码未绑定桌号", "请联系服务员");
        public static final List<String> QRCODE_NO_TABLE_NAME_FOR_ROUND_MEAL = Lists.newArrayList("门店二维码不支持先下单后付款场景", "请联系服务员");
        public static final List<String> N_ORDER_NOT_SUPPORT = Lists.newArrayList("碰一碰点单暂不可用，请扫描二维码点单");

    }

    public static class GatherResponse {
        /**
         * 店铺活动
         */
        public static final String ACTIVITY = "activity";
        /**
         * 首页商品列表
         */
        public static final String GOODS = "goods";
        /**
         * 二维码终端信息
         */
        public static final String TERMINAL = "terminal";
        /**
         * 门店信息
         */
        public static final String STORE = "store";
        /**
         * 门店配置
         */
        public static final String MCC = "mcc";
        /**
         * 商户配置
         */
        public static final String MCH_MCC = "mch_mcc";
        /**
         * 商品分类
         */
        public static final String CATEGORY = "category";
        /**
         * 阿波罗配置
         */
        public static final String config = "config";
        /**
         * 数据拉取错误结果
         */
        public static final String ERRORS = "errors";
        /**
         * 耗时情况
         */
        public static final String DURATION = "duration";
    }

    public static class SaleTerminal {

        public static final Integer MINI = 1;

    }


    public static class CartCheckItemChangeType {
        public static final Integer CHANGE_TYPE_INVALID = 1;
        public static final Integer CHANGE_TYPE_PRICE = 2;
        public static final Integer CHANGE_TYPE_STOCK = 3;
    }

    public static class DeliveryFeeType {
        public static final String TOTAL = "total"; // 配送费总额
        public static final String CAMPUS = "campus"; // 校园配送费总额
        public static final String THIRD = "third"; // 第三方配送费
        public static final String SELF = "self"; // 自配送费用
        public static final String SHARE = "share"; // 校园商家承担配送费
        public static final String USER = "user"; // 用户配送费
        public static final String REDUCTION = "reduction"; // 配送费减免
        public static final String FLOOR = "floor"; // 楼层费
        public static final String PRICE_EXTRA = "priceExtra";
    }

    public static class CampusDeliveryType {
        /**
         * 不配送
         */
        public static final int NONE = 0;
        /**
         * 收钱吧配送
         */
        public static final int SQB = 1;
        /**
         * 自配送
         */
        public static final int SELF = 2;
    }

    public interface PrintConstants {
        //结账单默认打印  true/false
        String BILL_DEFAULT_PRINT_SWITCH = "bill_default_print_switch";
        //结账单打印发票码 [smart,out-platform]  smart-自营外卖，out-platform-三方外卖
        String TAKEOUT_BILL_PRINT_INVOICE = "takeout_bill_print_invoice";
    }
}
