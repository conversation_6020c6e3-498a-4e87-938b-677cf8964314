package com.wosai.pantheon.uf4c.api;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.model.dto.RecommendItemQueryByStoresRequest;
import com.wosai.pantheon.uf4c.model.vo.StoreRecommendItemTuple;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/7
 */
@JsonRpcService(value = "/rpc/item/recommend")
@Validated
public interface RecommendItemRemoteService {

    /**
     * 多门店查询推荐商品
     * @param request
     * @return
     */
    List<StoreRecommendItemTuple> findByStores(@Valid RecommendItemQueryByStoresRequest request);

    /**
     * 刷新缓存使用，目前做法是直接删除缓存
     * @param storeId
     */
    void refresh(@NotBlank(message = "门店ID不能为空") String storeId);

}
