package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.pantheon.uf4c.constant.ItemDiscountTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 商品优惠价格包装类
 */
@Data
public class ItemDiscountPriceVO implements Serializable {

    /**
     * 当前用户是否可以享受，false代表不能享受，但是我们还是要露出、推荐的优惠
     */
    private boolean canEnjoy = true;
    /**
     * 优惠类型
     */
    ItemDiscountTypeEnum discountType;

    /**
     * 优惠价格
     */
    private Integer discountPrice;

    /**
     * 优惠享受数量（限多少份），为空代表不限制
     */
    private Integer quotaCount;

    /**
     * 优惠折扣
     */
    private Integer discount;

}
