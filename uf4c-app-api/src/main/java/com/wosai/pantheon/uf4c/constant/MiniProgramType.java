package com.wosai.pantheon.uf4c.constant;

import lombok.Getter;

@Getter
public enum MiniProgramType {
    ALIPAY("1", "支付宝小程序"),
    WECHAT("3", "微信小程序"),
    H5_WECHAT("3", "微信H5"),
    UNION_PAY("17", "云闪付");

    private String code;
    private String message;

    MiniProgramType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static MiniProgramType get(String programType) {
        for (MiniProgramType type : MiniProgramType.values()) {
            if (type.name().equalsIgnoreCase(programType)) {
                return type;
            }
        }
        return MiniProgramType.WECHAT;
    }
}
