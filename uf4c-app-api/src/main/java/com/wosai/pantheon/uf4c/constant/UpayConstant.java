package com.wosai.pantheon.uf4c.constant;

/**
 * Created by x<PERSON><PERSON><PERSON> on 17/10/31.
 */
public class UpayConstant {
    public static final String CONTENT_TYPE = "application/json";
    public static final int CONNECT_TIME_OUT = 3000;
    public static final int READ_TIME_OUT = 15000;
    public static final String CHARSET = "UTF-8";

    public static final String TERMINAL_SN = "terminal_sn";
    public static final String CLIENT_SN = "client_sn";
    public static final String TOTAL_AMOUNT = "total_amount";
    public static final String PAYWAY = "payway";
    public static final String SUB_PAYWAY = "sub_payway";
    public static final String PAYER_UID = "payer_uid";
    public static final String SUBJECT = "subject";
    public static final String OPERATOR = "operator";
    public static final String REFUND_REQUEST_NO = "refund_request_no";
    public static final String NOTIFY_URL = "notify_url";
    public static final String RETURN_URL = "return_url";
    public static final String SIGN = "sign";
    public static final String RESULT_CODE = "result_code";
    public static final String BIZ_RESPONSE = "biz_response";
    public static final String DATA = "data";
    public static final String SN = "sn";
    public static final String NET_AMOUNT = "net_amount";

    //-------------接口调用结果--------------------------
    public static final String RESULT_SUCCESS = "200";
    //-------------错误信息代码及详情---------------------
    public static final String ERROR_MESSAGE = "error_message";
    public static final String ERROR_CODE = "error_code";

    public static final String PRE_CREATE = "pre_create";
    public static final String PAY = "pay";

    //-------------业务执行结果--------------------------
    public static final String BIZ_SUCCESS = "SUCCESS";
    public static final String BIZ_FAIL = "FAIL";

    //-------------预下单状态----------------------------
    public static final String PRE_CREATE_SUCCESS = "PRECREATE_SUCCESS";
    public static final String PRE_CREATE_FAIL = "PRECREATE_FAIL";
    public static final String PRE_CREATE_FAIL_IN_PROG = "PRECREATE_FAIL_IN_PROGRESS";
    public static final String PRE_CREATE_FAIL_ERROR = "PRECREATE_FAIL_ERROR";

    //-------------退款状态----------------------------
    public static final String REFUND_SUCCESS = "REFUND_SUCCESS";

    public static final String WAP_PAY_REQUEST = "wap_pay_request";

    //-------------支付网关订单状态------------
    public static final String ORDER_STATUS = "order_status";
    public static final String ORDER_STATUS_CREATED = "CREATED";
    public static final String ORDER_STATUS_PAID = "PAID";
    public static final String ORDER_STATUS_PAY_CANCELED = "PAY_CANCELED";
    public static final String ORDER_STATUS_PAY_IN_PROC = "PAY_IN_PROGRESS";
    
}
