package com.wosai.pantheon.uf4c.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ItemDiscountTypeEnum {
    SINGLE_DISCOUNT("单品优惠",1),
    SECOND_DISCOUNT("第二份优惠",2),
    MEMBER_PRICE_DISCOUNT("会员价",3),
    MEMBER_GOODS_DISCOUNT("会员商品折扣",4),
    GIFT_CARD_DISCOUNT("储值优惠价",5),
    CATEGORY_SINGLE_DISCOUNT("分类上的单品优惠",6)

    ;

    private String name;

    private int value;
}
