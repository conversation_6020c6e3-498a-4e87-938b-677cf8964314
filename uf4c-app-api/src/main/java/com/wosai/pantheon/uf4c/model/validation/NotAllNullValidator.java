package com.wosai.pantheon.uf4c.model.validation;

import org.springframework.beans.BeanWrapperImpl;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * created by shij on 2019/8/19
 */
public class NotAllNullValidator implements ConstraintValidator<NotAllNull, Object> {

    private String[] fieldNames;

    public void initialize(NotAllNull notAllNullAnnotation) {
        this.fieldNames = notAllNullAnnotation.fieldNames();
    }

    @Override
    public boolean isValid(Object object, ConstraintValidatorContext context) {

        if (object == null) {
            return false;
        }

        final BeanWrapperImpl beanWrapper = new BeanWrapperImpl(object);

        for (final String f : fieldNames) {
            final Object fieldValue = beanWrapper.getPropertyValue(f);

            if (fieldValue != null) {
                return true;
            }
        }

        return false;
    }
}
