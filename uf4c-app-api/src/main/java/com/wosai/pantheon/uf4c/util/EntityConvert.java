package com.wosai.pantheon.uf4c.util;


import com.wosai.market.tethys.api.enums.DiscountsEnum;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.*;
import com.wosai.pantheon.order.model.dto.request.Spec;
import com.wosai.pantheon.order.utils.OrderUtil;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.uf4c.model.DeliverInfo;
import com.wosai.pantheon.uf4c.model.Order;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/30
 */

@Slf4j
public class EntityConvert {

    private static final String CART_NUM_UNIT = "x";

    private static final String EXTRA_KEY_SALE_WEIGHT = "sale_weight";

    private static final String EXTRA_KEY_UNIT_TYPE = "unit_type";

    public static final String EXTRA_KEY_CELLPHONE = "cellPhone";


    public static Order convertOrderDTO4Print(OrderDTO orderDTO) {

        // 基本字段
        Order order = Order.builder()
                .id(orderDTO.getId())
                .sn(orderDTO.getSn())
                .type(orderDTO.getOrderType().getMsg())
                .transSn(orderDTO.getOrderSeq())
                .status(orderDTO.getStatus().getCode())
                .subject(orderDTO.getSubject())
                .originalAmount(orderDTO.getOriginalAmount())
                .effectiveAmount(orderDTO.getEffectiveAmount())
                .profitSharingAmount(orderDTO.getProfitSharingAmount())
                .refundAmount(orderDTO.getRefundAmount())
                .totalDiscount(0L)
                .merchantDiscount(0L)
                .packAmount(orderDTO.getPackAmount())
                .buyerUid(orderDTO.getUserId())
                .buyerLogin(orderDTO.getUserName())
                .merchantId(orderDTO.getMerchantId())
                .storeId(orderDTO.getStoreId())
                .storeName(orderDTO.getStoreName())
                .terminalId(orderDTO.getTerminalId())
                .qrCode(orderDTO.getTerminalId())
                .qrCodeName(StringUtils.isEmpty(orderDTO.getTerminalName()) ? orderDTO.getTableNo() : orderDTO.getTerminalName())
                .qrCodeType(orderDTO.getTerminalType())
                .payway(orderDTO.getPayway())
                .subPayway(orderDTO.getSubPayway())
                .clientSn(orderDTO.getClientSn())
                .tradeNo(orderDTO.getTransSn())
                .packed(1 == Optional.ofNullable(orderDTO.getPacked()).map(PackType::getCode).orElse(0L))
                .remark(orderDTO.getRemark())
                .ctime(orderDTO.getCtime())
                .mtime(orderDTO.getMtime())
                .payTime(orderDTO.getPayTime())
                .orderTag(orderDTO.getOrderTag())
                .deleted(false)
                .version(orderDTO.getVersion())
                .tableNo(orderDTO.getTableNo())
                .tableId(orderDTO.getTableId())
                .campusOrder(Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false))
                .bookOrder(orderDTO.isBookOrder())
                .bookTime(Optional.ofNullable(orderDTO.getBookOrderInfoDTO()).map(BookOrderInfoDTO::getBookTime).orElse(-1L))
                .orderCampusDelivery(orderDTO.getOrderCampusDelivery())
                .orderCampusStation(orderDTO.getOrderCampusStation())
                .campusException(orderDTO.getCampusException())
                .build();


        List<OrderItemDTO> orderItemDTOS = orderDTO.getItems();

        List<CartItemCreate> items = Optional.ofNullable(orderItemDTOS)
                .map(it -> it.stream().map(info -> convertOrderItemDto2CartItemCreate(info, null, false)).collect(Collectors.toList()))
                .orElse(null);
        order.setItems(items);

        // 优惠
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderDTO.getRedeems())) {
            List<RedeemResult.RedeemDetail> redeemDetails = new ArrayList<>();
            Long totalDiscount = 0L;
            long effectReductionAmount = 0;
            for (OrderRedeemDTO redeem : orderDTO.getRedeems()) {
                totalDiscount += redeem.getDiscountAmount();
                if (3 == redeem.getType() && redeem.getDiscountType() != 16) {
                    effectReductionAmount += redeem.getDiscountAmount();
                    order.setMerchantActivitySn(redeem.getActivitySn());
//                    order.setMerchantDiscount(redeem.getDiscountAmount());
                    order.setMerchantDiscountName(redeem.getName());
//                    order.setEffectiveAmount(order.getEffectiveAmount() - order.getMerchantDiscount());
                    if (!(orderDTO.getOrderSource() == OrderSource.CASHIER || orderDTO.getOrderSource() == OrderSource.APP || orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER)) {
                        //围餐的不要这样处理
                        order.setRefundAmount(Optional.ofNullable(order.getRefundAmount()).orElse(0L) - order.getMerchantDiscount());
                    }
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                } else if (1 == redeem.getType()) {
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                }
                order.setRedeemDetails(redeemDetails);
            }
            order.setMerchantDiscount(effectReductionAmount);
            order.setEffectiveAmount(order.getOriginalAmount() - effectReductionAmount);
            order.setTotalDiscount(totalDiscount);
        }


        // Extra字段
        Order.OrderExtra orderExtra = new Order.OrderExtra();
        if (orderDTO.getOrderAddress() != null) {
            DeliverInfo deliverInfo = new DeliverInfo();
            BeanUtils.copyProperties(orderDTO.getOrderAddress(), deliverInfo);
            deliverInfo.setDeliveryFee(Optional.ofNullable(orderDTO.getDeliverAmount()).map(Long::intValue).orElse(null));
            deliverInfo.setDeliveryType(Optional.ofNullable(orderDTO.getDeliverType()).map(String::valueOf).orElse(null));
            if (!CollectionUtils.isEmpty(orderDTO.getRedeems())) {
                orderDTO.getRedeems().forEach(r -> {
                    if (Objects.equals(r.getDiscountType(), DiscountsEnum.TAKEOUT_DELIVERY_FEE_REDUCTION_ACTIVITY.getCode())) {
                        deliverInfo.setReductionAmount(r.getDiscountAmount().intValue());
                    }
                });
            }
            orderExtra.setDeliveryInfo(deliverInfo);
            //校园单信息
            if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
                orderExtra.setCampusDelivery(orderDTO.getOrderCampusDelivery());
                orderExtra.setCampusStation(orderDTO.getOrderCampusStation());
            }
        }
        orderExtra.setStoreName(orderDTO.getStoreName());
        if (null != orderDTO.getExtra() && orderDTO.getExtra().containsKey(EXTRA_KEY_CELLPHONE)) {
            orderExtra.setCellphone((String) orderDTO.getExtra().get(EXTRA_KEY_CELLPHONE));
        }
        orderExtra.setDmInfo(orderDTO.getDeliveryInfo());
        order.setExtra(orderExtra);
        if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
            if (Objects.nonNull(orderDTO.getOrderCampusDelivery()) && orderDTO.getOrderCampusDelivery().getType() == 1) {
                if (Objects.nonNull(orderDTO.getOrderCampusStation())) {
                    order.setStationAddress(orderDTO.getOrderCampusStation().getAddress());
                }
            }
        }


        return order;
    }

    public static Order convertOrderDTO(OrderDTO orderDTO) {

        // 基本字段
        Order order = Order.builder()
                .id(orderDTO.getId())
                .sn(orderDTO.getSn())
                .type(orderDTO.getOrderType().getMsg())
                .transSn(orderDTO.getOrderSeq())
                .status(orderDTO.getStatus().getCode())
                .subject(orderDTO.getSubject())
                .originalAmount(orderDTO.getOriginalAmount())
                .effectiveAmount(orderDTO.getEffectiveAmount())
                .receiveAmount(orderDTO.getReceiveAmount())
                .buyerPayAmount(orderDTO.getBuyerPayAmount())
                .refundAmount(orderDTO.getRefundAmount())
                .totalDiscount(0L)
                .merchantDiscount(0L)
                .packAmount(orderDTO.getPackAmount())
                .buyerUid(orderDTO.getUserId())
                .buyerLogin(orderDTO.getUserName())
                .merchantId(orderDTO.getMerchantId())
                .storeId(orderDTO.getStoreId())
                .storeName(orderDTO.getStoreName())
                .terminalId(orderDTO.getTerminalId())
                .qrCode(orderDTO.getTerminalId())
                .qrCodeName(StringUtils.isEmpty(orderDTO.getTerminalName()) ? orderDTO.getTableNo() : orderDTO.getTerminalName())
                .qrCodeType(orderDTO.getTerminalType())
                .payway(orderDTO.getPayway())
                .subPayway(orderDTO.getSubPayway())
                .clientSn(orderDTO.getClientSn())
                .tradeNo(orderDTO.getTransSn())
                .packed(1 == Optional.ofNullable(orderDTO.getPacked()).map(PackType::getCode).orElse(0L))
                .remark(orderDTO.getRemark())
                .ctime(orderDTO.getCtime())
                .mtime(orderDTO.getMtime())
                .payTime(orderDTO.getPayTime())
                .deleted(false)
                .version(orderDTO.getVersion())
                .processStatus(getProcessStatusForShow(orderDTO.getProcessStatus(), orderDTO.getStatus()))
                .tableNo(orderDTO.getTableNo())
                .tableId(orderDTO.getTableId())
                .extraInfo(orderDTO.getExtra())
                .orderTag(orderDTO.getOrderTag())
                .campusOrder(Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false))
                .bookOrder(orderDTO.isBookOrder())
                .bookTime(Optional.ofNullable(orderDTO.getBookOrderInfoDTO()).map(BookOrderInfoDTO::getBookTime).orElse(null))
                .orderCampusDelivery(orderDTO.getOrderCampusDelivery())
                .orderCampusStation(orderDTO.getOrderCampusStation())
                .build();


        if (orderDTO.getOrderSource() == OrderSource.CASHIER || orderDTO.getOrderSource() == OrderSource.APP || orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER) {
            //围餐的effectiveAmount是已经减过优惠金额的， 所以这里需要兼容下， 返回总金额
            order.setEffectiveAmount(order.getOriginalAmount());
        }


//
        List<CartItemCreate> items = Optional.ofNullable(orderDTO.getItems())
                .map(it -> it.stream().map(info -> convertOrderItemDto2CartItemCreate(info, GoodsRefPayType.PAY, false)).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(null);


        order.setItems(items);


        // 优惠
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(orderDTO.getRedeems())) {

            List<RedeemResult.RedeemDetail> redeemDetails = new ArrayList<>();
            Long totalDiscount = 0L;
            for (OrderRedeemDTO redeem : orderDTO.getRedeems()) {
                totalDiscount += redeem.getDiscountAmount();
                if (3 == redeem.getType() && redeem.getDiscountType() != 16) {
                    order.setMerchantActivitySn(redeem.getActivitySn());
                    order.setMerchantDiscount(redeem.getDiscountAmount());
                    order.setMerchantDiscountName(redeem.getName());
                    if (Objects.equals(redeem.getDiscountType(), DiscountsEnum.SECOND_ACTIVITY.getCode()) || Objects.equals(redeem.getDiscountType(), DiscountsEnum.SINGLE_ACTIVITY.getCode())) {
                        order.setMerchantActivityType(Constants.GOODS_DISCOUNT_GROUP);
                    } else {
                        order.setMerchantActivityType(Constants.COMMON_DISCOUNT_GROUP);
                    }
                    order.setEffectiveAmount(order.getEffectiveAmount() - order.getMerchantDiscount());
//                    if (!(orderDTO.getOrderSource() == OrderSource.CASHIER || orderDTO.getOrderSource() == OrderSource.APP || orderDTO.getOrderType() == OrderType.EAT_FIRST_ORDER)) {
//                        //围餐的不要这样处理
//                        order.setRefundAmount(Optional.ofNullable(order.getRefundAmount()).orElse(0L) - order.getMerchantDiscount());
//                    }
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                } else if (1 == redeem.getType()) {
                    RedeemResult.RedeemDetail redeemDetail = new RedeemResult.RedeemDetail();
                    redeemDetail.setName(redeem.getName());
                    redeemDetail.setType(redeem.getType());
                    redeemDetail.setMessage(redeem.getName());
                    redeemDetail.setDiscountAmount(redeem.getDiscountAmount());
                    redeemDetail.setSubType(redeem.getDiscountType());
                    redeemDetails.add(redeemDetail);
                }
                order.setRedeemDetails(redeemDetails);
            }
            order.setTotalDiscount(totalDiscount);
        }


        // Extra字段
        Order.OrderExtra orderExtra = new Order.OrderExtra();
        if (orderDTO.getOrderAddress() != null) {
            DeliverInfo deliverInfo = new DeliverInfo();
            BeanUtils.copyProperties(orderDTO.getOrderAddress(), deliverInfo);
            deliverInfo.setDeliveryFee(Optional.ofNullable(orderDTO.getDeliverAmount()).map(Long::intValue).orElse(null));
            deliverInfo.setDeliveryType(Optional.ofNullable(orderDTO.getDeliverType()).map(String::valueOf).orElse(null));
            if (!CollectionUtils.isEmpty(orderDTO.getRedeems())) {
                orderDTO.getRedeems().forEach(r -> {
                    if (Objects.equals(r.getDiscountType(), DiscountsEnum.TAKEOUT_DELIVERY_FEE_REDUCTION_ACTIVITY.getCode())) {
                        deliverInfo.setReductionAmount(r.getDiscountAmount().intValue());
                    }
                });
            }
            orderExtra.setDeliveryInfo(deliverInfo);
            //校园单信息
            if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
                orderExtra.setCampusDelivery(orderDTO.getOrderCampusDelivery());
                orderExtra.setCampusStation(orderDTO.getOrderCampusStation());
            }
        }
        orderExtra.setDmInfo(orderDTO.getDeliveryInfo());
        orderExtra.setStoreName(orderDTO.getStoreName());
        if (null != orderDTO.getExtra() && orderDTO.getExtra().containsKey(EXTRA_KEY_CELLPHONE)) {
            orderExtra.setCellphone((String) orderDTO.getExtra().get(EXTRA_KEY_CELLPHONE));
        }
        if (Optional.ofNullable(orderDTO.getCampusOrder()).orElse(false)) {
            if (Objects.nonNull(orderDTO.getOrderCampusDelivery()) && orderDTO.getOrderCampusDelivery().getStatusCode() > 0) {
                OrderDeliveryDTO orderDeliveryDTO = new OrderDeliveryDTO();
                BeanUtils.copyProperties(orderDTO.getOrderCampusDelivery(), orderDeliveryDTO);
                orderExtra.setDmInfo(orderDeliveryDTO);
            }
        }
        order.setExtra(orderExtra);


        return order;
    }


    public static CartItemCreate convertOrderItemDto2CartItemCreate(OrderItemDTO info, GoodsRefPayType filterRefPayType, Boolean isPackageGoods) {
        if (filterRefPayType != null) {
            if (info.getRefPayType() != filterRefPayType) {
                return null;
            }
        }
        CartItemCreate cartItemCreate = new CartItemCreate();

        cartItemCreate.setProcessStatus(info.getProcessStatus());
        cartItemCreate.setOrderTime(info.getOrderTime());
        cartItemCreate.setExtraInfo(info.getExtraInfo());
        cartItemCreate.setItemUid(info.getId());

        if (OrderUtil.hasTag(info.getGoodsTag(), OrderGoodsTagEnum.OPENTABLE_MUST_ORDER.getValue())) {
            cartItemCreate.setOpenTableMustOrder(true);
        } else {
            cartItemCreate.setOpenTableMustOrder(false);
        }

        if (MapUtils.getBoolean(info.getExtraInfo(), com.wosai.pantheon.order.constant.Constants.OrderGoodsExtraKey.EXTRA_KEY_MUST_ORDER_EDITABLE,false)){
            cartItemCreate.setOpenTableItemEditable(true);
        }else{
            cartItemCreate.setOpenTableItemEditable(false);
        }

        Spec spec = info.getSpecInfo();
        if (spec != null) {
            CartItemCreate.Spec itemSpec = new CartItemCreate.Spec();
            itemSpec.setId(spec.getId());
            itemSpec.setName(spec.getName());
            itemSpec.setPrice(Optional.ofNullable(spec.getPrice()).map(price -> price.intValue()).orElse(0));
            cartItemCreate.setSpec(itemSpec);
        }

        cartItemCreate.setAttributes(
                Optional.ofNullable(info.getAttributeInfos())
                        .map(attributes -> attributes.stream()
                                .map(attribute -> {
                                    CartItemCreate.Attribute itemAttribute = new CartItemCreate.Attribute();
                                    itemAttribute.setId(attribute.getId());
                                    itemAttribute.setName(attribute.getName());
                                    itemAttribute.setTitle(attribute.getTitle());
                                    return itemAttribute;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );
        cartItemCreate.setMaterials(
                Optional.ofNullable(info.getMaterials())
                        .map(materials -> materials.stream()
                                .map(dbMaterial -> {
                                    CartItemCreate.Material material = new CartItemCreate.Material();
                                    material.setId(dbMaterial.getId());
                                    material.setName(dbMaterial.getName());
                                    material.setPrice(dbMaterial.getPrice());
                                    material.setNumber(dbMaterial.getNumber());
                                    material.setSource(dbMaterial.getSource());
                                    return material;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null)
        );

        CartItemCreate.Item item = new CartItemCreate.Item();
        item.setId(info.getItemId());
        item.setName(info.getName());
        item.setNumber(Math.toIntExact(info.getCount().intValue()));
        item.setUrl(info.getUrl());
        item.setPhotoUrl(info.getUrl());
        item.setPrice(info.getOriginalAmountPer().intValue());
        item.setSpuType(Optional.ofNullable(info.getSpuType()).orElse(SpuType.PRODUCT).name());
        item.setCategoryId(info.getCategoryId());
        item.setDiscountNumber(info.getDiscountCount() != null ? info.getDiscountCount().intValue() : 0);
        item.setDiscountPrice(info.getDiscountAmount() != null ? info.getDiscountAmount().intValue() : 0);

        if (!isPackageGoods) {
            if (info.getSpuType() == SpuType.PACKAGE && CollectionUtil.isNotEmpty(info.getPackageGoods())) {
                cartItemCreate.setPackageItems(info.getPackageGoods().stream()
                        .map(itemDto -> convertOrderItemDto2CartItemCreate(itemDto, null, true))
                        .collect(Collectors.toList()));
            }
        }


        item.setUnit(info.getSaleUnit());
        if (info.getExtraInfo() != null && Objects.equals(GoodsUnitTypeEnum.WEIGHT.name(), info.getExtraInfo().get(EXTRA_KEY_UNIT_TYPE))) {
            try {
                Object saleWight = info.getExtraInfo().get(EXTRA_KEY_SALE_WEIGHT);
                if (saleWight != null) {
                    double weight = Double.parseDouble(String.valueOf(saleWight));
                    item.setWeight(BigDecimal.valueOf(weight).stripTrailingZeros());
                }
            } catch (Exception e) {
                log.warn("称重商品重量值计算错误", e);
            }
            item.setUnitType(1);
            item.setNumber(info.getCount().intValue());
        } else {
            item.setUnitType(0);
            item.setNumber(info.getCount().intValue());
        }

        cartItemCreate.setItem(item);

        item.setAttachedInfo(generateAttachInfo(cartItemCreate));

        return cartItemCreate;
    }


    private static String getProcessStatusForShow(OrderProcessStatusEnum processStatus, OrderStatus orderStatus) {
        if (processStatus == null) {
            return null;
        }
        if (processStatus != OrderProcessStatusEnum.COMPLETED) {
            return processStatus.getCode();
        }

        if (orderStatus == OrderStatus.REFUNDED || orderStatus == OrderStatus.PARTIAL_REFUNDED) {
            //结账状态下，如果是退款，那么显示退款
            return "REFUNDED";
        }

        return processStatus.getCode();
    }

    public static String generateAttachInfo(CartItemCreate create) {
        return generateAttachInfo(create, false);
    }

    /**
     * @param create            商品信息
     * @param isPackageSubGoods 是否是套餐内的商品
     * @return
     */
    public static String generateAttachInfo(CartItemCreate create, boolean isPackageSubGoods) {
        List<String> attachInfoList = new ArrayList<>();
        CartItemCreate.Spec spec = create.getSpec();
        List<CartItemCreate.Attribute> attributes = create.getAttributes();
        List<CartItemCreate.Material> materials = create.getMaterials();


        if (!isPackageSubGoods && Objects.equals(create.getItem().getSpuType(), SpuType.PACKAGE.name())) {

            List<CartItemCreate> cartItemCreateList = CartHelper.mergeSameGoodsCartItem(create.getPackageItems());

            List<String> packageAttachInfoList = cartItemCreateList.stream()
                    .map(i -> {
                        String packageGoodsAttachInfo = generateAttachInfo(i, true);
                        if (StringUtils.isEmpty(packageGoodsAttachInfo)) {
                            return i.getItem().getName() + CART_NUM_UNIT + i.getItem().getNumber();
                        } else {
                            return i.getItem().getName() + "(" + packageGoodsAttachInfo + ")" + CART_NUM_UNIT + i.getItem().getNumber();
                        }
                    })
                    .collect(Collectors.toList());

            String attachInfo = String.join("+", packageAttachInfoList);
            create.getItem().setAttachedInfoWithoutMaterials(attachInfo);
            create.getItem().setAttachedInfo(attachInfo);
            create.setPackageItems(cartItemCreateList);
            return attachInfo;
        }

        if (spec != null && !StringUtils.isEmpty(spec.getName())) {
            attachInfoList.add(spec.getName());
        }

        if (!CollectionUtils.isEmpty(attributes)) {
            attributes.forEach(attribute -> {
                attachInfoList.add(attribute.getName());
            });
        }

        String attachInfoWithoutMaterials = String.join(",", attachInfoList);

        if (!CollectionUtils.isEmpty(materials)) {
            materials.forEach(material -> {
                String materialInfo = material.getName();

                if (material.getNumber() != null && material.getNumber() > 1) {
                    materialInfo = materialInfo + CART_NUM_UNIT + material.getNumber();
                }
                attachInfoList.add(materialInfo);
            });
        }

        String attachInfo = String.join(",", attachInfoList);
        create.getItem().setAttachedInfo(attachInfo);
        create.getItem().setAttachedInfoWithoutMaterials(attachInfoWithoutMaterials);
        return attachInfo;
    }

}
