package com.wosai.pantheon.uf4c.model;

import com.wosai.pantheon.order.enums.OrderType;
import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import com.wosai.pantheon.uf4c.model.dto.RedeemRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * 下单页聚合接口请求
 */
@Data
public class GatherOrderRequest implements Serializable {
    /**
     * 商户ID
     */
    private String merchantId;
    /**
     * 门店ID
     */
    private String storeId;
    /**
     * 订单类型：
     * subscribe_order：堂食轻餐
     * take_out_order：外卖
     * pre_order：自取
     * EAT_FIRST_ORDER：堂食围餐
     */
    private String orderType;
    private OrderType type;
    /**
     * 堂食是否打包
     */
    private boolean packed = false;
    /***
     * 优惠计算相关参数
     */
    private RedeemRequest discountParams = new RedeemRequest();
    /**
     * 桌台ID，堂食围餐场景必传
     */
    private Integer tableId;
    /**
     * 用户ID,从token中获取
     */
    private String userId;
    /**
     * openid
     */
    private String thirdpartyUserId;
    /**
     * 小程序类型，从header中获取
     */
    private MiniProgramType miniProgramType;
    /**
     * 服务类型，根据orderType计算而来
     */
    private int serviceType;
    /**
     * 标识来自于购物车（储值折扣版本小程序才会设置，老版本都是false)
     */
    private boolean fromCart = false;
    /**
     * 接口版本号
     */
    private Integer apiVersion;

    /**
     * 取单时间
     */
    private Long presetTime;
    /**
     * 小程序appid
     */
    private String appid;
}
