package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @BelongProjecet uf4c-app
 * @BelongPackage com.wosai.pantheon.uf4c.model
 * @Copyright 2013-3102
 * @Date 2023/8/8 16:26
 * @Description 指定通过商品id或者 skuId 直接添加购物车的入参
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpecSpuCartItemCreate {

    private String merchantId;

    @NotBlank
    @Size(max = 36)
    private String storeId;

    private Integer serviceType = 0;

    private Integer payway;

    private Integer subPayway;

    private String discountStrategy;

    private String terminalSn;

    private OrderMealTypeEnum mealType = OrderMealTypeEnum.SINGLE;

    /**
     * 桌台id
     */
    private String tableId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户名头像
     */
    private String userIcon;

    /**
     * 营销的透传参数, 透传给优惠平台
     */
    private Map<Object,Object> mkCustomInfo;


    @NotNull(message = "商品ID不能为空")
    @Size(min = 1, message = "商品ID数量必须大于 1")
    List<SpecificItem> specificItems;

    @Getter
    @Setter
    public static class SpecificItem {

        @NotBlank(message = "商品ID不能为空")
        public String spuId;

        public String skuId;

    }

}
