package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.pantheon.uf4c.constant.Constants;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ServiceTypeData {
    // 默认选中的服务类型在集合中的下标
    private Integer currentServiceTypeIndex = 0;
    // 默认选中的服务类型 1-外卖和自取  2-堂食
    private Integer serviceType = 0;
    // 默认选中的服务类型名称（订单类型) subscribe_order,take_out_order,pre_order
    private String serviceTypeName;
    // 服务类型选择是否默认弹出
    private Boolean selectServiceTypeShow = true;
    // 该场景支持的服务类型列表
    private List<ServiceItem> serviceTypeList = new ArrayList<>();

    @Data
    public static class ServiceItem {
        /**
         * 1-堂食 2-外卖 3-自取
         */
        @JsonIgnore
        private int sort = 0;
        private String alias;
        private String name;
        private Integer serviceType;
        private String serviceTypeName;
        private String shortName;
        /**
         * 是否默认选中
         */
        private boolean active = false;
        private String action = Constants.ServiceTypeAction.CONFIRM;

    }


}
