package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.pantheon.core.uitem.model.CategoryExt;

import java.util.List;

/**
 * @Author: FuL
 * @Date 2021/6/10
 */
public class CategoryVo extends CategoryExt {
    public static final String ITEMS = "items";
    public static final String SUB_CATEGORIES = "sub_categories";


    public List<ItemDetailVO> getItems() {
        return get(ITEMS, List.class);
    }

    public void setItems(List<ItemDetailVO> items) {
        put(ITEMS, items);
    }

    public void setSubCategories(List<CategoryVo> subCategories) {
        put(SUB_CATEGORIES, subCategories);
    }

    public List<CategoryVo> getSubCategories() {
        return get(SUB_CATEGORIES, List.class);
    }

}
