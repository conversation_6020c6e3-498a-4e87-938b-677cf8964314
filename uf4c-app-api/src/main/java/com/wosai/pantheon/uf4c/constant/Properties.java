package com.wosai.pantheon.uf4c.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Data
@Component
@ConfigurationProperties(prefix = "wosai")
public class Properties {
    public static final String WEIXIN_UA = "micromessenger";
    public static final String ALIPAY_UA = "alipayclient";
    public static final String ALIPAY_AUTH_URL = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id=%s&response_type=code&scope=auth_user&redirect_uri=%s&state=%s";
    public static final String WEIXIN_AUTH_URL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&state=%s#wechat_redirect";
    public static final String WEIXIN_GET_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";
    public static final String GRANTING_COOKIE_VALUE= "GC-UF4C";
    private int cpuNum;
    private int concurrency;
    private String h5;
    private String h5QrenterPrefix;
    private Rpc rpc;
    private Alipay alipay;
    private Weixin weixin;
    private WeixinPlatform weixinPlatform;
    private boolean secureCookieEnable;
    private int rememberMeTtl;
    private int requestTimeout;

    @Data
    public static class Alipay {
        private String appId;
        private String privateKey;
        private String publicKey;
        private SignType signType;
    }

    public enum SignType {
        RSA,
        RSA2
    }

    @Data
    public static class Weixin implements Serializable {
        private String appId;
        private String secret;
    }

    @Data
    public static class WeixinPlatform {
        private String uri;
        private String appId;
        private String[] whiteList;
    }

    @Data
    public static class Rpc {
        private int readTimeout = 2000;
        private int connectionTimeout = 2000;
    }
}
