package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.pantheon.core.uitem.model.AttributeDto;
import com.wosai.pantheon.core.uitem.model.ItemTag;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * created by shij on 2019/4/12
 */
@Data
public class ItemDetailVO {

    @NotNull
    @Valid
    private ItemVO item;

    @Valid
    private ItemSpecVO specs;

    @Valid
    private List<AttributeDto> attributes;

    private List<String> materialIds;

    private List<MaterialVO> materials;

    private List<ItemTag> itemTags;

    private List<MaterialGroupVO> materialGroups;

    private List<String> ingredientNames;

    /**
     * 套餐必选商品
     */
    private List<ItemDetailVO> packageMustOrderProducts;

    /**
     * 套餐可选商品分组
     */
    private List<PackageOptionalGroup> packageOptionalGroups;

    /**
     * 购买次数
     */
    private BigDecimal boughtTimes;

    /**
     * 最近一次购买时间
     */
    private Long latestBuyTime;

    /**
     * 售卖时间
     */
    private ItemSaleTimeVO saleTime;
}
