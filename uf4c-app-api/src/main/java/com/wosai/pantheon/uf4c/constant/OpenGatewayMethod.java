package com.wosai.pantheon.uf4c.constant;

/**
 * created by shij on 2019/4/1
 */
public class OpenGatewayMethod {

    public static final String ORDER_CREATE = "com.wosai.ufood.order.create";

    public static final String ORDER_GET_BY_SN = "com.wosai.ufood.order.getBySn";

    public static final String ORDER_FIND_PAGE = "com.wosai.ufood.order.find";

    public static final String ORDER_SN_GET = "com.wosai.ufood.order.nextSn";

    public static final String ORDER_PAY_SUCCESS = "com.wosai.ufood.order.paySuccess";

    public static final String ORDER_REFUND = "com.wosai.ufood.order.refund";

    public static final String ORDER_PAY_CANCEL = "com.wosai.ufood.order.payCancel";

    public static final String ORDER_PAY_FAIL = "com.wosai.ufood.order.payFail";

    public static final String ORDER_BATCH_CTE = "com.wosai.ufood.order.batchUpdateToCTE";

    public static final String ORDER_UPDATE_PAYINPROC = "com.wosai.ufood.order.update.payinproc";

    public static final String ORDER_CLIENT_SN_UPDATE = "com.wosai.ufood.order.updateClientSn";

    public static final String ORDER_UPDATE = "com.wosai.ufood.order.update";

    public static final String TERMINAL_GET_BY_ID = "com.wosai.upay.core.terminal.get";

    public static final String CATEGORY_FIND_BY_STORE_ID = "com.wosai.uitem.core.category.findByStoreId";

    public static final String ITEM_FIND_PAGE = "com.wosai.uitem.core.item.findDetailsByCategoryIdPaginated";

    public static final String ITEM_FIND = "com.wosai.uitem.core.item.findDetailsByCategoryId";

    public static final String ITEM_SEARCH = "com.wosai.uitem.core.item.findDetails";

    public static final String ITEM_GET_BY_ID = "com.wosai.uitem.core.item.getDetail";

    public static final String TRADE_PARAMS_GET = "com.wosai.upay.core.trade.params.get";

    public static final String TRADE_PARAMS_KEY_MAP_GET = "com.wosai.upay.core.trade.params.keymap.get";

    public static final String QR_CODE_GET = "com.wosai.open.food.qrcode.get";

    public static final String SEND_PRINT_JOB = "com.wosai.uprint.core.printjob.print";

}
