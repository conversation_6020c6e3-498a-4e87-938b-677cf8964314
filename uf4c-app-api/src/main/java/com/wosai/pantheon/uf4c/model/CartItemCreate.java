package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wosai.pantheon.order.enums.GoodsProcessStatus;
import com.wosai.pantheon.uf4c.model.validation.NotAllNull;
import com.wosai.pantheon.uf4c.model.vo.BrandActivityProduct;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * created by shij on 2019/7/26
 */
@Data
@NotAllNull(fieldNames = {"itemUid", "item"})
@JsonIgnoreProperties(ignoreUnknown = true)
public class CartItemCreate {

    /**
     * 这个是前端传参问题，有时候前端传上来的是id, 他等效于itemUid
     */
    private String id;

    @Size(max = 32)
    private String itemUid;

    @NotBlank
    @Size(max = 36)
    private String storeId;

    private String merchantId;

    /**
     * 桌台id
     */
    private String tableId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户名头像
     */
    private String userIcon;

    @Valid
    private Item item;

    @Valid
    private Spec spec;

    @Valid
    private List<Attribute> attributes;

    @Valid
    private List<Material> materials;

    private Long ctime;

    private String categoryId;

    private Integer serviceType = 0;

    /**
     * 商品数量
     */
    private Integer number = 1;


    /**
     * 下单时间
     */
    private Long orderTime;

    /**
     * 菜品处理状态
     */
    private GoodsProcessStatus processStatus;
    /**
     * 额外信息
     */
    private Map<Object, Object> extraInfo;

    /**
     * 商品所属的分组id
     */
    private String packageGroupId;

    /**
     * 商品所属的分组名称
     */
    private String packageGroupName;
    /**
     * 套餐被选中的商品列表
     */
    private List<CartItemCreate> packageItems;


    private Integer quotaCount;

    private Boolean must;

    /**
     * 是否是开台必点商品
     */
    private boolean openTableMustOrder = false;

    /**
     * 开台必点商品是否可以编辑
     */
    private boolean openTableItemEditable = false;

    /**
     * 就餐人数,仅针对开台必点商品
     */
    private Integer peopleNum;

    private Long packFee;

    /**
     * 推荐加料
     */
    private List<RecommendMaterial> recommendMaterials;

    //以下参数用来计算优惠
    private String orderSn;

    private Integer payway;

    private Integer subPayway;

    private String discountStrategy;

    private String terminalSn;

    /**
     * 营销的透传参数, 透传给优惠平台
     */
    private Map<Object, Object> mkCustomInfo;

    // 兼容性参数，当传入的值为true时，表示为新版本
    private Boolean compatible;

    private OrderMealTypeEnum mealType = OrderMealTypeEnum.SINGLE;

    /**
     * 是否是赠菜
     */
    private boolean giftFood = false;

    /**
     * 订单详情使用，返回商品享受的优惠
     */
    private String activityName;
    /**
     * 订单详情使用，商品上展示的标签名
     */
    private String tagName;

    /**
     * 标识来自于购物车（储值折扣版本小程序才会设置，老版本都是false)
     */
    private boolean fromCart = false;

    /**
     * 是否是手动改价
     */
    private boolean manualChangePrice = false;

    /**
     * 本地购物车专有属性
     * 是否使用提交的购物车数据覆盖服务端数据
     * 若cover =true
     * 1.使用客户端的商品数量覆盖服务端已有数量，不再是在原有基础上增加数量的方式
     * 2.当数量为0时，在购物车中删除该商品
     */
    private boolean cover = false;

    /**
     * 本地购物车专有属性
     * 客户端购物车版本号
     * 只有在  cover = true 才会使用该字段进行逻辑处理
     * 若已有购物车版本号小与最新版本号，则覆盖
     * 与前端约定，该版本号使用时间戳数据
     */
    private long clientVersion = 0;

    /**
     * 是否微信加价购活动
     */
    private boolean brandAct = false;

    /**
     * 微信加价购活动信息
     */
    private BrandActivityProduct brandActProduct;

    /**
     * 合并后的购物车所包含的品牌购商品信息
     */
    @JsonIgnore
    private List<BrandActivityProduct> brandActivityProducts;

    /**
     * 零售商品下单时所需要的商品快照
     */
    @JsonIgnore
    private Map<String, Object> snapshot;

    /**
     * 零售称重商品库存转换信息
     */
    private Map<String, Object> skuConvert;


    /**
     * 品牌商品来源 BRAND:品牌 STORE:门店
     */
    private String brandProductSource;

    /**
     * 是否是品牌自动加购
     */
    private boolean fromBrandAuto = false;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Item {
        @NotNull
        private String id;

        private String categoryId;

        @NotBlank
        private String name;

        private String attachedInfo;

        private String attachedInfoWithoutMaterials;

        private Integer status = 0;

        private Boolean must;

        @NotNull
        @Min(0)
        private Integer price;

        private Integer discountPrice;

        private Integer number = 1;

        /**
         * 用于存储支持小数的number
         */
        private BigDecimal numberDecimal;

        /**
         * 退菜数量
         */
        private Integer returnNumber;

        private Integer discountNumber = 0;

        private BigDecimal weight;

        private String url;

        private String photoUrl;

        private String unit;

        private Integer isMultiple;

        private Integer sku;

        private Boolean outOfStock;

        /**
         * 计价单位类型 0-按件 1-按称重
         */
        private Integer unitType;

        /**
         * 商品类型
         */
        private String spuType;

        /**
         * 商品特别的tag
         */
        private Long itemTag;

        /**
         * 最小售卖数量
         */
        private Integer minSaleNum;

        /**
         * 类目排序值
         */
        private Integer categorySort;

        /**
         * 商品排序值
         */
        private Integer displayOrder;

        /**
         * 订单详情使用， 该商品总价
         */
        private Long totalAmount;

        /**
         * 库存id
         */
        private String stockId;

    }

    @Data
    public static class Spec {
        @NotNull
        private String id;

        @NotBlank
        private String name;

        @NotNull
        @Min(0)
        private Integer price;
    }

    @Data
    public static class Attribute {
        @NotNull
        private String id;

        private String title;

        @NotBlank
        private String name;

        private Integer seq;
    }

    @Data
    public static class Material {
        @NotNull
        private String id;

        @NotBlank
        private String name;

        @NotNull
        @Min(0)
        private Long price;

        private Integer number;

        /**
         * 加购加料的来源
         * {@linkplain com.wosai.pantheon.order.enums.MaterialAddSourceEnum#getCode()}
         */
        private Integer source;

    }

    @Data
    public static class RecommendMaterial extends Material {
        /**
         * 是否被选中，推荐加料中使用
         */
        private boolean selected = false;
    }

}
