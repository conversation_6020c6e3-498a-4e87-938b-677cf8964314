package com.wosai.pantheon.uf4c.constant;

/**
 * 微信小程序页面场景值
 */

import lombok.Getter;

@Getter
public enum MpPageSceneEnums {
    HOME("page-home", "点单首页"),
    USER_COUNT("page-user-count", "围餐-选择人数"),
    SUBMIT_ORDER("page-submit-order", "围餐-下单"),
    PAY_FIRST_TABLE_ORDER_SUBMIT_PAGE("page-firstpay-round-batch-list", "先付围餐-下单"),
    AGGREGATION("page-aggregation", "聚合码"),
    AGGREGATION_LIST("page-aggregation-list", "聚合码的聚合码"),
    CLOSING("page-closing", "店铺关闭"),
    CAMPUS("page-campus", "校园外卖"),
    GATHER_STORE_ENTRANCE("gatherStoreEntrance", "聚合页"),
    GATHER_STORE("gatherStore", "聚合页"),
    INDEX("page-index", "店铺列表页");

    private String page;
    private String desc;

    MpPageSceneEnums(String page, String desc) {
        this.page = page;
        this.desc = desc;
    }
}
