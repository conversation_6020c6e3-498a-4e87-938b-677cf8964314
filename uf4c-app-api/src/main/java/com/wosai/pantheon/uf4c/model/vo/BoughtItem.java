package com.wosai.pantheon.uf4c.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2021/10/26
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BoughtItem implements Serializable {

    private String itemId;

    private BigDecimal boughtTimes;

    private Long latestBuyTime;

    private int seq;

}
