package com.wosai.pantheon.uf4c.model.dto;

import com.wosai.pantheon.uf4c.model.DeliverInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/3/23
 */
@Data
public class DeliverFeeRequest implements Serializable {

    @NotEmpty
    private String storeId;

    // 兼容性参数，当传入的值为true时，表示为新版本
    private boolean compatible;

    @NotNull
    private DeliverInfo deliveryInfo;
}
