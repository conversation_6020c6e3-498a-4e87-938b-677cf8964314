package com.wosai.pantheon.uf4c.model.dto;

import com.wosai.market.enums.SaleSceneEnum;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/7
 */
@Data
public class RecommendItemQueryByStoresRequest implements Serializable {

    /**
     * 门店ID
     */
    @NotEmpty(message = "门店ID不能为空")
    private List<String> storeIds;

    /**
     * 售卖场景
     * {@linkplain SaleSceneEnum#getServiceType()}
     */
    private Integer serviceType;

    /**
     * 数量限制，默认=3
     */
    @Max(value = 10, message = "每个门店最多返回10个商品")
    private Integer countLimit;

}
