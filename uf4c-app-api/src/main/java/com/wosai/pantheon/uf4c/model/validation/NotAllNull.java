package com.wosai.pantheon.uf4c.model.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * created by shij on 2019/8/19
 */
@Target(ElementType.TYPE)
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {NotAllNullValidator.class})
public @interface NotAllNull {

    String message() default "{fieldNames}不能全部为null";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

    String[] fieldNames();
}
