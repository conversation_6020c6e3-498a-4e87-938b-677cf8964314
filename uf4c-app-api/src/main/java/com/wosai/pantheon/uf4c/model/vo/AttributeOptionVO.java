package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.validation.constraints.UUID;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * created by shij on 2019/7/24
 */
@Data
public class AttributeOptionVO {
    private String id;

    @NotNull
    @Size(max = 20)
    private String name;

    @NotNull
    @Min(1)
    private Integer seq;

}
