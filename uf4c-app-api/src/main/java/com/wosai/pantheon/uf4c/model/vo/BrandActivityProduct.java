package com.wosai.pantheon.uf4c.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BrandActivityProduct {
    /**
     * 活动id
     */
    private String activityId;
    /**
     * 活动产品id
     */
    private Long productId;
    /**
     * 活动商品名称
     */
    private String name;

    /**
     * 活动商品图片
     */
    private String url;

    /**
     * 已加购数量
     */
    private Integer number;

    /**
     * 原始名称
     */
    private String oriName;

    /**
     * 原始图片
     */
    private String oriUrl;

}
