package com.wosai.pantheon.uf4c.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PackageOptionalGroup implements Serializable {
    /**
     * 分组id
     */
    private String groupId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 必点商品数量
     */
    private Integer mustOrderNum;
    /**
     * 是否支持重复点
     */
    private boolean supportDuplicate;

    /**
     * 商品列表
     */
    private List<ItemDetailVO> products;

}