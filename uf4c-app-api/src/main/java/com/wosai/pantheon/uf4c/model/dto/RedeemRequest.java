package com.wosai.pantheon.uf4c.model.dto;

import com.wosai.market.trade.modal.CombinedPaymentRequest;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class RedeemRequest implements Serializable {

    private String merchantId;

    @NotEmpty
    private String storeId;
    private String tableId;
    private String sn;
//    private List<OrderItemAddRequest> items;
    private String totalAmount;
    private Integer payway;
    private Integer subPayway;
//    private String payerUid;
//    private String weixinAppId;
//    private String userId;
    private String discountStrategy;
    private String terminalSn;
    private Boolean packed;
    // 兼容性参数，当传入的值为true时，表示为新版本
    private Boolean compatible;
    private String orderSn;

    /**
     * 是否是储值并支付
     */
    private boolean rechargeAndPay = false;

    /**
     * 微信加价购商品列表
     */
    private List<WxGoodsSubsidy> wxGoods;

    //储值折扣优惠规则id
    private String rechargeInterestId;

    private boolean fromCart = false;
    /**
     * 组合支付相关信息
     */
    private CombinedPaymentRequest combinedPayment;

    private List<CartItemCreate> redeemItems;

    private boolean banGiftCartDiscount;
    /**
     * 自定义资源, 透传给优惠平台
     */
    private Map<Object,Object> mkCustomInfo;

    /**
     * 统一收银台的统一逻辑
     */
    private Object usingPayTools;

    /**
     * 是否来自提交订单页
     */
    private boolean fromSubmitOrder;
}
