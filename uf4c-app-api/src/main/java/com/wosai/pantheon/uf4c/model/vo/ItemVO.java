package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.pantheon.core.uitem.model.TimeSection;
import com.wosai.validation.constraints.NullOrNotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ItemVO {

    private String id;

    @NotBlank
    @Size(max = 20)
    private String name;

    /**
     * 历史字段兼容，优先使用images字段
     */
    @Size(max = 1024)
    @URL
    @Deprecated
    private String photoUrl;

    /**
     * 图片列表
     */
    private List<ItemImageVO> images;

    @NullOrNotBlank
    @Size(max = 256)
    private String description;

    @NotNull
    @Min(1)
    private Integer displayOrder;

    private Boolean valid = true;

    /**
     * 若零售有二级分类时。此处为1级分类
     */
    @NotBlank
    private String categoryId;

    /**
     * 二级分类。零售使用
     */
    private String subCategoryId;

    @Size(max = 36)
    private String merchantId;

    @NotBlank
    @Size(max = 36)
    private String storeId;

    @NotNull
    @Min(0)
    private Integer price;

    private Integer activityPrice;

    /**
     * 储值折扣价
     */
    @Deprecated
    private Integer giftCardDiscountPrice;

    /**
     * 优惠价格
     */
    private List<ItemDiscountPriceVO> discountPrices;

    /**
     * 优惠文案
     */
    private List<ItemDiscountText> discountTexts;
    /**
     * 第二份优惠折扣
     */
    @Deprecated
    private Integer secondActivityDiscount;

    @Deprecated
    private Integer quotaCount;

    private Boolean forSale = true;

    private Boolean outOfStock = false;

    private Integer sku; //t:String
    private Integer skuDefault; // t:String
    private Boolean resetSku; //t:Boolean

    private String unit;
    private Integer unitType = 0;

    private String categoryName;
    private String categorySort;

    private String spuType;

    /**
     * 规格的个数
     */
    private Integer isMultiple;

    /**
     * 商品特别的tag
     */
    private Long itemTag;

    /**
     * 套餐必选商品必须加购的数量
     */
    private Integer addCount;


    /**
     * 过去30天的销量
     */
    private Long last30DaysSaleCount;

    /**
     * 热销排名
     */
    private Integer hotsaleSeq;

    /**
     * 是否是热销商品
     */
    private boolean hotsaleProduct;

    /**
     * 到店价/外卖价保持一致
     */
    private Boolean keepPriceEqual;

    /**
     * 到店价
     */
    private Integer arrivalPrice;

    /**
     * 外卖价
     */
    private Integer takeoutPrice;

    /**
     * 起售数量
     */
    private Integer minSaleNum;

    /**
     * 售卖时段
     */
    private List<TimeSection> saleTimes;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 售卖终端
     */
    private List<Integer> saleTerminals;

}
