package com.wosai.pantheon.uf4c.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022/11/17
 */
@Data
@Accessors(chain = true)
public class HotSaleItemQueryRequest implements Serializable {

    /**
     * 门店ID
     */
    @NotBlank(message = "门店ID不能为空")
    private String storeId;

    /**
     * 服务类型
     * 1：外卖
     * 2：堂食
     * 不传默认=2
     */
    private Integer serviceType;

    /**
     * 销量统计天数
     */
    @Max(value = 30, message = "统计天数最大30天")
    private Long days;

    /**
     * 数量限制
     */
    @Max(value = 15, message = "最多返回15条")
    private Integer countLimit;

}
