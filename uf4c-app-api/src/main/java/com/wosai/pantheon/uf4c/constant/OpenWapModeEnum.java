package com.wosai.pantheon.uf4c.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OpenWapModeEnum {
    ALIPAY_ON_WECHAT_ON("00", ""),
    ALIPAY_ON_WECHAT_OFF("01", ""),
    ALIPAY_ON_WECHAT_INDIRECT_ON("02", ""),
    ALIPAY_ON_WECHAT_DIRECT_ON("03", ""),
    ALIPAY_OFF_WECHAT_ON("10", ""),
    ALIPAY_OFF_WECHAT_OFF("11", ""),
    ALIPAY_OFF_WECHAT_INDIRECT_ON("12", ""),
    ALIPAY_OFF_WECHAT_DIRECT_ON("13", ""),

    ;

    private String code;
    private String desc;

}
