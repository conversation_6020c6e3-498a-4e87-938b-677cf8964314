package com.wosai.pantheon.uf4c.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * 购物车列表查询请求
 * 该请求中，包括优惠计算所需的部分信息
 */
@Data
public class CartsRequest implements Serializable {

    private String merchantId;
    @NotBlank
    private String storeId;

    private String orderSn;

    @NotNull
    private Integer serviceType;
    @NotBlank
    private String serviceTypeName;

    private Integer payway;
    private Integer subPayway;
    private String terminalSn;
    private String tableId;
    private boolean rechargeAndPay = false;
    private String rechargeInterestId;

    private Boolean fillRecommendMaterials;

    private boolean fromCart = false;

    /**
     * 自定义资源, 透传给优惠平台
     */
    private Map<Object, Object> mkCustomInfo;

}
