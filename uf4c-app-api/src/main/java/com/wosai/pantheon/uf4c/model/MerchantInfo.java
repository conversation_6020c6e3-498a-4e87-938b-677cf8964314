package com.wosai.pantheon.uf4c.model;

import com.wosai.pantheon.core.uitem.model.CategoryExt;
import com.wosai.pantheon.uf4c.model.table.TableInfoVO;
import com.wosai.smartbiz.base.enums.YesNoEnum;
import lombok.Data;

import java.util.List;

/**
 * created by shij on 2019/4/1
 */
@Data
public class MerchantInfo {

    private String storeId;

    private String sn;

    private String name;

    private Integer businessStatus;

    private String merchantId;

    private String merchantSn;

    private List<CategoryExt> categories;

    private String storeBulletin;

    /**
     * 是否是收银机模式
     */
    private YesNoEnum cashierMode;

    /**
     * 当前门店的就餐模式
     */
    private String mealType;

    /**
     * 绑定的桌台信息
     */
    private TableInfoVO tableInfo;

    /**
     * 是否收藏
     */
    private Boolean isCollection;
}
