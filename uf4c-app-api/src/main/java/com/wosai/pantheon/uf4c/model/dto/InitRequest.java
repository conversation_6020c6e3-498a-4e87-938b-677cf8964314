package com.wosai.pantheon.uf4c.model.dto;

import com.wosai.pantheon.uf4c.model.CartItemCreate;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/9
 */
@Data
public class InitRequest implements Serializable {

    /**
     * 是否需要检查购物车商品
     */
    private boolean checkCartItem = false;

    @NotNull
    private String storeId;

    @NotNull
    private String tableId;

    private String tableNo;

    private String orderSn;

    private String remark;

    private Long totalAmount;

    private Long discountAmount;


    private String payUid;

    @NotEmpty
    private List<CartItemCreate> items;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户名头像
     */
    private String userIcon;

    /**
     * 服务类型
     */
    private Integer serviceType;


}
