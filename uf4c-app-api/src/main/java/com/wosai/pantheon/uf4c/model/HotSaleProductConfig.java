package com.wosai.pantheon.uf4c.model;

import lombok.Data;

import java.util.List;

@Data
public class HotSaleProductConfig {

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 是否是自动生成
     */
    private boolean autoGenerate;

    /**
     * 最多展示多少条
     */
    private Integer maxShowNum;

    /**
     * 是否打开热销商品
     */
    private boolean openSaleProduct;

    /**
     * 商品信息
     */
    private List<ProductSpuInfo> spuIds;


    /**
     * 是否自动过滤: 指的是自动过滤 "商品名称包含米饭、餐具、打包盒和商品单价低于 0.5 元的商品"
     *
     * @link #autoGenerate 为 true 时此参数才有作用
     */
    private boolean autoFilter;

    /**
     * 是否有配置过滤商品信息,这里冗余存储避免每次查库去得到排除的具体数据
     */
    private boolean hasExcludeSpus;


    @Data
    public static class ProductSpuInfo {
        private String spuId;
    }
}

