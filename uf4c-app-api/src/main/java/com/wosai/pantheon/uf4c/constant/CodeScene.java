package com.wosai.pantheon.uf4c.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum CodeScene {

    T("t", "桌台码"),
    A("a", "通用码"),
    B("b", "自主门店点单码"),
    C("c", "门店点单码（物料）"),
    D("d", "门贴码"),
    P("p", "海报"),
    G("g", "聚合码"),
    S("s", "营销码");

    private String scene;
    private String desc;

    CodeScene(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }

    public static CodeScene getCodeScene(String scene) {
        if (StringUtils.isBlank(scene)) {
            return null;
        }
        for (CodeScene codeScene : values()) {
            if (codeScene.name().equalsIgnoreCase(scene)) {
                return codeScene;
            }
        }
        return null;
    }
}
