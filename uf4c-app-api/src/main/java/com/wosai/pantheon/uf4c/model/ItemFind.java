package com.wosai.pantheon.uf4c.model;

import com.wosai.data.VersionedRecord;
import com.wosai.web.api.Pagination;
import lombok.Data;

/**
 * created by shij on 2019/4/16
 */
@Data
public class ItemFind extends VersionedRecord {

    public static final String STORE_ID = "store_id";  // t:String
    public static final String CATEGORY_ID = "category_id";  // t:String
    public static final String PAGE = "page";  // t:Integer
    public static final String PAGE_SIZE = "page_size";  // t:Integer
    public static final String SEARCH_TERMS = "search_terms";  // t:SearchTermList
    public static final String PAGINATION = "pagination";  // t:Pagination
    public static final String SERVICE_TYPE = "service_type"; // t:Integer
    public static final String API_VERSION = "api_version";
    public static final String CURSOR = "cursor";

    // 图片展示尺寸
    public static final String DISPLAY_IMAGE_SIZE = "display_image_size";

    public String getStoreId() {
        return get(STORE_ID, String.class);
    }

    public void setStoreId(String storeId) {
        put(STORE_ID, storeId);
    }

    public String getCategoryId() {
        return get(CATEGORY_ID, String.class);
    }

    public void setCategoryId(String categoryId) {
        put(CATEGORY_ID, categoryId);
    }

    public Integer getPage() {
        return get(PAGE, Integer.class);
    }

    public void setPage(Integer page) {
        put(PAGE, page);
    }

    public Integer getPageSize() {
        return get(PAGE_SIZE, Integer.class);
    }

    public void setPageSize(Integer pageSize) {
        put(PAGE_SIZE, pageSize);
    }

    public SearchTermList getSearchTerms() {
        return get(SEARCH_TERMS, SearchTermList.class);
    }

    public void setSearchTerms(SearchTermList searchTerms) {
        put(SEARCH_TERMS, searchTerms);
    }

    public Pagination getPagination() {
        return get(PAGINATION, Pagination.class);
    }

    public void setPagination(Pagination pagination) {
        put(PAGINATION, pagination);
    }

    public Integer getServiceType() {
        return get(SERVICE_TYPE, Integer.class);
    }

    public void setServiceType(Integer pageSize) {
        put(SERVICE_TYPE, pageSize);
    }

    public Integer getApiVersion() {
        return get(API_VERSION, Integer.class);
    }

    public void setApiVersion(Integer apiVersion) {
        put(API_VERSION, apiVersion);
    }

    public String getCursor() {
        return get(CURSOR, String.class);
    }

    public void setCursor(String cursor) {
        put(CURSOR, cursor);
    }

    public Integer getDisplayImageSize() {
        return get(DISPLAY_IMAGE_SIZE, Integer.class);
    }

    public void setDisplayImageSize(Integer displayImageSize) {
        put(DISPLAY_IMAGE_SIZE, displayImageSize);
    }

}