package com.wosai.pantheon.uf4c.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/11/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RecommendMaterialFindRequest {

    @NotBlank
    private String storeId;

    @NotEmpty
    private List<String> itemIds;

}
