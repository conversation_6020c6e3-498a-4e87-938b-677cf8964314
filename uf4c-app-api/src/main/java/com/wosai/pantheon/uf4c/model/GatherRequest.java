package com.wosai.pantheon.uf4c.model;

import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

@Data
public class GatherRequest implements Serializable {
    /**
     * 二维码地址
     */
    private String url = "";

    /**
     * 二维码上的TAG_CASHIER对应的value信息
     * 收钱音箱二维码会使用
     */
    private String terminalTagValue;

    private String query;
    /**
     * 门店ID
     */
    private String storeId = "";
    /**
     * 来源
     */
    private String from = "";
    /**
     * 校园ID
     */
    private Integer campusId = 0;
    /**
     * 聚合页ID
     */
    private Integer storeSetId = 0;
    /**
     * 数据范围：store, merchant, mcc, category, goods, activity
     */
    private String resources = "";
    /**
     * 微信登录授权码
     */
    private String code = "";
    /**
     * 小程序appid，此数据为微信调用时附带的，非前端传参
     */
    private String appid;
    /**
     * 第三方用户id，例如openid
     */
    private String thirdPartUserId;
    private String wxUnionId;

    /**
     * 传了该参数，直接用userId来登录
     */
    private String loginUserId;

    /**
     * 服务类型，若前端不传入，则自动计算
     */
    private Integer serviceType;
    private String merchantId;
    /**
     * 再来一单订单类型
     */
    private String buyAgainOrderType;

    /**
     * 小程序使用的apollo版本号
     */
    private String version;
    private String token;
    private String userId;


    private String miniProgramType = MiniProgramType.WECHAT.name();
    private String scene = "qrcode_t";
    private String mpScene;
    // 每个请求，生成为一个UUID
    private String reqId;
    // 是否为测试环境
    private boolean isBetaEnv = false;
    // 点过的菜计数器
    private CountDownLatch recentItemCountDownLatch;
    // 主题预览id
    private String draftId;
    // 预览id
    private Long previewId;
    // openid
    private String openid;
    // 扫码时间
    private String scancode_time;
    private String unionid;
    // 是否为缓存更新请求
    private boolean cacheUpdate = false;
    /**
     * 接口版本号
     */
    private Integer apiVersion;

    /**
     * 是否零售门店
     */
    private boolean retailStore;

    private String acceptLanguage;

    // 模板id
    private Long templateId;
    // 模板版本号
    private String templateVersion;

    public boolean needMcc() {
        return this.resources.contains("mcc");
    }

    public boolean needStore() {
        return this.resources.contains("store");
    }


    public boolean needCategory() {
        return this.resources.contains("category");
    }

    public boolean needGoods() {
        return this.resources.contains("goods");
    }

    public boolean needActivity() {
        return this.resources.contains("activity");
    }

    public boolean needLogin() {
        return StringUtils.isNotBlank(this.code) || (StringUtils.isNotBlank(this.thirdPartUserId) && StringUtils.isNotBlank(this.appid));
    }

    public boolean needConfig() {
        return this.resources.contains("config");
    }

    public void recentCountDown() {
        if (Objects.nonNull(this.recentItemCountDownLatch)) {
            this.recentItemCountDownLatch.countDown();
        }
    }

}
