package com.wosai.pantheon.uf4c.model;

import com.wosai.web.api.ListResult;

import java.util.List;

public class CursorResult<T> extends ListResult<T> {
    private String cursor;

    public CursorResult() {
    }

    public CursorResult(List<T> records, String cursor, long total) {
        this.cursor = cursor;
        super.setTotal(total);
        super.setRecords(records);
    }

    public String getCursor() {
        return cursor;
    }
    public void setCursor(String cursor) {
        this.cursor = cursor;
    }


}
