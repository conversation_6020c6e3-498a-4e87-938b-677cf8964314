package com.wosai.pantheon.uf4c.model;

import com.wosai.market.trade.modal.HuabeiFenqiRequest;
import com.wosai.pantheon.order.model.dto.BookOrderInfoDTO;
import com.wosai.pantheon.order.model.dto.OrderDeliveryDTO;
import com.wosai.pantheon.order.model.dto.v2.OrderCampusDeliveryDTO;
import com.wosai.pantheon.order.model.dto.v2.OrderCampusStationDTO;
import com.wosai.pantheon.order.pojo.OrderExtraMemberInfo;
import com.wosai.pantheon.order.pojo.OrderRefundApplyDTO;
import com.wosai.pantheon.uf4c.model.dto.WxGoodsSubsidy;
import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.oms.api.domain.MembershipPayInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Order {

    private String id;

    private String sn;

    private String type;

    private String transSn;

    private String sqbTransSn;

    private String orderSeq;

    private Long status;

    private String subject;

    private List<CartItemCreate> items;

    private Long originalAmount;


    private Long effectiveAmount;

    private Long profitSharingAmount;

    private Long receiveAmount;

    /**
     * 未上报的优惠金额
     */
    private Long notReportedDiscountAmount;

    /**
     * 用户实付金额
     */
    private Long buyerPayAmount;

    private Long totalDiscount;

    private Long refundAmount;

    private Long packAmount;

    private Long discountAmount;

    private Long merchantDiscount;

    private String merchantDiscountName;

    private String merchantActivitySn;

    private String merchantActivityType;

    private String buyerUid;

    private String buyerLogin;

    private String merchantId;

    private String storeId;

    private String storeName;

    private String terminalId;

    private String qrCode;

    private String qrCodeName;

    private Integer qrCodeType;

    private Integer payway;

    private Integer subPayway;

    private String clientSn;

    private String tradeNo;

    private Boolean packed;

    private String remark;

    private OrderExtra extra;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

    private boolean compatible;

    private String processStatus;

    private boolean cashierMode;

    private String tableNo;

    private String tableId;

    private String areaId;

    private Map<Object, Object> extraInfo;

    private String terminalSn;

    private Long orderTag;


    private boolean campusOrder;

    private OrderCampusDeliveryDTO orderCampusDelivery;
    private OrderCampusStationDTO orderCampusStation;
    private String stationAddress;
    private String campusException;

    private Date payTime;
    private boolean bookOrder;
    private Long bookTime;


    private boolean refreshOnMini = false;


    private boolean cancelEnabled = false;
    private boolean refundApplyEnabled = false;
    private boolean refundRevokeEnabled = false;

    private List<RedeemResult.RedeemDetail> redeemDetails;


    private OrderRefundApplyDTO refundApply;

    private List<WxGoodsSubsidy> wxGoods;

    /**
     * 会员余额信息，含支付信息
     */
    private List<MembershipPayInfoVO> membershipList;

    /**
     * 支付渠道信息
     */
    private List<PayChannelInfo> payChannelList;


    /**
     * c端收银台传入的参数，原样进行透传
     */
    private Map<String, Object> cashierBizParams;

    /**
     * 商品的批次信息
     */
    private List<GoodsBatchInfo> goodsBatchInfos;

    /**
     * 控制小程序的支付（暂时只有先付围餐使用）
     */
    private PayControll payControll;

    /**
     * 是否允许储值卡支付
     */
    @Builder.Default
    private boolean allowCardPay = true;

    /**
     * 发票展示相关信息
     */
    private InvoiceVo invoice;

    /**
     * 订单上楼信息
     */
    private String deliveryFloor;

    /**
     * 订单状态追踪对象。
     * 目前仅外卖订单使用
     */
    private List<OrderTrackStatusDTO> orderTrackStatus;


    /**
     * 订单属性标记
     */
    private String orderFlags;

    /**
     * 最后一次退款信息
     */
    private String lastRefundMessage;

    /**
     * 下单附加信息，不写入订单表，用于记录一些下单过程中使用的信息
     */
    private Map<String, Object> additionalInfo;

    /**
     * 后付围餐结账入口展示状态
     */
    @Builder.Default
    private boolean canCheckout = true;

    /**
     * 后付围餐是否展示活动信息
     */
    private boolean showActivity = false;

    /**
     * 结账单是否打印发票
     */
    private boolean checkInvoice;

    /**
     * 结账单发票链接
     */
    private String billWithInvoiceUrl;

    /**
     * 根据商家打印机配置进行打印-无需打印的模板类型
     */
    private List<String> excludeTemplateTypes;

    // 餐牌号
    private String diningCode;


    @Data
    public static class OrderExtra {
        private String cellphone;

        private String storeName;

        private HuabeiFenqiRequest hbFq;

        private DeliverInfo deliveryInfo;

        private OrderDeliveryDTO dmInfo;

        private OrderCampusStationDTO campusStation;

        private OrderCampusDeliveryDTO campusDelivery;

        private BookOrderInfoDTO bookOrderInfoDTO;

        private String preReduceNo;

        /**
         * 微信或者支付宝小程序的场景值
         */
        @ApiModelProperty("微信或者支付宝小程序的场景值")
        private String mpScene;

        /**
         * 门店快送订单类型的一个标识
         */
        @ApiModelProperty("微信门店快送业务特有的 traceId 参数")
        private String wxTraceId;

        // 商户行业
        private String merchantIndustryType;

        /**
         * 拼团ID
         */
        private String groupBuyingId;

        private OrderExtraMemberInfo membershipInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class GoodsBatchInfo implements Serializable {
        /**
         * 下单时间，也就是批次号
         */
        private Long orderTime;
        /**
         * 批次的状态描述， 前端直接使用
         */
        private String statusDescription;

        /**
         * 下单人的icon
         */
        private String icon;

        /**
         * 下单人的name
         */
        private String name;

        /**
         * 是否显示支付按钮
         */
        private boolean allowPay;

        /**
         * 是否展示取消按钮
         */
        private boolean allowCancel;

        /**
         * 批次备注信息
         */
        private String remark;

        /**
         * 这个桌台已经下单的订单号
         */
        private String sn;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PayControll {

        /**
         * 是否允许小程序下单支付
         */
        boolean allowPay = false;
    }

    /**
     * 客户端埋点透传参数
     */
    private Map<Object, Object> clientTrackingData;

}
