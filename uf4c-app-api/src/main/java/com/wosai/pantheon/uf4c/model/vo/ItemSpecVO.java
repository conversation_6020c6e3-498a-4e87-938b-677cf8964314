package com.wosai.pantheon.uf4c.model.vo;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * created by shij on 2019/7/22
 */
@Data
public class ItemSpecVO {

    /**
     * 规格标题
     */
    @NotBlank
    @Size(max = 20)
    private String title;

    /**
     * 规格选项
     */
    @Valid
    private List<SpecOptionVO> options;

}
