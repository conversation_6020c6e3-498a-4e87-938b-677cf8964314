package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wosai.pantheon.order.model.dto.OrderAddressDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/4/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeliverInfo extends OrderAddressDTO {

    private String deliveryType;

    private Integer deliveryFee;

    // 校园外卖商家承担配送费
    private Integer deliveryFeeAffordAmount;

    // 上楼费
    private Integer floorFee;

    // 大额附加费
    private Integer priceExtraFee;

    private String deliveryNo;

    // 减免金额
    private Integer reductionAmount;

    // 是否转自配送
    private Boolean isConvert;

    private Double distance;

    // 取单时间
    private Long presetTime;

    /**
     * 0-校外 1-校内
     */
    private Integer type = 0;
    /**
     * 校园id
     */
    private Integer campusId;
    /**
     * 楼号
     */
    private String buildingNumber;
    /**
     * 校园外卖配送站ID
     */
    private String stationId;
    /**
     * 校内配送方式
     */
    private Integer campusDeliveryType;
    /**
     * 校外配送方式
     */
    private Integer campusOutsideDeliveryType;

    /**
     * 校园地址code
     */
    private String addressCode;

    /**
     * 校园地址name
     */
    private String addressName;
    /**
     * 可送区域ID
     */
    private Integer areaId;

    /**
     * 指定配送地址
     */
    private Integer addressId;

    /**
     * 版本号，主要用来区分新旧小程序版本
     */
    private Long appVersion;

    /**
     * 用户选择的楼层，空表示没有选择。0表示选择到楼下，非0表示具体楼层
     */
    private String floor;

    /**
     * 聚合配送错误code
     */
    private String deliveryErrorCode;
    /**
     * 聚合配送错误描述
     */
    private String deliveryErrorMsg;

    /**
     * 配送费详情map对象
     */
    private Map<String, Integer> deliveryFeeMap;

    /**
     * 校园配送预下单id
     */
    private String preCreateDeliveryId;


}
