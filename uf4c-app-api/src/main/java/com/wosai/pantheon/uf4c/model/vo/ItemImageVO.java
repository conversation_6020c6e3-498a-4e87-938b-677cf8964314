package com.wosai.pantheon.uf4c.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品图片
 */
@Data
public class ItemImageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图片地址
     */
    private String displayUrl;

    /**
     * 图片规格
     */
    private List<Spec> specs;

    /**
     * 不同图片裁剪的规格尺寸&结果
     */
    @Data
    public static class Spec implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 类型
         * 0=原图
         * 1=1:1
         * 2=4:3
         * 3=16:9
         */
        private Integer type;

        /**
         * 裁剪后的图片地址
         */
        private String url;

    }

}
