package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wosai.pantheon.order.enums.GoodsProcessStatus;
import com.wosai.pantheon.uf4c.model.validation.NotAllNull;
import com.wosai.smartbiz.oms.api.enums.OrderMealTypeEnum;
import com.wosai.validation.constraints.UUID;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 从CartItemCreate拷贝而来，有一些冗余字段
 */
@Data
@NotAllNull(fieldNames = {"itemUid", "item"})
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderItem {
    /**
     * 这个是前端传参问题，有时候前端传上来的是id, 他等效于itemUid
     */
    private String id;

    @Size(max = 32)
    private String itemUid;

    @NotBlank
    @Size(max = 36)
    private String storeId;

    /**
     * 桌台id
     */
    private String tableId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户名头像
     */
    private String userIcon;

    @Valid
    private CartItemCreate.Item item;

    @Valid
    private CartItemCreate.Spec spec;

    @Valid
    private List<CartItemCreate.Attribute> attributes;

    @Valid
    private List<CartItemCreate.Material> materials;

    private Long ctime;

    private String categoryId;

    private Integer serviceType = 0;

    /**
     * 商品数量
     */
    private Integer number = 1;


    /**
     * 下单时间
     */
    private Long orderTime;

    /**
     * 菜品处理状态
     */
    private GoodsProcessStatus processStatus;
    /**
     * 额外信息
     */
    private Map<Object, Object> extraInfo;

    /**
     * 商品所属的分组id
     */
    private String packageGroupId;

    /**
     * 商品所属的分组名称
     */
    private String packageGroupName;
    /**
     * 套餐被选中的商品列表
     */
    private List<CartItemCreate> packageItems;


    private Integer quotaCount;

    private Boolean must;

    /**
     * 是否是开台必点商品
     */
    private boolean openTableMustOrder = false;

    /**
     * 开台必点商品是否可以编辑
     */
    private boolean openTableItemEditable = false;

    /**
     * 就餐人数,仅针对开台必点商品
     */
    private Integer peopleNum;

    private Long packFee;

    /**
     * 推荐加料
     */
    private List<CartItemCreate.RecommendMaterial> recommendMaterials;

    //以下参数用来计算优惠
    private String orderSn;

    private Integer payway;

    private Integer subPayway;

    private String discountStrategy;

    private String terminalSn;
    // 兼容性参数，当传入的值为true时，表示为新版本
    private Boolean compatible;

    private OrderMealTypeEnum mealType = OrderMealTypeEnum.SINGLE;

    /**
     * 是否是赠菜
     */
    private boolean giftFood = false;

    /**
     * 订单详情使用，返回商品享受的优惠
     */
    private String activityName;

    @Data
    public static class Item {
        @NotNull
        private String id;

        private String categoryId;

        @NotBlank
        private String name;

        private String attachedInfo;

        private String attachedInfoWithoutMaterials;

        private Integer status = 0;

        private Boolean must;

        @NotNull
        @Min(0)
        private Integer price;

        private Integer discountPrice;

        private Integer number = 1;

        /**
         * 退菜数量
         */
        private Integer returnNumber;

        private Integer discountNumber = 0;

        private BigDecimal weight;

        private String url;

        private String photoUrl;

        private String unit;

        private Integer isMultiple;

        private Integer sku;

        private Boolean outOfStock;

        /**
         * 计价单位类型 0-按件 1-按称重
         */
        private Integer unitType;

        /**
         * 商品类型
         */
        private String spuType;

        /**
         * 商品特别的tag
         */
        private Long itemTag;

        /**
         * 最小售卖数量
         */
        private Integer minSaleNum;

        /**
         * 类目排序值
         */
        private Integer categorySort;

        /**
         * 商品排序值
         */
        private Integer displayOrder;

        /**
         * 订单详情使用， 该商品总价
         */
        private Long totalAmount;

    }

    @Data
    public static class Spec {
        @NotNull
        private String id;

        @NotBlank
        private String name;

        @NotNull
        @Min(0)
        private Integer price;
    }

    @Data
    public static class Attribute {
        @NotNull
        private String id;

        @NotBlank
        private String name;
    }

    @Data
    public static class Material {
        @NotNull
        private String id;

        @NotBlank
        private String name;

        @NotNull
        @Min(0)
        private Long price;

        private Integer number;

        /**
         * 加购加料的来源
         * {@linkplain com.wosai.pantheon.order.enums.MaterialAddSourceEnum#getCode()}
         */
        private Integer source;

    }

    @Data
    public static class RecommendMaterial extends CartItemCreate.Material {
        /**
         * 是否被选中，推荐加料中使用
         */
        private boolean selected = false;
    }

}
