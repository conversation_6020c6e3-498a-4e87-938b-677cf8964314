package com.wosai.pantheon.uf4c.model;

import com.wosai.smartbiz.base.pojo.Result;
import com.wosai.smartbiz.oms.api.enums.DealTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * created by shij on 2019/7/26
 */
@Data
public class Cart {

    /**
     * 这个桌台已经下单的订单号
     */
    private String sn;
    /**
     * 就餐人数
     */
    private Integer peopleNum;
    /**
     * 版本号
     */
    private Long version;

    private Integer total = 0;

    private Long totalPrice = 0L;

    private List<Record> records;

    private Record lastDealRecord;


    private Map<String, Integer> spuCountMap;

    private String tableOpenId;

    private Result.StatusInfo checkResult;

    @Data
    public static class Record {
        private String id;

        private String itemId;

        private String specId;

        private String name;

        private String attachedInfo;

        private Integer status = 0;

        private Long price;

        private Integer num;

        private Long ctime;

        private Long mtime;

        private String categoryId;

        private String url;

        private Integer sku;

        private Boolean outOfStock;

        private List<String> userIcons;

        private DealTypeEnum lastDealType;

        private Integer lastAddNum;

        private String userId;

        private String userName;

        private String userIcon;

        private boolean lastAdd;

        private boolean openTableMustOrder;

        private boolean openTableMustOrderEditable;

        private String spuType;

        private Integer minSaleNum;

        private Integer displayOrder;

        private Integer categorySort;

        private List<Material> materials;

        /**
         * 是否微信加价购活动
         */
        private boolean isBrandAct = false;

        private Long brandAcdProductId;

        /**
         * 推荐加料列表
         */
        private List<Material> recommendMaterials;

        private List<Attribute> attributes;


        private Long clientVersion;
    }

    @Data
    public static class Material {

        private String id;

        private String name;

        private long price;

        private int number;

        /**
         * 加购加料的来源
         * {@linkplain com.wosai.pantheon.order.enums.MaterialAddSourceEnum#getCode()}
         */
        private Integer source;

        private boolean selected;
    }

    public enum CartRecordStatus {
        NORMAL(0, "正常"),
        OUT_OF_STOCK(1, "售罄"),
        NOT_FOR_SALE(2, "已下架");

        private Integer code;
        private String desc;

        CartRecordStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }


    @Data
    public static class Attribute {
        private String id;

        private String title;

        private String name;

        private Integer seq;
    }
}
