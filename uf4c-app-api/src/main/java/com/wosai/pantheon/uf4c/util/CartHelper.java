package com.wosai.pantheon.uf4c.util;

import com.google.common.collect.Lists;
import com.wosai.market.enums.ProductTypeEnum;
import com.wosai.pantheon.order.enums.*;
import com.wosai.pantheon.order.model.dto.request.Attribute;
import com.wosai.pantheon.order.model.dto.request.Material;
import com.wosai.pantheon.order.utils.OrderUtil;
import com.wosai.pantheon.uf4c.constant.Constants;
import com.wosai.pantheon.uf4c.model.Cart;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.smartbiz.base.exceptions.BusinessException;
import com.wosai.smartbiz.base.exceptions.ParamException;
import com.wosai.smartbiz.base.utils.MD5Util;
import com.wosai.smartbiz.base.utils.MoneyUtil;
import com.wosai.smartbiz.base.utils.TagUtil;
import com.wosai.smartbiz.oms.api.enums.DealTypeEnum;
import com.wosai.smartbiz.oms.api.pojo.CartInfoDTO;
import com.wosai.smartbiz.oms.api.pojo.PreReduceGoodsDTO;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartGoodsDTO;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/11/15
 */
@Slf4j
public class CartHelper {


    public static Cart convertCart(CartInfoDTO cartInfoDTO) {
        Cart cart = new Cart();
        cart.setSn(cartInfoDTO.getSn());
        cart.setPeopleNum(cartInfoDTO.getPeopleNum());
        cart.setVersion(cartInfoDTO.getVersion());
        cart.setTableOpenId(cartInfoDTO.getTableOpenId());
        BigDecimal total = BigDecimal.ZERO;
        Long totalPrice = 0L;
        if (cartInfoDTO.getShoppingCartItems() != null) {
            Map<String, Integer> spuCountMap = new HashMap<>();
            List<Cart.Record> records = new ArrayList<>();

            Cart.Record lastRecord = null;
            for (ShoppingCartItemDTO item : cartInfoDTO.getShoppingCartItems()) {

                ShoppingCartGoodsDTO goods = item.getShoppingCartGoods();
                Long price = MoneyUtil.getNotNullAmount(goods.getOriginSalePrice());
                if (goods.getSaleCount().compareTo(BigDecimal.ZERO) > 0) {
                    total = total.add(goods.getSaleCount());

                    if (goods.getNowDiscountPrice() != null && goods.getNowDiscountPrice() > 0) {
                        price = goods.getNowDiscountPrice();
                    }


                    totalPrice += goods.getSaleCount().multiply(new BigDecimal(price)).setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
                }
                Cart.Record record = new Cart.Record();
                record.setUrl(goods.getMainImageUrl());
                record.setAttachedInfo(goods.getAttachedInfo());
                record.setCategoryId(goods.getCategoryId());
                record.setId(goods.getItemUid());
                record.setItemId(goods.getSpuId());
                record.setSpecId(goods.getSkuId());
                record.setName(goods.getSpuTitle());
                record.setCtime(item.getCtime());
                record.setMtime(item.getMtime());
                record.setPrice(price);
                record.setNum(goods.getSaleCount().intValue());
                record.setUserName(item.getUserName());
                record.setUserIcon(item.getUserIcon());
                record.setLastDealType(item.getLastDealType());
                record.setUserId(item.getUserId());
                record.setLastAdd(false);
                record.setSpuType(Optional.of(goods.getSpuType()).map(t -> t.name()).orElse(ProductTypeEnum.NORMAL.name()));
                record.setSku(goods.getStock());
                record.setOutOfStock(goods.isOutOfStock());
                record.setOpenTableMustOrder(goods.isOpenTableMustOrder());
                record.setOpenTableMustOrderEditable(goods.isOpenTableItemEditable());

                record.setMinSaleNum(goods.getMinSaleNum());
                record.setDisplayOrder(goods.getSort());
                record.setCategorySort(goods.getCategorySort());
                record.setMaterials(CartHelper.toRoundMealCartMaterials(goods.getMaterials()));
                record.setRecommendMaterials(CartHelper.toRoundCartRecommendMaterials(goods.getRecommendMaterials()));

                if (item.getLastDealNum() != null) {
                    record.setLastAddNum(item.getLastDealNum().intValue());
                }

                Set<String> userIcons = item.getUserIcons();
                if (!org.springframework.util.CollectionUtils.isEmpty(userIcons)) {
                    record.setUserIcons(userIcons.stream().collect(Collectors.toList()));
                }

                if (lastRecord == null || record.getMtime() > lastRecord.getMtime()) {
                    lastRecord = record;
                }

                try {
                    CartHelper.addSpuCount(spuCountMap, goods.getSpuId(), goods.getSaleCount().intValue());
                    if (CollectionUtil.isNotEmpty(goods.getPackageGoods())) {
                        goods.getPackageGoods().stream().forEach(packageGoods -> {
                            CartHelper.addSpuCount(spuCountMap, packageGoods.getSpuId(), packageGoods.getSaleCount().intValue());
                        });
                    }
                } catch (Exception ex) {
                    log.warn("count cart spu count error", ex);
                }
                records.add(record);
            }
            cart.setSpuCountMap(spuCountMap);

            if (lastRecord != null) {
                if (lastRecord.getLastDealType() == DealTypeEnum.ADD) {
                    lastRecord.setLastAdd(true);
                }
                cart.setLastDealRecord(lastRecord);

            }
            if (cartInfoDTO.getLastDealType() != null && StringUtils.equals(cartInfoDTO.getLastDealType().getLastDealType(), DealTypeEnum.CLEAN.name())) {

                Cart.Record lastCleanRecord = new Cart.Record();
                lastCleanRecord.setLastDealType(DealTypeEnum.CLEAN);
                lastCleanRecord.setMtime(cartInfoDTO.getLastDealType().getCtime());
                lastCleanRecord.setUserIcon(cartInfoDTO.getLastDealType().getUserIcon());
                lastCleanRecord.setUserName(cartInfoDTO.getLastDealType().getUserName());
                lastCleanRecord.setUserId(cartInfoDTO.getLastDealType().getUserId());
                cart.setLastDealRecord(lastCleanRecord);
            }

            CartHelper.limitRecommendMaterial(records);
            cart.setRecords(records);
        }
        cart.setTotal(total.intValue());
        cart.setTotalPrice(totalPrice);

        return cart;
    }

    public static ShoppingCartGoodsDTO convert2RoundGoods(CartItemCreate itemCreate) {
        return convert2RoundGoods(itemCreate, false);
    }

    public static ShoppingCartGoodsDTO convert2RoundGoods(CartItemCreate itemCreate, boolean isPackageSubGoods) {
        ShoppingCartGoodsDTO goods = new ShoppingCartGoodsDTO();


        CartItemCreate.Item item = itemCreate.getItem();
        if (item != null) {
            goods.setLocalGoodsId(itemCreate.getItemUid());

            goods.setCategoryId(item.getCategoryId());
            goods.setSaleCount(new BigDecimal(item.getNumber()));
            goods.setSpuId(item.getId());
            goods.setSpuTitle(item.getName());
            goods.setSpuType(OrderHelper.getNotNullSpuType(item.getSpuType()));
            goods.setMainImageUrl(item.getPhotoUrl());
            goods.setSaleUnit(item.getUnit());
            goods.setSort(item.getDisplayOrder());
            goods.setCategorySort(item.getCategorySort());

            goods.setMinSaleNum(item.getMinSaleNum());

            goods.setExtraInfo(itemCreate.getExtraInfo());

            goods.setStock(item.getSku());
            goods.setOutOfStock(Optional.ofNullable(item.getOutOfStock()).orElse(false));

            if (itemCreate.getCtime() == null) {
                goods.setCtime(System.currentTimeMillis());
            } else {
                goods.setCtime(itemCreate.getCtime());
            }

            Arrays.asList(OrderGoodsTagEnum.HOT_SALE, OrderGoodsTagEnum.RECOMMEND, OrderGoodsTagEnum.BOUGHT, OrderGoodsTagEnum.DISCOUNT).forEach(p -> {
                if (TagUtil.hasTag(item.getItemTag(), p.getValue())) {
                    goods.setGoodsTag(OrderUtil.addTag(goods.getGoodsTag(), p.getValue()));
                }
            });

            goods.setAttachedInfo(EntityConvert.generateAttachInfo(itemCreate));

            CartItemCreate.Spec spec = itemCreate.getSpec();
            if (spec != null) {
                goods.setSkuId(spec.getId());
                goods.setSkuTitle(spec.getName());
            }

            if (Objects.nonNull(item.getPrice())) {
                goods.setOriginSalePrice(item.getPrice().longValue());
            }


            Integer specCount = item.getIsMultiple();
            if (specCount != null && specCount > 1) {
                goods.setSkuType(SkuType.MULTI);
            } else {
                goods.setSkuType(SkuType.SINGLE);
            }

            if (item.getUnitType() != null) {
                goods.setUnitType(GoodsUnitTypeEnum.NUMBER);
            } else {
                goods.setUnitType(Objects.nonNull(item.getUnitType()) && 1 == item.getUnitType() ? GoodsUnitTypeEnum.WEIGHT : GoodsUnitTypeEnum.NUMBER);
            }

            if (!isPackageSubGoods && Objects.equals(item.getSpuType(), SpuType.PACKAGE.name())) {
                goods.setPackageGoods(Optional.ofNullable(itemCreate.getPackageItems())
                        .map(packageGoods -> packageGoods.stream().map(i -> convert2RoundGoods(i, true)).collect(Collectors.toList()))
                        .orElse(null));
            }
        }

        goods.setOpenTableMustOrder(itemCreate.isOpenTableMustOrder());
        goods.setOpenTableItemEditable(itemCreate.isOpenTableItemEditable());
        goods.setPackageGroupId(itemCreate.getPackageGroupId());
        goods.setPackageGroupName(itemCreate.getPackageGroupName());

        goods.setRecipes(
                Optional.ofNullable(itemCreate.getAttributes())
                        .map(attributes -> attributes.stream()
                                .map(it -> {
                                    com.wosai.pantheon.order.model.dto.request.Attribute attribute = new com.wosai.pantheon.order.model.dto.request.Attribute();
                                    attribute.setId(it.getId());
                                    attribute.setTitle(it.getTitle());
                                    attribute.setName(it.getName());
                                    attribute.setSeq(it.getSeq());
                                    return attribute;
                                })
                                .collect(Collectors.toList())
                        )
                        .orElse(null));

        goods.setMaterials(
                Optional.ofNullable(itemCreate.getMaterials())
                        .map(materials -> materials.stream().filter(m -> m.getNumber() != null)
                                .map(m -> OrderHelper.convertMaterial(m))
                                .collect(Collectors.toList()))
                        .orElse(null));

        goods.setRecommendMaterials(Optional.ofNullable(itemCreate.getRecommendMaterials())
                .orElseGet(Lists::newArrayList)
                .stream()
                .map(OrderHelper::toRecommendMaterial)
                .collect(Collectors.toList()));


        if (org.apache.commons.lang3.StringUtils.isNotBlank(itemCreate.getItemUid())) {
            goods.setItemUid(itemCreate.getItemUid());

            goods.setSaleCount(new BigDecimal(itemCreate.getNumber()));

        }

        return goods;
    }

    public static PreReduceGoodsDTO convert2PreReduceGoods(CartItemCreate itemCreate, boolean isRoundMeal) {
        return convert2PreReduceGoods(itemCreate, isRoundMeal, false);
    }

    public static PreReduceGoodsDTO convert2PreReduceGoods(CartItemCreate itemCreate, boolean isRoundMeal, boolean isPackageSubGoods) {
        PreReduceGoodsDTO goods = new PreReduceGoodsDTO();
        CartItemCreate.Item item = itemCreate.getItem();
        if (item != null) {
            goods.setSaleCount(new BigDecimal(item.getNumber()));
            goods.setSpuId(item.getId());
            goods.setSpuTitle(item.getName());
            goods.setSpuType(OrderHelper.getNotNullSpuType(item.getSpuType()));
            goods.setSaleUnit(item.getUnit());
            goods.setAttachedInfo(EntityConvert.generateAttachInfo(itemCreate));
            goods.setStockId(item.getStockId());
            // 标记商品来源
            if (Objects.equals(itemCreate.getBrandProductSource(), Constants.BRAND_PRODUCT_SOURCE_BRAND)) {
                goods.setDataSourceType(Constants.GOODS_DATA_SOURCE_TYPE_BRAND);
            }

            CartItemCreate.Spec spec = itemCreate.getSpec();
            if (spec != null) {
                goods.setSkuId(spec.getId());
                goods.setSkuTitle(spec.getName());
            }

            if (Objects.nonNull(item.getPrice())) {
                goods.setOriginSalePrice(item.getPrice().longValue());
            }

            Integer specCount = item.getIsMultiple();
            if (specCount != null && specCount > 1) {
                goods.setSkuType(SkuType.MULTI);
            } else {
                goods.setSkuType(SkuType.SINGLE);
            }

            goods.setUnitType(GoodsUnitTypeEnum.NUMBER);

            if (!isPackageSubGoods && Objects.equals(item.getSpuType(), SpuType.PACKAGE.name())) {
                goods.setPackageGoods(Optional.ofNullable(itemCreate.getPackageItems())
                        .map(packageGoods -> packageGoods.stream().map(i -> convert2PreReduceGoods(i, isRoundMeal, true)).collect(Collectors.toList()))
                        .orElse(null));
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(itemCreate.getItemUid())) {
            if (isRoundMeal) {
                goods.setSaleCount(new BigDecimal(itemCreate.getNumber()));
            }
            goods.setItemUid(itemCreate.getItemUid());
        }
        if(itemCreate.getSkuConvert() != null){
            if(MapUtils.getDoubleValue(itemCreate.getSkuConvert(), "per_weight") == 0){
                throw new BusinessException(goods.getSpuTitle() + "商品下单失败，请联系商家处理");
            }
            goods.setSkuConvert(itemCreate.getSkuConvert());
        }

        return goods;
    }

    /**
     * 限制展示推荐加料的购物车记录
     *
     * @param records
     */
    public static void limitRecommendMaterial(List<Cart.Record> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 推荐加料仅展示前2条
        int recommendMaterialLimit = 2;
        for (Cart.Record record : records) {
            if (Objects.isNull(record) || CollectionUtil.isEmpty(record.getRecommendMaterials())
                    || Optional.ofNullable(record.getNum()).orElse(0) <= 0) {
                continue;
            }
            if (recommendMaterialLimit <= 0) {
                record.setRecommendMaterials(Lists.newArrayList());
            } else {
                recommendMaterialLimit--;
            }
        }
    }

    /**
     * 生成购物车商品唯一ID
     *
     * @param create
     * @return
     */
    public static String generateItemUid(CartItemCreate create) {
        return CartHelper.generateItemUid(create, false);
    }

    /**
     * 生成购物车商品唯一ID
     *
     * @param create
     * @param isPackageSubGoods
     * @return
     */
    public static String generateItemUid(CartItemCreate create, boolean isPackageSubGoods) {
        CartItemCreate.Item item = create.getItem();
        CartItemCreate.Spec spec = create.getSpec();
        List<CartItemCreate.Attribute> attributes = create.getAttributes();
        List<CartItemCreate.Material> materials = create.getMaterials();
        List<CartItemCreate.RecommendMaterial> rms = create.getRecommendMaterials();

        StringBuilder sb = new StringBuilder(item.getId());

        if (!isPackageSubGoods && Objects.equals(item.getSpuType(), SpuType.PACKAGE.name())) {

            if (CollectionUtil.isEmpty(create.getPackageItems())) {
                throw new ParamException("未选择套餐内商品，请查证后再试");
            }

            List<String> packageItemUidList = create.getPackageItems().stream()
                    .map(i -> generateItemUid(i, true))
                    .collect(Collectors.toList());
            packageItemUidList.stream().forEach(uid -> sb.append(uid));

        }

        if (spec != null) {
            sb.append(spec.getId());
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(attributes)) {
//            try {
//                attributes.sort(Comparator.comparing(CartItemCreate.Attribute::getName));
//            } catch (Exception ignored) {
//            }

            for (CartItemCreate.Attribute attribute : attributes) {
                sb.append(attribute.getId());
            }
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(materials)) {
//            try {
//                materials.sort(Comparator.comparing(CartItemCreate.Material::getName));
//            } catch (Exception ignored) {
//            }

            for (CartItemCreate.Material material : materials) {
                sb.append(material.getId());
                sb.append(material.getNumber());
            }
        }

        sb.append(create.isOpenTableMustOrder());

        if (CollectionUtils.isNotEmpty(rms)) {
            for (CartItemCreate.RecommendMaterial rm : rms) {
                sb.append(rm.getId()).append(rm.getNumber()).append(rm.isSelected());
            }
            sb.append(UUID.randomUUID());
        }

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes());
    }

    public static List<Cart.Material> toRoundCartRecommendMaterials(List<ShoppingCartGoodsDTO.RecommendMaterial> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(p.getNumber());
                    tmp.setSource(p.getSource());
                    tmp.setSelected(p.isSelected());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public static List<Cart.Material> toSingleCartRecommendMaterials(List<CartItemCreate.RecommendMaterial> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(Optional.ofNullable(p.getNumber()).orElse(0));
                    tmp.setSource(p.getSource());
                    tmp.setSelected(p.isSelected());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public static List<Cart.Material> toRoundMealCartMaterials(List<com.wosai.pantheon.order.model.dto.request.Material> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(p.getNumber());
                    tmp.setSource(p.getSource());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public static List<Cart.Material> toSingleMealCartMaterials(List<CartItemCreate.Material> origins) {
        if (CollectionUtils.isEmpty(origins)) {
            return Lists.newArrayList();
        }
        return origins.stream()
                .filter(p -> p.getNumber() != null)
                .map(p -> {
                    Cart.Material tmp = new Cart.Material();
                    tmp.setId(p.getId());
                    tmp.setName(p.getName());
                    tmp.setPrice(p.getPrice());
                    tmp.setNumber(p.getNumber());
                    tmp.setSource(p.getSource());
                    return tmp;
                }).collect(Collectors.toList());
    }

    public static Integer calculateMaterialPrice(List<CartItemCreate.Material> materials) {
        if (CollectionUtils.isEmpty(materials)) {
            return 0;
        }
        return materials.stream()
                .filter(p -> p.getNumber() != null)
                .map(m -> m.getPrice().intValue() * m.getNumber())
                .reduce(0, Integer::sum);
    }

    public static List<CartItemCreate> changeAfterAddOrRemoveMaterial(CartItemCreate existRecord, CartItemCreate changeRecord) {
        if (Objects.isNull(existRecord)) {
            return null;
        }
        CartItemCreate newRecord = new CartItemCreate();
        BeanUtils.copyProperties(existRecord, newRecord);

        // 删除的记录
        CartItemCreate deleteRecord = new CartItemCreate();
        deleteRecord.setTableId(existRecord.getTableId());
        deleteRecord.setItemUid(existRecord.getItemUid());

        int existRecordReduceNumber = existRecord.getItem().getNumber();
        deleteRecord.setNumber(-existRecordReduceNumber);
        CartItemCreate.Item deleteItem = new CartItemCreate.Item();
        deleteItem.setNumber(-existRecordReduceNumber);
        deleteRecord.setItem(deleteItem);

        // 新增的记录
        newRecord.getItem().setNumber(existRecordReduceNumber);
        newRecord.setNumber(existRecordReduceNumber);
        newRecord = CartHelper.addOrRemoveMaterial(newRecord, changeRecord);

        return Lists.newArrayList(deleteRecord, newRecord);
    }

    /**
     * 购物车单个商品加减加料
     *
     * @param existRecord
     * @param changeRecord
     * @return
     */
    public static CartItemCreate addOrRemoveMaterial(CartItemCreate existRecord, CartItemCreate changeRecord) {
        if (Objects.isNull(existRecord)) {
            return null;
        }
        List<CartItemCreate.Material> changeMaterials = Optional.ofNullable(changeRecord)
                .map(CartItemCreate::getMaterials)
                .orElseGet(Lists::newArrayList)
                .stream()
                .filter(p -> Objects.nonNull(p.getNumber()) && !Objects.equals(p.getNumber(), 0))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(changeMaterials)) {
            return existRecord;
        }

        Map<String, CartItemCreate.Material> changeMidToMatMap = changeMaterials.stream()
                .collect(Collectors.toMap(CartItemCreate.Material::getId, Function.identity(), (k1, k2) -> k1));

        List<CartItemCreate.Material> existMaterials = ListUtils.defaultIfNull(existRecord.getMaterials(), Lists.newArrayList());
        int priceWithoutMaterial = existRecord.getItem().getPrice() - CartHelper.calculateMaterialPrice(existMaterials);

        for (Iterator<CartItemCreate.Material> iterator = existMaterials.iterator(); iterator.hasNext(); ) {
            CartItemCreate.Material existMaterial = iterator.next();
            CartItemCreate.Material changeMaterial = MapUtils.getObject(changeMidToMatMap, existMaterial.getId(), null);
            if (Objects.nonNull(changeMaterial)) {
                int finalNumber = existMaterial.getNumber() + changeMaterial.getNumber();
                if (finalNumber <= 0) {
                    iterator.remove();
                } else {
                    // 推荐加料限制只加一份
                    if (Objects.equals(existMaterial.getSource(), MaterialAddSourceEnum.ORDER_SUBMIT_RECOMMEND.getCode())) {
                        existMaterial.setNumber(1);
                    } else {
                        existMaterial.setNumber(finalNumber);
                    }
                }
                // 移除，这样剩下的就是新增的
                changeMaterials.remove(changeMaterial);
            }
        }
        // 过滤
        changeMaterials = changeMaterials.stream().filter(p -> p.getNumber() >= 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(changeMaterials)) {
            // 目前限制加一份，此处防止小程序传入大于1的数字
            changeMaterials.forEach(p -> p.setNumber(1));
            existMaterials.addAll(changeMaterials);
        }
        existRecord.setMaterials(existMaterials);

        if (CollectionUtils.isNotEmpty(existRecord.getRecommendMaterials())) {
            existRecord.getRecommendMaterials().forEach(p -> {
                CartItemCreate.Material selectedMaterial = existMaterials.stream()
                        .filter(m -> StringUtils.equals(m.getId(), p.getId()) && Objects.equals(m.getSource(), 3))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(selectedMaterial)) {
                    p.setNumber(selectedMaterial.getNumber());
                    p.setSelected(true);
                } else {
                    p.setNumber(0);
                    p.setSelected(false);
                }
            });
        }

        // 重新生成文本和价格
        String newItemUid = CartHelper.generateItemUid(existRecord);
        existRecord.setId(newItemUid);
        existRecord.setItemUid(newItemUid);
        EntityConvert.generateAttachInfo(existRecord);
        existRecord.getItem().setPrice(priceWithoutMaterial + CartHelper.calculateMaterialPrice(existMaterials));

        return existRecord;
    }


    public static void addSpuCount(Map<String, Integer> countMap, String spuId, Integer count) {
        Integer curCount = MapUtils.getInteger(countMap, spuId, 0);
        countMap.put(spuId, count + curCount);
    }

    /**
     * 合并相同商品
     * -对套餐内，同分组同商品同规格同属性的子商品合并
     *
     * @param itemCreateList 合并前商品信息列表
     * @return 合并后的商品信息列表
     */
    public static List<CartItemCreate> mergeSameGoodsCartItem(List<CartItemCreate> itemCreateList) {
        if (CollectionUtil.isEmpty(itemCreateList)) {
            return Collections.EMPTY_LIST;
        }
        HashMap<String, CartItemCreate> assortMap = new LinkedHashMap<>();
        for (CartItemCreate cartItemCreate : itemCreateList) {

            List<String> idList = new ArrayList<>();
            CartItemCreate.Item item = cartItemCreate.getItem();
            CartItemCreate.Spec spec = cartItemCreate.getSpec();
            List<CartItemCreate.Attribute> attributes = cartItemCreate.getAttributes();
            List<CartItemCreate.Material> materials = cartItemCreate.getMaterials();
            String packageGroupId = cartItemCreate.getPackageGroupId();

            idList.add(packageGroupId);
            if (ObjectUtils.allNotNull(item)) {
                idList.add(item.getId());
            }
            if (ObjectUtils.allNotNull(spec)) {
                idList.add(spec.getId());
            }
            if (CollectionUtil.isNotEmpty(attributes)) {
                List<String> attributeIdList = attributes.stream().map(CartItemCreate.Attribute::getId).sorted().collect(Collectors.toList());
                idList.addAll(attributeIdList);
            }
            if (CollectionUtil.isNotEmpty(materials)) {
                List<String> materialIdList = materials.stream().map(CartItemCreate.Material::getId).sorted().collect(Collectors.toList());
                idList.addAll(materialIdList);
            }
            String uniqueKey = MD5Util.encryptWithMD5(String.join(":", idList));
            if (assortMap.containsKey(uniqueKey)) {
                CartItemCreate oldItemCreate = assortMap.get(uniqueKey);
                Integer oldNumber = oldItemCreate.getItem().getNumber();
                cartItemCreate.getItem().setNumber(cartItemCreate.getItem().getNumber() + oldNumber);
            }
            assortMap.put(uniqueKey, cartItemCreate);
        }

        return Lists.newArrayList(assortMap.values());
    }

    public static List<ShoppingCartGoodsDTO> mergeSameGoodsCartDTO(List<ShoppingCartGoodsDTO> packageGoods) {
        if (CollectionUtil.isEmpty(packageGoods)) {
            return new ArrayList<>();
        }
        HashMap<String, ShoppingCartGoodsDTO> assortMap = new LinkedHashMap<>();
        for (ShoppingCartGoodsDTO goods : packageGoods) {
            List<String> idList = new ArrayList<>();

            String spuId = goods.getSpuId();
            String skuId = goods.getSkuId();
            List<Attribute> recipes = goods.getRecipes();
            List<Material> materials = goods.getMaterials();
            String packageGroupId = goods.getPackageGroupId();

            idList.add(packageGroupId);
            if (StringUtils.isNotBlank(spuId)) {
                idList.add(spuId);
            }
            if (StringUtils.isNotBlank(skuId)) {
                idList.add(skuId);
            }
            if (CollectionUtil.isNotEmpty(recipes)) {
                List<String> attributeIdList = recipes.stream().map(Attribute::getId).sorted().collect(Collectors.toList());
                idList.addAll(attributeIdList);
            }
            if (CollectionUtil.isNotEmpty(materials)) {
                List<String> materialIdList = materials.stream().map(Material::getId).sorted().collect(Collectors.toList());
                idList.addAll(materialIdList);
            }
            String uniqueKey = MD5Util.encryptWithMD5(String.join(":", idList));
            if (assortMap.containsKey(uniqueKey)) {
                ShoppingCartGoodsDTO oldGoods = assortMap.get(uniqueKey);
                BigDecimal saleCount = oldGoods.getSaleCount();
                goods.setSaleCount(saleCount.add(goods.getSaleCount()));
            }
            assortMap.put(uniqueKey, goods);
        }
        return Lists.newArrayList(assortMap.values());
    }

}
