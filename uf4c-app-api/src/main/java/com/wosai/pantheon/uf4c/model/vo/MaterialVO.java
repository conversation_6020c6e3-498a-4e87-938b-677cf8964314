package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.validation.constraints.UUID;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * created by shij on 2019/7/23
 */
@Data
public class MaterialVO {
    private String id;

    @NotBlank
    @Size(max = 20)
    private String name;

    @NotNull
    @Min(0)
    private Integer price;

    @NotNull
    @Min(1)
    private Integer seq;

    /**
     * 状态
     * 0：估清
     * 1：在售
     */
    private Integer status;
}
