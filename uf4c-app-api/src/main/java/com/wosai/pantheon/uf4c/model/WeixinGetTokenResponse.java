package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * 微信接口响应字段
 */
@Data
public class WeixinGetTokenResponse implements Serializable {
    private int errcode;
    private String errmsg;
    private String accessToken;
    private int expiresIn;
    private String refreshToken;
    private String openid;
    private String scope;

    @JsonIgnore
    public boolean isSuccess() {
        return this.errcode == 0;
    }
}
