package com.wosai.pantheon.uf4c.api;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.pantheon.uf4c.model.dto.HotSaleItemQueryRequest;
import com.wosai.pantheon.uf4c.model.vo.ItemDetailVO;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @create 2022/11/17
 */
@JsonRpcService(value = "/rpc/item/hotSale")
@Validated
public interface HotSaleItemRemoteService {

    /**
     * 门店热销商品
     * @param request
     * @return
     */
    ListResult<ItemDetailVO> listByStore(@Valid HotSaleItemQueryRequest request);

}
