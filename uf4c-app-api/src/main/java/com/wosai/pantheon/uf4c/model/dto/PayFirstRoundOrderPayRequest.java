package com.wosai.pantheon.uf4c.model.dto;

import com.wosai.pantheon.uf4c.model.CartItemCreate;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class PayFirstRoundOrderPayRequest extends PayRequest{
    /**
     * 是否需要检查购物车商品
     */
    private boolean checkCartItem = false;

    @NotNull
    private String storeId;

    private String merchantId;

    @NotNull
    private String tableId;

    private String tableNo;

    private String orderSn;

    private String remark;

    private Long totalAmount;

    private Long discountAmount;


    private String payUid;

    @NotEmpty
    private List<CartItemCreate> items;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户名头像
     */
    private String userIcon;

    /**
     * 服务类型
     */
    private Integer serviceType;

    /**
     * 是否是下单并支付
     */
    private boolean addAndPay;

    private String batchNo;

    /**
     * 是否是整单支付
     */
    private boolean allPay;

    private boolean needLocalRedeems=true;

    /**
     * 统一收银台的统一逻辑
     */
    private Object usingPayTools;


}
