package com.wosai.pantheon.uf4c.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class AlipayResponse implements Serializable {
    private ErrorResponse errorResponse;
    private AlipaySystemOauthTokenResponse alipaySystemOauthTokenResponse;
    private AlipayUserInfoShareResponse alipayUserInfoShareResponse;

    @JsonIgnore
    public boolean isSuccess() {
        return (alipaySystemOauthTokenResponse != null && StringUtils.isNotEmpty(alipaySystemOauthTokenResponse.getUserId()))
                || (alipayUserInfoShareResponse != null);
    }

    @JsonIgnore
    public String getCode() {
        if (errorResponse != null) {
            if (StringUtils.isNotEmpty(errorResponse.getSubCode())) {
                return errorResponse.getSubCode();
            }
            if (StringUtils.isNotEmpty(errorResponse.getCode())) {
                return errorResponse.getCode();
            }
        }
        return "unknown";
    }

    @JsonIgnore
    public String getMsg() {
        if (errorResponse != null) {
            if (StringUtils.isNotEmpty(errorResponse.getSubMsg())) {
                return errorResponse.getSubMsg();
            }
            if (StringUtils.isNotEmpty(errorResponse.getMsg())) {
                return errorResponse.getMsg();
            }
        }
        return "An unknown error occurred during the process of alipay authorization";
    }


    @Data
    public static class ErrorResponse {
        private String code;
        private String msg;
        private String subCode;
        private String subMsg;
    }

    @Data
    public static class AlipaySystemOauthTokenResponse {
        private String userId;
        private String accessToken;
    }

    @Data
    public static class AlipayUserInfoShareResponse {
        private String nickName;
        private String userId;
    }
}
