package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.validation.constraints.UUID;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * created by shij on 2019/7/22
 */
@Data
public class SpecOptionVO {

    private String id;

    @NotNull
    @Size(max = 20)
    private String name;

    @NotNull
    @Min(0)
    private Integer price;

    @NotNull
    @Min(1)
    private Integer seq;


    private Integer activityPrice;

    @Deprecated
    private Integer quotaCount;
    /**
     * 第二份优惠折扣
     */
    @Deprecated
    private Integer secondActivityDiscount;

    /**
     * 优惠价列表
     */
    private List<ItemDiscountPriceVO> discountPrices;

    /**
     * 优惠文案列表
     */
    private List<ItemDiscountText> discountTexts;

    /**
     * 到店价
     */
    private Integer arrivalPrice;

    /**
     * 外卖价
     */
    private Integer takeoutPrice;
}
