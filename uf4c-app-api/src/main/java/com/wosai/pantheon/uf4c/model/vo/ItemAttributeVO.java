package com.wosai.pantheon.uf4c.model.vo;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * created by shij on 2019/7/24
 */
@Data
public class ItemAttributeVO {

    private String id;

    @NotBlank
    @Size(max = 20)
    private String title;

    private Integer multiple;

    private Integer seq;

    @Valid
    private List<AttributeOptionVO> options;

}
