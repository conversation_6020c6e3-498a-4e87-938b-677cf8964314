package com.wosai.pantheon.uf4c.constant;

public class OrderConstant {
    public static final String SN = "sn";                                   //varchar(32) NOT NULL COMMENT '订单编号'
    public static final String TRANS_SN = "trans_sn";                       //varchar(32) NOT NULL COMMENT '门店码当日流水号'
    public static final String STATUS = "status";                           //int(10) NOT NULL DEFAULT '0' COMMENT '状态 与支付网关相同 0-CREATED 1000-PAY_IN_PROC 1200-PAID 1500-PAY_ERROR 2200-REFUNDED 2201-PARTIAL_REFUNDED 2500-REFUND_ERROR'
    public static final String SUBJECT = "subject";                         //varchar(64) NOT NULL COMMENT '订单标题'
    public static final String ITEMS = "items";                             //blob COMMENT '商品明细（JSON格式）'
    public static final String ORIGINAL_AMOUNT = "original_amount";         //int(10) NULL DEFAULT 0 COMMENT '订单总金额'
    public static final String EFFECTIVE_AMOUNT = "effective_amount";       //int(10) NULL DEFAULT 0 COMMENT '向支付通道请求的支付总金额'
    public static final String TOTAL_DISCOUNT = "total_discount";           //int(10) NULL DEFAULT 0 COMMENT '订单总金额中的优惠部分(优惠包括商户补贴和平台补贴)'
    public static final String REFUND_AMOUNT = "refund_amount";             //int(10) NULL DEFAULT 0 COMMENT '退款金额'
    public static final String PACK_AMOUNT = "pack_amount";                 //int(10) NULL DEFAULT 0 COMMENT '打包费用'
    public static final String BUYER_UID = "buyer_uid";                     //DEFAULT NULL COMMENT '付款人在支付服务商的用户ID'
    public static final String BUYER_LOGIN = "buyer_login";                 //varchar(45) DEFAULT NULL COMMENT '付款人在支付服务商登录帐号'
    public static final String MERCHANT_ID = "merchant_id";                 //NOT NULL COMMENT '商户id'
    public static final String STORE_ID = "store_id";                       //NOT NULL COMMENT '门店id'
    public static final String TERMINAL_ID = "terminal_id";                 //NOT NULL COMMENT '终端id'
    public static final String QR_CODE = "qr_code";                         //NOT NULL COMMENT '桌台码编号'
    public static final String QR_CODE_NAME = "qr_code_name";               //NOT NULL COMMENT '桌台码名称'
    public static final String PAY_WAY = "payway";                          //int(11) DEFAULT NULL COMMENT '支付通道 1-支付宝 3-微信, 与收钱吧支付网关保持一致'
    public static final String SUB_PAY_WAY = "sub_payway";                  //int(11) DEFAULT NULL COMMENT '支付方式 1-b2c 2-c2b 3-wap 4-小程序'
    public static final String TRADE_NO = "trade_no";                       //varchar(128) DEFAULT NULL COMMENT '支付网关返回的订单号'
    public static final String PAYMENT_LIST = "payment_list";               //blob COMMENT '支付明细（JSON格式）'
    public static final String PACKED = "packed";                           //tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否外带打包'
    public static final String EXTRA = "extra";                             //blob COMMENT '附加信息'
    public static final String EXTENDED = "extended";                       //blob COMMENT '支付通道透传参数, 扫码点单透传子属性 sqb_catering'
    public static final String CLIENT_SN = "client_sn";                     //varchar(64) DEFAULT NULL COMMENT '支付网关支付接口必须传的client_sn',
    public static final String SCENE = "scene";
    public static final String REMARK = "remark";
    public static final String TYPE = "type";


    //订单状态
    public static final int STATUS_CREATED = 0;

    public static final int STATUS_PAY_IN_PROC = 1000;
    public static final int STATUS_PAID = 1200;
    public static final int STATUS_PAY_CANCELED = 1300;
    public static final int STATUS_PAY_ERROR = 1501;

    public static final int STATUS_REFUND_INPROGRESS = 2100;

    public static final int STATUS_REFUNDED = 2201;
    public static final int STATUS_PARTIAL_REFUNDED = 2210;
    public static final int STATUS_REFUND_ERROR = 2501;
    public static final int STATUS_CANCEL_INPROGRESS = 3100;
    public static final int STATUS_CANCELED = 3201;
    public static final int STATUS_CANCEL_ERROR = 3501;
    public static final int STATUS_CREATE_THEN_EXPIRED = 4000;
    public static final int STATUS_DONE = 4001;


    public static final int FRONT_STATUS_TO_BE_PAID = 0;
    public static final int FRONT_STATUS_PAID = 1;
    public static final int FRONT_STATUS_REFUNDED = 2;
    public static final int FRONT_STATUS_INVALID = 3;
    public static final int FRONT_STATUS_FINISH = 4;

}
