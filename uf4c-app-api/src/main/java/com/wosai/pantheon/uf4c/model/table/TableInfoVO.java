package com.wosai.pantheon.uf4c.model.table;

import com.wosai.smartbiz.gds.enums.TableStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class TableInfoVO {
    /**
     * 桌台id
     */
    private String tableId;

    /**
     * 桌台号
     */
    private String tableNo;

    /**
     * 桌台状态
     */
    private TableStatusEnum status;

    /**
     * 座位数
     */
    private Integer seatCount;
}
