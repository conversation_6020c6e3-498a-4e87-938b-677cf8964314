package com.wosai.pantheon.uf4c.model;

import com.wosai.smartbiz.base.pojo.RedeemResult;
import com.wosai.smartbiz.base.pojo.Result;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CartAndRedeem implements Serializable {
    private Cart cart;

    private RedeemResult redeemResult;

}
