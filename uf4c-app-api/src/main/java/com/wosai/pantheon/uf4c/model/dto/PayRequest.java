package com.wosai.pantheon.uf4c.model.dto;

import com.wosai.market.tethys.api.dto.response.GroupBuyingActivity;
import com.wosai.market.trade.modal.CombinedPaymentRequest;
import com.wosai.market.trade.modal.HuabeiFenqiRequest;
import com.wosai.pantheon.uf4c.model.DeliverInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/9
 */
@Data
public class PayRequest implements Serializable {

    /**
     * 是否需要检查购物车商品
     */
    private boolean checkCartItem = false;

    @NotEmpty
    private String storeId;

    private String terminalId;

    private Integer payWay;

    @NotEmpty
    private String type;

    private String sn;

    private Boolean packed;

    private Long packAmount;

    private DeliverInfo deliveryInfo;

    private String remark;

    private HuabeiFenqiRequest hbFq;

    private Long orderVersion;


    // 兼容性参数，当传入的值为true时，表示为新版本
    private boolean compatible;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户名头像
     */
    private String userIcon;

    private String tableId;

    private String tableName;

    private String clientSn;

    private String terminalSn;

    /**
     * 扫码点餐消费者的手机号
     */
    private String cellPhone;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 微信加价购商品列表
     */
    private List<WxGoodsSubsidy> wxGoods;

    /**
     * 是否是储值并支付
     */
    private boolean rechargeAndPay = false;

    /**
     * 充值金额
     */
    private Long rechargeAmount ;

    /**
     * 充值方案id
     */
    private String rechargeRuleId;

    /**
     * 处置的scene
     */
    private String storedScene;

    /**
     * 服务类型
     */
    private Integer serviceType;

    /**
     * c端收银台传入的参数，原样进行透传
     */
    private Map<String, Object> cashierBizParams;

    /**
     * 优惠签名信息
     */
    private String redeemDigest;
    /**
     * 优惠金额
     */
    private Long totalDiscount;

    /**
     * 二次支付参数
     */
    private String acquiring;
    /**
     * 微信或者支付宝小程序的场景值
     */
    @ApiModelProperty("微信小微信或者支付宝小程序的场景值程序场景值")
    private String mpScene;

    /**
     * 门店快送订单类型的一个标识
     */
    @ApiModelProperty("微信门店快送业务特有的 traceId 参数")
    private String wxTraceId;

    /**
     * 组合支付信息， 用于计算优惠
     */
    private CombinedPaymentRequest combinedPayment;

    /**
     * 营销的透传参数, 透传给优惠平台
     */
    private Map<Object,Object> mkCustomInfo;

    /**
     * 客户端埋点透传参数
     */
    private Map<Object,Object> clientTrackingData;

    /**
     * 下单额外字段
     */
    private Map<String, Object> extra;

    /**
     * 拼团参数
     */
    private GroupBuyingActivity groupBuyingActivity;

    /**
     * 场景值
     */
    private String sqbPaySource;

    /**
     * 交易场景,用来后续判断交易应该选择哪个tradeApp, 或者应该如何收费
     * 如：tradeScene=payQrCode, 表示扫结账单去小程序支付的，使用收银员点单tradeApp(不收点单服务费)
     */
    private String tradeScene;

    /**
     * 支付请求头中需要存储的数据
     */
    private Map payHeaders;

    /**
     * 操作人
     */
    private String operator;

    //region[打印控制]

    /**
     * 是否勾选打印结账单
     */
    private Boolean checkPrintBill;

    /**
     * 是否勾选结账单中透出发票
     */
    private Boolean checkInvoice;

    //endregion[打印控制]
}
