package com.wosai.pantheon.uf4c.util;

import com.wosai.pantheon.order.enums.SpuType;
import com.wosai.pantheon.uf4c.model.CartItemCreate;
import com.wosai.smartbiz.oms.api.pojo.ShoppingCartGoodsDTO;
import org.apache.commons.lang3.EnumUtils;

public class OrderHelper {
    public static SpuType getNotNullSpuType(String spuType) {
        SpuType spuTypeEnum = EnumUtils.getEnum(SpuType.class, spuType);
        if (spuTypeEnum == null) {
            return SpuType.PRODUCT;
        }
        return spuTypeEnum;
    }

    /**
     * 转换加料对象
     *
     * @param m
     * @return
     */
    public static com.wosai.pantheon.order.model.dto.request.Material convertMaterial(CartItemCreate.Material m) {
        com.wosai.pantheon.order.model.dto.request.Material material = new com.wosai.pantheon.order.model.dto.request.Material();
        material.setId(m.getId());
        material.setName(m.getName());
        material.setNumber(m.getNumber());
        material.setPrice(m.getPrice());
        material.setSource(m.getSource());
        return material;
    }

    public static ShoppingCartGoodsDTO.RecommendMaterial toRecommendMaterial(CartItemCreate.RecommendMaterial m) {
        ShoppingCartGoodsDTO.RecommendMaterial rm = new ShoppingCartGoodsDTO.RecommendMaterial();
        rm.setId(m.getId());
        rm.setName(m.getName());
        rm.setPrice(m.getPrice());
        rm.setNumber(m.getNumber());
        rm.setSelected(m.isSelected());
        return rm;
    }
}
