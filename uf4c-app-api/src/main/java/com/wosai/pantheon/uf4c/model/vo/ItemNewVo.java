package com.wosai.pantheon.uf4c.model.vo;

import com.wosai.pantheon.core.uitem.model.ItemTag;
import com.wosai.pantheon.core.uitem.model.TimeSection;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ItemNewVo implements Serializable {
    /**
     * 小程序渲染唯一ID
     */
    private String uuid;
    /**
     * 商品ID
     */
    private String id;
    /**
     * 分类ID
     * 若零售有二级分类时。此处为1级分类
     */
    private String categoryId;

    /**
     * 二级分类id(零售用)
     */
    private String subCategoryId;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 售卖时段
     */
    private List<TimeSection> saleTimes;
    /**
     * 是否包含加料
     */
    private boolean hasMaterial = false;
    /**
     * 是否为多规格商品
     */
    private boolean isMultiple = false;
    /**
     * 是否需要选择规格
     */
    private boolean needChooseSpec = false;
    /**
     * 商品价格
     */
    private Integer price = 0;
    /**
     * 商品优惠价格
     */
    private Integer activityPrice = 0;

    /**
     * 储值折扣价展示字段
     */
    private String giftCardDiscountPriceText;
    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品图片
     * 2025-03-15 兼容历史版本小程序，优先返回1:1尺寸图片；否则返回原图
     */
    private String photoUrl;

    /**
     * 单位，例如个、盒、包等
     */
    private String unit;
    /**
     * 单位类型
     */
    private Integer unitType;
    /**
     * 商品库存
     */
    private Integer sku;
    /**
     * 是否售罄
     */
    private boolean outOfStock;
    /**
     * 是否为套餐商品
     */
    private boolean isPackage = false;
    /**
     * 是否为热销商品
     */
    private boolean isHotSale = false;
    /**
     * 热销数量
     */
    private Integer saleCount = 0;
    /**
     * 热销排名
     */
    private Integer hotSaleSeq = 0;
    /**
     * 起售数量
     */
    private Integer minSaleNum;
    private Long itemTag;
    /**
     * 商品标签
     */
    private List<ItemTag> itemTags;

    /**
     * 优惠标签信息，用于取代discountText、quotaCount
     */
    private List<ItemDiscountText> discountTexts;
    /**
     * 优惠文本(单品优惠与第二份优惠)
     */
    @Deprecated
    private String discountText;
    /**
     * 第二份优惠数量
     */
    @Deprecated
    private Integer quotaCount;
    /**
     * 商品优惠类型 1-单品优惠 2-第二份优惠
     */
    private Integer discountType;

    /**
     * 售卖时间
     */
    private ItemSaleTimeVO saleTime;

}
