package com.wosai.pantheon.uf4c.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RangeAreaDto implements Serializable {
    public static final String TYPE_CAMPUS = "campus";
    public static final String TYPE_AREA = "area";
    public static final String TYPE_ADDRESS = "address";
    private boolean enabled = false;
    private String type;
    private List<Area> areas;

    @Data
    public static class Area {
        private String name;
        private String campusPageName;
        private Integer id;
        private String longitude;
        private String latitude;
        private String address;
        private Integer deliveryFee;
    }
}
