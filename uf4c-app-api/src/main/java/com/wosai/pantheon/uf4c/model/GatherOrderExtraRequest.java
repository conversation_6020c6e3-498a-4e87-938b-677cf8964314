package com.wosai.pantheon.uf4c.model;

import com.wosai.pantheon.uf4c.constant.MiniProgramType;
import lombok.Data;

import java.io.Serializable;

@Data
public class GatherOrderExtraRequest implements Serializable {
    private MiniProgramType miniProgramType;
    private String storeId;
    private String merchantId;
    private String storeSn;

    private String terminalCode;
    private String fieldCode;
    private String fieldStyle;
    private String clientVersion;
    private int page = 1;
    private int pageSize = 10;

    private Integer payway;
    private Integer subPayway;

}
