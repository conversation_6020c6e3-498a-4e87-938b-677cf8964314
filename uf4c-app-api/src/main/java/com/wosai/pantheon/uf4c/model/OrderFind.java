package com.wosai.pantheon.uf4c.model;

import com.wosai.upay.common.bean.OrderBy;
import lombok.Data;

import java.util.List;

/**
 * created by shij on 2019/4/17
 */
@Data
public class OrderFind {

    private Integer page;
    private Integer pageSize;
    private List<OrderBy> orderBy;
    private String buyerUid;
    private String storeId;
    private String merchantId;
    private int serviceType;

    private String mealType;
    private String tableId;
}
