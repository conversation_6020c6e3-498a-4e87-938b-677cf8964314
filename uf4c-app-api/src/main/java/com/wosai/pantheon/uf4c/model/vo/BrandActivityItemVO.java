package com.wosai.pantheon.uf4c.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class BrandActivityItemVO extends ItemNewVo implements Serializable {
    /**
     * 活动id
     */
    private String activityId;
    /**
     * 活动产品id
     */
    private Long productId;
    /**
     * 活动规格名称
     */
    private String activitySpec;

    /**
     * 已加购数量
     */
    private int activityNumber;

    /**
     * 品牌商品使用，库存id
     * Constants.BRAND_PRODUCT_SOURCE_BRAND
     */
    private String stockId;
}
