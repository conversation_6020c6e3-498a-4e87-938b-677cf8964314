<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wosai.pantheon</groupId>
    <artifactId>uf4c-app-api</artifactId>
    <packaging>jar</packaging>
    <name>uf4c-app-api</name>
    <description>Wosai Project For Spring Boot</description>

    <parent>
        <groupId>com.wosai.pantheon</groupId>
        <artifactId>uf4c-app</artifactId>
        <version>5.21.0</version>
    </parent>

    <properties>
        <maven.source.version>3.0.1</maven.source.version>
        <maven.compiler.version>3.5.1</maven.compiler.version>
        <delombok.version>*********</delombok.version>
        <bitwalker.version>1.20</bitwalker.version>
        <commons.collections4.version>4.1</commons.collections4.version>
        <smartbiz.user.center.version>1.1.1</smartbiz.user.center.version>
        <wosai.awesome.goods.version>1.1.7</wosai.awesome.goods.version>
        <wosai.uprint.core.version>1.2.9-SNAPSHOT</wosai.uprint.core.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>awesome-order-api</artifactId>
            <version>4.99.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>apollo-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>tethys-api</artifactId>
            <version>4.93.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.svc.access</groupId>
                    <artifactId>ka-svc-access-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay.cashier</groupId>
            <artifactId>upay-cashier-api</artifactId>
            <version>1.2.5</version>
        </dependency>

        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.4</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>awesome-goods-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.5</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.market</groupId>
            <artifactId>trade-api</artifactId>
            <version>5.86.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>goods-center-api</artifactId>
            <version>2.80.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>uitem-core-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.smartbiz</groupId>
            <artifactId>order-center-api</artifactId>
            <version>6.68.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai.pantheon</groupId>
                    <artifactId>uprint-core-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>wosai-database-instrumentation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-data</artifactId>
        </dependency>

    </dependencies>

</project>
